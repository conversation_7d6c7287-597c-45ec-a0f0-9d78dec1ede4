/**业务模块构建入口文件 */

import { ModuleRegistryObject } from '@soyoung/react-native-bridge';

// 声明全局的lazyRequire函数
declare function lazyRequire(modulePath: string): any;

const bidI = 5;
ModuleRegistryObject.registeModule(
  bidI,
  () => {
    return {
      'income/feeExplain': {
        component: lazyRequire('./../src/views/income/feeExplain'),
        options: {
          title: '手工费明细',
          headerShown: false,
        },
      },
      'income/performanceScore': {
        component: lazyRequire('./../src/views/income/performanceScore'),
        options: {
          title: '绩效页面',
          headerShown: false,
        },
      },
      'performance/index': {
        component: lazyRequire('./../src/views/performance/index'),
        options: {
          title: '执行记录',
          headerShown: false,
        },
      },
      'staff/chat': {
        component: lazyRequire('./../src/views/entry/index'),
        options: {
          title: '应用入口 后面再替换',
          headerShown: false,
        },
      },

      'appEntry/index': {
        component: lazyRequire('./../src/views/entry/index'),
        options: {
          title: '应用入口',
          headerShown: false,
        },
      },
      // 'test/index': {
      //   component: lazyRequire('./../src/views/test/index'),
      //   options: {
      //     title: '首个测试页面',
      //     headerShown: false,
      //   },
      // },
      'client/list': {
        component: lazyRequire('./../src/views/client/list'),
        options: {
          title: '客户列表',
          headerShown: false,
        },
      },
      'client/detail': {
        component: lazyRequire('./../src/views/client/detail'),
        options: {
          title: '客户详情',
          headerShown: false,
        },
      },
      'client/info': {
        component: lazyRequire('./../src/views/client/info'),
        options: {
          title: '客户信息',
          headerShown: false,
        },
      },
      'client/filter': {
        component: lazyRequire('./../src/views/client/filter'),
        options: {
          title: '筛选客户',
          headerShown: false,
        },
      },
      'income/manage': {
        component: lazyRequire('./../src/views/income/manage'),
        options: {
          title: '提成管理',
          headerShown: false,
        },
      },
      'followup/appointment': {
        component: lazyRequire('./../src/views/followup/appointment'),
        options: {
          title: '预约页面',
          headerShown: false,
        },
      },
      'followup/visited': {
        component: lazyRequire('./../src/views/followup/visited'),
        options: {
          title: '回访页面',
          headerShown: false,
        },
      },
      'followup/task': {
        component: lazyRequire('./../src/views/followup/task'),
        options: {
          title: '任务页面',
          headerShown: false,
        },
      },
      'followup/taskDetail': {
        component: lazyRequire('./../src/views/followup/taskDetail'),
        options: {
          title: '任务详情',
          headerShown: false,
        },
      },
      'followup/imagelist': {
        component: lazyRequire('./../src/views/followup/imagelist/index'),
        options: {
          title: '资源列表',
          headerShown: false,
        },
      },
      'followup/taskFinish': {
        component: lazyRequire('./../src/views/followup/taskFinish'),
        options: {
          title: '任务完成',
          headerShown: false,
        },
      },
      'followup/filters': {
        component: lazyRequire('./../src/views/followup/filters'),
        options: {
          title: '筛选页面',
          headerShown: false,
        },
      },
      'target/index': {
        component: lazyRequire('./../src/views/target/index'),
        options: {
          title: '目标页面',
          headerShown: false,
        },
      },
      'record/index': {
        component: lazyRequire('./../src/views/record/index'),
        options: {
          title: '服务记录',
          headerShown: false,
        },
      },
      'record/detail': {
        component: lazyRequire('./../src/views/record/detail'),
        options: {
          title: '服务记录详情',
          headerShown: false,
        },
      },
      'staff/mine': {
        component: lazyRequire('./../src/views/my/index'),
        options: {
          title: '我的页面',
          headerShown: false,
        },
      },
      'staff/store': {
        component: lazyRequire('./../src/views/my/store'),
        options: {
          title: '门店',
          headerShown: false,
        },
      },
      'staff/about': {
        component: lazyRequire('./../src/views/my/about'),
        options: {
          title: '关于我们',
          headerShown: false,
        },
      },
      'followup/calendar': {
        component: lazyRequire('./../src/views/followup/calendar'),
        options: {
          title: '日历页面',
          headerShown: false,
        },
      },
      'followup/visitedDetail': {
        component: lazyRequire('./../src/views/followup/detail'),
        options: {
          title: '回访详情',
          headerShown: false,
        },
      },

      'staff/cross': {
        component: lazyRequire('./../src/views/my/cross/index'),
        options: {
          title: '协同',
          headerShown: false,
        },
      },
      'staff/createReserve': {
        component: lazyRequire('./../src/views/workbench/create-reserve/index'),
        options: {
          title: '新建预约',
          headerShown: false,
        },
      },
      'staff/page-select': {
        component: lazyRequire('./../src/views/workbench/page-select/index'),
        options: {
          title: '选择页面',
          headerShown: false,
        },
      },
      'data/index': {
        component: lazyRequire('./../src/views/data/index'),
        options: {
          title: '数据',
          headerShown: false,
        },
      },
      'data/rankings': {
        component: lazyRequire('./../src/views/data/rankings'),
        options: {
          title: '',
          headerShown: false,
        },
      },
    };
  },
  () => {
    //TODO: 模块即将销毁，如果需求，清理相关内存
  }
);

export default () => {
  // Do nothing
};
