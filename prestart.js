const {execSync} = require('child_process');
const os = require('os');
const platform = os.platform();
const open = require('open');
const PORT = 8081; //RN 运行默认端口，不可更改

// 获取本地IP地址
function getLocalIP() {
  const os = require('os');
  const ifaces = os.networkInterfaces();
  let ip = '';
  for (let dev in ifaces) {
    for (let i = 0; i < ifaces[dev].length; i++) {
      if (
        !ifaces[dev][i].internal &&
        ifaces[dev][i].family === 'IPv4' &&
        !ifaces[dev][i].address.includes('::') &&
        ifaces[dev][i].address !== '127.0.0.1'
      ) {
        ip = ifaces[dev][i].address;
        break;
      }
    }
  }
  return ip;
}

//获取端口进程信息
function getProcessInfo(port) {
  if (platform === 'win32') {
    const order = `netstat -aon | findstr ${port}`;
    try {
      const stdout = execSync(order);
      const portInfo = stdout.toString().trim().split(/\s+/);
      const pId = portInfo[portInfo.length - 1];
      const processStdout = execSync(`tasklist | findstr ${pId}`);
      const [pName] = processStdout.toString().trim().split(/\s+/);
      return {
        pId,
        pName,
      };
    } catch (error) {
      return error;
    }
  } else {
    const order = `lsof -i :${port}`;
    try {
      const stdout = execSync(order);
      const [pName, pId] = stdout.toString().trim().split(/\n/)[1].split(/\s+/);
      return {
        pId,
        pName,
      };
    } catch (error) {
      return error;
    }
  }
}

const localIP = getLocalIP(); // 本地IP
const {pId, pName} = getProcessInfo(PORT); //占用端口的进程ID和名称

if (pId) {
  console.log(`端口号${PORT}已经占用，杀掉进程${pName}-${pId}`);
  if (platform === 'win32') {
    execSync(`taskkill /f /pid ${pId}`);
  } else {
    execSync(`kill ${pId}`);
  }
} else {
  console.log(`端口号${PORT}没有占用`);
}

const cli_im = `https://api.cl2wm.cn/api/qrcode/code?mhid=sELPDFnok80gPHovKdI&text=app.soyoung://rn/common/dev?rn_server_url=${localIP}:${PORT}`;

console.log(`打开扫码页面 ${cli_im}，请用手机照相功能扫码`);
open(`${cli_im}`);
