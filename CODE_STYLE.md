# 代码风格指南

本项目配置了 ESLint、Prettier 和 Husky 来确保代码风格的一致性。

## 工具配置

### ESLint
- **配置文件**: `.eslintrc.js`
- **忽略文件**: `.eslintignore`
- **作用**: 检查代码质量和风格问题

### Prettier
- **配置文件**: `.prettierrc.js`
- **忽略文件**: `.prettierignore`
- **作用**: 自动格式化代码

### Husky
- **配置目录**: `.husky/`
- **作用**: Git hooks，在提交前自动检查代码

## 可用的 npm scripts

```bash
# 检查代码格式和质量问题
npm run lint

# 自动修复可修复的问题
npm run lint:fix

# 检查代码格式是否符合 Prettier 规范
npm run format:check

# 格式化所有代码文件
npm run format

# 类型检查（仅检查不生成文件）
npm run type-check
```

## Git 提交流程

当你执行 `git commit` 时，会自动触发以下检查：

1. **lint-staged**: 只对暂存的文件运行 ESLint 和 Prettier
2. **类型检查**: 运行 TypeScript 编译检查
3. **提交信息格式检查**: 确保提交信息符合规范

### 提交信息格式

提交信息必须遵循以下格式：
```
<类型>: <描述>
```

**支持的类型**：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 重构代码
- `test`: 测试相关
- `chore`: 杂务(依赖更新等)
- `perf`: 性能优化
- `build`: 构建相关
- `ci`: CI/CD相关
- `revert`: 回滚代码

**示例**：
```bash
git commit -m "feat: 添加用户登录功能"
git commit -m "fix: 修复首页加载问题"
git commit -m "docs: 更新API文档"
```

## VSCode 配置

### 推荐扩展
项目已配置 `.vscode/extensions.json`，会推荐安装：
- ESLint
- Prettier
- TypeScript 相关扩展
- React Native 相关扩展

### 自动格式化
项目已配置 `.vscode/settings.json`：
- 保存时自动格式化
- 自动修复 ESLint 问题
- 统一的格式化工具配置

## 代码风格规则

### 基础规则
- 使用 2 个空格缩进
- 使用单引号
- 行末加分号
- 每行最大 80 个字符
- 使用 ES5 风格的尾随逗号

### TypeScript 规则
- 优先使用接口而非 any 类型
- 未使用的变量需要以 `_` 开头
- 启用严格的类型检查

### React Native 规则
- 避免使用内联样式
- 避免使用颜色字面量
- 组件应该有适当的类型定义

## 常见问题

### 1. 提交被拒绝
如果提交被拒绝，请按照错误信息修复问题：

```bash
# 修复 ESLint 问题
npm run lint:fix

# 手动格式化代码
npm run format

# 检查类型错误
npm run type-check
```

### 2. 跳过检查（不推荐）
如果紧急情况需要跳过检查：

```bash
# 跳过 pre-commit hooks
git commit --no-verify -m "紧急修复"
```

### 3. 批量格式化现有代码
对整个项目进行格式化：

```bash
npm run format
```

## 团队协作

1. **首次设置**: 安装推荐的 VSCode 扩展
2. **保持同步**: 定期拉取最新的配置文件更新
3. **问题反馈**: 如果遇到配置问题，及时反馈给团队

这样可以确保团队的代码风格保持一致，提高代码质量和可维护性。 