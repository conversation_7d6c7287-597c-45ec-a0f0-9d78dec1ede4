module.exports = {
  // 基础配置
  semi: true, // 语句结尾加分号
  singleQuote: true, // 使用单引号
  quoteProps: 'as-needed', // 仅在需要时给对象属性加引号
  trailingComma: 'es5', // 尾随逗号（ES5风格）
  
  // 缩进和空格
  tabWidth: 2, // 缩进宽度2个空格
  useTabs: false, // 使用空格而不是tab
  
  // 换行
  printWidth: 80, // 每行最大字符数
  endOfLine: 'lf', // 换行符类型
  
  // JSX 相关
  jsxSingleQuote: true, // JSX中使用单引号
  bracketSameLine: false, // JSX标签的闭合括号换行
  
  // 括号
  bracketSpacing: true, // 对象字面量的括号间加空格 { foo: bar }
  bracketSameLine: false, // 多行元素的>放在最后一行的末尾
  
  // 箭头函数
  arrowParens: 'avoid', // 单参数箭头函数省略括号
  
  // 其他
  insertPragma: false, // 不插入@prettier标记
  requirePragma: false, // 不需要@prettier标记
  proseWrap: 'preserve', // 按原样处理markdown
  htmlWhitespaceSensitivity: 'css', // HTML空格敏感性
  embeddedLanguageFormatting: 'auto', // 自动格式化嵌入的语言
}; 