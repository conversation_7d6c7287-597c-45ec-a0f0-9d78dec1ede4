#!/bin/sh

# export BUILD_VERSIOM="2.0"
if [[ "$BUILD_VERSIOM" == "1.0" ]]
then
  sh ./build/buildO.sh $@
  exit
fi

firstParam=$(echo $1 | grep -E "^new$|^old$")
if [[ "$firstParam" == "" ]]
then
  echo "首个参数必须指定 \"new\" 或者 \"old\" "
  exit
fi

shellParams=""
if [[ $# > 1 ]]
then
  params="$@"
  shellParams=${params: 4}
fi

if [[ $firstParam == new ]]
then
  echo 'sh ./build/buildN.sh '$shellParams
  sh ./build/buildN.sh $shellParams
else
  echo 'sh ./build/buildO.sh '$shellParams
  sh ./build/buildO.sh $shellParams
fi