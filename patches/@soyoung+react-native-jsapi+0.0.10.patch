diff --git a/node_modules/@soyoung/react-native-jsapi/src/v1.ts b/node_modules/@soyoung/react-native-jsapi/src/v1.ts
index 0d19836..f2ed84b 100644
--- a/node_modules/@soyoung/react-native-jsapi/src/v1.ts
+++ b/node_modules/@soyoung/react-native-jsapi/src/v1.ts
@@ -56,7 +56,8 @@ module jsApiV1 {
     if (!hasNativeCode()) {
       return;
     }
-    return new NativeEventEmitter(syNativeModules.SYEventPipe);
+    // 使用正确的原生模块避免 Invariant Violation 错误
+    return Platform.OS === 'android' ? new NativeEventEmitter() : new NativeEventEmitter(syNativeModules.SYEventPipe);
   };
   /**
    * 获取当前domain实现
