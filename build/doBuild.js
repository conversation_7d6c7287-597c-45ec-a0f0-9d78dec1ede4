'use strict';

/**
 * Metro 构建脚本
 */

const BasePath = __dirname;

function yargsArgvParse() {
  const yargs = require('yargs');

  let yargsObject = yargs
    .usage('构建RNBundle工具')
    .option('platform', {
      alias: 'P',
      description: '构建平台[ios/android]',
      type: 'string',
      demandOption: true,
    })
    .option('entry-file', {
      alias: 'E',
      description: '构建入口文件',
      type: 'string',
      demandOption: true,
    })
    .option('bundle-output', {
      alias: 'B',
      description: '构建输出路径',
      type: 'string',
      demandOption: true,
    })
    .option('assets-dest', {
      alias: 'A',
      description: '资源文件写入路径',
      type: 'string',
      demandOption: true,
    })
    .option('dev', {
      alias: 'D',
      description: '是否dev模式',
      type: 'boolean',
      demandOption: false,
      default: false,
    })
    .option('sourcemap-output', {
      alias: 'S',
      description: 'source map文件输出路径',
      type: 'string',
      demandOption: false,
    })
    .option('reset-cache', {
      alias: 'R',
      description: '是否移除缓存文件，默认移除',
      type: 'boolean',
      demandOption: false,
      default: true,
    })
    .option('config', {
      alias: 'C',
      description: '配置文件路径，默认使用metro.config.js',
      type: 'string',
      demandOption: false,
    })
    .version()
    .help();
  const _yargsObject = yargsObject;
  yargsObject = yargsObject.fail((msg, err, yargs1) => {
    throw {yargs: true, info: _yargsObject, inner: new Error(msg)};
  });
  const commandArgvs = yargsObject.argv;
  return commandArgvs;
}

async function main() {
  try {
    const fs = require('fs');
    let rn_loadConfig;
    try {
      rn_loadConfig = require('react-native/cli').loadConfig;
    } catch (e) {
      rn_loadConfig = require('@react-native-community/cli/build').loadConfig;
    }

    const bid = fs.readFileSync(`${BasePath}/config/bid.conf`).toString();
    if (
      bid === undefined ||
      bid === '' ||
      isNaN(Number(bid)) ||
      Number(bid) < 0
    ) {
      throw new Error('获取bid错误');
    }
    global.ID_MASK_SHIFT = 16;

    // 判断bid最大限制
    // eslint-disable-next-line no-bitwise
    if (bid >= 1 << (global.ID_MASK_SHIFT - 1)) {
      throw new Error(
        // eslint-disable-next-line no-bitwise
        `bid超出最大限制, 最大限制为: ${(1 << (global.ID_MASK_SHIFT - 1)) - 1}`,
      );
    }

    // 设置是否构建公共包
    if (Number(bid) === 0) {
      global.BUILD_COMMON = true;
    } else {
      global.BUILD_COMMON = false;
    }

    // 创建缓存控件，供配置文件使用
    global.BUILD_TEMP_CACHE = {};

    // 设置公共参数存储bid
    global.BUILD_BID = Number(bid);
    process.env.BUILD_BID = bid;

    const commandArgvs = yargsArgvParse();
    const platform = commandArgvs.platform;

    if (!fs.existsSync(`${BasePath}/config/mapping`)) {
      fs.mkdirSync(`${BasePath}/config/mapping`);
    }

    // 设置公共包id mapping
    const commonMappingExist = fs.existsSync(
      `${BasePath}/config/mapping/common.${platform}.mapping`,
    );
    if (global.BUILD_COMMON === false && commonMappingExist === false) {
      throw new Error(
        '未找到common.mapping, 构建业务bundle之前请先构建基础bundle',
      );
    }
    if (commonMappingExist) {
      const commonJson = fs
        .readFileSync(`${BasePath}/config/mapping/common.${platform}.mapping`)
        .toString();
      const origCommonArray = JSON.parse(commonJson);
      // 获取最后一个id
      const lastItem = origCommonArray.at(-1);
      global.COMMON_NEXT_ID = lastItem === undefined ? 0 : lastItem.id + 1;
      // 数据结构转换
      /**
       * [{id:0, path:'xxx1'},{id:1, path:'xxx2'}]
       * 转换成
       * {'xxx1':0, 'xxx2': 1}
       */
      const commonMapping = {};
      origCommonArray.forEach(element => {
        commonMapping[element.path] = element.id;
      });
      global.READONLY_COMMON_MAPPING = Object.freeze(commonMapping);
    } else {
      global.COMMON_NEXT_ID = 0;
      global.READONLY_COMMON_MAPPING = Object.freeze({});
    }

    global.COMMON_MAPPING = {};

    global.BUSS_NEXT_ID = 0;
    global.BUSS_MAPPING = {};
    const rpath = require('path');
    global.ENTRY_FILE_ABSOLUTE_PATH = rpath.resolve(commandArgvs['entry-file']);

    const rnConfig = rn_loadConfig();

    const metroPlugin = require('@react-native-community/cli-plugin-metro/build/commands');
    if (
      !metroPlugin ||
      !metroPlugin.default ||
      !(metroPlugin.default instanceof Array) ||
      metroPlugin.default.length < 3
    ) {
      throw new Error('获取metroPlugin失败');
    }

    // 确认输出bundle类型
    if (global.BUILD_COMMON === true) {
      const bundleCommand = metroPlugin.default[0];
      if (!bundleCommand.func || !(bundleCommand.func instanceof Function)) {
        throw new Error('获取metroPlugin.bundleCommand失败');
      }
      if (
        !commandArgvs.config &&
        fs.existsSync(`${BasePath}/config/build.comm.config.js`)
      ) {
        commandArgvs.config = `${BasePath}/config/build.comm.config.js`;
      }

      await bundleCommand.func([], rnConfig, commandArgvs, undefined);
    } else {
      let ramBundleCommand = metroPlugin.default[1];

      // 安卓暂时支持不了ram bundle, 所以先打plan bundle
      if (platform === 'android') {
        ramBundleCommand = metroPlugin.default[0];
      }

      if (
        !ramBundleCommand.func ||
        !(ramBundleCommand.func instanceof Function)
      ) {
        throw new Error('获取metroPlugin.ramBundleCommand失败');
      }
      if (
        !commandArgvs.config &&
        fs.existsSync(`${BasePath}/config/build.buss.config.js`)
      ) {
        commandArgvs.config = `${BasePath}/config/build.buss.config.js`;
      }

      // 安卓暂时支持不了ram bundle, 所以先打plan bundle
      if (platform === 'android') {
        await ramBundleCommand.func([], rnConfig, commandArgvs, undefined);
      } else {
        await ramBundleCommand.func([], rnConfig, commandArgvs);
      }
    }

    // 数据结构转换
    /**
     * [{id:0, path:'xxx1'},{id:1, path:'xxx2'}]
     * 转换成
     * {'xxx1':0, 'xxx2': 1}
     */
    const transformAndSortJsonFromMapping = function (mapping) {
      const array = [];
      Object.keys(mapping).forEach(key => {
        array.push({
          id: mapping[key],
          path: key,
        });
      });

      return array.sort(function (item1, item2) {
        return item1.id - item2.id;
      });
    };

    if (global.BUILD_COMMON === true) {
      const saveMapping = transformAndSortJsonFromMapping(
        global.COMMON_MAPPING,
      );
      fs.writeFileSync(
        `${BasePath}/config/mapping/common.${platform}.mapping`,
        JSON.stringify(saveMapping, undefined, '\t'),
      );
    } else {
      const saveMapping = transformAndSortJsonFromMapping(global.BUSS_MAPPING);
      fs.writeFileSync(
        `${BasePath}/config/mapping/buss.${platform}.mapping`,
        JSON.stringify(saveMapping, undefined, '\t'),
      );
    }
    process.exit(0);
  } catch (error) {
    if (error.yargs && error.yargs === true) {
      console.log(
        `Error.buildBundle: ${error.inner.name}-${error.inner.message}`,
      );
      error.info.showHelp();
    } else {
      console.log(`Error.buildBundle: ${error.message}`);
      console.log(error);
    }
    process.exit(1);
  }
}

main();
