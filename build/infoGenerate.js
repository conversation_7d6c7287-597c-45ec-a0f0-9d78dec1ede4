'use strict';

/**
 * args[0]: ios/android
 * args[1]: bundle所在目录上级文件夹
 * args[2]: md5导出地址
 * args[2]: info文件导出地址
 */

const BasePath = __dirname;
const fs = require('fs');

function generateMd5(resPath, outputPath) {
  /**
   * #CodePush Hash
   * codePushJsPath="$node_modules_dir/node_modules/react-native-code-push/scripts/generateBundledResourcesHash.js"
   * #资源文件目录
   * resPath="$androidDir/CodePush/android/"
   * #bundle目录
   * bundlePath="$workspace/app/src/main/assets/main.jsbundle"
   * #生成CodePushHash文件存放的目录
   * assetsPath="$workspace/app/src/main/assets/"
   * node "$codePushJsPath" "$resPath" "$bundlePath" "$assetsPath"
   */
  const hashPath = `${outputPath}/CodePushHash`;
  if (fs.existsSync(hashPath)) {
    fs.unlinkSync(hashPath);
  }

  const hashInfoPath = `${outputPath}/CodePushHashInfo`;
  if (fs.existsSync(hashInfoPath)) {
    fs.unlinkSync(hashInfoPath);
  }

  const cp = require('child_process');
  const path = require('path');

  const codePushJsPath = `${BasePath}/./generateBundledResourcesHash.js`;
  const bundlePath = path.join(resPath, 'main.jsbundle');

  return new Promise((resolve, reject) => {
    cp.exec(
      `node ${codePushJsPath} ${resPath} ${bundlePath} ${outputPath}`,
      (err, stdout, stderr) => {
        if (err) {
          reject(err);
          return;
        }

        const md5 = fs.readFileSync(`${outputPath}/CodePushHash`).toString();
        resolve(md5);
      },
    );
  });
}

try {
  const args = process.argv.slice(2);
  if (args.length !== 4) {
    throw new Error('请分别传入os、Bundle路径、md5导出地址、输出路径');
  }

  const os = args[0];
  if (['ios', 'android'].includes(os) === false) {
    throw new Error('传入系统参数错误');
  }

  const bid = fs.readFileSync(`${BasePath}/config/bid.conf`).toString();
  if (bid === undefined || bid === '') {
    throw new Error('获取bid错误');
  }

  var resPath = args[1];
  if (resPath.startsWith('./')) {
    resPath = resPath.substring(2);
  }

  generateMd5(resPath, args[2])
    .then(md5 => {
      if (md5 === undefined || md5 === '') {
        throw new Error('获取bundle md5错误');
      }

      const info = {
        bid: bid,
        os: os,
        md5: md5,
      };

      const outputPath = args[3];
      fs.writeFile(`${outputPath}/info`, JSON.stringify(info), err => {
        if (err) {
          throw err;
        }
        console.log('sucessed');
      });
    })
    .catch(e => {
      throw e;
    });
} catch (error) {
  console.log(`Error.infoGenerate: ${error.name}-${error.message}`);
}
