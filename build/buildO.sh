#!/bin/sh
# 各位大佬，改动脚本请通知@包龙星
readonly version=$1
readonly branch=$2
readonly TargetPath=$(dirname "$0")
readonly Output="${TargetPath}/output"
readonly SourceMap="${TargetPath}/sourcemap"
readonly Uploader="${TargetPath}/uploader"
readonly maven_url="http://maven1.sy.soyoung.com/repository/RN/"
readonly CRTDIR_DIR=$(pwd)
function realpath { echo $(cd $(dirname $1); pwd)/$(basename $1); }

function log() {
  if [ "$1" = "error" ]
  then
    echo "error: $2"
  else 
    echo "$1 -- $2"
  fi
}

log '>>>>>开始构建package:' 分支名：$branch-$version

# 校验参数
re="^([0-9]+.){2}[0-9]+$"
if [[ $version =~ $re ]]
then
log "$version 版本号校验通过"
else 
log error "填写正确的版本号，如:1.1.1（ ^(?:\d+.){2}\d+$ ）"
exit
fi
if [ ${#branch} -eq 0 ]
then
log error '分支不能为空'
exit
fi
# 校验参数 end

# diff package.json
which md5sum 
if [ "$?" -ne 0 ]
then
 brew install md5sha1sum
fi
# 当前package.json的md5
package_json_md5=$(md5sum package.json|cut -d" " -f1)
# 上次package.json的md5
last_package_json_md5=$(cat ${TargetPath}/packagejson.md5)
if [ "$package_json_md5" != "$last_package_json_md5" ]
then
log "package.json 文件有改动，执行npm安装依赖文件"
npm install
# 安装之后小版本可能会变动，写变动之后md5进入文件
package_json_md5=$(md5sum package.json|cut -d" " -f1)
echo $package_json_md5 > ${TargetPath}/packagejson.md5
fi
# diff package.json end

# 获取当前bundle的路由列表
router_list=$(node build/routerList.js)
if [[ $router_list == Error.routerList* ]]
then
 log error "运行 node routerList.js 失败，请检查 ./src/pages.ts 文件是否正确. ${router_list}"
 exit 1
fi
# build
rm -rf $Output $SourceMap $Uploader
mkdir -p $Output $SourceMap $Uploader

AbsoluteUploaderPath=$(realpath $Uploader)

# cp package.json
for loop in ios android
do
  log "构建开始:$loop"
  platform=$Output/$loop
  mkdir -p $platform

  CodePush="${platform}/CodePush"
  mkdir -p $CodePush/$loop

  cp package.json $CodePush/package.backup.json
  mkdir -p ./sourcemap
  ./node_modules/.bin/react-native bundle --platform $loop --entry-file index.ts --bundle-output $CodePush/$loop/main.jsbundle --assets-dest $CodePush/$loop/ --dev false --sourcemap-output $SourceMap/$loop.map --reset-cache 
  # 路由列表写入bundle
  echo "$router_list" > $CodePush/$loop/rts
  # zip release folder
  cd $platform
  zip -r -q $AbsoluteUploaderPath/$loop.zip CodePush
  cd $CRTDIR_DIR
  # upload 
  curl -v -u uploader:123456 --upload-file $Uploader/$loop.zip $maven_url${branch//\//"_"}/$version/
  curl -v -u uploader:123456 --upload-file $SourceMap/$loop.map $maven_url${branch//\//"_"}/$version/
  # reset workspace
  rm -rf $CodePush
  rm -rf ./uploader
  rm -rf ./sourcemap
  log ">>>>>release end"
done
# end

