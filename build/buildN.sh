#!/bin/sh
readonly version=$1
readonly branch=$2
readonly rn_bundle_branch=$3
readonly rn_bundle_version=$4
readonly TargetPath=$(dirname "$0")
readonly Output="${TargetPath}/output"
readonly SourceMap="${TargetPath}/sourcemap"
readonly Uploader="${TargetPath}/uploader"
readonly Mapping="${TargetPath}/config/mapping"
readonly base_maven_url="http://maven1.sy.soyoung.com/repository/RN/base/"
readonly maven_url="http://maven1.sy.soyoung.com/repository/RN/staff_upacking/"
readonly CRTDIR_DIR=$(pwd)
function realpath { echo $(cd $(dirname $1); pwd)/$(basename $1); }

function log() {
  if [ "$1" = "error" ]
  then
    echo "error: $2"
  else
    echo "$1 -- $2"
  fi
}

log '>>>>>开始构建package:' 分支名：$branch-$version

# 校验参数
re="^([0-9]+.){2}[0-9]+$"
if [[ $version =~ $re ]]
then
log "$version 版本号校验通过"
else
log error "填写正确的版本号，如:1.1.1（ ^(?:\d+.){2}\d+$ ）"
exit
fi
if [ ${#branch} -eq 0 ]
then
log error '分支不能为空'
exit
fi
# 校验参数 end

# diff package.json
which md5sum
if [ "$?" -ne 0 ]
then
 brew install md5sha1sum
fi
# 当前package.json的md5
package_json_md5=$(md5sum package.json|cut -d" " -f1)
# 上次package.json的md5
last_package_json_md5=$(cat ${TargetPath}/packagejson.md5)
if [ "$package_json_md5" != "$last_package_json_md5" ]
then
log "package.json 文件有改动，执行npm安装依赖文件"
npm install
# 安装之后小版本可能会变动，写变动之后md5进入文件
package_json_md5=$(md5sum package.json|cut -d" " -f1)
echo $package_json_md5 > ${TargetPath}/packagejson.md5
fi
# diff package.json end

# 获取当前bundle的路由列表
# router_list=$(node build/routerList.js)
# if [[ $router_list == Error.routerList* ]]
# then
#  log error "运行 node routerList.js 失败，请检查 ./src/pages.ts 文件是否正确. ${router_list}"
#  exit 1
# fi


platform_ary=(ios android)
# build
rm -rf $Mapping && mkdir -p $Mapping
if [ -n "${rn_bundle_branch}" ] && [ -n "${rn_bundle_version}" ]; then
	for loop in "${platform_ary[@]}"
    do 
    	mapping_url="$base_maven_url${rn_bundle_branch//\//_}/${rn_bundle_version}/common.${loop}.mapping"
    	if [ "$(curl -s -o /dev/null -I -w "%{http_code}" $mapping_url)" = "200" ]; then
    		curl -o $Mapping/common.$loop.mapping $mapping_url
      else
        echo "指定 mapping 版本不存在"
			  exit -1
      fi
    done
fi

# build
rm -rf $Output $SourceMap $Uploader
mkdir -p $Output $SourceMap $Uploader

AbsoluteUploaderPath=$(realpath $Uploader)

# cp package.json
for loop in "${platform_ary[@]}"
do
  log "构建开始:$loop"
  platform=$Output/$loop
  mkdir -p $platform

  CodePush="${platform}/CodePush"
  mkdir -p $CodePush

  # cp package.json $CodePush/package.backup.json
  # ./node_modules/.bin/react-native bundle --platform $loop --entry-file index.ts --bundle-output $CodePush/main.jsbundle --assets-dest $CodePush/ --dev true --sourcemap-output $SourceMap/$loop.map --reset-cache

   if [ "$loop" = "android" ] ;then
    node build/doBuild.js --platform $loop --entry-file ./buildEntry/build.index.ts --bundle-output $CodePush/main.jsbundle --assets-dest $CodePush/ --dev false --minify false --sourcemap-output $SourceMap/$loop.map --reset-cache
    sudo ./node_modules/hermes-engine/linux64-bin/hermesc -emit-binary -out $CodePush/main.jsbundle.hbc $CodePush/main.jsbundle
    rm -f $CodePush/main.jsbundle
    mv $CodePush/main.jsbundle.hbc $CodePush/main.jsbundle
  else
    node build/doBuild.js --platform $loop --entry-file ./buildEntry/build.index.ts --bundle-output $CodePush/main.jsbundle --assets-dest $CodePush/ --dev false --sourcemap-output $SourceMap/$loop.map --reset-cache
  fi

  buildExitCode=$?
  if [[ $buildExitCode != 0 ]]
  then
    log error "运行 node doBuild.js 失败. exit code: ${buildExitCode}"
    exit 1
  fi

  # 获取当前bundle md5 并且把 info 写入指定路径
  #
  # args[0]: ios/android
  # args[1]: bundle所在目录上级文件夹
  # args[2]: md5导出地址
  # args[2]: info文件导出地址
   router_list=$(node build/infoGenerate.js $loop $CodePush $platform $CodePush)
   if [[ $router_list == Error.infoGenerate* ]]
   then
     log error "运行 node infoGenerate.js 失败. ${router_list}"
     exit 1
   fi

  # # 路由列表写入bundle
  # echo "$router_list" > $CodePush/rts
  # zip release folder
   cd $platform
   zip -r -q $AbsoluteUploaderPath/$loop.zip CodePush
   cd $CRTDIR_DIR
   # upload
   curl -v -u uploader:123456 --upload-file $Uploader/$loop.zip $maven_url${branch//\//"_"}/$version/
   curl -v -u uploader:123456 --upload-file $SourceMap/$loop.map $maven_url${branch//\//"_"}/$version/
   curl -v -u uploader:123456 --upload-file $Mapping/buss.$loop.mapping $maven_url${branch//\//"_"}/$version/
   # reset workspace
  log ">>>>>release end"
done
# end

