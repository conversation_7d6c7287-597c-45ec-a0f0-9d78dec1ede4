/**
 * Metro configuration for React Native
 * https://github.com/facebook/react-native
 *
 * @format
 */

const {
  defaultCreateModuleIdFactory,
} = require('metro-config/src/defaults/defaults');

const path = require('path');
const projectRoot = path.join(__dirname, '../../');

// 判断文件是否需要打入bundle中Core
function processModuleFilterCore(filePath) {
  // 自执行文件
  if (
    filePath.indexOf('require-') === 0 &&
    filePath.slice('require-'.length) === global.ENTRY_FILE_ABSOLUTE_PATH
  ) {
    return true;
  }

  const relativePath = filePath.slice(projectRoot.length);

  /*// 不打入bundle过滤条件, 后面可以细分
  if (
    relativePath.indexOf('node_modules/@soyoung/base') === 0 ||
    relativePath.indexOf('node_modules') === 0
  ) {
    return false;
  }*/

  // 如果在node_modules文件夹，并且基础库已经打入进去的代码，就不需要打入了
  if (
    relativePath.indexOf('node_modules/') === 0 &&
    global.READONLY_COMMON_MAPPING[relativePath] !== undefined
  ) {
    return false;
  }

  /**
   * base库没有包括但是出现在node_modules文件下
   * 说明是当前业务自己引用了某些三方仓库
   * 打印提示
   */
  if (relativePath.indexOf('node_modules/') === 0) {
    console.log(`警告：不稳定模块打入bundle: ${relativePath}`);
  }

  return true;
}

// 判断文件是否需要打入bundle中，加入了缓存，防止多次判断
function processModuleFilterWrapping(filePath) {
  const cache = global.BUILD_TEMP_CACHE[filePath];
  if (cache) {
    return cache === 1 ? true : false;
  }

  if (processModuleFilterCore(filePath)) {
    global.BUILD_TEMP_CACHE[filePath] = 1;
    return true;
  }

  global.BUILD_TEMP_CACHE[filePath] = 2;
  return false;
}

// 黑名单文件，不需要打入bundle，也不需要计算id
function isBlackListFile(filePath) {
  if (
    filePath.indexOf('__prelude__') === 0 ||
    filePath.indexOf('source-map') === 0
  ) {
    return true;
  }

  // 自执行文件，只留入口文件就可以了
  if (filePath.indexOf('require-') === 0) {
    const realPath = filePath.slice('require-'.length);
    if (realPath === global.ENTRY_FILE_ABSOLUTE_PATH) {
      return false;
    }
    return true;
  }

  if (filePath.indexOf(projectRoot) !== 0) {
    throw new Error(`打包失败: 打包文件(${filePath})不在项目根目录下`);
  }

  const relativePath = filePath.slice(projectRoot.length);
  if (
    relativePath.indexOf('node_modules/metro-runtime') === 0 ||
    relativePath.indexOf('node_modules/@react-native/polyfills') === 0
  ) {
    return true;
  }

  return false;
}

module.exports = {
  projectRoot: path.resolve(projectRoot),
  transformer: {
    assetPlugins: ['./../../../build/config/assetDataPlugins-bid'],
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: false,
      },
    }),
  },
  resolver: {
    assetExts: ['png', 'jpeg', 'jpg'],
  },
  serializer: {
    createModuleIdFactory: () => {
      // const _fun = defaultCreateModuleIdFactory();
      return filePath => {
        // 黑名单判断
        if (isBlackListFile(filePath)) {
          // 不生效文件直接返回上一次id
          const res =
            // eslint-disable-next-line no-bitwise
            (global.BUILD_BID << global.ID_MASK_SHIFT) +
            (global.BUSS_NEXT_ID - 1);
          // console.log(res, filePath);
          return res;
        }

        let key;
        if (filePath.indexOf('require-') === 0) {
          key = filePath.slice('require-'.length).slice(projectRoot.length);
        } else {
          key = filePath.slice(projectRoot.length);
        }
        let moduleId;
        /**
         * 尝试查找base库
         * 业务项目中路径：'node_modules/@soyoung/base/src/exp.js'
         * base打包时路径：'src/exp.js'
         *
         * <打包的工程下没有任何源码，所以这里不需要路径转换了>
         **/
        /*if (key.indexOf('node_modules/@soyoung/base/') === 0) {
          const t = key.slice('node_modules/@soyoung/base/'.length);
          moduleId = global.READONLY_COMMON_MAPPING[t];
          if (moduleId !== undefined) {
            // console.log(moduleId, filePath);
            return moduleId;
          }
        }*/

        // 如果在node_module里面，尝试查找
        if (key.indexOf('node_modules/') === 0) {
          moduleId = global.READONLY_COMMON_MAPPING[key];
          if (moduleId !== undefined) {
            // console.log(moduleId, filePath);
            return moduleId;
          }
        }

        // 查找业务模块id缓存
        moduleId = global.BUSS_MAPPING[key];
        if (moduleId !== undefined) {
          // console.log(moduleId, filePath);
          return moduleId;
        }

        // 如果明确不需要打入bundle的module，就不需要生成新的模块id了
        if (processModuleFilterWrapping(filePath) === false) {
          // 不生效文件直接返回上一次id
          const res =
            // eslint-disable-next-line no-bitwise
            (global.BUILD_BID << global.ID_MASK_SHIFT) +
            (global.BUSS_NEXT_ID - 1);
          // console.log(res, filePath);
          return res;
        }

        // 单bundle文件数量过大
        // eslint-disable-next-line no-bitwise
        if (global.BUSS_NEXT_ID >= 1 << global.ID_MASK_SHIFT) {
          throw new Error(
            // eslint-disable-next-line no-bitwise
            `打包失败: 文件数量过多，超出了${1 << global.ID_MASK_SHIFT}限制`,
          );
        }

        moduleId =
          // eslint-disable-next-line no-bitwise
          (global.BUILD_BID << global.ID_MASK_SHIFT) + global.BUSS_NEXT_ID++;
        global.BUSS_MAPPING[key] = moduleId;
        // console.log(moduleId, filePath);
        return moduleId;
      };
    },
    processModuleFilter: module => {
      const filePath = module.path;

      // 黑名单判断
      if (isBlackListFile(filePath)) {
        return false;
      }
      const res = processModuleFilterWrapping(filePath);
      if (res) {
        // console.log('pre---', filePath);
      }
      return res;
    },
  },
};
