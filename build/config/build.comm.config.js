/**
 * Metro configuration for React Native
 * https://github.com/facebook/react-native
 *
 * @format
 */

// const {
//   defaultCreateModuleIdFactory,
// } = require('metro-config/src/defaults/defaults');

const path = require('path');
const projectRoot = path.join(__dirname, '../../');

module.exports = {
  projectRoot: path.resolve(projectRoot),
  transformer: {
    assetPlugins: ['./../../../build/config/assetDataPlugins-bid'],
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: false,
      },
    }),
  },
  resolver: {
    assetExts: ['png', 'jpeg', 'jpg'],
  },
  serializer: {
    createModuleIdFactory: () => {
      // const fun = defaultCreateModuleIdFactory();
      return filePath => {
        if (filePath.indexOf(projectRoot) !== 0) {
          throw new Error(`打包失败: 打包文件(${filePath})不在项目根目录下`);
        }

        const key = filePath.slice(projectRoot.length);
        let moduleId = global.READONLY_COMMON_MAPPING[key];
        if (moduleId === undefined) {
          moduleId = global.COMMON_MAPPING[key];
        }

        // 单bundle文件数量过大
        // eslint-disable-next-line no-bitwise
        if (moduleId >= 1 << global.ID_MASK_SHIFT) {
          throw new Error(
            // eslint-disable-next-line no-bitwise
            `打包失败: 文件数量过多，超出了${1 << global.ID_MASK_SHIFT}限制`,
          );
        }

        if (moduleId === undefined) {
          moduleId = global.COMMON_NEXT_ID++;
        }

        global.COMMON_MAPPING[key] = moduleId;
        return moduleId;
      };
    },
  },
};
