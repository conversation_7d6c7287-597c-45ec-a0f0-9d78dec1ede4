"use strict";

function obtainRouterList() {
    const fs = require('fs')
    const ts = require('typescript')

    const pagesFilePath = __dirname + "/../" + "/src/pages.ts"
    const pagesFile = ts.createSourceFile(pagesFilePath, fs.readFileSync(pagesFilePath).toString(), ts.ScriptTarget.ES2015, false);

    const routerList = new Array();

    const isPageFactoryFunctionDeclaration = (statement) => {
        // 只需要 变量声明/  import or exports  返回NO
        if (!ts.isVariableStatement(statement)) {
            return false;
        }

        // const 变量声明
        if (statement.declarationList.flags !== ts.NodeFlags.Const) {
            return false;
        }

        // const pageFactory
        if (statement.declarationList.declarations.length !== 1
            || statement.declarationList.declarations[0].name.escapedText !== "pageFactory") {
            return false;
        }

        return true;
    };

    pagesFile.statements.map(statement => {
        if (!isPageFactoryFunctionDeclaration(statement)) {
            return;
        }

        const pageFactoryVariableDeclaration = statement.declarationList.declarations[0];
        const pageFactoryFunctionInitializer = pageFactoryVariableDeclaration.initializer;
        pageFactoryFunctionInitializer.body.expression.properties.map(modPropertie => {
            const modName = modPropertie.name.escapedText;

            // common/evaluate/mine/ 对象的初始化方法
            if (!ts.isArrayLiteralExpression(modPropertie.initializer)) {
                throw new Error("pageFactory方法格式错误，属性下并非数组");
            }

            // 模块下面 每个页面对象
            modPropertie.initializer.elements.map(pageInitializer => {
                // page 构造
                if (!ts.isObjectLiteralExpression(pageInitializer)) {
                    throw new Error("pageFactory方法格式错误，mod下面并非page对象");
                }

                // 找到 name 属性
                const pageNameProperties = pageInitializer.properties.find((pageProperties) => {
                    return (pageProperties.name.escapedText === "name");
                });

                // 有name属性，并且value为 string
                if (pageNameProperties && ts.isStringLiteral(pageNameProperties.initializer)) {
                    const pageName = pageNameProperties.initializer.text;
                    if (pageName) {
                        routerList.push(`${modName}/${pageName}`);
                    }
                }
            });
        });
    });

    return routerList;
}

try {
    const trouterList = obtainRouterList();
    const routerList = trouterList.filter(item => (item !== "common/dev")).map(item => (item.toLowerCase()));
    if (routerList.length == 0) {
        throw new Error("获取路由列表为空");
    }
    console.log(routerList.join('\n'));
} catch (error) {
    console.log(`Error.routerList: ${error.name}-${error.message}`);
}


// const printer = ts.createPrinter();
// const result = printer.printFile(pagesFile)
// console.log(result);

