import React from 'react';
import RecordDetailPage from './page/detail';
import { NavigationProp } from '@react-navigation/native';

interface PageProps {
  navigation: NavigationProp<any>;
}

interface PageState {
  pageShow: boolean;
}

class RecordDetail extends React.Component<PageProps, PageState> {
  constructor(props: PageProps) {
    super(props);
    this.state = {
      pageShow: true,
    };
  }

  soyoungPageName() {
    return '服务记录详情';
  }

  /** 页面埋点 */
  soyoungPageInfo() {
    return {
      page_name: '服务记录详情页',
      page_type: 'detail',
    };
  }

  didAppear() {
    this.setState({
      pageShow: true,
    });
  }

  willDisappear() {
    this.setState({
      pageShow: false,
    });
  }

  preferredStatusBarStyle() {
    // 0默认 1 白色 2 黑色
    return '2';
  }

  render() {
    return (
      <RecordDetailPage {...this.props} pageShowFlag={this.state.pageShow} />
    );
  }
}

export default RecordDetail;
