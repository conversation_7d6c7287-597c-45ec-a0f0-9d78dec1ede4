import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TextInput,
  RefreshControl,
  FlatList,
} from 'react-native';
import jsApi from '@soyoung/react-native-jsapi';
import { SafeAreaView } from 'react-native-safe-area-context';
import RecordCard from '../components/RecordCard';
import { RecordListSkeleton } from '../components/RecordSkeleton';
import Header from '@/components/header';
import PopupModalTime from '@/components/PopupModalTime';
import PopupSelectUser from '../components/PopupSelectUser';
import SearchUserModal from '../components/SearchUserModal';
import { getRealSize } from '@/common/utils';
import dayjs from 'dayjs';
import api from '@/common/api';
import { ATrack } from '@soyoung/react-native-container';
import { checkRoles } from '@/common/checkPermission';
import { jumpReactNativePage } from '@/common/jumpPage';
import PermissionWrapper from '@/components/PermissionWrapper';
import { flatListProps } from '@/constant/flatlist_props';

interface Employee {
  id: string;
  user_id: string;
  name: string;
}

interface CustomerLevel {
  level_icon: string;
  level_name: string;
  level_value: number;
  level_value_name: string;
  period_validity: string;
}

interface Customer {
  base: CustomerInfo;
  customer_id: string;
  customer_level?: CustomerLevel;
}

interface CustomerInfo {
  customer_id: string | number;
  realname: string;
}

interface ConfirmData {
  start: string;
  end: string;
  type: number;
}

interface RecordItem {
  visit_id: string | number;
  tenant_id: string | number;
  date: string;
  avatar: string;
  customer_name: string;
  staff_list: Array<{ name: string }>;
  product_list: Array<{
    product_name: string;
    quantity: number;
    amount: number;
  }>;
  total_amount: number;
  evaluate_score?: number | string;
}

interface RecordData {
  list: RecordItem[];
  total: number;
  has_more: boolean;
}

interface PageProps {
  pageShowFlag?: boolean;
}

const RecordPage: React.FC<PageProps> = ({
  pageShowFlag: _pageShowFlag = true,
}) => {
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [customerId, setCustomerId] = useState<string>('');
  const [staffId, setStaffId] = useState<string>('');
  const [popupType, setPopupType] = useState<number>(0); // 1 时间 2 员工 3 客户
  const [dateType, setDateType] = useState<number>(1); // 1 今日 2 本月 3 自定义
  const [employeeList, setEmployeeList] = useState<Employee[]>([]);
  const [fuzzySearchCustomer, setFuzzySearchCustomer] = useState<{
    has_more: boolean;
    list: Customer[];
    total: number;
  }>({
    has_more: false,
    list: [],
    total: 0,
  });
  const [fuzzySearchCustomerIndex, setFuzzySearchCustomerIndex] =
    useState<number>(0);
  const [fuzzySearchCustomerText, setFuzzySearchCustomerText] =
    useState<string>('');
  const [recordData, setRecordData] = useState<RecordData>({
    list: [],
    total: 0,
    has_more: false,
  });
  const [serviceRecordPages, setServiceRecordPages] = useState<number>(1);
  const [isManager, setIsManager] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [hasPermission, setHasPermission] = useState<boolean>(true);

  const searchInputRef = useRef<TextInput>(null);
  // 添加 mounted ref 来跟踪组件是否已卸载
  const mountedRef = useRef(true);

  // 获取时间按钮显示内容
  const getTimeButtonContent = useCallback(() => {
    switch (dateType) {
      case 1:
        return '今日';
      case 2:
        return '本月';
      case 3:
        return `${dayjs(startDate).format('MM-DD')}至${dayjs(endDate).format('MM-DD')}`;
      default:
        return '今日';
    }
  }, [dateType, startDate, endDate]);

  // 获取当前选中的员工数据
  const currentStaffData = useMemo(() => {
    if (staffId !== '') {
      return employeeList.find(item => item.id === staffId) || {};
    }
    return {};
  }, [staffId, employeeList]);

  // 跳转到详情页
  const goToDetail = useCallback((item: RecordItem) => {
    jumpReactNativePage(
      `record/detail?visit_id=${item.visit_id}&tenant_id=${item.tenant_id}&date=${item.date}`
    );
  }, []);

  // 日期补零
  const dateSupplementZero = useCallback((time: Date) => {
    const date = new Date(time);
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${date.getFullYear()}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`;
  }, []);

  // 获取员工列表
  const getMyTenantAllUsers = useCallback(async () => {
    try {
      const res = await api.pagefetch({
        path: '/chain-wxapp/v1/tenant/getMyTenantAllUsers',
        params: {},
        isLoading: false,
      });
      // 检查组件是否已卸载
      if (!mountedRef.current) return [];

      if (res.errorCode === 0 && res.responseData?.length) {
        return res.responseData;
      }
      return [];
    } catch (error) {
      console.error('getMyTenantAllUsers error:', error);
      return [];
    }
  }, []);

  // 简单的数据刷新函数
  const fetchData = useCallback(async () => {
    console.log('fetchData calle1d', {
      startDate,
      endDate,
      customerId,
      staffId,
      serviceRecordPages,
    });

    // 检查组件是否已卸载
    if (!mountedRef.current) return;

    setLoading(true);
    // 直接在这里计算员工用户ID，避免循环依赖
    const currentStaff =
      staffId !== '' ? employeeList.find(item => item.id === staffId) : null;
    const staffUserId = currentStaff?.user_id;

    try {
      const res = await api.pagefetch({
        path: '/chain-wxapp/v1/execution/workbenchExecutionList',
        isLoading: false,
        params: {
          start_date: startDate,
          end_date: endDate,
          customer_id: customerId,
          staff_id: staffUserId,
          page_size: 10,
          page: serviceRecordPages,
        },
      });

      // 检查组件是否已卸载
      if (!mountedRef.current) return;
      if (res.errorCode === 0 && res.responseData) {
        setRecordData({
          list: res.responseData.list || [],
          total: res.responseData.total,
          has_more: res.responseData.has_more,
        });
      } else if (res.errorCode === 10002) {
        setHasPermission(false);
      }
    } catch (error) {
      console.log('workbenchExecutionList error:', error);
    } finally {
      // 检查组件是否已卸载
      if (mountedRef.current) {
        setLoading(false);
      }
    }
  }, [
    startDate,
    endDate,
    customerId,
    staffId,
    employeeList,
    serviceRecordPages,
  ]);

  // 打开弹窗
  const openPopup = useCallback((type: number) => {
    setPopupType(type);
  }, []);

  // 关闭弹窗
  const closePopup = useCallback(() => {
    setPopupType(0);
  }, []);

  const closeUserPopup = useCallback(() => {
    setPopupType(0);
    setFuzzySearchCustomerIndex(0);
    setFuzzySearchCustomerText('');
    setFuzzySearchCustomer({ has_more: false, list: [], total: 0 });
  }, []);

  const popupModalTimeConfirm = useCallback(
    (data: ConfirmData) => {
      setStartDate(data.start);
      setEndDate(data.end);
      setDateType(data.type);
      // 当状态更新后，useEffect 会自动触发数据重新加载
    },
    [setStartDate, setEndDate, setDateType]
  );

  const validateTime = useCallback((start: string, end: string) => {
    // 计算时间间隔
    const startDateTime = new Date(start);
    const endDateTime = new Date(end);
    const diffTime = Math.abs(endDateTime.getTime() - startDateTime.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    // 判断是否超过一个月（30天）
    if (diffDays > 30) {
      jsApi.toNative('showToast', {
        toast: '时间间隔不能超过一个月',
      });
      return false;
    }
    return true;
  }, []);

  const popupModalSelectStaff = useCallback(
    (staff: Employee) => {
      setStaffId(staff.id === staffId ? '' : staff.id);
      // 当状态更新后，useEffect 会自动触发数据重新加载
    },
    [staffId]
  );

  // 搜索用户
  const searchUser = useCallback(
    async (text: string) => {
      setFuzzySearchCustomerText(text);
      if (text) {
        if (popupType !== 3) {
          setPopupType(3);
        }
        try {
          const res = await api.pagefetch({
            path: '/chain-wxapp/v1/execution/searchCustomerList',
            isLoading: false,
            params: {
              index: fuzzySearchCustomerIndex,
              key_word: text || fuzzySearchCustomerText,
              limit: 30,
              select_tab: 'all',
              filter: '',
            },
          });

          // 检查组件是否已卸载
          if (!mountedRef.current) return;

          if (res.errorCode === 0 && res.responseData) {
            const newList = res.responseData.list || [];
            setFuzzySearchCustomer({
              has_more: res.responseData.has_more,
              total: res.responseData.total,
              list: newList,
            });
          }
        } catch (error) {
          console.error('searchCustomerList error:', error);
        }
      } else {
        // 清空搜索
        setPopupType(0);
        setCustomerId('');
        setFuzzySearchCustomerIndex(0);
        setFuzzySearchCustomer({ has_more: false, list: [], total: 0 });
        // 当状态更新后，useEffect 会自动触发数据重新加载
      }
    },
    [popupType, fuzzySearchCustomerIndex, fuzzySearchCustomerText]
  );

  // 选择用户
  const selectUser = useCallback((user: Customer, index: number) => {
    searchInputRef.current?.blur();
    setPopupType(0);
    setServiceRecordPages(1);
    setCustomerId(user.customer_id);
    setFuzzySearchCustomerText(user.base.realname);
    setFuzzySearchCustomerIndex(index);
    // 当状态更新后，useEffect 会自动触发数据重新加载
  }, []);

  // 刷新数据
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    setServiceRecordPages(1);
    await fetchData();
    setRefreshing(false);
  }, [fetchData]);

  // 加载更多
  const loadMore = useCallback(async () => {
    console.log(
      'loadMore',
      loadingMore,
      recordData.list.length,
      recordData.total,
      recordData.has_more
    );
    // 防止重复加载或数据为空时加载
    if (loadingMore || !recordData.has_more || recordData.total === 0) {
      return;
    }

    setLoadingMore(true);
    try {
      const newPage = serviceRecordPages + 1;

      // 直接调用 API 获取更多数据，传入新的页码
      const res = await api.pagefetch({
        path: '/chain-wxapp/v1/execution/workbenchExecutionList',
        isLoading: false,
        params: {
          start_date: startDate,
          end_date: endDate,
          customer_id: customerId,
          staff_id:
            staffId !== ''
              ? employeeList.find(item => item.id === staffId)?.user_id
              : undefined,
          page_size: 10,
          page: newPage,
        },
      });

      // 检查组件是否已卸载
      if (!mountedRef.current) return;

      if (res.errorCode === 0 && res.responseData) {
        setServiceRecordPages(newPage);
        setRecordData(prevState => ({
          list: [...prevState.list, ...(res.responseData.list || [])],
          total: res.responseData.total,
          has_more: res.responseData.has_more,
        }));
      } else if (res.errorCode === 10002) {
        setHasPermission(false);
      }
    } catch (error) {
      console.error('loadMore error:', error);
    } finally {
      // 检查组件是否已卸载
      if (mountedRef.current) {
        setLoadingMore(false);
      }
    }
  }, [
    loadingMore,
    recordData.list.length,
    recordData.total,
    serviceRecordPages,
    startDate,
    endDate,
    customerId,
    staffId,
    employeeList,
    recordData.has_more,
  ]);

  // 渲染列表项
  const renderItem = useCallback(
    ({ item }: { item: RecordItem }) => (
      <ATrack onPress={() => goToDetail(item)} style={styles.recordItem}>
        <RecordCard record={item} />
      </ATrack>
    ),
    [goToDetail]
  );

  // 渲染空状态
  const renderEmpty = useCallback(
    () => (
      <View style={styles.emptyContainer}>
        <Image
          source={{
            uri: 'https://static.soyoung.com/sy-design/aqnomvpf3ki11753429315465.png',
          }}
          style={styles.emptyIcon}
        />
        <Text style={styles.emptyText}>暂无相关数据</Text>
      </View>
    ),
    []
  );

  // 渲染列表底部
  const renderFooter = useCallback(() => {
    if (loadingMore && recordData.has_more && recordData.list.length > 0) {
      return (
        <View style={styles.footer}>
          <Text style={styles.footerText}>加载中...</Text>
        </View>
      );
    }

    if (!loadingMore && !recordData.has_more && recordData.list.length > 0) {
      return (
        <View style={styles.footer}>
          <Text style={styles.footerText}>没有更多啦</Text>
        </View>
      );
    }

    return null;
  }, [loadingMore, recordData.has_more, recordData.list.length]);

  const onClickStaff = useCallback(() => {
    if (popupType === 0) {
      searchInputRef.current?.blur();
      openPopup(2);
    }
  }, [popupType, openPopup]);

  const onClickDate = useCallback(() => {
    if (popupType === 0) {
      searchInputRef.current?.blur();
      openPopup(1);
    }
  }, [popupType, openPopup]);

  // 初始化 - 分离数据加载逻辑，避免循环依赖
  useEffect(() => {
    const initPage = async () => {
      // 检查权限
      const role = await checkRoles('store-manager');
      setIsManager(role);

      // 获取员工列表
      const employees = await getMyTenantAllUsers();

      // 检查组件是否已卸载
      if (!mountedRef.current) return;

      if (Array.isArray(employees)) {
        setEmployeeList(employees as Employee[]);
      }

      // 获取初始数据
      const dateString = dateSupplementZero(new Date());
      setStartDate(dateString);
      setEndDate(dateString);
      setLoading(false);
    };

    initPage();
  }, []); // 只在组件挂载时执行一次

  // 当日期和其他筛选条件变化时，重新加载数据
  useEffect(() => {
    if (startDate && endDate) {
      setServiceRecordPages(1); // 重置页面
      fetchData(); // 获取数据
    }
  }, [startDate, endDate, customerId, staffId]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      mountedRef.current = false;

      // 清理可能的动画状态
      try {
        // 停止所有可能的动画
        if (searchInputRef.current) {
          searchInputRef.current.blur();
        }
      } catch (error) {
        console.warn('Cleanup error:', error);
      }
    };
  }, []);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <View style={styles.content}>
        {/* Header */}
        <Header
          title='服务记录'
          zIndex={102}
          bgColor='#FFFFFF'
          hideBack={false}
        />
        {/* Filter Bar */}
        <View style={styles.filterBar}>
          <ATrack style={styles.filterDate} onPress={onClickDate}>
            <Text style={styles.filterDateText}>{getTimeButtonContent()}</Text>
            <Image
              source={{
                uri: 'https://static.soyoung.com/sy-design/3u5f7j35qkn711753259920593.png',
              }}
              style={styles.filterDateIcon}
            />
          </ATrack>
          {isManager && (
            <ATrack style={styles.filterDate} onPress={onClickStaff}>
              <Text style={styles.filterDateText}>
                {(currentStaffData as any)?.name || '全部员工'}
              </Text>
              <Image
                source={{
                  uri: 'https://static.soyoung.com/sy-design/3u5f7j35qkn711753259920593.png',
                }}
                style={styles.filterDateIcon}
              />
            </ATrack>
          )}
          <View style={styles.filterSearch}>
            <Image
              source={{
                uri: 'https://static.soyoung.com/sy-design/jsc2u0zt10171753259744187.png',
              }}
              style={styles.searchIcon}
            />
            <TextInput
              style={styles.searchInput}
              ref={searchInputRef}
              value={fuzzySearchCustomerText}
              onChangeText={searchUser}
              placeholder='搜索客户'
              placeholderTextColor='#AAABB3'
              onFocus={() => {
                if (fuzzySearchCustomer.list.length) {
                  openPopup(3);
                }
              }}
            />
          </View>
        </View>

        {/* Body */}
        <View style={styles.body}>
          {!hasPermission ? (
            <PermissionWrapper hasPermission={false} />
          ) : loading && recordData.list.length === 0 ? (
            <View style={styles.skeletonContainer}>
              <RecordListSkeleton />
            </View>
          ) : (
            <FlatList
              {...flatListProps}
              data={recordData.list}
              renderItem={renderItem}
              keyExtractor={(item, index) => `record_${item.visit_id}_${index}`}
              refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
              }
              onEndReached={loadMore}
              ListEmptyComponent={renderEmpty}
              ListFooterComponent={renderFooter}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={
                recordData.list.length === 0 ? styles.emptyList : styles.list
              }
            />
          )}
        </View>
        {popupType === 3 ? (
          <SearchUserModal
            visible={popupType === 3}
            activeIndex={fuzzySearchCustomerIndex}
            searchText={fuzzySearchCustomerText}
            list={fuzzySearchCustomer.list}
            onClose={() => closeUserPopup()}
            onConfirm={(data: Customer, index: number) =>
              selectUser(data, index)
            }
          />
        ) : null}
      </View>
      <PopupModalTime
        visible={popupType === 1}
        onClose={() => closePopup()}
        validateFunction={validateTime}
        onConfirm={(data: ConfirmData) => popupModalTimeConfirm(data)}
      />
      <PopupSelectUser
        visible={popupType === 2}
        staffId={staffId}
        employeeList={employeeList}
        onClose={() => closePopup()}
        onConfirm={(data: Employee) => popupModalSelectStaff(data)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  content: {
    flex: 1,
    position: 'relative',
    zIndex: 100,
  },
  filterBar: {
    height: getRealSize(66),
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getRealSize(15),
    paddingVertical: getRealSize(15),
    marginBottom: getRealSize(10),
    zIndex: 102,
  },
  filterDate: {
    minWidth: getRealSize(70),
    paddingHorizontal: getRealSize(15),
    height: getRealSize(36),
    backgroundColor: '#F8F8F8',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: getRealSize(10),
  },
  filterDateText: {
    fontSize: getRealSize(13),
    color: '#333333',
    fontWeight: '400',
  },
  filterDateIcon: {
    width: getRealSize(9),
    height: getRealSize(7),
    marginLeft: getRealSize(5),
  },
  filterSearch: {
    flex: 1,
    height: getRealSize(36),
    paddingHorizontal: getRealSize(10),
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
  },
  searchIcon: {
    width: getRealSize(16),
    height: getRealSize(16),
    marginRight: getRealSize(5),
  },
  searchInput: {
    flex: 1,
    fontSize: getRealSize(13),
    color: '#333333',
    padding: 0,
  },
  body: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  list: {
    paddingHorizontal: getRealSize(15),
  },
  emptyList: {
    flex: 1,
    paddingHorizontal: getRealSize(15),
  },
  recordItem: {
    marginBottom: getRealSize(10),
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    paddingTop: getRealSize(210),
  },
  emptyIcon: {
    width: getRealSize(35),
    height: getRealSize(35),
  },
  emptyText: {
    fontSize: getRealSize(14),
    color: '#333333',
    marginTop: getRealSize(20),
    fontWeight: '500',
  },
  footer: {
    paddingVertical: getRealSize(15),
    alignItems: 'center',
  },

  footerText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#999999',
  },
  skeletonContainer: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
});

export default RecordPage;
