import React, { useState, useEffect, useCallback, useRef } from 'react';
import { View, Text, StyleSheet, ScrollView, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Header from '@/components/header';
import { getRealSize } from '@/common/utils';
import jsApi from '@soyoung/react-native-jsapi';
import api from '@/common/api';
import { ATrack } from '@soyoung/react-native-container';
import LinearGradient from 'react-native-linear-gradient';
import { jumpReactNativePage } from '@/common/jumpPage';
import { RecordDetailSkeleton } from '../components/DetailSkeleton';
import { pushChangeTenant } from '../../../common/pushChangeTenant';
import PermissionWrapper from '@/components/PermissionWrapper';
import DashedLine from '@/components/DashedLine';
// 类型定义
interface CustomerInfo {
  age: number;
  customer_id: number;
  customer_id_str?: string;
  customer_name: string;
  gender: string;
  avatar: string;
  tags?: string[];
}

interface MyService {
  is_mine: number;
  product_names?: string;
  service_time?: string;
  income?: Array<{
    amount: number;
    name: string;
    performance_with?: string;
  }>;
  evaluation?: Array<{
    role: string;
    evaluation: string;
  }>;
}

interface DetailInfo {
  consultant?: string[];
  doctor?: string[];
  nurse?: string[];
  arrive_time?: string;
  leave_time?: string;
}

interface TreatmentProduct {
  title: string;
  execute_num: number;
  status: number;
}

interface RecordInfo {
  record_id: number;
  product_name: string;
  quantity: number;
  product_id: number;
  tags?: Array<{
    type: number;
    name: string;
  }>;
}

interface SheetItem {
  record_list: RecordInfo[];
  doctor?: string[];
  nurse?: string[];
  consultant?: string[];
  sheet_id: number;
}

interface EvaluationInfo {
  name: string;
  score?: number | string;
  detail?: string;
}

interface DetailData {
  my_service: MyService;
  customer_info: CustomerInfo;
  sheet_list: SheetItem[];
  evaluation_info: EvaluationInfo[];
  treatment_product: TreatmentProduct[];
  detail_info: DetailInfo;
  total_amount: number | string;
}

interface PageProps {
  pageShowFlag?: boolean;
  route: any;
}

// Tab定义
const tabs = [
  { name: '我的服务', id: 'tab0' },
  { name: '服务明细', id: 'tab1' },
  { name: '开单项目', id: 'tab2' },
  { name: '用户评价', id: 'tab3' },
];

const runLabels = [
  { name: '治疗师', value: 'doctor' },
  { name: '护士', value: 'nurse' },
  { name: '咨询师', value: 'consultant' },
];

const statusIconMaps: { [key: number]: string } = {
  1: 'https://static.soyoung.com/sy-pre/jian-1733292600627.png',
  2: 'https://static.soyoung.com/sy-design/8z1vknct4e5v1724903220588.png',
  3: 'https://static.soyoung.com/sy-design/1ogu7m7wye0j81724903220415.png',
};

const RecordDetailPage: React.FC<PageProps> = ({
  pageShowFlag: _pageShowFlag = true,
  route,
}) => {
  const params = route?.params?.params as any;
  const [detailData, setDetailData] = useState<DetailData>({
    my_service: { is_mine: 0 },
    customer_info: {
      age: 0,
      customer_id: 0,
      customer_name: '',
      gender: '',
      avatar: '',
    },
    sheet_list: [],
    evaluation_info: [],
    treatment_product: [],
    detail_info: {},
    total_amount: '',
  });

  const [currentTab, setCurrentTab] = useState(tabs[0]);
  const scrollViewRef = useRef<ScrollView>(null);
  const [tabTop, setTabTop] = useState<number[]>([]);
  const [isScrolling, setIsScrolling] = useState(false);
  const [stickyTabVisible, setStickyTabVisible] = useState(false);
  const [originalTabTop, setOriginalTabTop] = useState(0);
  const [headerHeight, setHeaderHeight] = useState(getRealSize(44));
  const [loading, setLoading] = useState(true);
  const [hasPermission, setHasPermission] = useState(true);

  // 跳转到客户详情
  const gotoDetail = useCallback(() => {
    jumpReactNativePage(
      `client/detail?id=${detailData.customer_info.customer_id}`
    );
  }, [detailData.customer_info.customer_id]);

  // 判断是否显示某个Tab
  const shouldShowBox = useCallback(
    (tabId: string) => {
      if (tabId === 'tab2') {
        return (
          detailData.sheet_list.length > 0 ||
          detailData.treatment_product.length > 0
        );
      } else if (tabId === 'tab0') {
        return detailData.my_service && detailData.my_service.is_mine === 1;
      }
      return true;
    },
    [detailData]
  );

  // 获取可见的tabs列表（统一的过滤逻辑）
  const getVisibleTabs = useCallback(() => {
    return tabs.filter(t => shouldShowBox(t.id));
  }, [shouldShowBox]);

  // 切换Tab
  const switchTab = useCallback(
    (tab: (typeof tabs)[0]) => {
      // 使用统一的可见tab列表计算索引
      const visibleTabs = getVisibleTabs();
      const index = visibleTabs.indexOf(tab);

      if (index > -1 && tabTop[index] && scrollViewRef.current) {
        setIsScrolling(true);
        scrollViewRef.current.scrollTo({
          y: tabTop[index] - 50,
          animated: true,
        });
        // 滚动完成后设置正确的tab并重置状态
        setTimeout(() => {
          setCurrentTab(tab); // 滚动完成后才设置tab
          setIsScrolling(false);
        }, 350); // 稍微延长一点时间，确保滚动完全结束
      } else {
        // 如果没有滚动，直接设置tab
        setCurrentTab(tab);
      }
    },
    [tabTop, getVisibleTabs]
  );

  // 记录每个tab内容区域的位置
  const handleTabLayout = useCallback((event: any, tabIndex: number) => {
    const { y } = event.nativeEvent.layout;
    setTabTop(prev => {
      const newTabTop = [...prev];
      newTabTop[tabIndex] = y;
      return newTabTop;
    });
  }, []);

  // 获取详情数据
  const fetchDetail = useCallback(async () => {
    setLoading(true);
    try {
      const res = await api.pagefetch({
        path: '/chain-wxapp/v1/execution/workbenchExecutionDetail',
        isLoading: false,
        params: {
          visit_id: params?.visit_id,
          tenant_id: params?.tenant_id,
          date: params?.date,
        },
      });

      if (res.errorCode === 0 && res.responseData) {
        setDetailData({
          customer_info: res.responseData.customer_info || {},
          sheet_list: res.responseData.sheet_list || [],
          evaluation_info: res.responseData.evaluation_info || [],
          detail_info: res.responseData.detail_info || {},
          total_amount: res.responseData.total_amount || '',
          my_service: res.responseData.my_service || { is_mine: 0 },
          treatment_product: res.responseData.treatment_product || [],
        });

        if (res.responseData.my_service?.is_mine !== 1) {
          setCurrentTab(tabs[1]);
        }
      } else if (res.errorCode === 10002) {
        setHasPermission(false);
      } else {
        jsApi.toNative('showToast', {
          toast: res.errorMsg || '获取详情失败',
        });
      }
    } catch (error) {
      console.error('fetchDetail error:', error);
      jsApi.toNative('showToast', {
        toast: '获取详情异常',
      });
    } finally {
      setLoading(false);
    }
  }, [params]);

  const getPushDataChangeTenant = useCallback(async (): Promise<boolean> => {
    const tenantId = params?.tenant_id;
    const tenantUserId = params?.tenant_user_id;

    if (tenantId && tenantUserId) {
      return await pushChangeTenant(tenantId, tenantUserId).catch(() => {
        return false;
      });
    } else {
      return false;
    }
  }, [params]);

  // 初始化默认日期和推送租户变更
  useEffect(() => {
    const initializePage = async () => {
      try {
        await getPushDataChangeTenant();
      } finally {
        fetchDetail();
      }
    };

    initializePage();
  }, [getPushDataChangeTenant, fetchDetail]);

  // 渲染头像
  const renderAvatar = useCallback(() => {
    if (detailData.customer_info.avatar) {
      return (
        <Image
          source={{ uri: detailData.customer_info.avatar }}
          style={styles.avatarImg}
        />
      );
    }
    return (
      <View style={styles.basicAvatar}>
        <Text style={styles.basicAvatarText}>
          {detailData.customer_info.customer_name?.slice(0, 1) || '用'}
        </Text>
      </View>
    );
  }, [detailData.customer_info]);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      {/* Header */}
      <View
        onLayout={event => {
          const { height } = event.nativeEvent.layout;
          setHeaderHeight(height);
        }}
      >
        <Header title='服务记录详情' bgColor='#FFFFFF' hideBack={false} />
      </View>
      {!hasPermission ? (
        <PermissionWrapper hasPermission={false} />
      ) : loading ? (
        <RecordDetailSkeleton />
      ) : (
        <>
          {/* Sticky Tab List - 只在需要时显示 */}
          {stickyTabVisible && (
            <View style={[styles.stickyTabContainer, { top: headerHeight }]}>
              <View style={styles.tabList}>
                {tabs.map(tab => {
                  if (!shouldShowBox(tab.id)) return null;
                  return (
                    <ATrack
                      key={`sticky-${tab.id}`}
                      style={styles.tabItem}
                      onPress={() => switchTab(tab)}
                    >
                      <Text
                        style={[
                          styles.tabItemText,
                          currentTab.id === tab.id && styles.tabItemTextActive,
                        ]}
                      >
                        {tab.name}
                      </Text>
                      {currentTab.id === tab.id && (
                        <View style={styles.tabItemLine} />
                      )}
                    </ATrack>
                  );
                })}
              </View>
            </View>
          )}

          <ScrollView
            ref={scrollViewRef}
            style={styles.content}
            showsVerticalScrollIndicator={false}
            onScroll={event => {
              const scrollY = event.nativeEvent.contentOffset.y;

              // 控制吸顶tab的显示/隐藏
              setStickyTabVisible(scrollY > originalTabTop);

              // 如果正在程序性滚动，只处理吸顶显示逻辑，不处理tab切换
              if (isScrolling) return;

              // 找到当前滚动位置对应的tab
              const currentIndex = tabTop.findIndex((top, i) => {
                const nextTop = tabTop[i + 1];
                return (
                  scrollY + 70 >= top && (!nextTop || scrollY + 70 < nextTop)
                );
              });

              if (currentIndex > -1) {
                const visibleTabs = getVisibleTabs();
                if (
                  visibleTabs[currentIndex] &&
                  visibleTabs[currentIndex].id !== currentTab.id
                ) {
                  setCurrentTab(visibleTabs[currentIndex]);
                }
              }
            }}
            scrollEventThrottle={16}
          >
            {/* User Box */}
            <ATrack style={styles.userBox} onPress={gotoDetail}>
              <View style={styles.avatarBox}>{renderAvatar()}</View>
              <View style={styles.userInfo}>
                <View style={styles.userHead}>
                  {detailData.customer_info.customer_name ? (
                    <Text style={styles.userName}>
                      {detailData.customer_info.customer_name}
                    </Text>
                  ) : null}
                  {detailData.customer_info.gender ? (
                    <Text style={styles.userSex}>
                      {detailData.customer_info.gender}
                    </Text>
                  ) : null}
                  {detailData.customer_info.age &&
                  Number(detailData.customer_info.age) > 0 ? (
                    <>
                      <View style={styles.divider} />
                      <Text style={styles.userAge}>
                        {detailData.customer_info.age}岁
                      </Text>
                    </>
                  ) : null}
                </View>
                {detailData.customer_info.customer_id ? (
                  <Text style={styles.userId}>
                    ID：{detailData.customer_info.customer_id_str}
                  </Text>
                ) : null}
                {detailData.customer_info.tags &&
                  detailData.customer_info.tags.length > 0 && (
                    <View style={styles.userTag}>
                      {detailData.customer_info.tags.map((tag, index) => (
                        <View key={index} style={styles.userTagItem}>
                          <Text style={styles.userTagText}>
                            {tag || '标签'}
                          </Text>
                        </View>
                      ))}
                    </View>
                  )}
              </View>
            </ATrack>

            {/* Original Tab List */}
            <View
              style={styles.tabList}
              onLayout={event => {
                const { y } = event.nativeEvent.layout;

                setOriginalTabTop(y);
              }}
            >
              {tabs.map(tab => {
                if (!shouldShowBox(tab.id)) return null;
                return (
                  <ATrack
                    key={tab.id}
                    style={styles.tabItem}
                    onPress={() => switchTab(tab)}
                  >
                    <Text
                      style={[
                        styles.tabItemText,
                        currentTab.id === tab.id && styles.tabItemTextActive,
                      ]}
                    >
                      {tab.name}
                    </Text>
                    {currentTab.id === tab.id && (
                      <View style={styles.tabItemLine} />
                    )}
                  </ATrack>
                );
              })}
            </View>
            <LinearGradient
              colors={['#FFFFFF', '#F8F8F8']}
              start={{ x: 0, y: 0 }}
              end={{ x: 0, y: 1 }}
              locations={[0, 1]}
            >
              <View style={styles.tabShadow} />
            </LinearGradient>

            {/* Content Boxes */}
            {tabs.map((tab, _index) => {
              if (!shouldShowBox(tab.id)) return null;
              const visibleTabs = getVisibleTabs();
              const visibleIndex = visibleTabs.indexOf(tab);
              return (
                <View
                  key={tab.id}
                  style={[
                    styles.box,
                    { marginTop: visibleIndex == 0 ? getRealSize(-10) : 0 },
                  ]}
                  onLayout={event => handleTabLayout(event, visibleIndex)}
                >
                  <View style={styles.boxTitle}>
                    <View style={styles.titleLeft}>
                      <View style={styles.leftLine} />
                      <Text style={styles.leftName}>{tab.name}</Text>
                    </View>
                  </View>

                  {/* Tab Content */}
                  {tab.id === 'tab0' && detailData.my_service ? (
                    <View style={styles.serviceBox}>
                      {/* 执行项目 */}
                      {detailData.my_service.product_names ? (
                        <View style={styles.serviceItem}>
                          <Text style={styles.serviceName}>执行项目</Text>
                          <Text style={styles.serviceValue}>
                            {detailData.my_service.product_names}
                          </Text>
                        </View>
                      ) : null}

                      {/* 服务时长 */}
                      {detailData.my_service.service_time ? (
                        <View style={styles.serviceItem}>
                          <Text style={styles.serviceName}>服务时长</Text>
                          <Text style={styles.serviceValue}>
                            {detailData.my_service.service_time}
                          </Text>
                        </View>
                      ) : null}

                      {/* 收入信息 */}
                      <View style={styles.serviceIncome}>
                        <Text style={styles.incomeName}>收入</Text>
                        <View style={styles.incomeList}>
                          {(detailData.my_service.income || []).map(
                            (item, i) => (
                              <View key={i} style={styles.incomeItem}>
                                <Text style={styles.incomePrice}>
                                  ¥{item.amount}
                                </Text>
                                <View style={styles.incomeTag}>
                                  <Text style={styles.incomeTagText}>
                                    {item.name}
                                  </Text>
                                </View>
                                {item.performance_with ? (
                                  <Text style={styles.incomeTips}>
                                    （{item.performance_with}）
                                  </Text>
                                ) : null}
                              </View>
                            )
                          )}
                        </View>
                      </View>

                      {/* 用户评价 */}
                      <View style={styles.serviceItem}>
                        <Text style={styles.serviceName}>用户评价</Text>
                        <View style={styles.serviceValue}>
                          {(detailData.my_service.evaluation || []).map(
                            (item, i) => (
                              <View key={i} style={styles.serviceValueItem}>
                                <Text style={styles.serviceValueItemText}>
                                  {item.role}
                                </Text>
                                <Text
                                  style={[
                                    styles.serviceValueItemEva,
                                    Number(item.evaluation) < 3 &&
                                      styles.serviceValueItemEvaRed,
                                  ]}
                                >
                                  {item.evaluation}分
                                </Text>
                                {i !==
                                (detailData.my_service.evaluation || [])
                                  .length -
                                  1 ? (
                                  <View style={styles.serviceValueLine} />
                                ) : null}
                              </View>
                            )
                          )}
                        </View>
                      </View>
                    </View>
                  ) : null}

                  {tab.id === 'tab1' ? (
                    <View style={styles.detailBox}>
                      {/* 咨询师 */}
                      <View style={styles.detailItem}>
                        <Text style={styles.detailName}>咨询师</Text>
                        <Text style={styles.detailPrice}>
                          {detailData.detail_info.consultant?.join('、') || ''}
                        </Text>
                      </View>
                      {/* 医生/操作护士 */}
                      <View style={styles.detailItem}>
                        <Text style={styles.detailName}>医生/操作护士</Text>
                        <Text style={styles.detailPrice}>
                          {detailData.detail_info.doctor?.join('、') || ''}
                        </Text>
                      </View>
                      {/* 配台护士 */}
                      <View style={styles.detailItem}>
                        <Text style={styles.detailName}>配台护士</Text>
                        <Text style={styles.detailPrice}>
                          {detailData.detail_info.nurse?.join('、') || ''}
                        </Text>
                      </View>
                      {/* 到店时间 */}
                      <View style={styles.detailItem}>
                        <Text style={styles.detailName}>到店时间</Text>
                        <Text style={styles.detailPrice}>
                          {detailData.detail_info.arrive_time || ''}
                        </Text>
                      </View>

                      {/* 离店时间 */}
                      <View style={styles.detailItem}>
                        <Text style={styles.detailName}>离店时间</Text>
                        <Text style={styles.detailPrice}>
                          {detailData.detail_info.leave_time || ''}
                        </Text>
                      </View>
                    </View>
                  ) : null}

                  {tab.id === 'tab2' ? (
                    <View style={styles.billingBox}>
                      {/* 治疗单项目 */}
                      {detailData.treatment_product.length > 0 ? (
                        <View style={styles.treatmentBox}>
                          <Text style={styles.treatmentHead}>治疗单项目</Text>
                          <View style={styles.treatmentBody}>
                            {detailData.treatment_product.map((item, inx) => (
                              <View
                                key={item.title + inx}
                                style={[
                                  styles.treatmentItem,
                                  {
                                    marginBottom:
                                      inx ===
                                      detailData.treatment_product.length - 1
                                        ? 0
                                        : getRealSize(5),
                                  },
                                ]}
                              >
                                <Image
                                  source={{ uri: statusIconMaps[item.status] }}
                                  style={styles.treatmentIcon}
                                />
                                <Text style={styles.treatmentName}>
                                  {item.title}*{item.execute_num}
                                </Text>
                              </View>
                            ))}
                          </View>
                        </View>
                      ) : null}

                      {/* 执行项目 */}
                      {detailData.sheet_list.length > 0 && (
                        <View style={styles.runBox}>
                          <View style={styles.runHead}>
                            <Text style={styles.runTitle}>执行项目</Text>
                            <View style={styles.runHeadRight}>
                              <Text style={styles.runTip}>共计</Text>
                              <Text style={styles.runPrice}>
                                <Text style={styles.runPriceFlag}>¥</Text>
                                {detailData.total_amount || 0}
                              </Text>
                            </View>
                          </View>
                          <View style={styles.runBody}>
                            {detailData.sheet_list.map((item, index) => (
                              <View
                                key={item.sheet_id || index}
                                style={[
                                  styles.runList,
                                  {
                                    paddingBottom:
                                      index === detailData.sheet_list.length - 1
                                        ? 0
                                        : getRealSize(10),
                                  },
                                ]}
                              >
                                {/* 记录列表 */}
                                {(item.record_list || []).map(info => (
                                  <View
                                    key={info.record_id}
                                    style={styles.runLi}
                                  >
                                    <Text style={styles.runLiName}>
                                      {info.product_name}*{info.quantity}
                                    </Text>
                                    {info.tags && info.tags.length > 0 && (
                                      <View style={styles.runLiTag}>
                                        {info.tags.map(tag => {
                                          const getTagItemStyle = () => {
                                            switch (tag.type) {
                                              case 1:
                                                return styles.runLiTagItem1;
                                              case 2:
                                                return styles.runLiTagItem2;
                                              case 3:
                                                return styles.runLiTagItem3;
                                              default:
                                                return {};
                                            }
                                          };
                                          const getTagTextStyle = () => {
                                            switch (tag.type) {
                                              case 1:
                                                return styles.runLiTagItemText1;
                                              case 2:
                                                return styles.runLiTagItemText2;
                                              case 3:
                                                return styles.runLiTagItemText3;
                                              default:
                                                return {};
                                            }
                                          };
                                          return (
                                            <View
                                              key={tag.type}
                                              style={[
                                                styles.runLiTagItem,
                                                getTagItemStyle(),
                                              ]}
                                            >
                                              <Text
                                                style={[
                                                  styles.runLiTagItemText,
                                                  getTagTextStyle(),
                                                ]}
                                              >
                                                {tag.name}
                                              </Text>
                                            </View>
                                          );
                                        })}
                                      </View>
                                    )}
                                  </View>
                                ))}
                                {/* 底部信息 */}
                                <View style={styles.runBottom}>
                                  {runLabels.map((label, labelIndex) => {
                                    const itemValue = item[
                                      label.value as keyof typeof item
                                    ] as string[] | undefined;
                                    if (itemValue && itemValue.length > 0) {
                                      return (
                                        <View
                                          key={label.value}
                                          style={styles.runBottomItem}
                                        >
                                          <Text style={styles.runBottomTip}>
                                            {label.name}
                                          </Text>
                                          <Text style={styles.runBottomContent}>
                                            {itemValue.join('、')}
                                          </Text>
                                          {labelIndex !==
                                          runLabels.length - 1 ? (
                                            <DashedLine
                                              style={styles.runBottomLine}
                                              dashLength={2}
                                              dashGap={2}
                                              dashThickness={1}
                                              dashColor='#bababa'
                                            />
                                          ) : null}
                                        </View>
                                      );
                                    }
                                    return null;
                                  })}
                                </View>
                                {index !== detailData.sheet_list.length - 1 ? (
                                  <DashedLine
                                    style={styles.runListLine}
                                    dashLength={2}
                                    dashGap={2}
                                    dashThickness={1}
                                    dashColor='#DEDEDE'
                                  />
                                ) : null}
                              </View>
                            ))}
                          </View>
                        </View>
                      )}
                    </View>
                  ) : null}

                  {tab.id === 'tab3' ? (
                    <View style={styles.evaluateBox}>
                      {detailData.evaluation_info.length > 0 ? (
                        <>
                          {detailData.evaluation_info.map((item, i) => (
                            <View key={i} style={styles.evaluateItem}>
                              <View style={styles.evaluateItemContent}>
                                <Text style={styles.evaluateLabel}>
                                  {item.name}
                                </Text>
                                {item.score ? (
                                  <>
                                    <DashedLine
                                      style={styles.evaluateLine}
                                      dashLength={2}
                                      dashGap={2}
                                      dashThickness={1}
                                      dashColor='#bababa'
                                    />
                                    <Text
                                      style={[
                                        styles.evaluateValue,
                                        Number(item.score) < 3 &&
                                          styles.evaluateValueRed,
                                      ]}
                                    >
                                      {item.score}
                                    </Text>
                                  </>
                                ) : null}
                              </View>
                              {item.detail ? (
                                <View style={styles.evaluateItemInfo}>
                                  <Text style={styles.evaluateDetailText}>
                                    {item.detail}
                                  </Text>
                                </View>
                              ) : null}
                            </View>
                          ))}
                        </>
                      ) : (
                        <View style={styles.evaluateBoxPlaceholder}>
                          <Text style={styles.evaluatePlaceholderText}>
                            暂无评价
                          </Text>
                        </View>
                      )}
                    </View>
                  ) : null}
                </View>
              );
            })}
          </ScrollView>
        </>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    height: '100%',
    backgroundColor: '#F8F8F8',
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  content: {
    flex: 1,
  },
  stickyTabContainer: {
    backgroundColor: '#FFFFFF',
    zIndex: 10,
    position: 'absolute',
    left: 0,
    right: 0,
  },
  userBox: {
    width: getRealSize(345),
    padding: getRealSize(15),
    backgroundColor: '#FFFFFF',
    marginHorizontal: getRealSize(15),
    marginTop: getRealSize(10),
    flexDirection: 'row',
  },
  avatarBox: {
    width: getRealSize(40),
    height: getRealSize(40),
    marginRight: getRealSize(10),
  },
  avatarImg: {
    width: '100%',
    height: '100%',
    borderRadius: getRealSize(20),
  },
  basicAvatar: {
    width: '100%',
    height: '100%',
    borderRadius: getRealSize(20),
    backgroundColor: '#A9EA6A',
    alignItems: 'center',
    justifyContent: 'center',
  },
  basicAvatarText: {
    fontSize: getRealSize(16),
    color: '#FFFFFF',
    fontWeight: '500',
  },
  userInfo: {
    flex: 1,
  },
  userHead: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getRealSize(4),
  },
  userName: {
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '500',
    marginRight: getRealSize(5),
  },
  divider: {
    width: getRealSize(1),
    height: getRealSize(8),
    backgroundColor: '#DEDEDE',
    marginHorizontal: getRealSize(5),
  },
  userSex: {
    fontSize: getRealSize(12),
    color: '#777777',
    fontWeight: '400',
  },
  userAge: {
    fontSize: getRealSize(12),
    color: '#777777',
    fontWeight: '400',
  },
  userId: {
    fontSize: getRealSize(12),
    color: '#777777',
    fontWeight: '400',
  },
  userTag: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  userTagItem: {
    paddingHorizontal: getRealSize(5),
    height: getRealSize(18),
    backgroundColor: '#F5F5F5',
    borderRadius: getRealSize(2),
    justifyContent: 'center',
    marginRight: getRealSize(5),
    marginTop: getRealSize(6),
  },
  userTagText: {
    fontSize: getRealSize(10),
    color: '#555555',
  },
  tabList: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    height: getRealSize(42),
    backgroundColor: '#FFFFFF',
    marginTop: getRealSize(10),
  },
  tabShadow: {
    height: getRealSize(25),
  },
  tabItem: {
    position: 'relative',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabItemText: {
    fontSize: getRealSize(16),
    color: '#777777',
    fontWeight: '500',
  },
  tabItemTextActive: {
    color: '#333333',
  },
  tabItemLine: {
    position: 'absolute',
    bottom: 0,
    width: getRealSize(20),
    height: getRealSize(2),
    backgroundColor: '#333333',
  },
  box: {
    width: getRealSize(345),
    marginHorizontal: getRealSize(15),
    marginBottom: getRealSize(10),
    backgroundColor: '#FFFFFF',
    paddingHorizontal: getRealSize(15),
    paddingVertical: getRealSize(15),
  },
  boxTitle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  titleLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftLine: {
    width: getRealSize(2),
    height: getRealSize(14),
    backgroundColor: '#A9EA6A',
    marginRight: getRealSize(5),
  },
  leftName: {
    fontSize: getRealSize(15),
    color: '#333333',
    fontWeight: '500',
  },
  serviceBox: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  serviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: getRealSize(10),
  },
  serviceName: {
    fontSize: getRealSize(14),
    color: '#555555',
    width: getRealSize(60),
    fontWeight: '400',
  },
  serviceValue: {
    fontSize: getRealSize(13),
    color: '#333333',
    marginLeft: getRealSize(10),
    flex: 1,
    fontWeight: '400',
  },
  serviceIncome: {
    flexDirection: 'row',
    marginTop: getRealSize(10),
  },
  incomeName: {
    fontSize: getRealSize(14),
    color: '#555555',
    width: getRealSize(60),
    fontWeight: '400',
  },
  incomeList: {
    marginLeft: getRealSize(10),
    flex: 1,
  },
  incomeItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  incomePrice: {
    fontSize: getRealSize(14),
    color: '#FF4040',
    fontWeight: '400',
  },
  incomeTag: {
    marginHorizontal: getRealSize(5),
    paddingHorizontal: getRealSize(5),
    backgroundColor: '#EBFBDC',
    borderRadius: getRealSize(2),
    height: getRealSize(20),
    justifyContent: 'center',
  },
  incomeTagText: {
    fontSize: getRealSize(12),
    color: '#61B43E',
    fontWeight: '400',
  },
  incomeTips: {
    fontSize: getRealSize(12),
    color: '#999999',
    fontWeight: '400',
  },
  serviceValueItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  serviceValueItemText: {
    fontSize: getRealSize(13),
    color: '#777777',
    fontWeight: '400',
  },
  serviceValueItemEva: {
    fontSize: getRealSize(13),
    color: '#333333',
    marginLeft: getRealSize(5),
    fontWeight: '400',
  },
  serviceValueItemEvaRed: {
    color: '#FF4040',
  },
  serviceValueLine: {
    width: getRealSize(1),
    height: getRealSize(12),
    backgroundColor: '#D8D8D8',
    marginHorizontal: getRealSize(10),
  },
  detailBox: {},
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: getRealSize(10),
  },
  detailName: {
    fontSize: getRealSize(14),
    color: '#777777',
    fontWeight: '400',
  },
  detailPrice: {
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '400',
    width: getRealSize(220),
    textAlign: 'right',
  },
  billingBox: {
    // Billing box styles
  },
  // 治疗单项目样式
  treatmentBox: {
    marginTop: getRealSize(10),
  },
  treatmentHead: {
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '500',
  },
  treatmentBody: {
    marginTop: getRealSize(10),
    backgroundColor: 'rgba(246, 249, 249, 0.8)',
    padding: getRealSize(10),
  },
  treatmentItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: getRealSize(5),
  },
  treatmentIcon: {
    width: getRealSize(15),
    height: getRealSize(15),
    marginRight: getRealSize(5),
    marginTop: getRealSize(2.5),
  },
  treatmentName: {
    flex: 1,
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '500',
  },
  // 执行项目样式
  runBox: {
    marginTop: getRealSize(15),
  },
  runHead: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  runTitle: {
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '500',
  },
  runHeadRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  runTip: {
    fontSize: getRealSize(12),
    color: '#AAABB3',
    fontWeight: '400',
  },
  runPrice: {
    marginLeft: getRealSize(5),
    fontSize: getRealSize(15),
    color: '#FF4040',
    fontWeight: '500',
  },
  runPriceFlag: {
    fontSize: getRealSize(12),
  },
  runBody: {
    backgroundColor: 'rgba(246, 249, 249, 0.8)',
    padding: getRealSize(10),
    marginTop: getRealSize(10),
  },
  runList: {
    paddingBottom: getRealSize(10),
  },
  runListLine: {
    width: '100%',
    height: getRealSize(1),
    marginTop: getRealSize(10),
  },
  runLi: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    minHeight: getRealSize(20),
    marginBottom: getRealSize(10),
  },
  runLiName: {
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '500',
    flex: 1,
  },
  runLiTag: {
    flexDirection: 'row',
    alignItems: 'center',
    maxWidth: getRealSize(100),
    justifyContent: 'flex-end',
  },
  runLiTagItem: {
    height: getRealSize(20),
    paddingHorizontal: getRealSize(5),
    marginLeft: getRealSize(5),
    justifyContent: 'center',
    alignItems: 'center',
  },
  runLiTagItem1: {
    backgroundColor: '#EBFBDC',
    borderWidth: getRealSize(0.5),
    borderColor: '#61B43E',
  },
  runLiTagItem2: {
    backgroundColor: '#F1F1F5',
    borderWidth: getRealSize(0.5),
    borderColor: '#979797',
  },
  runLiTagItem3: {
    backgroundColor: '#FEEFE8',
    borderWidth: getRealSize(0.5),
    borderColor: '#FF6B33',
  },
  runLiTagItemText: {
    fontSize: getRealSize(12),
    fontWeight: '400',
  },
  runLiTagItemText1: {
    color: '#61B43E',
  },
  runLiTagItemText2: {
    color: '#777777',
  },
  runLiTagItemText3: {
    color: '#FF6B33',
  },
  runBottom: {
    fontSize: getRealSize(13),
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  runBottomItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  runBottomTip: {
    color: '#555555',
    fontSize: getRealSize(13),
  },
  runBottomContent: {
    fontSize: getRealSize(13),
    color: '#333333',
    fontWeight: '500',
    marginLeft: getRealSize(5),
  },
  runBottomLine: {
    height: getRealSize(12),
    width: getRealSize(1),
    backgroundColor: '#D8D8D8',
    marginHorizontal: getRealSize(10),
  },
  evaluateBox: {
    paddingTop: getRealSize(0),
  },
  evaluateItem: {
    marginTop: getRealSize(10),
  },
  evaluateItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: getRealSize(20),
  },
  evaluateLabel: {
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '400',
  },
  evaluateLine: {
    flex: 1,
    height: getRealSize(1),
    marginHorizontal: getRealSize(16),
  },
  evaluateValue: {
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '500',
  },
  evaluateValueRed: {
    color: '#FF4040',
  },
  evaluateItemInfo: {
    marginTop: getRealSize(10),
    backgroundColor: 'rgba(246, 249, 249, 0.8)',
    borderRadius: getRealSize(6),
    padding: getRealSize(10),
  },
  evaluateDetailText: {
    fontSize: getRealSize(13),
    color: '#333333',
    fontWeight: '400',
    lineHeight: getRealSize(18),
  },
  evaluateBoxPlaceholder: {
    paddingTop: getRealSize(5),
    alignItems: 'center',
  },
  evaluatePlaceholderText: {
    fontSize: getRealSize(14),
    color: '#777777',
    lineHeight: getRealSize(20),
    textAlign: 'center',
  },
});

export default RecordDetailPage;
