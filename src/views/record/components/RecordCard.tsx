import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { getRealSize } from '@/common/utils';

interface StaffItem {
  name: string;
}

interface ProductItem {
  product_name: string;
  quantity: number;
  amount: number;
}

interface RecordItem {
  visit_id: string | number;
  tenant_id: string | number;
  date: string;
  avatar: string;
  customer_name: string;
  staff_list: StaffItem[];
  product_list: ProductItem[];
  total_amount: number;
  evaluate_score?: number | string;
}

interface RecordCardProps {
  record: RecordItem;
}

const RecordCard: React.FC<RecordCardProps> = ({ record }) => {
  // 获取员工名称列表
  const getStaffNameList = () => {
    try {
      return record.staff_list?.map(item => item.name)?.join('、') || '';
    } catch (error) {
      console.error(error);
      return '';
    }
  };

  return (
    <View style={styles.recordCard}>
      {/* Header */}
      <View style={styles.recordCardHeader}>
        <Image
          source={{
            uri:
              record.avatar ||
              'https://static.soyoung.com/sy-pre/default-avatar.png',
          }}
          style={styles.recordCardHeaderAvatar}
        />
        <Text style={styles.recordCardHeaderName} numberOfLines={1}>
          {record.customer_name}
        </Text>
        <Text style={styles.recordCardHeaderDate}>到店时间 {record.date}</Text>
      </View>

      {/* Staff */}
      {record.staff_list && record.staff_list.length > 0 && (
        <View style={styles.recordCardStaff}>
          <Text style={styles.recordCardStaffTitle}>服务员工</Text>
          <Text style={styles.recordCardStaffName} numberOfLines={1}>
            {getStaffNameList()}
          </Text>
        </View>
      )}

      {/* Product */}
      <View style={styles.recordCardProduct}>
        {record.product_list && record.product_list.length > 0 && (
          <>
            <View style={styles.recordCardProductHeader}>
              <Text style={styles.recordCardProductHeaderLeft}>执行项目</Text>
              <View style={styles.recordCardProductHeaderRight}>
                <Text style={styles.recordCardProductHeaderRightLabel}>
                  总金额
                </Text>
                <Text style={styles.recordCardProductHeaderRightSymbol}>
                  ￥
                </Text>
                <Text style={styles.recordCardProductHeaderRightValue}>
                  {record.total_amount}
                </Text>
              </View>
            </View>
            <View style={styles.recordCardProductBody}>
              {record.product_list.map((item, index) => (
                <View
                  key={`product_${index}`}
                  style={styles.recordCardProductItem}
                >
                  <Text style={styles.recordCardProductItemName}>
                    {item.product_name}
                    {item.quantity > 1 && ` *${item.quantity}`}
                  </Text>
                  <Text style={styles.recordCardProductItemPrice}>
                    ¥{item.amount}
                  </Text>
                </View>
              ))}
            </View>
          </>
        )}

        {/* Score */}
        <View style={styles.recordCardProductScore}>
          <Text style={styles.recordCardProductScoreLeft}>用户评价</Text>
          <Text
            style={[
              styles.recordCardProductScoreRight,
              record.evaluate_score
                ? Number(record.evaluate_score) <= 3
                  ? styles.recordCardProductScoreRed
                  : null
                : styles.recordCardProductScoreGray,
            ]}
          >
            {record.evaluate_score ? `${record.evaluate_score}分` : '未评价'}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  recordCard: {
    backgroundColor: '#FFFFFF',
    padding: getRealSize(15),
  },
  recordCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getRealSize(9),
  },
  recordCardHeaderAvatar: {
    width: getRealSize(20),
    height: getRealSize(20),
    marginRight: getRealSize(5),
    borderRadius: getRealSize(10),
  },
  recordCardHeaderName: {
    flex: 1,
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '500',
  },
  recordCardHeaderDate: {
    fontSize: getRealSize(12),
    color: '#777777',
    fontWeight: '400',
  },
  recordCardStaff: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  recordCardStaffTitle: {
    fontSize: getRealSize(13),
    color: '#777777',
    fontWeight: '400',
    paddingRight: getRealSize(5),
  },
  recordCardStaffName: {
    flex: 1,
    fontSize: getRealSize(13),
    color: '#333333',
    fontWeight: '400',
  },
  recordCardProduct: {},
  recordCardProductHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: getRealSize(12),
    paddingBottom: getRealSize(11),
  },
  recordCardProductHeaderLeft: {
    fontSize: getRealSize(13),
    color: '#777777',
    fontWeight: '400',
  },
  recordCardProductHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  recordCardProductHeaderRightLabel: {
    fontSize: getRealSize(12),
    color: '#AAABB3',
    fontWeight: '400',
    paddingRight: getRealSize(5),
  },
  recordCardProductHeaderRightSymbol: {
    fontSize: getRealSize(12),
    color: '#61B43E',
    fontWeight: '400',
  },
  recordCardProductHeaderRightValue: {
    fontSize: getRealSize(15),
    color: '#61B43E',
    fontWeight: '500',
  },
  recordCardProductBody: {
    padding: getRealSize(10),
    backgroundColor: 'rgba(246, 249, 249, 0.8)',
  },
  recordCardProductItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  recordCardProductItemName: {
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '400',
    flex: 1,
  },
  recordCardProductItemPrice: {
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '400',
    marginLeft: getRealSize(15),
  },
  recordCardProductScore: {
    marginTop: getRealSize(10),
    flexDirection: 'row',
    alignItems: 'center',
  },
  recordCardProductScoreLeft: {
    fontSize: getRealSize(14),
    color: '#333333',
  },
  recordCardProductScoreRight: {
    marginLeft: getRealSize(10),
    fontSize: getRealSize(14),
    fontWeight: '500',
    color: '#333333',
  },
  recordCardProductScoreRed: {
    color: '#FE6631',
  },
  recordCardProductScoreGray: {
    color: '#777777',
  },
});

export default RecordCard;
