import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { getRealSize } from '@/common/utils';
import { ATrack } from '@soyoung/react-native-container';

interface CustomerInfo {
  customer_id: string | number;
  realname: string;
}

interface CustomerLevel {
  level_icon: string;
  level_name: string;
  level_value: number;
  level_value_name: string;
  period_validity: string;
}

interface Customer {
  base: CustomerInfo;
  customer_id: string;
  customer_level?: CustomerLevel;
}

interface PopupModalProps {
  visible: boolean;
  activeIndex: number;
  searchText: string;
  list: Customer[];
  onClose: () => void;
  onConfirm: (confirmData: Customer, index: number) => void;
}

const PopupModal: React.FC<PopupModalProps> = ({
  visible,
  activeIndex,
  list,
  searchText,
  onClose,
  onConfirm,
}) => {
  const insets = useSafeAreaInsets();
  const onClickItem = (item: Customer, index: number) => {
    onConfirm(item, index);
  };
  if (!visible || !list) return null;
  return (
    <View style={styles.popupOverlay}>
      <ATrack style={styles.popupMask} onPress={onClose} />
      <View
        style={[styles.popupContent, { top: getRealSize(108) + insets.top }]}
      >
        <View style={styles.performancePopup}>
          {list.length > 0 ? (
            <ScrollView
              style={styles.list}
              showsVerticalScrollIndicator={false}
              showsHorizontalScrollIndicator={false}
              overScrollMode='never'
              bounces={false}
              nestedScrollEnabled={false}
              scrollEventThrottle={16}
            >
              {list.map((item, index) => (
                <ATrack
                  key={`record_${item.customer_id}_${index}`}
                  style={styles.employeeItem}
                  onPress={() => onClickItem(item, index)}
                >
                  <Text
                    style={[
                      styles.employeeItemText,
                      index === activeIndex && searchText === item.base.realname
                        ? styles.employeeItemTextActive
                        : null,
                    ]}
                  >
                    {item.base.realname}
                  </Text>
                </ATrack>
              ))}
            </ScrollView>
          ) : (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>未搜索到客户</Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // 弹窗样式
  popupOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 101,
  },
  popupMask: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  popupContent: {
    position: 'absolute',
    left: 0,
    right: 0,
  },
  performancePopup: {
    backgroundColor: '#fff',
    paddingHorizontal: getRealSize(15),
    paddingBottom: getRealSize(15),
  },
  list: {
    flex: 1,
    maxHeight: getRealSize(360),
    overflow: 'hidden',
  },
  emptyContainer: {
    padding: getRealSize(10),
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: getRealSize(13),
    color: '#BABABA',
  },
  employeeItem: {
    padding: getRealSize(10),
  },
  employeeItemText: {
    fontSize: getRealSize(13),
    color: '#333333',
  },
  employeeItemTextActive: {
    color: '#61B43E',
  },
});

export default PopupModal;
