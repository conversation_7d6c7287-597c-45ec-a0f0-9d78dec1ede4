import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TextInput,
  ScrollView,
  Dimensions,
  Platform,
} from 'react-native';
import Modal from 'react-native-modal';
import { getRealSize } from '@/common/utils';
import { ATrack } from '@soyoung/react-native-container';
import { modalAnimation } from '../../../constant/modal_animation';

const { width: screenWidth } = Dimensions.get('window');

interface Employee {
  id: string;
  user_id: string;
  name: string;
}

interface PopupModalProps {
  visible: boolean;
  staffId: string;
  employeeList: Employee[];
  onClose: () => void;
  onConfirm: (confirmData: Employee) => void;
}

const PopupModal: React.FC<PopupModalProps> = ({
  visible,
  staffId,
  employeeList,
  onClose,
  onConfirm,
}) => {
  const [fuzzySearchCustomerText, setFuzzySearchCustomerText] =
    useState<string>('');
  const [fuzzySearchEmployeeList, setFuzzySearchEmployeeList] = useState<
    Employee[]
  >([]);
  const scrollViewRef = useRef<ScrollView>(null);
  // 简化状态：只需要存储单个item的高度
  const [itemHeight, setItemHeight] = useState<number>(0);

  // 搜索员工
  const searchStaff = (text: string) => {
    setFuzzySearchCustomerText(text);
    if (text) {
      setFuzzySearchEmployeeList(
        employeeList.filter(item => item.name.includes(text))
      );
    } else {
      setFuzzySearchEmployeeList(employeeList);
    }
  };

  const onClickItem = (item: Employee) => {
    // 如果选择的是"全部员工"，清空搜索文本
    if (item.id === '0') {
      setFuzzySearchCustomerText('');
    } else {
      setFuzzySearchCustomerText(item.name);
    }
    onClose();
    onConfirm(item);
  };

  // 记录item的高度（只需要记录一次）
  const onItemLayout = (height: number) => {
    if (itemHeight === 0) {
      setItemHeight(height);
    }
  };

  // 滚动到选中的员工位置
  const scrollToSelectedEmployee = () => {
    if (
      !staffId ||
      !scrollViewRef.current ||
      itemHeight === 0 ||
      !fuzzySearchCustomerText
    )
      return;

    // 构建实际渲染的列表（包含"全部员工"选项）
    const allEmployeesOption = { id: '0', user_id: '', name: '全部员工' };
    const currentList =
      fuzzySearchEmployeeList.length > 0
        ? fuzzySearchEmployeeList
        : employeeList;
    const actualRenderList = [allEmployeesOption].concat(currentList);

    // 在实际渲染的列表中查找选中员工的索引
    const selectedIndex = actualRenderList.findIndex(
      item => item.id === staffId
    );

    if (selectedIndex !== -1) {
      // 计算选中项的位置
      const selectedItemY = selectedIndex * itemHeight;

      // 计算ScrollView的可视区域高度
      const scrollViewHeight = getRealSize(430);

      // 计算滚动位置，让选中项显示在可视区域的中间
      const scrollY = Math.max(
        0,
        selectedItemY - scrollViewHeight / 2 + itemHeight / 2
      );

      scrollViewRef.current?.scrollTo({
        y: scrollY,
        animated: true,
      });
    }
  };

  useEffect(() => {
    setFuzzySearchEmployeeList(employeeList);
    // 重置布局状态
    setItemHeight(0);
    // 如果弹窗是打开状态，设置员工列表后需要滚动到选中的员工
    if (visible && employeeList.length > 0 && staffId) {
      setTimeout(() => {
        scrollToSelectedEmployee();
      }, 100);
    }
  }, [employeeList, visible, staffId]);

  // 当布局准备好且弹窗显示时，滚动到选中的员工
  useEffect(() => {
    if (visible && employeeList.length > 0 && staffId && itemHeight > 0) {
      setTimeout(() => {
        scrollToSelectedEmployee();
      }, 100);
    }
  }, [itemHeight, visible, staffId]);

  // 当搜索结果更新时，重置布局状态
  useEffect(() => {
    setItemHeight(0);
  }, [fuzzySearchEmployeeList]);

  const renderContent = () => {
    return (
      <View style={styles.performancePopup}>
        <View style={styles.performancePopupTitle}>
          <Text style={styles.performancePopupTitleText}>选择员工</Text>
        </View>
        <ATrack style={styles.performancePopupClose} onPress={onClose}>
          <Image
            style={styles.performancePopupCloseIcon}
            source={{
              uri: 'https://static.soyoung.com/sy-design/bzsokyai5osd1753688976847.png',
            }}
          />
        </ATrack>
        <View style={styles.filterSearch}>
          <Image
            source={{
              uri: 'https://static.soyoung.com/sy-design/jsc2u0zt10171753259744187.png',
            }}
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            value={fuzzySearchCustomerText}
            onChangeText={searchStaff}
            placeholder='搜索员工'
            placeholderTextColor='#AAABB3'
          />
        </View>
        <ScrollView
          ref={scrollViewRef}
          style={styles.employeeList}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          overScrollMode='never'
          bounces={false}
          nestedScrollEnabled={false}
          scrollEventThrottle={16}
        >
          {[{ id: '0', user_id: '', name: '全部员工' }]
            .concat(fuzzySearchEmployeeList)
            .map((item, index) => (
              <ATrack
                key={`record_${item.id}_${index}`}
                style={styles.employeeItem}
                onPress={() => onClickItem(item)}
                onLayout={({ nativeEvent }) =>
                  onItemLayout(nativeEvent.layout.height)
                }
              >
                <Text
                  style={[
                    styles.employeeItemText,
                    staffId === item.id && fuzzySearchCustomerText !== ''
                      ? styles.employeeItemTextActive
                      : null,
                  ]}
                >
                  {item.name}
                </Text>
              </ATrack>
            ))}
        </ScrollView>
      </View>
    );
  };

  if (!visible) return null;
  return (
    <Modal
      isVisible={visible}
      {...modalAnimation}
      onBackdropPress={onClose}
      animationIn='slideInUp'
      animationOut='slideOutDown'
      style={styles.modal}
      avoidKeyboard={false}
      statusBarTranslucent={Platform.OS === 'android'}
    >
      {Platform.OS === 'android' ? (
        <View
          style={{
            flex: 1,
            justifyContent: 'flex-end', // 使用固定定位避免键盘影响
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
          }}
        >
          <View
            style={{
              ...styles.popBody,
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
            }}
          >
            {renderContent()}
          </View>
        </View>
      ) : (
        <View style={styles.popBody}>{renderContent()}</View>
      )}
    </Modal>
  );
};

const styles = StyleSheet.create({
  // 弹窗样式
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  popBody: {
    width: screenWidth,
    height: getRealSize(560),
    backgroundColor: '#ffffff',
  },
  performancePopup: {
    paddingHorizontal: getRealSize(15),
    paddingBottom: getRealSize(20),
  },
  performancePopupClose: {
    position: 'absolute',
    right: getRealSize(15),
    top: getRealSize(15),
    width: getRealSize(20),
    height: getRealSize(20),
  },
  performancePopupCloseIcon: {
    width: getRealSize(20),
    height: getRealSize(20),
  },
  performancePopupTitle: {
    paddingVertical: getRealSize(15),
    justifyContent: 'center',
    alignItems: 'center',
  },
  performancePopupTitleText: {
    fontSize: getRealSize(17),
    color: '#030303',
    fontWeight: '500',
    textAlign: 'center',
    fontFamily: 'PingFangSC-Medium',
  },
  filterSearch: {
    height: getRealSize(36),
    paddingHorizontal: getRealSize(10),
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
  },
  searchIcon: {
    width: getRealSize(16),
    height: getRealSize(16),
    marginRight: getRealSize(5),
  },
  searchInput: {
    flex: 1,
    fontSize: getRealSize(13),
    color: '#333333',
    padding: 0,
  },
  employeeList: {
    marginTop: getRealSize(10),
    overflow: 'hidden',
    height: getRealSize(430),
  },
  employeeItem: {
    padding: getRealSize(10),
  },
  employeeItemText: {
    fontSize: getRealSize(13),
    color: '#333333',
  },
  employeeItemTextActive: {
    color: '#61B43E',
  },
});

export default PopupModal;
