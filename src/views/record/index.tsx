import React from 'react';
import RecordPage from './page/index';
import { NavigationProp } from '@react-navigation/native';

interface PageProps {
  navigation: NavigationProp<any>;
}

interface PageState {
  pageShow: boolean;
}

class RecordMainPage extends React.Component<PageProps, PageState> {
  constructor(props: PageProps) {
    super(props);
    this.state = {
      pageShow: true,
    };
  }

  soyoungPageName() {
    return '服务记录';
  }

  /** 页面埋点 */
  soyoungPageInfo() {
    return {
      page_name: '服务记录页',
      page_type: 'list',
    };
  }

  didAppear() {
    this.setState({
      pageShow: true,
    });
  }

  willDisappear() {
    this.setState({
      pageShow: false,
    });
  }

  preferredStatusBarStyle() {
    // 0默认 1 白色 2 黑色
    return '2';
  }

  render() {
    return <RecordPage {...this.props} pageShowFlag={this.state.pageShow} />;
  }
}

export default RecordMainPage;
