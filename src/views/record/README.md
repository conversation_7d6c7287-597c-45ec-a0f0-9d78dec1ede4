# 服务记录模块 (Record Module)

## 概述

本模块是从微信小程序 `sy-chain/src/packageServe` 迁移到 React Native 的服务记录功能模块。

## 目录结构

```
record/
├── index.tsx           # 主入口文件（包含完整功能实现）
├── components/
│   └── RecordCard.tsx # 服务记录卡片组件
├── detail.tsx         # 服务记录详情页
└── README.md         # 本文档
```

## 已完成功能

### 列表页 (index.tsx)
- ✅ 服务记录列表展示
- ✅ 时间筛选（今日、本月、自定义）
- ✅ 员工筛选（仅店长权限可见）
- ✅ 客户搜索功能
- ✅ 下拉刷新
- ✅ 分页加载更多
- ✅ 空状态展示
- ✅ RecordCard 组件展示记录信息
- ✅ Class Component 结构（包含埋点和生命周期）

### 详情页 (detail.tsx)
- ✅ 客户信息展示
- ✅ Tab切换（我的服务、服务明细、开单项目、用户评价）
- ✅ 基础页面结构和样式

## TODO 项

### 核心功能
1. **Popup组件**
   - 需要实现或引入Popup弹窗组件
   - 用于时间选择、员工选择、客户搜索的弹窗

2. **权限控制**
   - 目前使用 `TODO_PERMISSION` 占位
   - 需要确认实际的权限key

3. **路由配置**
   - 详情页路由名称需要确认（目前使用 `RecordDetail`）
   - 客户详情页路由需要确认（目前使用 `ClientDetail`）

### 详情页内容实现
1. **我的服务 (tab0)**
   - 执行项目展示
   - 服务时长
   - 收入明细
   - 用户评价

2. **服务明细 (tab1)**
   - 咨询师信息
   - 医生/操作护士信息
   - 配台护士信息
   - 到店/离店时间

3. **开单项目 (tab2)**
   - 治疗单项目
   - 执行项目列表
   - 项目金额统计

4. **用户评价 (tab3)**
   - 评价列表
   - 评分展示
   - 评价详情

### 其他优化
1. 日期选择器集成优化
2. 滚动到指定Tab位置功能
3. 性能优化（列表渲染优化）
4. 错误处理完善

## API 接口

使用的主要接口：
- `/chain-wxapp/v1/tenant/getMyTenantAllUsers` - 获取员工列表
- `/chain-wxapp/v1/execution/searchCustomerList` - 搜索客户
- `/chain-wxapp/v1/execution/workbenchExecutionList` - 获取服务记录列表
- `/chain-wxapp/v1/execution/workbenchExecutionDetail` - 获取服务记录详情

## 注意事项

1. 代码中使用了 `TODO` 标记需要后续完善的地方
2. 样式尺寸使用了 `getRealSize` 进行适配
3. 使用了 `ATrack` 组件进行埋点
4. 遵循了项目现有的代码风格和结构
5. 主文件 `index.tsx` 现在包含了完整的功能实现和 Class Component 结构

## 使用方式

在应用入口页面点击"服务记录"即可进入该模块。 