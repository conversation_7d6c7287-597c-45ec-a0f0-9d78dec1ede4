import React from 'react';
import PerformancePage from './page';

interface PageProps {}

interface State {
  pageShow: boolean;
}

class PerformancePageContainer extends React.Component<PageProps, State> {
  constructor(props: PageProps) {
    super(props);
    this.state = {
      pageShow: true,
    };
  }

  soyoungPageName() {
    return 'employee_other_work_packageperformance_page';
  }

  /** 页面埋点 */
  soyoungPageInfo() {
    return {
      info: 'employee_other_work_packageperformance_page',
    };
  }

  preferredStatusBarStyle() {
    // 0默认 1 白色 2 黑色
    return '2';
  }

  didAppear() {
    this.setState({
      pageShow: true,
    });
  }

  willDisappear() {
    this.setState({
      pageShow: false,
    });
  }

  render() {
    return <PerformancePage {...this.props} />;
  }
}

export default PerformancePageContainer;
