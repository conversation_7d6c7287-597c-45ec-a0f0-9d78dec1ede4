import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  RefreshControl,
  Clipboard,
  ActivityIndicator,
} from 'react-native';
import { flatListProps } from '@/constant/flatlist_props';
import { ATrack } from '@soyoung/react-native-container';
import { getRealSize } from '../../../common/utils';
import jsApi from '@soyoung/react-native-jsapi';
import api from '../../../common/api';
import dayjs from 'dayjs';
import Header from '../../../components/header';
import PopupModalTime from '@/components/PopupModalTime';
import { SafeAreaView } from 'react-native-safe-area-context';
import PermissionWrapper from '@/components/PermissionWrapper';

// PerformanceCard 组件
interface PerformanceCardProps {
  item: ListItem;
  onCopy: (code: string) => void;
}

const PerformanceCard = React.memo<PerformanceCardProps>(({ item, onCopy }) => {
  const handleCopyPress = () => onCopy(item.code);

  return (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
        <View style={styles.cardHeaderCode}>
          <Text style={styles.cardHeaderCodeText}>{item.code || '-'}</Text>
          {item.code && (
            <ATrack onPress={handleCopyPress}>
              <Image
                style={styles.cardHeaderCopy}
                source={{
                  uri: 'https://static.soyoung.com/sy-design/copy1754881491734.png',
                }}
              />
            </ATrack>
          )}
        </View>
        <View
          style={[
            styles.cardHeaderStatus,
            item.status_id === 1 && styles.cardHeaderStatus1,
            item.status_id === 2 && styles.cardHeaderStatus2,
          ]}
        >
          <Text
            style={[
              styles.cardHeaderStatusText,
              item.status_id === 1 && styles.cardHeaderStatusText1,
              item.status_id === 2 && styles.cardHeaderStatusText2,
            ]}
          >
            {item.status_name}
          </Text>
        </View>
      </View>
      <View style={styles.cardBody}>
        <Text style={styles.cardBodyName}>{item.product_name}</Text>
      </View>
      <View style={styles.cardFooter}>
        <Text style={styles.cardYuan}>￥</Text>
        <Text style={styles.cardFooterAmount}>{item.exec_amount}</Text>
      </View>
    </View>
  );
});
interface PerformanceForm {
  performance_sdate: string;
  performance_edate: string;
  tab_id: number;
  page: number;
  size: number;
}

interface Tabs {
  tab_id: number;
  tab_name: string;
  summarize_data: {
    name: string;
    amount: string;
  }[];
}

interface ListItem {
  id: number;
  code: string;
  date: string;
  exec_times: number;
  status_id: number;
  status_name: string;
  exec_amount: string;
  performance_amount: string;
  product_name: string;
}

interface PageProps {
  // 添加下划线前缀表示未使用
}

const PerformancePage = React.memo<PageProps>(() => {
  const today = dayjs().format('YYYY-MM-DD');

  const [form, setForm] = useState<PerformanceForm>({
    performance_sdate: today,
    performance_edate: today,
    tab_id: 0,
    page: 1,
    size: 10,
  });

  const [tabs, setTabs] = useState<Tabs[]>([]);
  const [tableList, setTableList] = useState<ListItem[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [dateType, setDateType] = useState<number>(1);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [dataChanging, setDataChanging] = useState<boolean>(false);
  const [hasPermission, setHasPermission] = useState<boolean>(true);

  // 用于避免过时闭包的ref
  const formRef = useRef(form);
  const abortControllerRef = useRef<AbortController | null>(null);
  const mountedRef = useRef(true);
  // 更新formRef
  useEffect(() => {
    formRef.current = form;
  }, [form]);

  // 汇总数据
  const summarizeData = useMemo(() => {
    return tabs.reduce(
      (prev, next) => {
        prev.push(...(next.summarize_data || []));
        return prev;
      },
      [] as Tabs['summarize_data']
    );
  }, [tabs]);

  // 按日期分组的表格数据，优化为扁平化结构用于FlatList
  const flattenedList = useMemo(() => {
    if (tableList.length === 0) {
      return [];
    }

    const dateMaps: Record<string, { date: string; items: ListItem[] }> = {};

    // 分组 - 优化：减少对象创建
    for (let i = 0; i < tableList.length; i++) {
      const item = tableList[i];
      const date = item.date;
      if (!dateMaps[date]) {
        dateMaps[date] = {
          date,
          items: [],
        };
      }
      dateMaps[date].items.push(item);
    }

    // 转换为扁平化数组 - 优化：预分配数组大小
    const dateGroups = Object.values(dateMaps);
    const totalItems = dateGroups.reduce(
      (sum, group) => sum + group.items.length,
      0
    );
    const result: Array<
      | { type: 'date'; date: string }
      | { type: 'item'; item: ListItem; id: string }
    > = new Array(totalItems + dateGroups.length);

    let index = 0;
    for (let i = 0; i < dateGroups.length; i++) {
      const group = dateGroups[i];
      result[index++] = { type: 'date', date: group.date };
      for (let j = 0; j < group.items.length; j++) {
        const item = group.items[j];
        result[index++] = {
          type: 'item',
          item,
          id: `${item.date}-${item.id}`, // 使用唯一的复合key
        };
      }
    }

    return result;
  }, [tableList]);

  // 时间按钮内容
  const timeButtonContent = useCallback(() => {
    switch (dateType) {
      case 1:
        return '今日';
      case 2:
        return '本月';
      case 3:
        if (form.performance_sdate === form.performance_edate) {
          return form.performance_sdate === today
            ? '今日'
            : form.performance_sdate;
        }
        return `${form.performance_sdate} 至 ${form.performance_edate}`;
      default:
        return '今日';
    }
  }, [dateType, form.performance_sdate, form.performance_edate, today]);

  // 获取数据列表
  const getTcList = useCallback(
    async (isRefresh = false, customForm?: PerformanceForm) => {
      // 取消之前的请求
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      const controller = new AbortController();
      abortControllerRef.current = controller;

      const requestForm = customForm || formRef.current;

      // 批量更新状态
      if (isRefresh) {
        setLoading(true);
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      try {
        const response = await api.pagefetch({
          path: '/chain-wxapp/v1/performance/tcList',
          params: requestForm,
          isLoading: false,
          // 注意：这里假设api.pagefetch支持signal参数，如果不支持需要移除
        });

        // 检查请求是否被取消或组件已卸载
        if (controller.signal.aborted || !mountedRef.current) {
          return;
        }
        if (response.errorCode === 10002) {
          setHasPermission(true);
          return;
        }

        if (response.errorCode !== 0) {
          jsApi.toNative('showToast', {
            toast: response.errorMsg || '获取数据失败',
          });
          return;
        }

        const responseData = response.responseData;

        // 批量状态更新
        if (customForm && responseData.tabs?.length > 0) {
          const updatedForm = {
            ...customForm,
            tab_id: customForm.tab_id || responseData.tabs[0].tab_id,
          };
          setForm(updatedForm);
          setTabs(responseData?.tabs || []);
          if (isRefresh || requestForm.page === 1) {
            setTableList(responseData?.list || []);
          } else {
            setTableList(prev => [...prev, ...(responseData?.list || [])]);
          }
          setTotal(responseData?.total || 0);
        } else {
          setTabs(responseData?.tabs || []);
          if (responseData?.tabs?.length) {
            setForm(prev => ({
              ...prev,
              tab_id: prev.tab_id || responseData.tabs[0].tab_id,
            }));
            if (isRefresh || requestForm.page === 1) {
              setTableList(responseData?.list || []);
            } else {
              setTableList(prev => [...prev, ...(responseData?.list || [])]);
            }
            setTotal(responseData?.total || 0);
          }
        }
      } catch (error: unknown) {
        if (error instanceof Error && error.name === 'AbortError') {
          // Request was cancelled
          return;
        }
        // 生产环境可以使用日志服务替代console
        if (__DEV__) {
          // eslint-disable-next-line no-console
          console.error('getTcList error:', error);
        }
        jsApi.toNative('showToast', {
          toast: '获取数据异常',
        });
      } finally {
        setLoading(false);
        setRefreshing(false);
        setDataChanging(false);
        if (abortControllerRef.current === controller) {
          abortControllerRef.current = null;
        }
      }
    },
    [] // 移除form依赖，使用ref避免过时闭包
  );

  // 打开/关闭弹窗
  const openPopup = useCallback(() => {
    setIsOpen(!isOpen);
  }, [isOpen]);

  const closePopup = useCallback(() => {
    setIsOpen(false);
  }, []);

  // 处理时间选择确认
  const handleTimeConfirm = useCallback(
    (confirmData: { start: string; end: string; type: number }) => {
      const { start, end, type } = confirmData;
      setDateType(type);
      setDataChanging(true);

      const newForm = {
        ...formRef.current,
        performance_sdate: start,
        performance_edate: end,
        page: 1,
      };

      setForm(newForm);

      // 添加小延迟，让用户看到加载状态开始，然后开始数据请求
      setTimeout(() => {
        getTcList(true, newForm);
      }, 50);
    },
    [getTcList]
  );

  // 切换标签
  const changeTab = useCallback(
    (tab: Tabs) => {
      setDataChanging(true);
      const newForm = { ...formRef.current, tab_id: tab.tab_id, page: 1 };
      setForm(newForm);
      // 不立即清空列表，让新数据加载完成后再更新
      getTcList(true, newForm);
    },
    [getTcList]
  );

  // 删除未使用的formatExecAmount函数

  // 复制代码
  const handleCopy = useCallback((code: string) => {
    Clipboard.setString(code);
    jsApi.toNative('showToast', {
      toast: '复制成功',
    });
  }, []);

  // 下拉刷新
  const onRefresh = useCallback(() => {
    const newForm = { ...formRef.current, page: 1 };
    setForm(newForm);
    // 下拉刷新时保持当前数据，避免闪烁
    getTcList(true, newForm);
  }, [getTcList]);

  // 加载更多 - 参考服务记录页面的优化逻辑
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const loadMore = useCallback(async () => {
    // 防止重复加载或数据为空时加载
    if (
      loadingMore ||
      loading ||
      tableList.length >= total ||
      tableList.length === 0
    ) {
      return;
    }

    setLoadingMore(true);
    try {
      const newPage = formRef.current.page + 1;
      const newForm = { ...formRef.current, page: newPage };
      // 直接调用 API 获取更多数据
      const response = await api.pagefetch({
        path: '/chain-wxapp/v1/performance/tcList',
        params: newForm,
        isLoading: false,
      });

      // 检查组件是否已卸载
      if (!mountedRef.current) return;

      if (response.errorCode === 0 && response.responseData) {
        setForm(newForm);
        setTableList(prev => [...prev, ...(response.responseData.list || [])]);
        setTotal(response.responseData.total || 0);
      } else if (response.errorCode === 10002) {
        setHasPermission(true);
      }
    } catch (error) {
      if (__DEV__) {
        console.error('loadMore error:', error);
      }
    } finally {
      // 检查组件是否已卸载
      if (mountedRef.current) {
        setLoadingMore(false);
      }
    }
  }, [loadingMore, loading, tableList.length, total]);

  // 预定义的静态样式对象
  const staticStyles = useMemo(
    () => ({
      pageHeaderDateSelected: [
        styles.pageHeaderDate,
        styles.pageHeaderDateSelected,
      ],
      emptyContainer: { flex: 1 },
    }),
    []
  );

  // FlatList渲染项
  const renderItem = useCallback(
    ({ item }: { item: (typeof flattenedList)[0] }) => {
      if (item.type === 'date') {
        return <Text style={styles.pageContentDate}>{item.date}</Text>;
      }

      return <PerformanceCard item={item.item} onCopy={handleCopy} />;
    },
    [handleCopy]
  );

  // FlatList的key提取器
  const keyExtractor = useCallback((item: (typeof flattenedList)[0]) => {
    return item.type === 'date' ? `date-${item.date}` : item.id;
  }, []);

  // FlatList头部组件
  const ListHeaderComponent = useCallback(
    () => (
      <>
        {/* Page Header */}
        <View style={styles.pageHeader}>
          <ATrack
            style={
              isOpen
                ? staticStyles.pageHeaderDateSelected
                : styles.pageHeaderDate
            }
            onPress={openPopup}
          >
            <Text style={styles.pageHeaderDateText}>{timeButtonContent()}</Text>
            <Image
              source={{
                uri: 'https://static.soyoung.com/sy-design/1sntk1g3r416o1754981812238.png',
              }}
              resizeMode='contain'
              style={styles.pageHeaderDateTriangle}
            />
          </ATrack>
          <View style={styles.pageHeaderLine} />
          <View style={styles.pageHeaderTabs}>
            {summarizeData.map((item, index) => (
              <View key={index} style={styles.pageHeaderTab}>
                <Text style={styles.pageHeaderTabAmount}>{item.amount}</Text>
                <Text style={styles.pageHeaderTabName}>{item.name}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Page Tabs */}
        <View style={styles.pageTabs}>
          {tabs.map(item => (
            <ATrack
              key={item.tab_id}
              style={[
                styles.pageTab,
                form.tab_id === item.tab_id && styles.pageTabActive,
              ]}
              onPress={() => changeTab(item)}
            >
              <Text
                style={[
                  styles.pageTabText,
                  form.tab_id === item.tab_id && styles.pageTabTextActive,
                ]}
              >
                {item.tab_name}
              </Text>
              {form.tab_id === item.tab_id && (
                <View style={styles.pageTabActiveIndicator} />
              )}
            </ATrack>
          ))}
        </View>

        {/* 加载指示器 */}
        {(loading || dataChanging) && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size='small' color='#61B43E' />
            <Text style={styles.loadingText}>数据加载中...</Text>
          </View>
        )}
      </>
    ),
    [
      isOpen,
      staticStyles.pageHeaderDateSelected,
      openPopup,
      timeButtonContent,
      summarizeData,
      tabs,
      form.tab_id,
      changeTab,
      loading,
      dataChanging,
    ]
  );

  // 渲染列表底部加载状态
  const renderFooter = useCallback(() => {
    if (loadingMore && total > tableList.length && tableList.length > 0) {
      return (
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>加载中...</Text>
        </View>
      );
    }

    if (!loadingMore && total <= tableList.length && tableList.length > 0) {
      return (
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>没有更多啦</Text>
        </View>
      );
    }

    return null;
  }, [loadingMore, total, tableList.length]);

  // 初始化
  useEffect(() => {
    getTcList(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      // 取消未完成的请求
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return (
    <SafeAreaView style={staticStyles.emptyContainer} edges={['bottom']}>
      {/* Header */}
      <Header title='执行记录' bgColor='#F8F8F8' />
      <View style={styles.pageWrap}>
        {/* 骨架图显示 */}
        {!hasPermission ? (
          <PermissionWrapper hasPermission={false} />
        ) : !tabs.length ? (
          <View style={styles.pageEmpty}>
            <Image
              style={styles.pageEmptyImage}
              source={{
                uri: 'https://static.soyoung.com/sy-design/aqnomvpf3ki11753429315696.png',
              }}
            />
            <Text style={styles.pageEmptyText}>暂无内容</Text>
          </View>
        ) : (
          /* Page Content - 使用FlatList优化性能，头部内容作为ListHeaderComponent */
          <FlatList
            style={styles.pageContent}
            data={flattenedList}
            renderItem={renderItem}
            keyExtractor={keyExtractor}
            ListHeaderComponent={ListHeaderComponent}
            ListFooterComponent={renderFooter}
            ListEmptyComponent={EmptyListComponent}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
            onEndReached={loadMore}
            getItemLayout={undefined} // 由于有不同类型的item，不设置固定高度
            showsVerticalScrollIndicator={false}
            {...flatListProps}
          />
        )}

        {/* 弹窗 */}
        <PopupModalTime
          visible={isOpen}
          onClose={closePopup}
          onConfirm={handleTimeConfirm}
        />
      </View>
    </SafeAreaView>
  );
});

const styles = StyleSheet.create({
  pageWrap: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  pageEmpty: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pageEmptyImage: {
    width: getRealSize(35),
    height: getRealSize(35),
  },
  pageEmptyText: {
    fontSize: getRealSize(14),
    color: '#030303',
    marginTop: getRealSize(20),
  },
  pageHeader: {
    marginHorizontal: getRealSize(15),
    minHeight: getRealSize(140),
    paddingVertical: getRealSize(15),
    paddingHorizontal: getRealSize(20),
    marginBottom: getRealSize(15),
    marginTop: getRealSize(12),
    backgroundColor: '#A9EA6A',
  },
  pageHeaderDate: {
    backgroundColor: '#333',
    height: getRealSize(26),
    paddingHorizontal: getRealSize(15),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'flex-start',
  },
  pageHeaderDateSelected: {
    backgroundColor: '#555', // 选中状态的背景色
  },
  pageHeaderDateText: {
    fontSize: getRealSize(14),
    color: '#ffffff',
  },
  pageHeaderDateTriangle: {
    width: getRealSize(9),
    height: getRealSize(5),
    marginLeft: getRealSize(2),
  },
  pageHeaderLine: {
    marginVertical: getRealSize(12),
    width: '100%',
    height: 0,
    backgroundColor: '#fff',
    opacity: 0.5,
  },
  pageHeaderTabs: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  pageHeaderTab: {
    width: '50%',
  },
  pageHeaderTabAmount: {
    fontSize: getRealSize(26),
    color: '#000',
    fontWeight: '700',
    fontFamily: 'Outfit-Medium',
  },
  pageHeaderTabName: {
    fontSize: getRealSize(12),
    color: '#000',
    marginTop: getRealSize(8),
  },
  pageTabs: {
    height: getRealSize(42),
    marginBottom: getRealSize(5),
    marginTop: getRealSize(-10),
    flexDirection: 'row',
    justifyContent: 'flex-start',
    paddingHorizontal: getRealSize(15),
  },
  pageTab: {
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    position: 'relative',
  },
  pageTabActive: {
    // Add active tab styles
  },
  pageTabText: {
    fontSize: getRealSize(16),
    color: '#777777',
    fontWeight: '500',
  },
  pageTabTextActive: {
    color: '#333',
  },
  pageTabActiveIndicator: {
    position: 'absolute',
    bottom: 0,
    left: '50%',
    transform: [{ translateX: -getRealSize(10) }],
    width: getRealSize(20),
    height: getRealSize(2),
    backgroundColor: '#A9EA6A',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: getRealSize(15),
    backgroundColor: '#f6f9f9',
    marginHorizontal: getRealSize(15),
  },
  loadingText: {
    marginLeft: getRealSize(8),
    fontSize: getRealSize(14),
    color: '#666666',
  },
  pageContent: {
    flex: 1,
    backgroundColor: '#f6f9f9',
  },
  pageContentDate: {
    fontSize: getRealSize(14),
    color: '#646464',
    fontWeight: '400',
    marginVertical: getRealSize(10),
    paddingHorizontal: getRealSize(15),
  },
  card: {
    backgroundColor: '#ffffff',
    padding: getRealSize(15),
    marginBottom: getRealSize(13),
    marginHorizontal: getRealSize(15),
  },
  cardHeader: {
    height: getRealSize(20),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getRealSize(10),
  },
  cardHeaderCode: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  cardHeaderCodeText: {
    fontSize: getRealSize(14),
    color: '#646464',
  },
  cardHeaderCopy: {
    width: getRealSize(14),
    height: getRealSize(14),
    marginLeft: getRealSize(6),
  },
  cardHeaderStatus: {
    height: getRealSize(20),
    paddingHorizontal: getRealSize(6),
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardHeaderStatus1: {
    backgroundColor: '#EBFBDC',
  },
  cardHeaderStatus2: {
    backgroundColor: '#f1f1f5',
  },
  cardHeaderStatusText: {
    fontSize: getRealSize(12),
    fontWeight: '400',
  },
  cardHeaderStatusText1: {
    color: '#61B43E',
  },
  cardHeaderStatusText2: {
    color: '#777777',
  },
  cardBody: {
    marginBottom: getRealSize(5),
  },
  cardBodyName: {
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '500',
    lineHeight: getRealSize(20),
  },
  cardYuan: {
    fontSize: getRealSize(12),
    color: '#61B43E',
    fontWeight: '400',
  },
  cardFooter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardFooterAmount: {
    fontSize: getRealSize(15),
    color: '#61B43E',
    fontWeight: '400',
    fontFamily: 'Outfit-Medium',
  },
  footerContainer: {
    paddingVertical: getRealSize(15),
    alignItems: 'center',
  },
  footerText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#999999',
  },
});

// 空列表组件 - 定义在组件外部避免重新创建
const EmptyListComponent = React.memo(() => (
  <View style={styles.pageEmpty}>
    <Image
      style={styles.pageEmptyImage}
      source={{
        uri: 'https://static.soyoung.com/sy-design/aqnomvpf3ki11753429315696.png',
      }}
    />
    <Text style={styles.pageEmptyText}>暂无相关数据</Text>
  </View>
));

export default PerformancePage;
