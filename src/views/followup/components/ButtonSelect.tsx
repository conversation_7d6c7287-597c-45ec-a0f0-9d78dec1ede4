import { getRealSize } from '@/common/utils';
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image } from 'react-native';

export interface ButtonSelectOption {
  label: string;
  value: string | number;
}

interface ButtonSelectProps {
  active: string | number | Array<string | number>;
  options: ButtonSelectOption[];
  multiple?: boolean;
  noCancle?: boolean;
  onChange?: (value: string | number | Array<string | number>) => void;
}

const ButtonSelect: React.FC<ButtonSelectProps> = ({
  active,
  options,
  multiple = false,
  noCancle = false,
  onChange,
}) => {
  const updateActive = (data: number | string) => {
    if (!multiple) {
      if ((active || active === 0) && active === data) {
        if (noCancle) return;
        onChange?.('');
      } else {
        onChange?.(data);
      }
    } else {
      let curList = active ? (active as Array<string | number>) : [];
      if (curList.includes(data)) {
        curList = curList.filter(item => item !== data);
      } else {
        curList = [...curList, data];
      }
      onChange?.(curList);
    }
  };

  const isItemActive = (value: string | number) => {
    if (Array.isArray(active)) {
      return active.includes(value);
    }
    return active === value;
  };

  return (
    <View style={styles.buttonSelect}>
      {options.map(item => (
        <TouchableOpacity
          activeOpacity={1}
          key={item.value}
          style={[
            styles.buttonSelectItem,
            isItemActive(item.value) && styles.buttonSelectItemActive,
          ]}
          onPress={() => updateActive(item.value)}
        >
          <Text
            style={[styles.buttonSelectText]}
            numberOfLines={1}
            ellipsizeMode='tail'
          >
            {item.label}
          </Text>
          {isItemActive(item.value) ? (
            <View style={styles.buttonSelectActiveIcon}>
              <Image
                source={{
                  uri: 'https://static.soyoung.com/sy-design/3o6q6zpv0xqzv1752461495423.png',
                }}
                style={styles.buttonSelectActiveIconImage}
              />
            </View>
          ) : null}
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  buttonSelect: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: getRealSize(-5), // 负边距来抵消内部的边距
  },
  buttonSelectItem: {
    height: getRealSize(38),
    lineHeight: getRealSize(38),
    backgroundColor: '#f5f5f5',
    borderWidth: 2,
    borderColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: getRealSize(8),
    marginHorizontal: getRealSize(5),
    marginVertical: getRealSize(5),
    width: '30%', // 三列布局
    minWidth: getRealSize(80),
    position: 'relative',
  },
  buttonSelectItemActive: {
    backgroundColor: '#fff',
    borderColor: '#333',
  },
  buttonSelectText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#333333',
    textAlign: 'center',
    fontWeight: '400',
  },
  buttonSelectActiveIcon: {
    position: 'absolute',
    right: getRealSize(-1),
    bottom: getRealSize(-1),
  },
  buttonSelectActiveIconImage: {
    width: getRealSize(18),
    height: getRealSize(13),
  },
});

export default ButtonSelect;
