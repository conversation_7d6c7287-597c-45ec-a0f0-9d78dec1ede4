import React, { Component } from 'react';
import { VisitedContainer } from './containers';
interface PageProps {
  route: {
    params: {
      params: {
        name: string;
      };
    };
  };
  insets: {
    bottom: number;
    left: number;
    right: number;
    top: number;
  };
}

export default class Page extends Component<PageProps> {
  constructor(props: PageProps) {
    super(props);
    this.state = {
      pageShow: true,
    };
  }

  soyoungPageName() {
    return 'employee_other_followup_index_reception_page';
  }

  /** 页面埋点 */
  soyoungPageInfo() {
    return {};
  }

  didAppear() {
    console.log('visited------didAppear');
    this.setState({
      pageShow: true,
    });
  }

  preferredStatusBarStyle() {
    // 0默认 1 白色 2 黑色
    return '2';
  }

  willDisappear() {
    console.log('visited------willDisappear');
    this.setState({
      pageShow: false,
    });
  }
  render() {
    return <VisitedContainer {...this.props} pageShow={this.state.pageShow} />;
  }
}
