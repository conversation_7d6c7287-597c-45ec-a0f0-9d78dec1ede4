import React, { Component } from 'react';
import {
  Text,
  View,
  StyleSheet,
  ScrollView,
  Image,
  Platform,
} from 'react-native';
import { getRealSize } from '@/common/utils';
import { ATrack } from '@soyoung/react-native-container';
import VideoPlayerView from '@soyoung/react-native-video-player';
import JSA<PERSON> from '@soyoung/react-native-jsapi';
import jsApi from '@soyoung/react-native-jsapi';

interface PageProps {
  route: {
    params: {
      params: {
        source: string;
      };
      tabbar: boolean;
    };
  };
  insets: {
    bottom: number;
    left: number;
    right: number;
    top: number;
  };
}

interface PageState {}

export default class Page extends Component<PageProps, PageState> {
  constructor(props: PageProps) {
    super(props);
    this.state = {};
  }

  handleCloseImageModal() {
    JSAPI.toNative('backAnimated', {
      transitionType: '0',
      disableAnimation: '0',
    });
  }

  handlePreviewImage(url: string) {
    JSAPI.toNative('pictureBrowser', {
      list: JSON.parse(this.props.route.params.params.source)
        .filter((item: any) => item.type === 1)
        .map((item: any) => {
          return {
            img_url: item.url,
          };
        }),
      currentIndex: JSON.parse(this.props.route.params.params.source)
        .filter((item: any) => item.type === 1)
        .findIndex((item: any) => item.url === url),
      style: 0,
    });
  }

  handlePreviewVideo(url: string) {
    // 使用浏览器预览视频，或者可以替换为其他预览方式
    jsApi.toNative('pictureBrowser', {
      list: [
        {
          video_url: url,
        },
      ],
      currentIndex: 0,
      style: 0,
    });
  }

  render() {
    return (
      <View style={styles.modalContent}>
        <View style={styles.titleBar}>
          <View style={styles.placholder} />
          <Text style={styles.titleText}>图片视频</Text>
          <ATrack onPress={this.handleCloseImageModal}>
            <Image
              style={styles.closeIcon}
              source={{
                uri: 'https://static.soyoung.com/sy-design/bzsokyai5osd1753688976847.png',
              }}
            />
          </ATrack>
        </View>
        <ScrollView
          style={styles.modalScrollView}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.modalImageGrid}>
            {JSON.parse(this.props.route.params.params.source).map(
              (item: any, index: number) => (
                <ATrack
                  key={index}
                  onPress={() => {
                    if (item.type === 1) {
                      this.handlePreviewImage(item.url);
                    } else {
                      this.handlePreviewVideo(item.url);
                    }
                  }}
                >
                  {item.type === 1 ? (
                    <Image
                      source={{ uri: item.url }}
                      style={styles.modalImage}
                      resizeMode='cover'
                    />
                  ) : (
                    <View
                      style={[
                        styles.modalImage,
                        // eslint-disable-next-line react-native/no-inline-styles
                        { backgroundColor: '#f0f0f0' },
                      ]}
                    >
                      <VideoPlayerView
                        style={styles.videoPlayer}
                        placeholder={{
                          uri: item.video_cover,
                        }}
                        placeholderStyle={
                          Platform.OS === 'ios'
                            ? {
                                width: getRealSize(105),
                                height: getRealSize(105),
                              }
                            : undefined
                        }
                        videoInfo={{
                          url: item.url,
                        }}
                        muted={true}
                        loop={false}
                      />
                      <Image
                        source={{ uri: item.video_cover }}
                        style={styles.videoCoverPlayer}
                        resizeMode='cover'
                      />
                      <View style={styles.videoPlayIcon}>
                        <Image
                          source={{
                            uri: 'https://static.soyoung.com/sy-pre/20250804-211154-1754313000624.png',
                          }}
                          style={styles.videoPlayIconImage}
                        />
                      </View>
                    </View>
                  )}
                </ATrack>
              )
            )}
          </View>
        </ScrollView>
      </View>
    );
  }
}

// 样式定义
const styles = StyleSheet.create({
  modalContent: {
    backgroundColor: 'white',
    height: '100%',
    width: '100%',
  },
  titleBar: {
    height: getRealSize(44), // 88rpx
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: getRealSize(15), // 30rpx
  },
  placholder: {
    width: getRealSize(16),
  },
  closeIcon: {
    width: getRealSize(16),
    height: getRealSize(16),
  },
  titleText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(16), // 32rpx
    color: '#333333',
    fontWeight: '500',
  },
  modalScrollView: {
    flex: 1,
  },
  modalImageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: getRealSize(15),
    marginHorizontal: getRealSize(-7.5),
  },
  modalImage: {
    width: getRealSize(105),
    height: getRealSize(105),
    margin: getRealSize(7),
  },
  videoPlayer: {
    width: '100%',
    height: '100%',
  },
  videoPlayIcon: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoCoverPlayer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  videoPlayIconImage: {
    width: getRealSize(16),
    height: getRealSize(16),
  },
});
