import { jumpReactNativePage } from '@/common/jumpPage';
import { getRealSize } from '@/common/utils';
import React, { useEffect, useMemo, useState } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';

interface Product {
  title: string;
  id?: string;
  [key: string]: any;
}

interface Consultant {
  name: string;
  id?: string;
  [key: string]: any;
}

interface Review {
  total_score?: string | number;
  [key: string]: any;
}

export interface UserInfo {
  customer_id: string;
  customer_id_str: string;
  visit_id: string;
  reserve_id: string;
  real_name: string;
  status_key: string;
  status_icon: string;
  cure_class: string;
  reserve_time?: string;
  visit_time?: string;
  duration?: string;
  check_out_time?: string;
  product_list: Product[];
  consultant: Consultant[];
  review: Review;
  remark?: string;
  [key: string]: any;
}

interface AppointmentUserProps {
  userInfo: UserInfo;
  tabActive?: number;
}

const AppointmentUser: React.FC<AppointmentUserProps> = ({
  userInfo,
  tabActive = 0,
}) => {
  const [imageWidth, setImageWidth] = useState(0);

  // 根据状态生成显示项
  const filteredItems = useMemo(() => {
    const statusKey = userInfo.status_key;

    const statusItems: Record<string, any[]> = {
      appointment: [
        { title: '预约时间', content: userInfo.reserve_time, type: 'text' },
        {
          title: '预约项目',
          content: userInfo.product_list.filter((a: any, i: any) => i < 4),
          type: 'list',
          length: userInfo.product_list.length,
        },
        {
          title: '所属咨询',
          content: userInfo.consultant.map((i: any) => i.name).join(', '),
          type: 'text',
        },
        { title: '备注', content: userInfo.remark, type: 'text' },
      ],
      cancel_appointment: [
        { title: '预约时间', content: userInfo.reserve_time, type: 'text' },
        {
          title: '预约项目',
          content: userInfo.product_list.filter((a: any, i: any) => i < 4),
          type: 'list',
          length: userInfo.product_list.length,
        },
        {
          title: '所属咨询',
          content: userInfo.consultant.map((i: any) => i.name).join(', '),
          type: 'text',
        },
        { title: '备注', content: userInfo.remark, type: 'text' },
      ],
      wait_check: [
        { title: '到店时间', content: userInfo.visit_time, type: 'text' },
        {
          title: '所属咨询',
          content: userInfo.consultant.map((i: any) => i.name).join(', '),
          type: 'text',
        },
        {
          title: '预约项目',
          content: userInfo.product_list.filter((a: any, i: any) => i < 4),
          type: 'list',
          length: userInfo.product_list.length,
        },
        { title: '到访备注', content: userInfo.remark, type: 'text' },
      ],
      wait_consultation: [
        { title: '等待时长', content: userInfo.duration, type: 'text' },
        {
          title: '预约项目',
          content: userInfo.product_list.filter((a: any, i: any) => i < 4),
          type: 'list',
          length: userInfo.product_list.length,
        },
        {
          title: '咨询师',
          content: userInfo.consultant.map((i: any) => i.name).join(', '),
          type: 'text',
        },
        { title: '到访备注', content: userInfo.remark, type: 'text' },
      ],
      consultation: [
        { title: '咨询时长', content: userInfo.duration, type: 'text' },
        {
          title: '预约项目',
          content: userInfo.product_list.filter((a: any, i: any) => i < 4),
          type: 'list',
          length: userInfo.product_list.length,
        },
        {
          title: '咨询师',
          content: userInfo.consultant.map((i: any) => i.name).join(', '),
          type: 'text',
        },
        { title: '到访备注', content: userInfo.remark, type: 'text' },
      ],
      wait_treatment: [
        { title: '等待时长', content: userInfo.duration, type: 'text' },
        {
          title: '治疗项目',
          content: userInfo.product_list.filter((a: any, i: any) => i < 4),
          type: 'list',
          length: userInfo.product_list.length,
        },
        { title: '备注', content: userInfo.remark, type: 'text' },
      ],
      treatment: [
        { title: '治疗时长', content: userInfo.duration, type: 'text' },
        {
          title: '治疗项目',
          content: userInfo.product_list.filter((a: any, i: any) => i < 4),
          type: 'list',
          length: userInfo.product_list.length,
        },
        { title: '到访备注', content: userInfo.remark, type: 'text' },
      ],
      care: [
        { title: '到店时间', content: userInfo.visit_time, type: 'text' },
        { title: '治疗项目', content: userInfo.product_list, type: 'list' },
        {
          title: '用户评价',
          content: userInfo.review.total_score
            ? userInfo.review.total_score + '分'
            : '暂无评价',
          type: 'text',
          color:
            userInfo.review.total_score !== '' &&
            userInfo.review.total_score &&
            +userInfo.review.total_score < 3
              ? 'red'
              : '#333',
        },
      ],
      no_treatment: [
        { title: '到店时间', content: userInfo.visit_time, type: 'text' },
        {
          title: '预约项目',
          content: userInfo.product_list.filter((a: any, i: any) => i < 4),
          type: 'list',
          length: userInfo.product_list.length,
        },
        {
          title: '所属咨询',
          content: userInfo.consultant.map((i: any) => i.name).join(', '),
          type: 'text',
        },
        { title: '到访备注', content: userInfo.remark, type: 'text' },
      ],
      check_out: [
        { title: '总时长', content: userInfo.check_out_time, type: 'text' },
        { title: '治疗项目', content: userInfo.product_list, type: 'list' },
        {
          title: '咨询师',
          content: userInfo.consultant.map((i: any) => i.name).join(', '),
          type: 'text',
        },
        {
          title: '用户评价',
          content: userInfo.review.total_score
            ? userInfo.review.total_score + '分'
            : '暂无评价',
          type: 'text',
          color:
            userInfo.review.total_score !== '' &&
            userInfo.review.total_score &&
            +userInfo.review.total_score < 3
              ? 'red'
              : '#333',
        },
      ],
    };

    // 如果是reserve_开头，并且不在statusItems中
    if (statusKey.startsWith('reserve_') && !statusItems[statusKey]) {
      statusItems[statusKey] = [
        { title: '预约时间', content: userInfo.reserve_time, type: 'text' },
        {
          title: '预约项目',
          content: userInfo.product_list.filter((a: any, i: any) => i < 4),
          type: 'list',
          length: userInfo.product_list.length,
        },
        {
          title: '所属咨询',
          content: userInfo.consultant.map((i: any) => i.name).join(', '),
          type: 'text',
        },
        { title: '备注', content: userInfo.remark, type: 'text' },
      ];
    }

    return statusItems[statusKey] || [];
  }, [userInfo]);

  const jumpLink = async () => {
    // 已预约、已取消tab下,点击卡片跳转到用户资料页
    // 已到院、未评价tab下,点击卡片跳转到服务明细页
    const { customer_id_str, visit_id } = userInfo;
    if (tabActive === 0 || tabActive === 2) {
      jumpReactNativePage(`client/detail?id=${customer_id_str}`);
    } else if (tabActive === 1 || tabActive === 3) {
      jumpReactNativePage(
        `record/detail?visit_id=${visit_id}&tenant_id=${userInfo.tenant_id}`
      );
    }
  };

  useEffect(() => {
    if (userInfo?.status_icon) {
      Image.getSize(userInfo.status_icon, (width, height) => {
        const ratio = width / height;
        setImageWidth(ratio * getRealSize(24));
      });
    }
  }, [userInfo?.status_icon]);
  return (
    <TouchableOpacity
      style={styles.appointmentUser}
      activeOpacity={1}
      onPress={jumpLink}
    >
      {imageWidth !== 0 ? (
        <Image
          style={[
            styles.statusFlag,
            {
              width: imageWidth,
              height: getRealSize(24),
            },
          ]}
          source={{ uri: userInfo?.status_icon }}
          resizeMode='contain'
        />
      ) : null}
      <View style={styles.appointmentUserTitle}>
        <Text numberOfLines={1} style={styles.titleName}>
          {userInfo.real_name}
        </Text>
        {userInfo.cure_class ? (
          <Text
            style={[
              styles.titleFlag,
              {
                backgroundColor:
                  userInfo.cure_class === '新'
                    ? 'rgba(46,134,242,0.10)'
                    : '#EBFBDC',
                color: userInfo.cure_class === '新' ? '#2E86F2' : '#00A077',
                borderWidth: 0.5,
                borderColor:
                  userInfo.cure_class === '新' ? '#2E86F2' : '#61B43E',
              },
            ]}
          >
            {userInfo.cure_class}
          </Text>
        ) : null}
      </View>
      <View style={styles.appointmentUserInfo}>
        {filteredItems.map((item, index) => (
          <View key={index} style={styles.infoItem}>
            <View style={styles.infoItemTitle}>
              <Text style={styles.infoItemTitleText}>{item.title}</Text>
            </View>
            <View style={styles.infoItemContent}>
              {item.type === 'text' ? (
                <Text
                  style={[
                    styles.infoItemText,
                    { color: item.color || '#333333' },
                  ]}
                >
                  {item.content}
                </Text>
              ) : null}
              {item.type === 'list' ? (
                <>
                  <View style={styles.infoItemList}>
                    {item.content
                      ? item.content.map(
                          (product: Product, productIndex: number) => (
                            <View key={productIndex} style={styles.infoItemP}>
                              <Text style={styles.infoItemPText}>
                                {product.title}
                              </Text>
                            </View>
                          )
                        )
                      : null}
                  </View>
                  {item.length > 4 ? (
                    <Text style={styles.projectInfo}>
                      共{item.length}个项目
                    </Text>
                  ) : null}
                </>
              ) : null}
            </View>
          </View>
        ))}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  appointmentUser: {
    backgroundColor: '#ffffff',
    paddingHorizontal: getRealSize(15),
    paddingTop: getRealSize(15),
    paddingBottom: getRealSize(10),
    position: 'relative',
  },
  statusFlag: {
    position: 'absolute',
    top: 0,
    right: 0,
  },
  appointmentUserTitle: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getRealSize(10),
  },
  titleName: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(14),
    color: '#333333',
    letterSpacing: 0,
    maxWidth: '70%',
    fontWeight: '500',
    marginRight: getRealSize(5),
    lineHeight: getRealSize(20),
  },
  titleFlag: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(10),
    color: '#00A077',
    fontWeight: '400',
    paddingHorizontal: getRealSize(4),
    lineHeight: getRealSize(18),
    overflow: 'hidden',
  },
  appointmentUserInfo: {
    // 信息列表容器
  },
  infoItem: {
    flexDirection: 'row',
    marginBottom: getRealSize(5),
  },
  infoItemTitle: {
    minWidth: getRealSize(52),
    marginRight: getRealSize(5),
  },
  infoItemTitleText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    lineHeight: getRealSize(18),
    color: '#777777',
    letterSpacing: 0,
    fontWeight: '400',
  },
  infoItemContent: {
    flex: 1,
  },
  infoItemText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#333333',
    letterSpacing: 0,
    lineHeight: getRealSize(18),
    fontWeight: '400',
  },
  infoItemList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  infoItemP: {
    marginBottom: getRealSize(2),
  },
  infoItemPText: {
    fontSize: getRealSize(14),
    color: '#333333',
    marginRight: getRealSize(8),
  },
  projectInfo: {
    fontSize: getRealSize(12),
    color: '#999999',
    marginTop: getRealSize(4),
  },
});

export default AppointmentUser;
