import React, {
  useState,
  useEffect,
  useCallback,
  useRef,
  useMemo,
} from 'react';
import {
  View,
  Text,
  Image,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import dayjs from 'dayjs';
import Api from '../../../common/api';
import Header from '../../../components/header';
import AppointmentUser, { UserInfo } from './components/AppointmentUser';
import DateSelect from '../../../components/DateSelect';
import LinearGradient from 'react-native-linear-gradient';
import { getRealSize } from '@/common/utils';
import {
  AppointmentSkeleton,
  AppointmentFilterSkeleton,
} from '../components/Skeleton';

export interface FilterItem {
  name: string;
  cnt: number;
  key: string;
  color?: string;
  background?: string;
  border?: string;
  sub_list?: FilterItem[];
}

interface PageState {
  // 日期和数据
  curDate: string;
  endDateState: string;
  userList: UserInfo[];
  filterData: FilterItem[];

  // 选中状态
  tabActive: number;
  sonTab: number;
  sonItem: FilterItem | null;

  // 加载状态
  refreshing: boolean;
  loading: boolean;
}

const INITIAL_STATE: PageState = {
  curDate: dayjs().format('YYYY-MM-DD'),
  endDateState: dayjs().add(6, 'day').format('YYYY-MM-DD'),
  userList: [],
  filterData: [],
  tabActive: 0,
  sonTab: -1,
  sonItem: null,
  refreshing: false,
  loading: false,
};

interface AppointmentPageProps {
  pageShow: boolean;
}

const AppointmentPage: React.FC<AppointmentPageProps> = ({ pageShow }) => {
  const [state, setState] = useState<PageState>(INITIAL_STATE);
  const tabsSonScrollRef = useRef<ScrollView>(null);

  // 统一的状态更新函数
  const updateState = useCallback((updates: Partial<PageState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // 重置列表和筛选状态的工具函数
  const resetListState = useCallback(
    (resetFilter = false) => {
      const updates: Partial<PageState> = {
        userList: [],
        sonTab: -1,
      };

      if (resetFilter) {
        updates.filterData = [];
        updates.sonItem = null;
        updates.tabActive = 0;
      }

      updateState(updates);
    },
    [updateState]
  );

  // 统一的错误处理
  const handleError = useCallback((error: any, message: string) => {
    console.error(message, error);
    // 这里可以添加 Toast 提示或其他错误处理逻辑
  }, []);

  // 获取用户列表（预约接待）
  const fetchUserList = useCallback(
    async (params?: { date?: string; tabKey?: string }) => {
      const requestDate = params?.date || state.curDate;
      const requestTabKey = params?.tabKey;

      try {
        updateState({ loading: true });
        const res = await Api.pagefetch({
          path: '/chain-wxapp/v1/visit/receptionListNew',
          isLoading: false,
          params: {
            date: requestDate,
            tab: requestTabKey,
          },
        });

        const { responseData } = res;

        if (responseData) {
          const filterTabData = responseData?.filter_tab || [];
          const userListData = responseData?.list || [];

          updateState({
            filterData: filterTabData,
            userList: userListData,
          });

          console.log('responseData?.list', userListData[0]);

          // 初始化时设置第一个筛选项为选中状态
          if (filterTabData.length > 0 && !state.sonItem) {
            updateState({ sonItem: filterTabData[0] });
          }
        }
      } catch (error) {
        handleError(error, '获取用户列表失败');
        resetListState();
      } finally {
        updateState({ loading: false });
      }
    },
    [state.curDate, state.sonItem, updateState, handleError, resetListState]
  );

  // 顶部筛选点击
  const handleTopClick = useCallback(
    async (index: number, item: FilterItem) => {
      updateState({
        tabActive: index,
        sonTab: -1,
        sonItem: item,
        userList: [],
      });
      await fetchUserList({ tabKey: item.key });
      // 动画滚动到最左边
      setTimeout(() => {
        tabsSonScrollRef.current?.scrollTo({ x: 0, animated: false });
      }, 0);
    },
    [updateState, fetchUserList]
  );

  // 子标签点击
  const handleSonTabClick = useCallback(
    (index: number, item: FilterItem) => {
      if (state.sonTab === index) {
        // 取消选中子标签，回到父级
        const parentItem = state.filterData[state.tabActive];
        updateState({
          sonTab: -1,
          sonItem: parentItem,
          userList: [],
        });
        fetchUserList({ tabKey: parentItem?.key });
      } else {
        // 选中子标签
        updateState({
          sonTab: index,
          sonItem: item,
          userList: [],
        });
        fetchUserList({ tabKey: item.key });
      }
    },
    [
      state.sonTab,
      state.filterData,
      state.tabActive,
      updateState,
      fetchUserList,
    ]
  );

  // 日历点击
  const handleCalendarClick = useCallback(
    async (date: string) => {
      updateState({
        curDate: date,
        tabActive: 0,
        sonTab: -1,
        filterData: [],
        userList: [],
        sonItem: null,
      });

      await fetchUserList({ date, tabKey: undefined });

      // 动画滚动到最左边
      setTimeout(() => {
        tabsSonScrollRef.current?.scrollTo({ x: 0, animated: false });
      }, 0);
    },
    [updateState, fetchUserList]
  );

  // 下拉刷新
  const onRefresh = useCallback(async () => {
    try {
      const sonItem = state.filterData[state.tabActive] || null;
      updateState({
        refreshing: true,
        loading: true,
        userList: [],
        sonItem,
        sonTab: -1,
      });
      // resetListState(true);
      await fetchUserList({ tabKey: sonItem?.key });
      // 动画滚动到最左边
      setTimeout(() => {
        tabsSonScrollRef.current?.scrollTo({ x: 0, animated: false });
      }, 0);
    } catch (error) {
      handleError(error, '刷新数据失败');
    } finally {
      updateState({ refreshing: false });
    }
  }, [
    updateState,
    resetListState,
    fetchUserList,
    state.sonItem?.key,
    handleError,
  ]);

  useEffect(() => {
    if (pageShow) {
      const sonItem = state.filterData[state.tabActive] || null;
      updateState({
        loading: true,
        userList: [],
        sonItem,
        sonTab: -1,
      });
      fetchUserList({ tabKey: sonItem?.key });
      setTimeout(() => {
        tabsSonScrollRef.current?.scrollTo({ x: 0, animated: false });
      }, 0);
    }
  }, [pageShow]);

  // 页面初始化数据加载
  // useEffect(() => {
  //   fetchUserList({});

  //   // 模拟页面显示埋点
  //   console.log('Track: employee_other_followup_appointment_page - pageShow');

  //   return () => {
  //     // 模拟页面隐藏埋点
  //     console.log('Track: employee_other_followup_appointment_page - pageHide');
  //   };
  // }, []); // 移除依赖，避免循环

  // 渲染筛选标签
  const renderFilterTabs = useMemo(() => {
    if (!state.filterData.length && state.loading) {
      return <AppointmentFilterSkeleton />;
    }
    return (
      <>
        <View style={styles.tabsContent}>
          {state.filterData.map((item, index) => (
            <TouchableOpacity
              activeOpacity={1}
              key={item.key || index}
              style={styles.tabItem}
              onPress={() => handleTopClick(index, item)}
            >
              <Text
                style={[
                  styles.tabItemText,
                  state.tabActive === index && styles.tabItemTextActive,
                ]}
              >
                {item.name} {item.cnt}
              </Text>
              <View
                style={[
                  styles.flag,
                  state.tabActive === index && styles.flagActive,
                ]}
              />
            </TouchableOpacity>
          ))}
        </View>

        {state.filterData[state.tabActive]?.sub_list &&
        state.filterData[state.tabActive].sub_list!.length > 0 ? (
          <ScrollView
            ref={tabsSonScrollRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.tabsSon}
          >
            {state.filterData[state.tabActive].sub_list!.map((item, index) => (
              <TouchableOpacity
                activeOpacity={1}
                key={item.key || index}
                style={[
                  styles.sonTag,
                  index === 0 && styles.sonTagFirst,
                  {
                    borderWidth: 1,
                    borderColor:
                      state.sonTab !== index
                        ? item.border || item.color
                        : 'white',
                    backgroundColor:
                      state.sonTab === index ? item.background : 'white',
                  },
                ]}
                onPress={() => handleSonTabClick(index, item)}
              >
                <Text
                  style={[
                    styles.sonTagText,
                    {
                      color: state.sonTab === index ? 'white' : item.color,
                    },
                  ]}
                >
                  {item.name} {item.cnt}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        ) : null}
      </>
    );
  }, [
    state.filterData,
    state.tabActive,
    state.sonTab,
    handleTopClick,
    handleSonTabClick,
  ]);

  // 渲染用户列表
  const renderUserList = useMemo(() => {
    if (state.userList && state.userList.length > 0) {
      return (
        <ScrollView
          style={styles.appointmentUserList}
          refreshControl={
            <RefreshControl
              refreshing={state.refreshing}
              onRefresh={onRefresh}
            />
          }
        >
          {state.userList.map((item, index) => (
            <View key={index} style={styles.appointmentUserItem}>
              <AppointmentUser userInfo={item} tabActive={state.tabActive} />
            </View>
          ))}
        </ScrollView>
      );
    }

    if (state.loading) {
      return (
        <View style={styles.appointmentUserList}>
          {Array.from({ length: 4 }).map((_, index) => (
            <View key={index} style={styles.appointmentUserItem}>
              <AppointmentSkeleton />
            </View>
          ))}
        </View>
      );
    }

    return (
      <ScrollView
        style={styles.appointmentUserList}
        refreshControl={
          <RefreshControl refreshing={state.refreshing} onRefresh={onRefresh} />
        }
      >
        <View style={styles.noUser}>
          <Image
            style={styles.noDataImg}
            source={{
              uri: 'https://static.soyoung.com/sy-design/aqnomvpf3ki11753429315696.png',
            }}
            resizeMode='contain'
          />
          <Text style={styles.noDataText}>暂无相关数据</Text>
        </View>
      </ScrollView>
    );
  }, [
    state.userList,
    state.loading,
    state.refreshing,
    state.tabActive,
    onRefresh,
  ]);

  return (
    <SafeAreaView style={styles.appointmentPage} edges={['bottom']}>
      <Header title='预约接待' bgColor='#fff' />
      <View style={styles.scrollView}>
        <View style={styles.appointmentBox}>
          <View style={styles.appointmentDate}>
            <DateSelect
              value={state.curDate}
              endDate={state.endDateState}
              onChange={handleCalendarClick}
            />
          </View>
          <View style={styles.gradient} />
          {/* 筛选标签 */}
          <View style={styles.appointmentUserBox}>
            <View style={styles.tabContainer}>
              {renderFilterTabs}
              <LinearGradient
                colors={['#fff', '#f7f9f9']}
                style={styles.gradient}
              />
            </View>
          </View>
        </View>
        {renderUserList}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  appointmentPage: {
    flex: 1,
    backgroundColor: '#f7f9f9',
  },
  scrollView: {
    flex: 1,
  },
  appointmentBox: {
    backgroundColor: '#fff',
  },
  appointmentDate: {
    paddingTop: getRealSize(20),
    paddingLeft: getRealSize(15),
  },
  gradient: {
    height: getRealSize(15),
    backgroundColor: '#fff', // 对应Vue中的线性渐变效果
  },
  appointmentUserBox: {},
  tabContainer: {
    backgroundColor: '#fff',
    paddingBottom: 0,
    zIndex: 20,
  },
  tabsContent: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: getRealSize(15),
  },
  tabItem: {
    alignItems: 'center',
    marginRight: getRealSize(16),
  },
  tabItemText: {
    fontSize: getRealSize(14),
    lineHeight: getRealSize(22),
    color: '#646464',
    fontWeight: '500',
  },
  tabItemTextActive: {
    color: '#030303',
    fontFamily: 'PingFangSC-Medium',
    lineHeight: getRealSize(22),
    fontWeight: '500',
  },
  flag: {
    backgroundColor: '#fff',
    width: getRealSize(20),
    height: getRealSize(2), // 对应Vue中的3px
    borderRadius: getRealSize(1.5), // 对应Vue中的1.5px
    marginTop: getRealSize(5),
  },
  flagActive: {
    backgroundColor: '#030303',
  },
  tabsSon: {
    // paddingBottom: getRealSize(20),
    marginTop: getRealSize(15),
  },
  sonTag: {
    height: getRealSize(28),
    paddingHorizontal: getRealSize(10),
    marginLeft: getRealSize(10),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  sonTagFirst: {
    marginLeft: getRealSize(15),
  },
  sonTagText: {
    fontSize: getRealSize(13),
    lineHeight: getRealSize(26),
  },
  appointmentUserList: {
    flex: 1,
    paddingHorizontal: getRealSize(15),
    backgroundColor: '#F8F8F8',
  },
  appointmentUserItem: {
    marginBottom: getRealSize(10),
  },
  noUser: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: getRealSize(150), // 对应Vue中的340rpx
    paddingBottom: getRealSize(45),
  },
  noDataImg: {
    width: getRealSize(35), // 对应Vue中的150px
    height: getRealSize(35), // 对应Vue中的120px
    marginBottom: getRealSize(20),
  },
  noDataText: {
    fontSize: getRealSize(14),
    color: '#030303',
    fontWeight: '500',
  },
});

export default AppointmentPage;
