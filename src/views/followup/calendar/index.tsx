import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
  Platform,
  Alert,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import dayjs from 'dayjs';
import { SafeAreaView } from 'react-native-safe-area-context';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import isBetween from 'dayjs/plugin/isBetween';
import 'dayjs/locale/zh-cn';

import api, { FetchModule } from '../../../common/api';
import { getRealSize } from '../../../common/utils';
import Header from '../../../components/header';

import { back } from '@soyoung/react-native-base';
import { Bridge } from '@/common/bridge';
// 扩展 dayjs 插件
dayjs.extend(isSameOrBefore);
dayjs.extend(isBetween);
dayjs.locale('zh-cn');

const { width: screenWidth } = Dimensions.get('window');

interface DateItem {
  date: string;
  showDate: string;
  day: string;
  cashback?: string;
  isPrevMonth?: boolean; // 标记是否为上个月的日期
}

interface MonthItem {
  title: string;
  list: DateItem[];
  planList: string[];
  hasExported?: boolean;
  startDate?: string;
  endDate?: string;
}

interface CalendarProps {
  route?: any;
}

const Calendar: React.FC<CalendarProps> = ({ route }) => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const scrollViewRef = useRef<ScrollView>(null);
  const hasInitialScrolled = useRef<boolean>(false);

  // 状态定义
  const [type, setType] = useState<string>('0');
  const [curDate, setCurDate] = useState<string>('');
  const [calendarList, setCalendarList] = useState<MonthItem[]>([]);
  const [calendarType, setCalendarType] = useState<string>('');

  // 预约相关参数
  const [isReserveCalendar, setIsReserveCalendar] = useState(false);
  const [reserveTenantId, setReserveTenantId] = useState('');
  const [reserveOrderId, setReserveOrderId] = useState('');
  const [reserveTopOrderId, setReserveTopOrderId] = useState('');
  const [reserveProductId, setReserveProductId] = useState('');
  const [reserveOrderType, setReserveOrderType] = useState('');
  const [
    reserveCalendarNotAvailableDates,
    setReserveCalendarNotAvailableDates,
  ] = useState<string[]>([]);
  const [reserveCalendarNotOpen, setReserveCalendarNotOpen] = useState<
    string[]
  >([]);

  const headList = ['一', '二', '三', '四', '五', '六', '日'];

  const urlMap: { [key: string]: string } = {
    '0': '/chain-wxapp/v1/followup-plan/getSelfFollowupPlanNumByDate',
    '1': '/chain-wxapp/v1/followup-plan/getTenantFollowupPlanNumByDate',
    '2': '/chain-wxapp/v1/reserve/calendar', // 预约日历接口
  };

  // 判断预约日期是否禁用
  const isReserveDateDisabled = useCallback(
    (date: string) => {
      if (!isReserveCalendar) return false;

      // 在预约日历模式下，禁用过去的日期
      const today = dayjs().format('YYYY-MM-DD');
      return dayjs(date).isBefore(today);
    },
    [isReserveCalendar]
  );

  // 生成日期数组
  const generateDateArray = useCallback((): MonthItem[] => {
    const today = dayjs();
    let startDate;
    let endDate;

    // 根据不同的日历类型设置不同的起止日期
    if (isReserveCalendar) {
      // 预约日历: 当天到100天后
      startDate = today.startOf('day');
      endDate = today.add(100, 'day').endOf('day');
    } else {
      // 随访日历: 前6个月到后6个月
      startDate = today.subtract(6, 'month').startOf('month');
      endDate = today.add(6, 'month').endOf('month');
    }

    const dateArray: MonthItem[] = [];
    let currentMonth: MonthItem | null = null;

    for (
      let date = startDate;
      date.isSameOrBefore(endDate);
      date = date.add(1, 'day')
    ) {
      const formattedDate = date.format('YYYY-MM-DD');
      const monthTitle = date.format('YYYY年MM月');
      if (!currentMonth || currentMonth.title !== monthTitle) {
        currentMonth = {
          title: monthTitle,
          list: [],
          planList: [],
          hasExported: false,
          startDate: formattedDate,
          endDate: formattedDate,
        };
        dateArray.push(currentMonth);
      } else {
        currentMonth.endDate = formattedDate;
      }
      currentMonth.list.push({
        date: formattedDate,
        showDate:
          formattedDate === today.format('YYYY-MM-DD')
            ? '今天'
            : date.format('D'),
        day: date.format('dd'),
      });
    }

    return dateArray;
  }, [isReserveCalendar]);

  // 获取计划数据
  const getPlanData = useCallback(
    async (startDate: string, endDate: string, title: string) => {
      try {
        // 构建请求参数
        let requestData: any = {
          start_time: startDate,
          end_time: endDate,
        };

        let reqEndDate = endDate;

        // 如果是预约日历 & 是月的最后一天就加一天
        if (isReserveCalendar) {
          const _endDate = dayjs(endDate);
          if (_endDate.date() === _endDate.daysInMonth()) {
            reqEndDate = _endDate.add(1, 'day').format('YYYY-MM-DD');
          }
        }

        // 为预约日历添加额外参数
        if (isReserveCalendar) {
          requestData = {
            date_start: startDate,
            date_end: reqEndDate,
            tenant_id: reserveTenantId,
            order_id: reserveOrderId,
            top_order_id: reserveTopOrderId,
            product_id: reserveProductId,
            order_type: reserveOrderType,
          };
        }

        const res = await api.pagefetch({
          path: urlMap[type],
          params: requestData,
          method: FetchModule.Method.POST,
        });

        if (res.errorCode === 0) {
          setCalendarList(prev => {
            const newList = [...prev];
            const curMonth = newList.find(
              (item: MonthItem) => item.title === title
            );

            if (curMonth) {
              // 根据是否为预约日历决定 planList
              if (isReserveCalendar) {
                // 仅保留可预约的日期
                const newNotAvailableDates = [
                  ...reserveCalendarNotAvailableDates,
                ];
                const newNotOpenDates = [...reserveCalendarNotOpen];

                curMonth.planList = (res.responseData || [])
                  .filter((item: any) => {
                    const remain_num = Number(item.remain_num);
                    const total_num = Number(item.total_num);
                    // 尚未开始或无可预约则不显示
                    if (
                      isNaN(remain_num) ||
                      isNaN(total_num) ||
                      (remain_num === 0 && total_num === 0)
                    ) {
                      newNotAvailableDates.push(item.date);
                      newNotOpenDates.push(item.date);
                      return false;
                    }
                    // 剩余为0且总数>0或剩余>总数均视为不可预约
                    if (
                      (remain_num === 0 && total_num > 0) ||
                      (remain_num > total_num && total_num !== 0)
                    ) {
                      newNotAvailableDates.push(item.date);
                      return false;
                    }
                    return true;
                  })
                  .map((item: any) => item.date) as string[];

                setReserveCalendarNotAvailableDates(newNotAvailableDates);
                setReserveCalendarNotOpen(newNotOpenDates);
              } else {
                // 继承原有简洁逻辑
                curMonth.planList = (res.responseData || []).map(
                  (item: any) => item.date
                ) as string[];
              }

              // 将cashback字段更新到对应的日期项中
              (res.responseData || []).forEach((item: any) => {
                const dateItem = curMonth.list.find(i => i.date === item.date);
                if (dateItem && item.cashback) {
                  dateItem.cashback = item.cashback;
                }
              });
            }

            return newList;
          });
        } else {
          let errorMsg = '';
          if (isReserveCalendar) {
            errorMsg = `获取日历信息失败 ${res.errorMsg || '获取日历异常'}`;
          } else {
            errorMsg = res.errorMsg || '获取日历异常';
          }

          Bridge.showToast(errorMsg);
        }
      } catch (error) {
        console.error('获取计划数据失败:', error);
        Bridge.showToast('获取日历信息失败');
      }
    },
    [
      type,
      isReserveCalendar,
      reserveTenantId,
      reserveOrderId,
      reserveTopOrderId,
      reserveProductId,
      reserveOrderType,
      reserveCalendarNotAvailableDates,
      reserveCalendarNotOpen,
    ]
  );

  // 处理日期点击
  const handleClickDay = useCallback(
    async (day: string) => {
      if (isReserveCalendar) {
        // 检查是否为预约日历的不可用日期
        if (reserveCalendarNotAvailableDates.includes(day)) {
          // 区分未开放和已满两种状态
          if (reserveCalendarNotOpen.includes(day)) {
            Bridge.showToast('当前日期暂未开放预约');
          } else {
            Bridge.showToast('当前日期预约已满');
          }
          return; // 不执行后续操作
        }
        await AsyncStorage.setItem('reserveCalendarDate', day);
        await AsyncStorage.setItem(
          'reserveCalendarNotAvailableDates',
          JSON.stringify(reserveCalendarNotAvailableDates)
        );
      } else {
        if (calendarType === 'returnVisit') {
          await AsyncStorage.setItem('followupCalendarDate', day);
        } else if (calendarType === 'task') {
          await AsyncStorage.setItem('followupCalendarTaskDate', day);
        }
      }

      // 返回上一页
      back();
    },
    [
      isReserveCalendar,
      reserveCalendarNotAvailableDates,
      reserveCalendarNotOpen,
      calendarType,
      navigation,
    ]
  );

  // 初始化
  useEffect(() => {
    const params = route?.params?.params || {};
    console.log('params', params);
    // 检查是否为预约日历入口
    if (params?.calendar_type === 'reserve') {
      console.log('预约日历-params', params);
      setCalendarType('reserve');
      setType('2'); // 设置为预约日历类型
      setIsReserveCalendar(true);

      // 保存接收到的预约参数
      setReserveTenantId(params?.tenant_id || '');
      setReserveOrderId(params?.order_id || '');
      setReserveTopOrderId(params?.top_order_id || '');
      setReserveProductId(params?.product_id || '');
      setReserveOrderType(params?.order_type || '');

      // 设置页面标题
      navigation.setOptions({
        title: '预约日历',
      });
    } else if (params?.calendar_type === 'returnVisit') {
      // 原有逻辑：随访日历
      setCalendarType('returnVisit');
      setType(params?.type || '0');
      navigation.setOptions({
        title: '回访日历',
      });
    } else {
      setCalendarType('task');
      setType('0');
      navigation.setOptions({
        title: '任务日历',
      });
    }

    setCurDate(params?.curDate || dayjs().format('YYYY-MM-DD'));
    const dateArray = generateDateArray();
    setCalendarList(dateArray);
  }, [route?.params, navigation, generateDateArray]);

  // 滚动到指定日期的函数
  const scrollToCurrentDate = useCallback(() => {
    if (!curDate || calendarList.length === 0) return;

    // 查找curDate所在的月份索引
    const targetMonthIndex = calendarList.findIndex(month =>
      month.list.some(dateItem => dateItem.date === curDate)
    );

    if (targetMonthIndex !== -1) {
      // 精确计算目标位置
      let totalHeight = getRealSize(30); // ScrollView内部开始的空白高度

      // 累加目标月份之前所有月份的高度
      for (let i = 0; i < targetMonthIndex; i++) {
        const month = calendarList[i];
        totalHeight += calculateMonthHeight(month);
      }

      // // 加上目标月份的一部分高度，让目标日期更居中显示
      // const targetMonth = calendarList[targetMonthIndex];
      // const targetDateIndex = targetMonth.list.findIndex(
      //   dateItem => dateItem.date === curDate
      // );

      // if (targetDateIndex !== -1) {
      //   // 添加月份标题高度
      //   totalHeight += getRealSize(21) + getRealSize(5); // title lineHeight + marginBottom

      //   // 计算目标日期在第几行
      //   const firstDateDayIndex = headList.findIndex(
      //     h => h === targetMonth.list[0].day
      //   );
      //   const targetRowIndex = Math.floor(
      //     (targetDateIndex + firstDateDayIndex) / 7
      //   );

      //   // 添加目标行之前的行高
      //   totalHeight += targetRowIndex * (getRealSize(49) + getRealSize(6)); // row height + margin
      // }

      // 执行平滑滚动动画
      scrollViewRef.current?.scrollTo({
        y: totalHeight,
        animated: false,
      });
    }
  }, [curDate, calendarList]);

  // 计算单个月份的准确高度
  const calculateMonthHeight = useCallback((month: MonthItem) => {
    // 月份标题高度
    const titleHeight = getRealSize(21) + getRealSize(5); // lineHeight + marginBottom

    // 计算网格行数
    const firstDateDayIndex = headList.findIndex(h => h === month.list[0].day);
    const totalDays = month.list.length;
    const totalCells = totalDays + firstDateDayIndex; // 包括前面的空白位置
    const rowCount = Math.ceil(totalCells / 7);

    // 网格高度 = 行数 * (行高 + 行间距) - 最后一行的间距
    const gridHeight =
      rowCount * getRealSize(49) + (rowCount - 1) * getRealSize(6);

    // 月份底部间距
    const monthMarginBottom = getRealSize(15);

    return titleHeight + gridHeight + monthMarginBottom;
  }, []);

  // 加载初始数据
  const loadInitialData = useCallback(() => {
    // 找到当前日期所在的月份索引
    const currentMonthIndex = calendarList.findIndex(month =>
      month.list.some(dateItem => dateItem.date === curDate)
    );

    if (currentMonthIndex !== -1) {
      // 加载当前月份及前后2个月的数据（共5个月）
      const startIndex = Math.max(0, currentMonthIndex - 2);
      const endIndex = Math.min(calendarList.length - 1, currentMonthIndex + 2);

      for (let i = startIndex; i <= endIndex; i++) {
        const month = calendarList[i];
        if (month.startDate && month.endDate && !month.hasExported) {
          month.hasExported = true;
          getPlanData(month.startDate, month.endDate, month.title);
        }
      }
    } else {
      // 如果没找到当前日期，则加载前几个月的数据
      const loadCount = Math.min(3, calendarList.length);
      for (let i = 0; i < loadCount; i++) {
        const month = calendarList[i];
        if (month.startDate && month.endDate && !month.hasExported) {
          month.hasExported = true;
          getPlanData(month.startDate, month.endDate, month.title);
        }
      }
    }
  }, [calendarList, curDate, getPlanData]);

  // 初始数据加载
  useEffect(() => {
    if (calendarList.length > 0 && curDate) {
      loadInitialData();
    }
  }, [calendarList.length, curDate, loadInitialData]);

  // 初始滚动（只执行一次）
  useEffect(() => {
    if (calendarList.length > 0 && curDate && !hasInitialScrolled.current) {
      // 使用setTimeout确保在组件完全渲染后执行滚动
      const scrollTimer = setTimeout(() => {
        scrollToCurrentDate();
        hasInitialScrolled.current = true; // 标记已完成初始滚动
      }, 0); // 延迟300ms确保渲染完成

      return () => clearTimeout(scrollTimer);
    }
  }, [calendarList.length, curDate, scrollToCurrentDate]);

  // 渲染日历头部
  const renderCalendarHead = () => (
    <View style={styles.calendarHead}>
      {headList.map((item, index) => (
        <Text
          key={index}
          style={[
            styles.calendarHeadItem,
            (index === 5 || index === 6) && styles.weekend,
          ]}
        >
          {item}
        </Text>
      ))}
    </View>
  );

  // 渲染日期单元格
  const renderDayItem = (
    dateItem: DateItem,
    monthItem: MonthItem,
    index: number
  ) => {
    const isActive = dateItem.date === curDate;
    const isPrevMonth = dateItem.isPrevMonth; // 是否为上个月的日期
    const isDisabled =
      isPrevMonth || // 上个月的日期始终禁用
      isReserveDateDisabled(dateItem.date) ||
      (isReserveCalendar && !monthItem.planList.includes(dateItem.date));
    const isDatePickNoReserve =
      isReserveCalendar && !monthItem.planList.includes(dateItem.date);
    const isDatePickReserve =
      isReserveCalendar && monthItem.planList.includes(dateItem.date);
    const hasReturnVisitPlan =
      !isReserveCalendar &&
      calendarType === 'returnVisit' &&
      monthItem.planList.includes(dateItem.date);

    return (
      <TouchableOpacity
        activeOpacity={1}
        key={`${monthItem.title}-${dateItem.date}`}
        style={[
          styles.calendarBodyDay,
          isActive && styles.active,
          isDisabled && styles.disabled,
          isPrevMonth && styles.prevMonthDay,
        ]}
        onPress={() => handleClickDay(dateItem.date)}
        disabled={isDisabled}
      >
        <Text
          style={[
            styles.calendarBodyDayText,
            isActive && styles.activeText,
            isDisabled && styles.disabledText,
            isPrevMonth && styles.prevMonthText,
          ]}
        >
          {dateItem.showDate}
        </Text>

        {/* 预约返现金额 */}
        {isReserveCalendar &&
        monthItem.planList.includes(dateItem.date) &&
        dateItem.cashback &&
        dateItem.cashback !== '' ? (
          <Text
            style={[
              styles.calendarBodyDayCashback,
              isActive && styles.activeCashback,
            ]}
          >
            {dateItem.cashback}
          </Text>
        ) : null}

        {/* 回访计划标记 */}
        {hasReturnVisitPlan ? (
          <View
            style={[styles.calendarBodyDayPlan, isActive && styles.activePlan]}
          />
        ) : null}
      </TouchableOpacity>
    );
  };

  // 渲染月份内容
  const renderMonthItem = (item: MonthItem, index: number) => {
    // 创建7列的网格布局
    const rows: DateItem[][] = [];
    let currentRow: DateItem[] = [];

    item.list.forEach((dateItem, idx) => {
      if (idx === 0) {
        // 第一个日期需要根据星期几确定位置
        const dayIndex = headList.findIndex(h => h === dateItem.day);
        const firstDate = dayjs(dateItem.date);

        // 填充月初空白位置 - 显示上个月的日期
        for (let i = 0; i < dayIndex; i++) {
          const prevDate = firstDate.subtract(dayIndex - i, 'day');
          currentRow.push({
            date: prevDate.format('YYYY-MM-DD'),
            showDate: prevDate.format('D'),
            day: prevDate.format('dd'),
            isPrevMonth: true, // 标记为上个月的日期
          });
        }
        currentRow.push(dateItem);
      } else {
        currentRow.push(dateItem);
      }

      // 每7个日期一行
      if (currentRow.length === 7) {
        rows.push([...currentRow]);
        currentRow = [];
      }
    });

    // 处理最后一行
    if (currentRow.length > 0) {
      // 填充剩余空位
      while (currentRow.length < 7) {
        currentRow.push({} as DateItem);
      }
      rows.push(currentRow);
    }

    return (
      <View key={item.title} style={styles.calendarBodyMonth}>
        <Text style={styles.calendarBodyMonthTitle}>{item.title}</Text>
        <View
          style={[
            styles.calendarBodyMonthGrid,
            isReserveCalendar && styles.isReserve,
          ]}
        >
          {rows.map((row, rowIndex) => (
            <View key={rowIndex} style={styles.gridRow}>
              {row.map((dateItem, colIndex) => {
                if (!dateItem.date) {
                  // 月末空白位置
                  return (
                    <View
                      key={colIndex}
                      style={{
                        ...styles.calendarBodyDay,
                        borderColor: 'transparent',
                      }}
                    />
                  );
                }
                return renderDayItem(dateItem, item, colIndex);
              })}
            </View>
          ))}
        </View>
      </View>
    );
  };

  // 滚动事件处理 - 模拟 IntersectionObserver
  const handleScroll = useCallback(
    (event: any) => {
      const { contentOffset, layoutMeasurement } = event.nativeEvent;
      const viewHeight = layoutMeasurement.height;
      const scrollY = contentOffset.y;

      // 简单的懒加载逻辑：当月份区域进入可视范围时加载数据
      calendarList.forEach((month, index) => {
        const estimatedTopPosition = index * getRealSize(400); // 估算的月份高度
        const isInViewport =
          scrollY + viewHeight > estimatedTopPosition - getRealSize(800);

        if (
          isInViewport &&
          month.startDate &&
          month.endDate &&
          !month.hasExported
        ) {
          month.hasExported = true;
          getPlanData(month.startDate, month.endDate, month.title);
        }
      });
    },
    [calendarList, getPlanData]
  );

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Header
        title={
          calendarType === 'returnVisit'
            ? '回访日历'
            : calendarType === 'task'
              ? '任务日历'
              : '预约日历'
        }
      />
      {renderCalendarHead()}
      <ScrollView
        ref={scrollViewRef}
        style={styles.calendarBody}
        showsVerticalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={200}
      >
        <View style={{ height: getRealSize(15) }} />
        {calendarList.map((item, index) => renderMonthItem(item, index))}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  calendarHead: {
    height: getRealSize(39),
    backgroundColor: '#f8f8f8',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getRealSize(15),
  },
  calendarHeadItem: {
    flex: 1,
    textAlign: 'center',
    fontSize: getRealSize(13),
    color: '#555555',
    fontWeight: '400',
  },
  weekend: {
    color: '#f85d2d',
  },
  calendarBody: {
    flex: 1,
    paddingHorizontal: getRealSize(15),
    paddingBottom: getRealSize(34),
  },
  calendarBodyMonth: {
    marginBottom: getRealSize(15),
  },
  calendarBodyMonthTitle: {
    fontSize: getRealSize(15),
    lineHeight: getRealSize(21),
    color: '#222222',
    textAlign: 'center',
    fontWeight: '500',
    marginBottom: getRealSize(5),
  },
  calendarBodyMonthGrid: {
    // 使用 marginBottom 代替 gap
  },
  isReserve: {
    // 预约日历的特殊样式
  },
  gridRow: {
    flexDirection: 'row',
    marginBottom: getRealSize(6),
    paddingHorizontal: getRealSize(3),
  },

  calendarBodyDay: {
    flex: 1,
    height: getRealSize(49),
    backgroundColor: '#ffffff',
    borderWidth: getRealSize(1),
    borderColor: '#f2f2f2',
    // justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    marginHorizontal: getRealSize(3),
    paddingTop: getRealSize(10),
    boxSizing: 'border-box',
  },
  calendarBodyDayText: {
    fontSize: getRealSize(14),
    lineHeight: getRealSize(16),
    color: '#030303',
    fontWeight: '400',
  },
  active: {
    backgroundColor: '#373A36',
    borderColor: '#373A36',
  },
  activeText: {
    color: '#ffffff',
  },
  disabled: {
    backgroundColor: '#f5f5f5',
    borderColor: '#f5f5f5',
  },
  disabledText: {
    color: '#999999',
  },
  prevMonthText: {
    color: '#cccccc',
  },
  prevMonthDay: {
    backgroundColor: '#fff',
    borderColor: 'transparent',
  },
  calendarBodyDayPlan: {
    width: getRealSize(5),
    height: getRealSize(5),
    borderRadius: getRealSize(2.5),
    backgroundColor: '#A9EA6A',
    position: 'absolute',
    bottom: getRealSize(10),
  },
  activePlan: {
    backgroundColor: '#ffffff',
  },
  calendarBodyDayCashback: {
    position: 'absolute',
    top: getRealSize(27),
    left: 0,
    right: 0,
    fontSize: getRealSize(9),
    lineHeight: getRealSize(10),
    color: '#ff4040',
    textAlign: 'center',
    minHeight: getRealSize(20),
  },
  activeCashback: {
    color: '#ffffff',
  },
});

export default Calendar;
