import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Alert,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';

import Api from '../../../common/api';
import ButtonSelect, { ButtonSelectOption } from '../components/ButtonSelect';
import CommonSelect, {
  CommonSelectOption,
  ActiveItem,
} from '../../../components/CommonSelect';
import Header from '../../../components/header';
import { Bridge } from '../../../common/bridge';
import { back } from '@soyoung/react-native-base';
import { getRealSize } from '@/common/utils';
interface FilterItem {
  option_type: string;
  title: string;
  list?: (ButtonSelectOption | CommonSelectOption)[];
}

interface RouteParams {
  type?: string;
}

type FilterRouteProps = RouteProp<{ Filter: RouteParams }, 'Filter'>;

const FilterPage: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute<FilterRouteProps>();

  const [filterList, setFilterList] = useState<FilterItem[]>([]);
  const [filterData, setFilterData] = useState<Record<string, any>>({});
  const [footerHeight, setFooterHeight] = useState(0);
  const [currentTab, setCurrentTab] = useState<number>(0);
  const [followerList, setFollowerList] = useState<CommonSelectOption[]>([]);
  const footerRef = useRef<View>(null);

  // 页面显示时从存储中读取筛选数据
  useEffect(() => {
    const tabType = route.params?.params?.type
      ? parseInt(route.params.params.type.toString(), 10)
      : 0;
    setCurrentTab(tabType);
    loadFilterData();
    getFollowerList(tabType);

    // 计算底部高度
    setTimeout(() => {
      calcFooterHeight();
    }, 300);
  }, []);

  // 加载筛选数据
  const loadFilterData = async () => {
    try {
      const storedData = await AsyncStorage.getItem('followupFilterData');
      if (storedData) {
        setFilterData(JSON.parse(storedData));
      }
    } catch (error) {
      console.error('加载筛选数据失败:', error);
    }
  };

  // 判断数据是否有效
  const isValidData = (data: any): boolean => {
    if (Array.isArray(data)) {
      return data.length > 0;
    }
    if (typeof data === 'object' && data !== null) {
      return Boolean(data.name);
    }
    return Boolean(data || data === 0);
  };

  // 计算筛选数量
  const filterNumber = Object.keys(filterData).filter(item =>
    isValidData(filterData[item])
  ).length;

  // 获取跟进人员列表
  const getFollowerList = async (tabType: number) => {
    try {
      const res = await Api.pagefetch({
        path: '/chain-wxapp/v1/employee/tenantEmployeesById',
        params: {},
      });
      if (res.errorCode === 0) {
        const { responseData } = res;
        const followers = (responseData || []).map((item: any) => ({
          label: item.name,
          value: item.id,
        }));
        setFollowerList(followers as CommonSelectOption[]);
        getData(tabType);
      } else {
        console.error('获取跟进人员列表失败:', res.errorMsg);
        Bridge.showToast(res.errorMsg);
      }
    } catch (error) {
      console.error('获取跟进人员列表失败:', error);
      Alert.alert('错误', '获取跟进人员列表失败');
    }
  };

  // 获取筛选选项数据
  const getData = async (tabType: number) => {
    try {
      const res = await Api.pagefetch({
        path: '/chain-wxapp/v1/followup-plan/optionPlan',
        params: {},
      });
      if (res.errorCode === 0) {
        const { responseData } = res;
        let filterList = (responseData || []) as FilterItem[];
        if (tabType === 0) {
          filterList = filterList.filter(
            item => item.option_type !== 'followup_user_id'
          );
        }
        setFilterList(filterList);
      } else {
        console.error('获取筛选数据失败:', res.errorMsg);
        Bridge.showToast(res.errorMsg);
      }
    } catch (error) {
      console.error('获取筛选数据失败:', error);
      Bridge.showToast('获取筛选数据失败');
    }
  };

  // 计算底部高度
  const calcFooterHeight = () => {
    if (footerRef.current) {
      footerRef.current.measure((x, y, width, height) => {
        setFooterHeight(height);
      });
    }
  };

  // 重置筛选
  const handleReset = () => {
    setFilterData({});
  };

  // 确认筛选
  const handleConfirm = async () => {
    try {
      // 保存筛选数据到本地存储
      await AsyncStorage.setItem(
        'followupFilterData',
        JSON.stringify(filterData)
      );
      await AsyncStorage.setItem(
        'followupFilterNumber',
        filterNumber.toString()
      );

      // 返回上一页
      back();
    } catch (error) {
      console.error('保存筛选数据失败:', error);
      Alert.alert('错误', '保存筛选数据失败');
    }
  };

  // 更新筛选数据
  const updateFilterData = (optionType: string, value: any) => {
    setFilterData(prev => ({
      ...prev,
      [optionType]: value,
    }));
  };

  // 获取CommonSelect的活跃值
  const getCommonSelectActive = (optionType: string): ActiveItem => {
    const currentValue = filterData[optionType];
    if (!currentValue || typeof currentValue !== 'object') {
      return { id: '', name: '' };
    }
    return currentValue;
  };

  // 渲染筛选项
  const renderFilterItem = (item: FilterItem, index: number) => {
    const { option_type, title, list = [] } = item;

    // 如果是tenant_id类型，不显示标题
    const showTitle = option_type !== 'tenant_id';

    if (
      ['followup_result', 'satisfied_yn', 'followup_way'].includes(option_type)
    ) {
      // 使用ButtonSelect
      return (
        <View key={index} style={styles.filterMainItem}>
          {showTitle ? (
            <Text style={styles.filterMainItemTitle}>{title}</Text>
          ) : null}
          <View style={styles.filterMainItemContent}>
            <ButtonSelect
              active={filterData[option_type] || ''}
              options={list as ButtonSelectOption[]}
              multiple={false}
              onChange={value => updateFilterData(option_type, value)}
            />
          </View>
        </View>
      );
    } else if (['followup_type', 'followup_user_id'].includes(option_type)) {
      // 使用CommonSelect
      const options =
        option_type === 'followup_user_id'
          ? followerList
          : (list as CommonSelectOption[]);

      return (
        <View key={option_type} style={styles.filterMainItem}>
          {showTitle ? (
            <Text style={styles.filterMainItemTitle}>{title}</Text>
          ) : null}
          <View style={styles.filterMainItemContent}>
            <CommonSelect
              active={getCommonSelectActive(option_type)}
              options={options}
              title={title}
              multiple={false}
              onChange={value => updateFilterData(option_type, value)}
            />
          </View>
        </View>
      );
    }

    return null;
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Header title='回访筛选' bgColor='#fff' />
      <View style={styles.filterWrapper}>
        <View style={styles.filter}>
          <View
            style={[
              styles.filterBody,
              { height: Dimensions.get('window').height - footerHeight - 100 },
            ]}
          >
            <ScrollView
              style={styles.filterMain}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.filterMainContent}
            >
              {filterList.map(renderFilterItem)}
            </ScrollView>
          </View>

          <View
            ref={footerRef}
            style={styles.filterFooter}
            onLayout={() => calcFooterHeight()}
          >
            <View style={styles.filterButton}>
              <TouchableOpacity
                style={styles.filterButtonReset}
                activeOpacity={1}
                onPress={handleReset}
              >
                <Text style={styles.filterButtonResetText}>重置</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.filterButtonSubmit}
                activeOpacity={1}
                onPress={handleConfirm}
              >
                <Text style={styles.filterButtonSubmitText}>
                  确认
                  {filterNumber > 0 ? <Text> ({filterNumber})</Text> : null}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f6f9f9',
  },
  filterWrapper: {
    flex: 1,
    backgroundColor: '#f6f9f9',
  },
  filter: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: '#fff',
    borderRadius: getRealSize(12),
    overflow: 'hidden',
  },
  filterBody: {
    flex: 1,
    flexShrink: 0,
  },
  filterMain: {
    flex: 1,
  },
  filterMainContent: {
    padding: getRealSize(15),
  },
  filterMainItem: {
    marginBottom: getRealSize(30),
  },
  filterMainItemTitle: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(16),
    color: '#161616',
    fontWeight: '500',
    marginBottom: getRealSize(10),
  },
  filterMainItemContent: {
    // ButtonSelect和CommonSelect的容器
  },
  filterFooter: {
    flexShrink: 0,
    width: '100%',
    backgroundColor: '#ffffff',
  },
  filterButton: {
    height: getRealSize(54),
    marginHorizontal: getRealSize(15),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: getRealSize(10),
  },
  filterButtonReset: {
    width: getRealSize(75),
    height: getRealSize(42),
    borderWidth: getRealSize(1),
    borderColor: '#333',
    justifyContent: 'center',
    alignItems: 'center',
    boxSizing: 'border-box',
    marginRight: getRealSize(15),
  },
  filterButtonResetText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(13),
    color: '#161616',
    fontWeight: '500',
  },
  filterButtonSubmit: {
    flex: 1,
    height: getRealSize(42),
    boxSizing: 'border-box',
    backgroundColor: '#333333',
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterButtonSubmitText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(13),
    color: '#ffffff',
    fontWeight: '500',
  },
});

export default FilterPage;
