import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  ScrollView,
  RefreshControl,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import PermissionWrapper from '@/components/PermissionWrapper';
import { SafeAreaView } from 'react-native-safe-area-context';
import dayjs from 'dayjs';
import Header from '../../../components/header';
import DateSelect from '../../../components/DateSelect';
import Api from '../../../common/api';
import { jumpReactNativePage } from '@/common/jumpPage';
import { getRealSize } from '@/common/utils';
import CameraSelect, { ImageItem } from '@/components/CameraSelect';
import { TaskSkeleton } from '../components/Skeleton';
import { useRoute } from '@react-navigation/native';
import { pushChangeTenant } from '@/common/pushChangeTenant';
import LinearGradient from 'react-native-linear-gradient';

interface TaskItem {
  task_id: string;
  name: string;
  status: number;
  end_date: string;
  executor: string[];
}

interface TaskTab {
  id: number;
  name: string;
  cnt: number;
}

// 常量定义
const TASK_TABS: TaskTab[] = [
  { id: 0, name: '全部', cnt: 0 },
  { id: 1, name: '未完成', cnt: 0 },
  { id: 2, name: '已完成', cnt: 0 },
];

const STORAGE_KEYS = {
  TASK_DATE: 'followupCalendarTaskDate',
} as const;

const TASK_STATUS = {
  ALL: 0,
  UNFINISHED: 1,
  FINISHED: 2,
} as const;

const SCROLL_TO_TOP_DELAY = 100;
const EMPTY_IMAGE_URL =
  'https://static.soyoung.com/sy-design/aqnomvpf3ki11753429315696.png';
const TASK_COMPLETED_ICON_URL =
  'https://static.soyoung.com/sy-design/z8le9dra8ia91749036955733.png';
const TaskPage: React.FC<{ pageShow: boolean }> = ({ pageShow }) => {
  const router: any = useRoute();
  const scrollViewRef = useRef<ScrollView>(null);
  const previousPageShowRef = useRef<boolean>(false);

  // 保存上一次的筛选参数，用于比较是否需要刷新
  const previousParamsRef = useRef<{
    taskDate: string;
  } | null>(null);

  // 任务数据
  const [taskDate, setTaskDate] = useState(dayjs().format('YYYY-MM-DD'));
  const [startDate, setStartDate] = useState(dayjs().format('YYYY-MM-DD'));
  const [endDate, setEndDate] = useState(
    dayjs().add(7, 'day').format('YYYY-MM-DD')
  );
  const [taskTabActive, setTaskTabActive] = useState<number>(TASK_STATUS.ALL);
  const [taskList, setTaskList] = useState<TaskItem[]>([]);
  const [taskCount, setTaskCount] = useState(0);
  const [taskTabList, setTaskTabList] = useState<TaskTab[]>(TASK_TABS);
  const [refreshing, setRefreshing] = useState(false);
  const [taskItem, setTaskItem] = useState<TaskItem | null>(null);
  const [cameraSelectVisible, setCameraSelectVisible] = useState(false);
  const [taskLoading, setTaskLoading] = useState(false);
  const [hasPermission, setHasPermission] = useState(true);

  // 检查任务参数是否发生变化（不使用 useCallback 避免重复执行）
  const checkTaskParamsChanged = async () => {
    try {
      // 获取当前的任务日期参数
      const storedTaskDate = await AsyncStorage.getItem(STORAGE_KEYS.TASK_DATE);
      const currentTaskDate = storedTaskDate || dayjs().format('YYYY-MM-DD');

      // 如果是第一次访问，直接返回 true
      if (!previousParamsRef.current) {
        previousParamsRef.current = {
          taskDate: currentTaskDate,
        };
        return true;
      }

      // 比较参数是否发生变化
      const paramsChanged =
        previousParamsRef.current.taskDate !== currentTaskDate;

      // 更新缓存的参数
      if (paramsChanged) {
        previousParamsRef.current = {
          taskDate: currentTaskDate,
        };
      }

      return paramsChanged;
    } catch (error) {
      console.error('比较任务参数失败:', error);
      return true; // 出错时默认刷新
    }
  };

  // 获取任务列表
  const getTaskList = useCallback(
    async (date?: string, id?: number) => {
      try {
        setTaskLoading(true);
        const res = await Api.pagefetch({
          path: '/chain-wxapp/v1/taskmanagement/list',
          isLoading: false,
          params: {
            date: date || taskDate,
            status: id !== undefined ? id : taskTabActive,
          },
        });
        if (res.errorCode === 0 && res.responseData) {
          const { responseData } = res;
          // 更新任务列表
          setTaskList(responseData.list || []);

          // 更新tab计数
          const newTabList: TaskTab[] = [
            { id: TASK_STATUS.ALL, name: '全部', cnt: responseData.total || 0 },
            {
              id: TASK_STATUS.UNFINISHED,
              name: '未完成',
              cnt: responseData.unfinished_count || 0,
            },
            {
              id: TASK_STATUS.FINISHED,
              name: '已完成',
              cnt: responseData.finished_count || 0,
            },
          ];
          setTaskTabList(newTabList);

          // 更新任务计数
          setTaskCount(responseData.unfinished_count || 0);
        } else if (res.errorCode === 10002) {
          setHasPermission(false);
        } else {
          console.warn('获取任务列表失败:', res.errorMsg || '未知错误');
          // 重置为空状态
          setTaskList([]);
          setTaskTabList([...TASK_TABS]);
          setTaskCount(0);
        }
      } catch (error) {
        console.error('获取任务列表异常:', error);
        // 重置为空状态
        setTaskList([]);
        setTaskTabList([...TASK_TABS]);
        setTaskCount(0);
      } finally {
        setTaskLoading(false);
      }
    },
    [taskDate, taskTabActive]
  );

  // 重置任务数据到初始状态
  const resetTaskData = useCallback(() => {
    setTaskList([]);
    // setTaskTabList([...TASK_TABS]);
    setTaskCount(0);
  }, []);

  // 任务Tab切换
  const handleTaskTabClick = useCallback(
    (item: TaskTab) => {
      if (item.id === taskTabActive) return; // 避免重复点击
      setTaskTabActive(item.id);
      resetTaskData();
      getTaskList(undefined, item.id);
    },
    [taskTabActive, resetTaskData, getTaskList]
  );

  const updateTaskDate = useCallback((date: string) => {
    previousParamsRef.current = {
      taskDate: date,
    };
    AsyncStorage.setItem(STORAGE_KEYS.TASK_DATE, date);
  }, []);

  // 任务日期选择
  const handleTaskDateChange = useCallback(
    async (date: string) => {
      if (date === taskDate) return; // 避免重复设置相同日期

      setTaskDate(date);
      updateTaskDate(date);
      // setTaskTabActive(TASK_STATUS.ALL);
      resetTaskData();
      getTaskList(date);
    },
    [taskDate, resetTaskData, getTaskList]
  );

  // 任务详情点击
  const handleTaskDetailClick = useCallback((item: TaskItem) => {
    console.log('任务详情:', item.name);
    if (+item.status !== 3) {
      jumpReactNativePage(`followup/taskDetail?taskId=${item.task_id}`);
    }
  }, []);

  // 任务完成操作
  const handleGoTask = useCallback(
    (item: TaskItem) => {
      setCameraSelectVisible(true);
      setTaskItem(item);
    },
    [setCameraSelectVisible, setTaskItem]
  );

  const handleCompleteTask = useCallback(
    (result: ImageItem[]) => {
      console.log(taskItem);
      setTimeout(() => {
        jumpReactNativePage(
          `followup/taskFinish?taskId=${taskItem?.task_id}&imgList=${JSON.stringify(
            result
          )}`
        );
      }, 500);
    },
    [taskItem]
  );

  // 打开日历
  const handleOpenCalendar = useCallback(() => {
    jumpReactNativePage(
      `followup/calendar?type=${taskTabActive}&curDate=${taskDate}&calendar_type=task`
    );
  }, [taskTabActive, taskDate]);

  // 从存储加载日期数据
  const loadDateFromStorage = useCallback(async () => {
    try {
    } catch (error) {
      console.error('加载存储日期失败:', error);
      return taskDate;
    }
  }, [taskDate]);

  // 初始化数据
  const initData = useCallback(async () => {
    try {
      const storedDate =
        (await AsyncStorage.getItem(STORAGE_KEYS.TASK_DATE)) || taskDate;
      if (storedDate && storedDate !== taskDate) {
        console.log('loadDateFromStorage', storedDate);
        const newStartDate = dayjs(storedDate).format('YYYY-MM-DD');
        const newEndDate = dayjs(storedDate).add(7, 'day').format('YYYY-MM-DD');

        setTaskDate(storedDate);
        setStartDate(newStartDate);
        setEndDate(newEndDate);
      }
      await getTaskList(storedDate);
    } catch (error) {
      console.error('初始化数据失败:', error);
      resetTaskData();
    }
  }, [taskDate, loadDateFromStorage, getTaskList, resetTaskData]);

  // 滚动到顶部
  const scrollToTop = useCallback(() => {
    setTimeout(() => {
      scrollViewRef.current?.scrollTo({ y: 0, animated: true });
    }, SCROLL_TO_TOP_DELAY);
  }, []);

  // 重置页面状态
  const resetPageState = useCallback(() => {
    setTaskTabActive(TASK_STATUS.ALL);
    resetTaskData();
  }, [resetTaskData]);

  // 监听 pageShow 变化
  useEffect(() => {
    // 只在 pageShow 真正发生改变时才执行
    if (pageShow !== previousPageShowRef.current) {
      previousPageShowRef.current = pageShow;

      if (pageShow) {
        // 页面从隐藏变为显示
        // 检查筛选参数是否发生变化，只有变化时才刷新数据
        checkTaskParamsChanged().then((hasChanged: boolean) => {
          if (hasChanged) {
            console.log('检测到任务参数变化，刷新任务数据');
            resetPageState();
            scrollToTop();
            initData();
            console.log('Track: employee_other_followup_task_page - pageShow');
          } else {
            getTaskList();
            console.log('任务参数未变化，跳过数据刷新');
          }
        });
      }
    }
  }, [pageShow]);

  useEffect(() => {
    const initializePage = async () => {
      const tenantId = router.params?.params?.tenant_id;
      const tenantUserId = router.params?.params?.tenant_user_id;
      try {
        if (tenantId && tenantUserId) {
          await pushChangeTenant(tenantId, tenantUserId);
        }
      } catch (error) {
        console.error('pushChangeTenant失败:', error);
      }
    };
    initializePage();
    return () => {
      AsyncStorage.removeItem(STORAGE_KEYS.TASK_DATE);
    };
  }, []);

  // 下拉刷新
  const onRefresh = useCallback(async () => {
    if (refreshing) return; // 防止重复刷新

    setRefreshing(true);
    try {
      await getTaskList(taskDate);
    } catch (error) {
      console.error('刷新失败:', error);
    } finally {
      setRefreshing(false);
    }
  }, [refreshing, taskDate, getTaskList]);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Header title='任务' bgColor='#fff' />
      <View style={styles.taskContainer}>
        {/* 任务日期选择 */}
        <View style={styles.taskContainerBox}>
          <View style={styles.taskDateContainer}>
            <DateSelect
              value={taskDate}
              startDate={startDate}
              endDate={endDate}
              showPanel
              onChange={handleTaskDateChange}
              onClickPanel={handleOpenCalendar}
            />
          </View>

          {/* 任务Tab */}
          <View style={styles.taskContainerTab}>
            <View style={styles.tabsContent}>
              {taskTabList.map(item => (
                <TouchableOpacity
                  key={item.id}
                  activeOpacity={1}
                  style={[styles.tabItem]}
                  onPress={() => handleTaskTabClick(item)}
                >
                  <Text
                    style={[
                      styles.taskTabText,
                      taskTabActive === item.id && styles.taskTabTextActive,
                    ]}
                  >
                    {item.name} {item.cnt}
                  </Text>
                  <View
                    style={[
                      styles.flag,
                      taskTabActive === item.id && styles.flagActive,
                    ]}
                  />
                </TouchableOpacity>
              ))}
            </View>
          </View>
          <LinearGradient
            colors={['#fff', '#f7f9f9']}
            style={styles.gradient}
          />
        </View>
        {/* 任务列表 */}
        {taskList.length > 0 ? (
          <ScrollView
            style={styles.taskListContainer}
            ref={scrollViewRef}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
          >
            {taskList.map(item => (
              <TouchableOpacity
                key={item.task_id}
                activeOpacity={1}
                style={styles.listItem}
                onPress={() => handleTaskDetailClick(item)}
              >
                {/* 任务标题和状态 */}
                <View style={styles.listItemHeader}>
                  <Text style={styles.headerTitle} numberOfLines={1}>
                    {item.name}
                  </Text>
                  {Number(item.status) === TASK_STATUS.UNFINISHED ? (
                    <TouchableOpacity
                      activeOpacity={1}
                      style={styles.headerStatus}
                      onPress={e => {
                        e.stopPropagation();
                        handleGoTask(item);
                      }}
                    >
                      <Text style={styles.headerStatusText}>去完成</Text>
                    </TouchableOpacity>
                  ) : null}
                </View>

                {/* 截止时间 */}
                <View style={styles.listItemRow}>
                  <Text style={styles.labelText}>截止时间</Text>
                  <Text style={styles.valueText} numberOfLines={1}>
                    {item.end_date}
                  </Text>
                </View>

                {/* 参与人员 */}
                <View style={styles.listItemInfo}>
                  <Text style={styles.labelText}>参与人员</Text>
                  <View style={styles.executorContainer}>
                    {/* 第一个参与人员（如果包含@符号，显示为特殊样式） */}
                    {item.executor.length >= 1 &&
                    item.executor[0].includes('@') ? (
                      <Text style={styles.executorSuccess}>
                        {item.executor[0]}
                      </Text>
                    ) : null}
                    {/* 其余参与人员 - 合并显示并支持省略号 */}
                    {item.executor.length >= 2 ? (
                      <Text
                        style={styles.valueText}
                        numberOfLines={1}
                        ellipsizeMode='tail'
                      >
                        {item.executor.slice(1).join('  ')}
                      </Text>
                    ) : null}
                    {/* 无参与人员 */}
                    {item.executor.length === 0 ? (
                      <Text style={styles.valueText}>暂无参与人员</Text>
                    ) : null}
                  </View>
                  {/* 占位空间 */}
                  {Number(item.status) === TASK_STATUS.FINISHED ? (
                    <View style={styles.positionBox} />
                  ) : null}
                </View>

                {/* 完成状态图标 */}
                {Number(item.status) === TASK_STATUS.FINISHED ? (
                  <Image
                    style={styles.listItemImage}
                    source={{ uri: TASK_COMPLETED_ICON_URL }}
                  />
                ) : null}
              </TouchableOpacity>
            ))}
          </ScrollView>
        ) : !hasPermission ? (
          <PermissionWrapper hasPermission={false} bgColor='#F6F9F9' />
        ) : taskLoading ? (
          <View style={styles.taskListContainer}>
            <TaskSkeleton />
            <TaskSkeleton />
            <TaskSkeleton />
            <TaskSkeleton />
          </View>
        ) : (
          <View style={styles.taskListEmpty}>
            <View style={styles.taskListEmptyCore}>
              <Image
                style={styles.taskListEmptyImage}
                source={{ uri: EMPTY_IMAGE_URL }}
              />
              <Text style={styles.taskListEmptyText}>暂无相关数据</Text>
            </View>
          </View>
        )}
      </View>
      <CameraSelect
        openPanel={cameraSelectVisible}
        onClose={() => {
          setCameraSelectVisible(false);
          setTaskItem(null);
        }}
        onResult={handleCompleteTask}
        mediaType='photo'
        multiple={true}
        maxFiles={40}
        minFiles={1}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f7f9f9',
  },
  taskContainer: {
    flex: 1,
  },
  taskContainerBox: {
    backgroundColor: '#fff',
  },
  taskDateContainer: {
    backgroundColor: '#ffffff',
    paddingLeft: getRealSize(15),
    paddingTop: getRealSize(15),
    paddingBottom: getRealSize(15),
  },
  gradient: {
    height: getRealSize(15),
    backgroundColor: '#fff', // 对应Vue中的线性渐变效果
  },
  taskContainerTab: {
    paddingLeft: getRealSize(19),
    paddingRight: getRealSize(15),
    paddingTop: getRealSize(5),
    backgroundColor: '#fff',
  },
  tabsContent: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  tabItem: {
    display: 'flex',
    flexDirection: 'column',
    paddingRight: getRealSize(15),
    alignItems: 'center',
  },
  taskTabTextActive: {
    color: '#333333',
  },
  taskTabText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(15),
    color: '#8C8C8C',
    letterSpacing: 0,
    textAlign: 'center',
    lineHeight: getRealSize(22),
    fontWeight: '500',
  },
  flag: {
    width: getRealSize(20),
    height: getRealSize(2),
    marginTop: getRealSize(5),
    backgroundColor: '#fff',
  },
  flagActive: {
    backgroundColor: '#030303',
  },
  taskListContainer: {
    paddingHorizontal: getRealSize(15),
    paddingBottom: getRealSize(15),
    backgroundColor: '#F8F8F8',
  },
  taskListEmpty: {
    height: getRealSize(600),
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
  taskListEmptyCore: {
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    paddingBottom: getRealSize(150),
  },
  taskListEmptyImage: {
    width: getRealSize(35), // 对应Vue中的150px
    height: getRealSize(35), // 对应Vue中的120px
    marginBottom: getRealSize(20),
  },
  taskListEmptyText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(14),
    color: '#030303',
    letterSpacing: 0,
    fontWeight: '500',
    textAlign: 'center',
  },
  listItem: {
    backgroundColor: '#ffffff',
    padding: getRealSize(15),
    marginBottom: getRealSize(10),
  },
  listItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getRealSize(8),
  },
  headerTitle: {
    flex: 1,
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(14),
    color: '#333333',
    letterSpacing: 0,
    fontWeight: '500',
  },
  headerStatus: {
    backgroundColor: '#030303',
    paddingHorizontal: getRealSize(10),
    minWidth: getRealSize(50),
    height: getRealSize(25),
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: getRealSize(10),
  },
  headerStatusText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(12),
    lineHeight: getRealSize(25),
    color: '#fff',
    letterSpacing: 0,
    fontWeight: '500',
  },
  listItemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getRealSize(8),
  },
  labelText: {
    minWidth: getRealSize(52),
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#777777',
    letterSpacing: 0,
    fontWeight: '400',
    marginRight: getRealSize(10),
    lineHeight: getRealSize(18),
  },
  valueContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  valueText: {
    flex: 1,
    overflow: 'hidden',
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#333333',
    lineHeight: getRealSize(18),
    letterSpacing: 0,
    fontWeight: '400',
  },
  listItemInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    width: '100%',
  },
  executorContainer: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  executorSuccess: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    backgroundColor: '#EBFBDC',
    color: '#61B43E',
    paddingHorizontal: getRealSize(5),
    lineHeight: getRealSize(18),
    marginRight: getRealSize(10),
    fontWeight: '400',
  },
  positionBox: {
    width: getRealSize(40),
  },
  listItemImage: {
    width: getRealSize(45),
    height: getRealSize(45),
    position: 'absolute',
    right: getRealSize(5),
    bottom: getRealSize(5),
  },
});

export default TaskPage;
