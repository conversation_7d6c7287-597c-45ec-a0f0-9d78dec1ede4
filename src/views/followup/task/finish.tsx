import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  TextInput,
  Platform,
} from 'react-native';
import { ATrack } from '@soyoung/react-native-container';
import CameraSelect from '@/components/CameraSelect';
import Header from '@/components/header';
import { getRealSize } from '@/common/utils';
import jsApi from '@soyoung/react-native-jsapi';
import api from '@/common/api';
import VideoPlayerView from '@soyoung/react-native-video-player';
import { Bridge } from '@/common/bridge';

// 常量定义
const MAX_FILES_LIMIT = 40;

/**
 * 图片视频数据类型定义
 */
interface MediaItem {
  img_url?: string; // 图片URL或视频封面图URL
  video_url?: string; // 视频文件URL（仅视频类型）
  type: 1 | 2; // 1: 图片, 2: 视频
  id?: string;
}

interface TaskFinishProps {
  route: {
    params: {
      params: {
        taskId: string;
        imgList?: string;
      };
    };
  };
  insets: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
  pageShow: boolean;
}

const TaskFinishContainer: React.FC<TaskFinishProps> = ({ route, insets }) => {
  // 状态管理
  const [imgList, setImgList] = useState<MediaItem[]>([]);
  const [notes, setNotes] = useState<string>('');
  const [showAll, setShowAll] = useState<boolean>(false);
  const [taskId, setTaskId] = useState<number>(0);
  const [loading] = useState<boolean>(false);
  const [cameraSelectVisible, setCameraSelectVisible] =
    useState<boolean>(false);
  const [videoSelectVisible, setVideoSelectVisible] = useState<boolean>(false);

  // 获取路由参数
  const initialImgList = route?.params?.params?.imgList || '';

  // 初始化数据
  useEffect(() => {
    if (initialImgList) {
      try {
        const parsedList = JSON.parse(initialImgList);
        setImgList(parsedList);
      } catch (error) {
        console.error('解析初始图片列表失败:', error);
      }
    }
  }, [initialImgList]);

  useEffect(() => {
    setTaskId(+route?.params?.params?.taskId);
  }, [route?.params?.params?.taskId]);

  // 计算显示的图片列表
  const displayImages = useMemo(() => {
    if (showAll || imgList.length <= 9) {
      return imgList;
    }
    return imgList.slice(0, 8); // 显示前8个，第9个位置用来显示更多
  }, [imgList, showAll]);

  /**
   * 显示所有图片
   */
  const showAllImages = useCallback(() => {
    setShowAll(true);
  }, []);

  /**
   * 删除图片/视频
   */
  const handleDeleteItem = useCallback(
    (index: number) => {
      const newImgList = [...imgList];
      newImgList.splice(index, 1);
      setImgList(newImgList);
    },
    [imgList]
  );

  /**
   * 提交任务
   */
  const submitTask = useCallback(async () => {
    if (imgList.length === 0) {
      Bridge.showToast('请上传图片或视频');
      return;
    }
    const submitData = {
      content: JSON.stringify({
        source: imgList.map(item => ({
          url: item.type === 1 ? item.img_url : item.video_url,
          type: item.type,
          video_cover: item.type === 2 ? item.img_url : '',
        })),
        text: notes,
      }),
      task_id: +taskId,
    };

    try {
      const { errorCode, errorMsg } = await api.pagefetch({
        path: '/chain-wxapp/v1/taskmanagement/finishNew',
        params: submitData,
        isLoading: false,
      });
      if (errorCode === 0) {
        Bridge.showToast('提交成功');
        setTimeout(() => {
          jsApi.toNative('backAnimated', {
            transitionType: '0',
            disableAnimation: '0',
          });
        }, 1000);
      } else {
        Bridge.showToast(errorMsg);
      }
    } catch (error) {
      console.error('提交失败:', error);
    }
  }, [imgList, notes, taskId]);

  // 图片预览
  const handlePreviewImage = useCallback(
    (index: number) => {
      const imageItems = imgList.filter(item => item.type === 1);
      const imageIndex =
        imgList.slice(0, index + 1).filter(item => item.type === 1).length - 1;

      (jsApi.toNative as any)('pictureBrowser', {
        list: imageItems.map(item => ({
          img_url: item.img_url,
        })),
        currentIndex: imageIndex,
        style: 0,
      });
    },
    [imgList]
  );

  // 视频预览
  const handlePreviewVideo = useCallback((video_url: string) => {
    (jsApi.toNative as any)('pictureBrowser', {
      list: [
        {
          video_url,
        },
      ],
      currentIndex: 0,
      style: 0,
    });
  }, []);

  const renderMediaItem = useCallback(
    (item: MediaItem, index: number) => {
      return (
        <View key={index} style={styles.imgItem}>
          {/* 删除按钮 */}
          <ATrack
            style={styles.imgItemClose}
            onPress={() => handleDeleteItem(index)}
          >
            <Image
              source={{
                uri: 'https://static.soyoung.com/sy-design/9jb9ppka8hk11754290630129.png',
              }}
              style={styles.iconClose}
            />
          </ATrack>

          {/* 图片显示 */}
          {item.type === 1 && (
            <ATrack onPress={() => handlePreviewImage(index)}>
              <Image
                source={{ uri: item.img_url }}
                style={styles.mediaContent}
                resizeMode='cover'
              />
            </ATrack>
          )}

          {/* 视频显示 */}
          {item.type === 2 && (
            <ATrack onPress={() => handlePreviewVideo(item.video_url!)}>
              <View style={styles.videoContainer}>
                <VideoPlayerView
                  style={styles.videoPlayer}
                  videoInfo={{
                    url: item.video_url,
                  }}
                  placeholder={{
                    uri: item.img_url,
                  }}
                  placeholderStyle={
                    Platform.OS === 'ios'
                      ? {
                          width: getRealSize(105),
                          height: getRealSize(105),
                        }
                      : {}
                  }
                  muted={true}
                  loop={false}
                />
                <Image
                  source={{ uri: item.img_url }}
                  style={styles.videoCoverPlayer}
                  resizeMode='cover'
                />
                <View style={styles.videoPlayIcon}>
                  <Image
                    source={{
                      uri: 'https://static.soyoung.com/sy-pre/20250804-211154-1754313000624.png',
                    }}
                    style={styles.videoPlayIconImage}
                  />
                </View>
              </View>
            </ATrack>
          )}
        </View>
      );
    },
    [handleDeleteItem, handlePreviewImage, handlePreviewVideo]
  );

  const handleAddImage = useCallback(() => {
    if (imgList.length >= MAX_FILES_LIMIT) {
      Bridge.showToast(`最多上传${MAX_FILES_LIMIT}张图片或视频`);
      return;
    }
    setCameraSelectVisible(true);
  }, [imgList.length]);

  const handleAddVideo = useCallback(() => {
    if (imgList.length >= MAX_FILES_LIMIT) {
      Bridge.showToast(`最多上传${MAX_FILES_LIMIT}张图片或视频`);
      return;
    }
    setVideoSelectVisible(true);
  }, [imgList.length]);

  const handleCompleteAddImage = useCallback(
    (images: MediaItem[]) => {
      const remainingSlots = MAX_FILES_LIMIT - imgList.length;
      const imagesToAdd = images.slice(0, remainingSlots);

      if (imagesToAdd.length < images.length) {
        Bridge.showToast(
          `已达到最大限制，只添加了前${imagesToAdd.length}张图片`
        );
      }

      setImgList([...imgList, ...imagesToAdd]);
      setCameraSelectVisible(false);
    },
    [imgList]
  );

  const handleCompleteAddVideo = useCallback(
    (videos: MediaItem[]) => {
      const remainingSlots = MAX_FILES_LIMIT - imgList.length;
      const videosToAdd = videos.slice(0, remainingSlots);
      console.log('videosToAdd', videos);
      if (videosToAdd.length < videos.length) {
        Bridge.showToast(
          `已达到最大限制，只添加了前${videosToAdd.length}个视频`
        );
      }

      setImgList([...imgList, ...videosToAdd]);
      setVideoSelectVisible(false);
    },
    [imgList]
  );

  return (
    <View style={styles.container}>
      <Header title='完成任务' bgColor='#fff' />

      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.finishedTaskList}>
          {/* 图片视频区域 */}
          <View style={styles.finishedTaskListItem}>
            <View style={styles.finishedTaskListItemTitle}>
              <Text style={styles.titleText}>图片视频</Text>
              <Text style={styles.descriptionText}>
                (已上传{imgList.length}/{MAX_FILES_LIMIT}张)
              </Text>
            </View>

            <View style={styles.imgList}>
              {/* 渲染图片/视频列表 */}
              {displayImages.map((item, index) => renderMediaItem(item, index))}

              {/* 更多图片覆盖层 */}
              {imgList.length > 9 && !showAll && (
                <ATrack style={styles.imgItem} onPress={showAllImages}>
                  <Image
                    source={{
                      uri: imgList[8]?.img_url || '',
                    }}
                    style={styles.mediaContent}
                    resizeMode='cover'
                  />
                  <View style={styles.overlay}>
                    <Text style={styles.overlayText}>
                      +{imgList.length - 9}
                    </Text>
                  </View>
                </ATrack>
              )}
            </View>

            {/* 添加按钮组 */}
            <View style={styles.addGroupBtn}>
              <ATrack onPress={handleAddImage}>
                <View
                  style={[
                    styles.addGroupBtnItem,
                    imgList.length >= MAX_FILES_LIMIT && styles.disabled,
                  ]}
                >
                  <Image
                    source={{
                      uri: 'https://static.soyoung.com/sy-design/i6n2lhdw8o5u1754290911771.png',
                    }}
                    style={styles.addGroupBtnIcon}
                  />
                  <Text style={styles.addBtnText}>上传图片</Text>
                </View>
              </ATrack>

              <ATrack onPress={handleAddVideo}>
                <View
                  style={[
                    styles.addGroupBtnItem,
                    imgList.length >= MAX_FILES_LIMIT && styles.disabled,
                  ]}
                >
                  <Image
                    source={{
                      uri: 'https://static.soyoung.com/sy-design/393wsw697yxh41754290911755.png',
                    }}
                    style={styles.addGroupBtnIcon}
                  />
                  <Text style={styles.addBtnText}>上传视频</Text>
                </View>
              </ATrack>
            </View>
          </View>

          {/* 备注区域 */}
          <View style={styles.finishedTaskListItem}>
            <Text style={styles.finishedTaskListItemTitle}>备注</Text>
            <View style={styles.finishedTaskListItemContent}>
              <TextInput
                value={notes}
                onChangeText={setNotes}
                style={styles.contentTextarea}
                placeholder='请写备注说明'
                placeholderTextColor='#777777'
                maxLength={500}
                multiline={true}
                textAlignVertical='top'
              />
            </View>
          </View>
        </View>
      </ScrollView>

      {/* 提交按钮 */}
      <View style={[styles.submitBtnContainer, { bottom: insets.bottom }]}>
        <ATrack
          style={[
            styles.submitBtn,
            imgList.length === 0 && styles.submitBtnDisabled,
          ]}
          onPress={submitTask}
        >
          <Text style={styles.submitBtnText}>
            {loading ? '提交中...' : '提交'}
          </Text>
        </ATrack>
      </View>

      {/* 图片选择器 */}
      <CameraSelect
        openPanel={cameraSelectVisible}
        onClose={() => {
          setCameraSelectVisible(false);
        }}
        onResult={handleCompleteAddImage}
        mediaType='photo'
        multiple={true}
        maxFiles={Math.max(1, MAX_FILES_LIMIT - imgList.length)}
        minFiles={1}
      />

      {/* 视频选择器 */}
      <CameraSelect
        openPanel={videoSelectVisible}
        onClose={() => {
          setVideoSelectVisible(false);
        }}
        onResult={handleCompleteAddVideo}
        mediaType='video'
        multiple={false}
        maxFiles={Math.max(1, Math.min(1, MAX_FILES_LIMIT - imgList.length))}
        minFiles={1}
      />
    </View>
  );
};

export default TaskFinishContainer;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: getRealSize(15),
    paddingBottom: getRealSize(120),
  },
  finishedTaskList: {
    width: '100%',
    paddingVertical: getRealSize(15),
  },
  finishedTaskListItem: {
    width: '100%',
    marginBottom: getRealSize(10),
  },
  finishedTaskListItemTitle: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getRealSize(10),
  },
  titleText: {
    fontSize: getRealSize(15),
    fontWeight: '600',
    color: '#333',
  },
  descriptionText: {
    fontSize: getRealSize(12),
    color: '#999',
    marginLeft: getRealSize(5),
  },
  finishedTaskListItemContent: {
    width: '100%',
  },
  contentTextarea: {
    width: '100%',
    minHeight: getRealSize(100),
    maxHeight: getRealSize(150),
    padding: getRealSize(10),
    fontSize: getRealSize(14),
    color: '#333',
    backgroundColor: '#f8f8f8',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  imgList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: getRealSize(-7.5),
  },
  imgItem: {
    width: getRealSize(105),
    height: getRealSize(105),
    marginHorizontal: getRealSize(7),
    marginVertical: getRealSize(7.5),
    position: 'relative',
  },
  imgItemClose: {
    position: 'absolute',
    top: getRealSize(-8),
    right: getRealSize(-8),
    width: getRealSize(20),
    height: getRealSize(20),
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  iconClose: {
    width: getRealSize(16),
    height: getRealSize(16),
  },
  mediaContent: {
    width: '100%',
    height: '100%',
  },
  // 视频相关样式
  videoContainer: {
    position: 'relative',
    width: '100%',
    height: '100%',
  },
  videoCoverPlayer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  videoPlayer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
    height: '100%',
  },
  videoPlayIcon: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoPlayIconImage: {
    width: getRealSize(16),
    height: getRealSize(16),
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: getRealSize(4),
  },
  overlayText: {
    color: '#fff',
    fontSize: getRealSize(18),
    fontWeight: 'bold',
  },
  addGroupBtn: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginTop: getRealSize(10),
  },
  addGroupBtnItem: {
    width: getRealSize(105),
    height: getRealSize(36),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#61b43e',
    marginRight: getRealSize(15),
  },
  disabled: {
    opacity: 0.5,
  },
  addGroupBtnIcon: {
    width: getRealSize(18),
    height: getRealSize(18),
  },
  addBtnText: {
    fontSize: getRealSize(12),
    color: '#61b43e',
  },
  submitBtnContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#f8f8f8',
    paddingHorizontal: getRealSize(15),
    paddingVertical: getRealSize(15),
  },
  submitBtn: {
    width: '100%',
    height: getRealSize(44),
    backgroundColor: '#333333',
    justifyContent: 'center',
    alignItems: 'center',
  },
  submitBtnDisabled: {
    backgroundColor: '#ccc',
  },
  submitBtnText: {
    color: '#fff',
    fontSize: getRealSize(16),
    fontWeight: '600',
  },
});
