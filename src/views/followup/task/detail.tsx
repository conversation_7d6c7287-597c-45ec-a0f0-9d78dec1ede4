import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  Alert,
  ScrollView,
  Platform,
} from 'react-native';
import Modal from 'react-native-modal';
import { modalAnimation } from '@/constant/modal_animation';
import Header from '@/components/header';
import { ATrack } from '@soyoung/react-native-container';
import { getRealSize } from '@/common/utils';
import { jumpReactNativePage } from '@/common/jumpPage';
import api, { FetchModule } from '@/common/api';
import jsApi from '@soyoung/react-native-jsapi';
import VideoPlayerView from '@soyoung/react-native-video-player';
import CameraSelect, { ImageItem } from '@/components/CameraSelect';

// 常量定义
const MAX_FILES_LIMIT = 40;

interface TaskDetailProps {
  route: {
    params: {
      params: {
        taskId: string;
      };
    };
  };
  insets: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
  pageShow: boolean;
}

interface TaskInfo {
  task_id: string;
  task_name: string;
  status: number;
  description: string;
  start_date: string;
  end_date: string;
  executor_list: Array<{ name: string }>;
  content?: {
    source: Array<{
      url: string;
      type: number; // 1: 图片, 2: 视频
      video_cover?: string;
    }>;
    text?: string;
  };
  checker_name?: string;
  check_date?: string;
  check_status_desc?: string;
  check_remark?: string;
  check_result_desc?: string;
}

const TaskDetailContainer: React.FC<TaskDetailProps> = ({
  route,
  insets,
  pageShow,
}) => {
  const [taskInfo, setTaskInfo] = useState<TaskInfo>({
    task_id: '',
    task_name: '',
    status: 0,
    description: '',
    start_date: '',
    end_date: '',
    executor_list: [],
  });
  const [loading, setLoading] = useState(true);
  const [textShowMore, setTextShowMore] = useState(false);
  const [textRemarkShowMore, setTextRemarkShowMore] = useState(false);
  const [checkRemarkShowMore, setCheckRemarkShowMore] = useState(false);
  const [cameraSelectVisible, setCameraSelectVisible] = useState(false);

  const taskId = route?.params?.params?.taskId || '';

  // 数据获取
  const getData = useCallback(async () => {
    if (!taskId) return;

    try {
      setLoading(true);
      const { responseData, errorCode } = await api.pagefetch({
        path: '/chain-wxapp/v1/taskmanagement/detail',
        params: {
          task_id: taskId,
        },
        method: FetchModule.Method.POST,
      });
      if (errorCode === 0) {
        setTaskInfo(responseData);
      }
    } catch (error) {
      Alert.alert('错误', '获取数据失败');
    } finally {
      setLoading(false);
    }
  }, [taskId]);

  // 检查文本是否需要显示更多按钮
  const checkNeedShowMore = useCallback((text: string, threshold = 100) => {
    return text && text.length > threshold;
  }, []);

  // 图片预览
  const handlePreviewImage = useCallback(
    (url: string) => {
      (jsApi.toNative as any)('pictureBrowser', {
        list: taskInfo.content?.source
          .filter((item: any) => item.type === 1)
          .map((item: any) => {
            return {
              img_url: item.url,
            };
          }),
        currentIndex: taskInfo.content?.source
          .filter((item: any) => item.type === 1)
          .findIndex((item: any) => item.url === url),
        style: 0,
      });
    },
    [taskInfo.content?.source]
  );

  useEffect(() => {
    getData();
    return () => {
      console.log('pageShow', pageShow);
      setTaskInfo({
        task_id: '',
        task_name: '',
        status: 0,
        description: '',
        start_date: '',
        end_date: '',
        executor_list: [],
      });
    };
  }, [getData, pageShow]);

  // 视频预览
  const handlePreviewVideo = useCallback((url: string) => {
    // 使用浏览器预览视频，或者可以替换为其他预览方式
    (jsApi.toNative as any)('pictureBrowser', {
      list: [
        {
          video_url: url,
        },
      ],
      currentIndex: 0,
      style: 0,
    });
  }, []);

  // 展示更多图片弹窗
  const handleShowMoreImages = useCallback(() => {
    if (Platform.OS === 'ios') {
      jsApi.toNative('navigateWithPath', {
        url: 'app.soyoung://rn/followup/imagelist',
        params: {
          transitionType: '1',
          disableAnimation: '0',
          isTransparent: true,
          half_ratio: '0.3',
          source: JSON.stringify(taskInfo.content?.source) || [],
        } as never,
      });
    } else {
      jsApi.toNative('navigateWithPath', {
        url: 'app.soyoung://rn/followup/imagelist',
        params: {
          transitionType: '1',
          half_type: '1',
          disableAnimation: '0',
          isTransparent: 1,
          half_ratio: '0.3',
          source: JSON.stringify(taskInfo.content?.source) || [],
        } as never,
      });
    }
  }, [taskInfo.content?.source]);

  const handleCompleteTask = useCallback(
    (result: ImageItem[]) => {
      setTimeout(() => {
        jumpReactNativePage(
          `followup/taskFinish?taskId=${taskInfo.task_id}&imgList=${JSON.stringify(
            result
          )}`
        );
      }, 500);
    },
    [taskInfo.task_id]
  );

  // 任务完成操作
  const handleTaskComplete = useCallback(() => {
    setCameraSelectVisible(true);
  }, [setCameraSelectVisible]);

  return (
    <View style={styles.container}>
      <Header title='任务详情' bgColor='#fff' />
      <ScrollView
        style={styles.content}
        contentContainerStyle={{
          paddingBottom: getRealSize(80) + insets.bottom,
        }}
        showsHorizontalScrollIndicator={false}
      >
        {!loading && (
          <>
            <View style={styles.taskInfoCard}>
              {/* 状态标签 */}
              <View style={styles.header}>
                <Text style={styles.taskTitle}>{taskInfo.task_name || ''}</Text>
                <View
                  style={[
                    styles.statusTag,
                    taskInfo.status === 1
                      ? styles.statusActive
                      : styles.statusInactive,
                  ]}
                >
                  <Text
                    style={[
                      styles.statusText,
                      taskInfo.status === 1
                        ? styles.statusActiveText
                        : styles.statusInactiveText,
                    ]}
                  >
                    {taskInfo.status === 2 ? '已完成' : '未完成'}
                  </Text>
                </View>
              </View>
              {taskInfo.description ? (
                <View style={styles.detailContainer}>
                  <Text style={styles.labelText}>任务说明</Text>
                  <Text
                    style={styles.valueText}
                    numberOfLines={textShowMore ? 0 : 2}
                  >
                    {taskInfo.description}
                  </Text>
                  {checkNeedShowMore(taskInfo.description) ? (
                    <ATrack onPress={() => setTextShowMore(!textShowMore)}>
                      <Text style={styles.showMoreText}>
                        {textShowMore ? '收起' : '展开'}
                      </Text>
                    </ATrack>
                  ) : null}
                </View>
              ) : null}
              {taskInfo.start_date ? (
                <View style={styles.detailContainer}>
                  <Text style={styles.labelText}>任务时间</Text>
                  <Text style={styles.valueText}>
                    {taskInfo.start_date} ~ {taskInfo.end_date}
                  </Text>
                </View>
              ) : null}
              {/* 执行人 */}
              {taskInfo.executor_list.length > 0 ? (
                <View style={styles.detailContainer}>
                  <Text style={styles.labelText}>参与人员</Text>
                  <Text style={styles.valueText}>
                    {taskInfo.executor_list.map(item => item.name).join('、')}
                  </Text>
                </View>
              ) : null}

              {/* 时间信息 */}
            </View>

            {/* 图片视频展示 */}
            {taskInfo.content?.source &&
            taskInfo.content.source.length > 0 &&
            +taskInfo.status === 2 ? (
              <View style={styles.imagesCard}>
                <Text style={styles.sectionTitle}>图片视频</Text>
                <View style={styles.imageGrid}>
                  {taskInfo.content.source.slice(0, 9).map((item, index) => {
                    const isLastItem = index === 8;
                    const totalItems = taskInfo.content?.source.length || 0;
                    const remainingCount = totalItems - 9;

                    return (
                      <ATrack
                        key={index}
                        onPress={() =>
                          isLastItem && remainingCount > 0
                            ? handleShowMoreImages()
                            : item.type === 1
                              ? handlePreviewImage(item.url)
                              : handlePreviewVideo(item.url)
                        }
                      >
                        <View style={styles.imageContainer}>
                          {item.type === 1 ? (
                            // 图片渲染
                            <Image
                              source={{ uri: item.url }}
                              style={styles.image}
                              resizeMode='cover'
                            />
                          ) : (
                            // 视频渲染
                            <View style={styles.videoContainer}>
                              <VideoPlayerView
                                style={styles.videoPlayer}
                                placeholder={{
                                  uri: item.video_cover,
                                }}
                                placeholderStyle={
                                  Platform.OS === 'ios'
                                    ? {
                                        width: getRealSize(105),
                                        height: getRealSize(105),
                                      }
                                    : undefined
                                }
                                videoInfo={{
                                  url: item.url,
                                }}
                                muted={true}
                                loop={false}
                              />
                              <Image
                                source={{ uri: item.video_cover }}
                                style={styles.videoCoverPlayer}
                                resizeMode='cover'
                              />
                              <View style={styles.videoPlayIcon}>
                                <Image
                                  source={{
                                    uri: 'https://static.soyoung.com/sy-pre/20250804-211154-1754313000624.png',
                                  }}
                                  style={styles.videoPlayIconImage}
                                />
                              </View>
                            </View>
                          )}
                          {isLastItem && remainingCount > 0 && (
                            <View style={styles.imageOverlay}>
                              <Text style={styles.remainingText}>
                                +{remainingCount}
                              </Text>
                            </View>
                          )}
                        </View>
                      </ATrack>
                    );
                  })}
                </View>
              </View>
            ) : null}
            {/* 审核备注 */}
            {taskInfo.status === 2 ? (
              <View style={styles.detailSection}>
                <Text style={styles.sectionTitle}>备注</Text>
                <Text
                  style={styles.descriptionText}
                  numberOfLines={textRemarkShowMore ? undefined : 3}
                >
                  {taskInfo.content?.text || '请填写备注说明'}
                </Text>
                {checkNeedShowMore(taskInfo.content?.text || '') ? (
                  <ATrack
                    onPress={() => setTextRemarkShowMore(!textRemarkShowMore)}
                  >
                    <Text style={styles.showMoreText}>
                      {textRemarkShowMore ? '收起' : '展开'}
                    </Text>
                  </ATrack>
                ) : null}
              </View>
            ) : null}
            {/* 检查进度 */}
            {taskInfo.status === 2 ? (
              <View style={styles.detailSection}>
                <Text style={styles.sectionTitle}>检查进度</Text>
                <View style={styles.detailContainer}>
                  <Text style={styles.labelText}>任务结果</Text>
                  <Text style={styles.valueText}>
                    {taskInfo.check_result_desc}
                  </Text>
                </View>
                <View style={styles.detailContainer}>
                  <Text style={styles.labelText}>备注</Text>
                  <Text
                    style={styles.valueText}
                    numberOfLines={checkRemarkShowMore ? undefined : 3}
                  >
                    {taskInfo.check_remark}
                  </Text>
                  {checkNeedShowMore(taskInfo.check_remark || '') ? (
                    <ATrack
                      onPress={() =>
                        setCheckRemarkShowMore(!checkRemarkShowMore)
                      }
                    >
                      <Text style={styles.showMoreText}>
                        {checkRemarkShowMore ? '收起' : '展开'}
                      </Text>
                    </ATrack>
                  ) : null}
                </View>
                {taskInfo.check_status_desc !== '待检查' ? (
                  <>
                    <View style={styles.detailContainer}>
                      <Text style={styles.labelText}>检查人</Text>
                      <Text style={styles.valueText}>
                        {taskInfo.checker_name}
                      </Text>
                    </View>
                    <View style={styles.detailContainer}>
                      <Text style={styles.labelText}>检查时间</Text>
                      <Text style={styles.valueText}>
                        {taskInfo.check_date}
                      </Text>
                    </View>
                  </>
                ) : null}
              </View>
            ) : null}

            <View style={styles.bottomSpacer} />
          </>
        )}
      </ScrollView>

      {/* 操作按钮 */}
      {taskInfo.status === 1 ? (
        <View style={[styles.actionContainer, { bottom: insets.bottom }]}>
          <ATrack style={styles.completeBtn} onPress={handleTaskComplete}>
            <Text style={styles.completeBtnText}>去完成</Text>
          </ATrack>
        </View>
      ) : null}
      <CameraSelect
        openPanel={cameraSelectVisible}
        onClose={() => {
          setCameraSelectVisible(false);
        }}
        onResult={handleCompleteTask}
        mediaType='photo'
        multiple={true}
        maxFiles={MAX_FILES_LIMIT}
        minFiles={1}
      />
    </View>
  );
};

// 样式定义
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  content: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  taskInfoCard: {
    margin: getRealSize(15),
  },
  taskTitle: {
    fontSize: getRealSize(18),
    fontWeight: 'bold',
    color: '#333',
    maxWidth: getRealSize(260),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: getRealSize(12),
  },
  statusTag: {
    paddingHorizontal: getRealSize(8),
    paddingVertical: getRealSize(4),
    borderWidth: getRealSize(0.5),
  },
  statusActive: {
    backgroundColor: '#FFEBEA',
    borderColor: '#FE6631',
  },
  statusInactive: {
    backgroundColor: 'transparent',
    borderColor: '#61B43E',
  },
  statusText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    letterSpacing: 0,
    fontWeight: '400',
  },
  statusActiveText: {
    color: '#FE6631',
  },
  statusInactiveText: {
    color: '#61B43E',
  },
  detailContainer: {
    flexDirection: 'row',
    marginBottom: getRealSize(10),
  },
  labelText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(14),
    color: '#777777',
    letterSpacing: 0,
    fontWeight: '400',
  },
  valueText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(14),
    color: '#333333',
    letterSpacing: 0,
    fontWeight: '400',
    flex: 1,
    marginHorizontal: getRealSize(10),
  },
  detailSection: {
    margin: getRealSize(15),
  },
  sectionTitle: {
    fontSize: getRealSize(16),
    fontWeight: 'bold',
    color: '#333',
    marginBottom: getRealSize(12),
  },
  descriptionText: {
    fontSize: getRealSize(14),
    color: '#666',
    lineHeight: getRealSize(20),
  },

  showMoreText: {
    fontSize: getRealSize(12),
    color: '#61B43E',
  },
  imagesCard: {
    margin: getRealSize(15),
  },
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: getRealSize(-7.5),
  },
  imageContainer: {
    position: 'relative',
    margin: getRealSize(7),
  },
  image: {
    width: getRealSize(105),
    height: getRealSize(105),
  },
  imageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  remainingText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(15),
    color: '#FFFFFF',
    letterSpacing: 0,
    fontWeight: '500',
  },
  videoContainer: {
    position: 'relative',
    width: getRealSize(105),
    height: getRealSize(105),
  },
  videoPlayer: {
    width: '100%',
    height: '100%',
  },
  videoCoverPlayer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  videoPlayIcon: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoPlayIconImage: {
    width: getRealSize(16),
    height: getRealSize(16),
  },
  actionContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: getRealSize(15),
    paddingVertical: getRealSize(15),
  },
  completeBtn: {
    width: '100%',
    backgroundColor: '#333333',
    height: getRealSize(44),
    justifyContent: 'center',
    alignItems: 'center',
  },
  completeBtnText: {
    color: '#fff',
    fontSize: getRealSize(16),
    fontWeight: '600',
  },
  bottomSpacer: {
    height: 0,
  },
});

export default TaskDetailContainer;
