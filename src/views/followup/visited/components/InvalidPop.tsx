import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TextInput,
  Image,
  StyleSheet,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import Modal from 'react-native-modal';
import { getRealSize } from '@/common/utils';
import { Bridge } from '@/common/bridge';
import { SafeAreaView } from 'react-native-safe-area-context';

interface InvalidPopProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (reason: string) => void;
}
/**
 * 通用选择弹窗组件
 * 支持单选/多选模式，包含搜索功能和完整的UI交互
 */
const InvalidPop: React.FC<InvalidPopProps> = ({
  visible,
  onCancel,
  onConfirm,
}) => {
  const [reason, setReason] = useState('');
  /**
   * 取消操作
   */
  const handleCancel = useCallback(() => {
    onCancel?.();
    setReason('');
  }, [onCancel]);

  /**
   * 确认操作
   */
  const handleConfirm = useCallback(() => {
    if (!reason) {
      Bridge.showToast('请输入作废原因');
      return;
    }
    onConfirm?.(reason);
    onCancel?.();
    setReason('');
  }, [onConfirm, reason]);

  return (
    <Modal
      isVisible={visible}
      onBackdropPress={handleCancel}
      animationIn='slideInUp'
      animationOut='slideOutDown'
      style={styles.modal}
      useNativeDriver={true}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <SafeAreaView style={styles.modalContent} edges={['bottom']}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>作废回访</Text>
            <TouchableOpacity
              activeOpacity={1}
              style={styles.modalHeaderButton}
              onPress={handleCancel}
            >
              <Image
                source={{
                  uri: 'https://static.soyoung.com/sy-design/bzsokyai5osd1753416667708.png',
                }}
                style={styles.closeButton}
              />
            </TouchableOpacity>
          </View>
          <View style={styles.modalBody}>
            <TextInput
              style={styles.modalBodyInput}
              placeholder='请输入作废原因'
              value={reason}
              onChangeText={setReason}
            />
          </View>
          <View style={styles.modalFooter}>
            <TouchableOpacity
              style={{
                ...styles.modalFooterButton,
                ...styles.modalFooterButtonCancel,
              }}
              activeOpacity={1}
              onPress={handleCancel}
            >
              <Text style={styles.modalFooterButtonText}>取消</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={{
                ...styles.modalFooterButton,
                ...styles.modalFooterButtonConfirm,
              }}
              activeOpacity={1}
              onPress={handleConfirm}
            >
              <Text
                style={{
                  ...styles.modalFooterButtonText,
                  color: '#fff',
                }}
              >
                确定
              </Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    height: getRealSize(52),
  },
  modalHeaderButton: {
    position: 'absolute',
    right: getRealSize(15),
    top: getRealSize(16),
  },
  closeButton: {
    width: getRealSize(20),
    height: getRealSize(20),
  },
  modalTitle: {
    fontSize: getRealSize(17),
    color: '#030303',
    fontWeight: '500',
  },
  modalBody: {
    marginBottom: getRealSize(10),
    flexDirection: 'row',
    paddingHorizontal: getRealSize(15),
    alignItems: 'center',
  },
  modalBodyText: {
    fontSize: getRealSize(14),
    color: '#333',
  },
  modalBodyInput: {
    borderWidth: getRealSize(1),
    borderColor: '#ccc',
    borderRadius: getRealSize(5),
    flex: 1,
    height: getRealSize(38),
    fontSize: getRealSize(13),
    padding: 0,
    paddingHorizontal: getRealSize(10),
  },
  modalFooter: {
    flexDirection: 'row',
    height: getRealSize(62),
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalFooterButton: {
    width: getRealSize(155),
    height: getRealSize(40),
    borderWidth: getRealSize(1),
    borderColor: '#333',
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalFooterButtonText: {
    fontSize: getRealSize(13),
    color: '#333333',
    fontWeight: '500',
  },
  modalFooterButtonCancel: {
    borderColor: '#333',
    marginRight: getRealSize(15),
  },
  modalFooterButtonConfirm: {
    backgroundColor: '#333',
  },
});

export default InvalidPop;
