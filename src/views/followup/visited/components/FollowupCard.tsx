import React from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native';
import { getRealSize } from '../../../../common/utils';

interface FollowupCardProps {
  item: {
    customer_info: {
      base: {
        youxiang_uid: string;
        customer_id: string;
        customer_id_str: string;
        realname: string;
        avatar?: string;
        gender: number; // 1=男, 2=女
        age: number;
        mobile: string;
      };
      belong?: {
        join_c: boolean; // 是否微信客户
      };
      tag?: {
        dynamic_tag_list?: Array<{
          name: string;
          tag_color?: string;
        }>;
      };
    };
    followup_plan_status: number; // 1=已回访, 2=待回访, 4=已超时
    followup_plan_list: Array<{
      id: string;
      followup_type_desc: string;
      followup_result_desc?: string;
      satisfied_yn_desc?: string;
      followup_record?: string;
      followup_outline?: string;
      plan_date: string;
      status: number; // 1,4=active按钮, 2=普通按钮
      btn_name: string;
    }>;
  };
  onCardClick?: (id: string) => void;
}

// 十六进制颜色转rgba
const hexToRgb = (hex: string, alpha: number = 1): string => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  if (result) {
    const r = parseInt(result[1], 16);
    const g = parseInt(result[2], 16);
    const b = parseInt(result[3], 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  }
  return `rgba(0, 0, 0, ${alpha})`;
};

const FollowupCard: React.FC<FollowupCardProps> = ({ item, onCardClick }) => {
  // 状态图标映射
  const statusMap: Record<number, React.ReactNode> = {
    1: (
      <View
        style={{
          backgroundColor: '#F1F1F5',
          ...styles.cardIconWrapper,
        }}
      >
        <Text
          style={{
            color: '#777',
            ...styles.cardIconText,
          }}
        >
          已回访
        </Text>
      </View>
    ),
    2: (
      <View
        style={{
          backgroundColor: '#E6F7F3',
          ...styles.cardIconWrapper,
        }}
      >
        <Text
          style={{
            color: '#00A077',
            ...styles.cardIconText,
          }}
        >
          待回访
        </Text>
      </View>
    ),
    4: (
      <View
        style={{
          backgroundColor: '#FFECEC',
          ...styles.cardIconWrapper,
        }}
      >
        <Text
          style={{
            color: '#FF4040',
            ...styles.cardIconText,
          }}
        >
          已超时
        </Text>
      </View>
    ),
  };

  const handleFollowupClick = (planId: string) => {
    onCardClick?.(planId);
  };
  return (
    <View style={styles.card}>
      {/* 状态图标 */}
      <View style={styles.cardIcon}>
        {statusMap[item.followup_plan_status]}
      </View>

      {/* 卡片头部 */}
      <View style={styles.cardHead}>
        {/* 用户头像 */}
        {item.customer_info.base.avatar ? (
          <View style={styles.realAvatar}>
            <Image
              style={styles.realAvatarImg}
              source={{ uri: item.customer_info.base.avatar }}
            />
          </View>
        ) : (
          <View style={styles.basicAvatar}>
            <Text style={styles.basicAvatarText}>
              {item.customer_info.base.realname.slice(0, 1)}
            </Text>
          </View>
        )}

        {/* 用户信息 */}
        <View style={styles.cardInfo}>
          {/* 标题行：姓名、性别、年龄 */}
          <View style={styles.titleWrapper}>
            <Text style={styles.title} numberOfLines={1}>
              {item.customer_info.base.realname}
            </Text>
            <Text style={styles.subtitle}>
              {['男', '女'][item.customer_info.base.gender - 1]}
            </Text>
            <Text style={styles.divide}>|</Text>
            <Text style={styles.subtitle}>{item.customer_info.base.age}岁</Text>
          </View>

          {/* 副标题行：手机号、微信状态 */}
          <View style={styles.subtitleWrapper}>
            <Text style={styles.subtitle}>
              手机号：{item.customer_info.base.mobile}
            </Text>
            {item.customer_info.belong?.join_c ? (
              <Text style={styles.wxText}>@微信</Text>
            ) : null}
          </View>

          {/* 动态标签 */}
          {item.customer_info.tag?.dynamic_tag_list &&
          item.customer_info.tag.dynamic_tag_list.length > 0 ? (
            <View style={styles.tagWrapper}>
              {item.customer_info.tag.dynamic_tag_list.map((tag, index) => (
                <Text
                  key={index}
                  style={[
                    styles.tag,
                    tag.tag_color
                      ? {
                          backgroundColor: hexToRgb(tag.tag_color, 0.1),
                          color: tag.tag_color,
                          borderColor: hexToRgb(tag.tag_color, 0.4),
                          borderWidth: 1,
                        }
                      : {},
                  ]}
                >
                  {tag.name}
                </Text>
              ))}
            </View>
          ) : null}
        </View>
      </View>

      {/* 回访计划列表 */}
      {item.followup_plan_list?.map(plan => (
        <TouchableOpacity
          key={plan.id}
          style={styles.cardDetail}
          onPress={() => handleFollowupClick(plan.id)}
          activeOpacity={0.7}
        >
          <View style={styles.detailBox}>
            <View style={styles.detailTitleWrapper}>
              <Text style={styles.detailTitle}>
                回访类型：{plan.followup_type_desc}
              </Text>
              <Text style={styles.detailStatus}>
                {[plan.followup_result_desc, plan.satisfied_yn_desc]
                  .filter(desc => desc)
                  .map((desc, index, array) => (
                    <Text key={index}>
                      {desc}
                      {index < array.length - 1 ? <Text>&</Text> : null}
                    </Text>
                  ))}
              </Text>
            </View>

            <Text style={styles.detailText}>
              {item.followup_plan_status === 1 ? '回访情况：' : '回访提纲：'}
              {item.followup_plan_status === 1
                ? plan.followup_record || ''
                : plan.followup_outline || ''}
            </Text>
          </View>

          {/* 底部操作区域 */}
          <View style={styles.detailFooter}>
            <Text style={styles.detailTime}>计划时间：{plan.plan_date}</Text>

            {[1, 4].includes(plan.status) ? (
              <View style={[styles.detailButton, styles.detailButtonActive]}>
                <Text style={styles.detailButtonTextActive}>
                  {plan.btn_name}
                </Text>
              </View>
            ) : null}

            {[2].includes(plan.status) ? (
              <View style={styles.detailButton}>
                <Text style={styles.detailButtonText}>{plan.btn_name}</Text>
              </View>
            ) : null}
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#ffffff',
    padding: getRealSize(15),
    position: 'relative',
    marginBottom: getRealSize(10),
  },
  cardIcon: {
    width: getRealSize(56),
    height: getRealSize(24),
    position: 'absolute',
    top: 0,
    right: 0,
    zIndex: 10,
  },
  cardIconWrapper: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cardIconText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(12),
    letterSpacing: 0,
    textAlign: 'right',
    fontWeight: '500',
  },
  cardHead: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  realAvatar: {
    width: getRealSize(40),
    height: getRealSize(40),
    borderRadius: getRealSize(20),
    flexShrink: 0,
    overflow: 'hidden',
  },
  realAvatarImg: {
    width: getRealSize(40),
    height: getRealSize(40),
    borderRadius: getRealSize(20),
  },
  basicAvatar: {
    width: getRealSize(40),
    height: getRealSize(40),
    borderRadius: getRealSize(20),
    backgroundColor: '#00AB84',
    alignItems: 'center',
    justifyContent: 'center',
    flexShrink: 0,
  },
  basicAvatarText: {
    fontFamily: 'PingFangSC-Medium',
    fontWeight: '500',
    fontSize: getRealSize(16),
    color: '#ffffff',
    letterSpacing: 0,
  },
  cardInfo: {
    flex: 1,
    marginLeft: getRealSize(13),
  },
  titleWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    height: getRealSize(20),
    lineHeight: getRealSize(20),
    marginBottom: getRealSize(5),
  },
  subtitleWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    height: getRealSize(16),
    lineHeight: getRealSize(16),
    marginBottom: getRealSize(5),
  },
  tagWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  title: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(14),
    color: '#333333',
    letterSpacing: 0,
    fontWeight: '500',
    maxWidth: getRealSize(180),
    marginRight: getRealSize(5),
  },
  subtitle: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(11),
    color: '#777777',
    letterSpacing: 0,
    fontWeight: '400',
  },
  divide: {
    color: '#e5e5e5',
    fontSize: getRealSize(8),
    marginHorizontal: getRealSize(5),
  },
  wxText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(11),
    color: '#00c535',
    letterSpacing: 0,
    fontWeight: '400',
    marginLeft: getRealSize(2),
  },
  tag: {
    height: getRealSize(18),
    lineHeight: getRealSize(18),
    paddingHorizontal: getRealSize(5),
    backgroundColor: '#f5f5f5',
    borderRadius: getRealSize(4),
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(10),
    color: '#555555',
    fontWeight: '400',
    marginRight: getRealSize(5),
    textAlignVertical: 'center',
  },
  cardDetail: {
    marginTop: getRealSize(12),
  },
  detailBox: {
    backgroundColor: '#F8F8F8',
    padding: getRealSize(10),
  },
  detailTitleWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  detailTitle: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(13),
    color: '#333333',
    letterSpacing: 0,
    fontWeight: '500',
  },
  detailStatus: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#999999',
    letterSpacing: 0,
    fontWeight: '400',
  },
  detailText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#333333',
    letterSpacing: 0,
    fontWeight: '400',
    lineHeight: getRealSize(18),
    marginTop: getRealSize(5),
    // wordBreak: 'break-all',
  },
  detailFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: getRealSize(32),
    marginTop: getRealSize(10),
  },
  detailTime: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(11),
    color: '#777777',
    letterSpacing: 0,
    fontWeight: '400',
  },
  detailButton: {
    width: getRealSize(75),
    height: getRealSize(32),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: 'rgba(222, 222, 222, 0.8)',
  },
  detailButtonActive: {
    backgroundColor: '#333',
    borderColor: '#333',
  },
  detailButtonText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#161616',
    fontWeight: '500',
  },
  detailButtonTextActive: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    lineHeight: getRealSize(18),
    color: '#ffffff',
    fontWeight: '500',
  },
});

export default FollowupCard;
