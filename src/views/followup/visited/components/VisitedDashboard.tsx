import React from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { Bridge } from '../../../../common/bridge';
import { getRealSize } from '@/common/utils';
import { js2Native } from '@/common/syNativeModules';
interface StatData {
  todayPlanNum: number;
  todayFinishPlanNum: number;
  todayFinishRate: number;
  lastDaysOutTimePlanNum: number;
  lastDaysFinishPlanNum: number;
  lastDaysOnTimeFinishRate: number;
}

interface VisitedDashboardProps {
  loading?: boolean;
  statData: StatData;
}

const { width } = Dimensions.get('window');

const VisitedDashboard: React.FC<VisitedDashboardProps> = ({
  statData,
  loading,
}) => {
  const dashboardItems = [
    {
      key: 'todayPlan',
      value: statData.todayPlanNum,
      label: '当日计划数',
      helpText: '计划日期=选中日期的计划数量，排除作废',
    },
    {
      key: 'todayFinish',
      value: statData.todayFinishPlanNum,
      label: '当日已回访',
      helpText: '计划日期=选中日期 且 完成日期=选中日期的计划数，排除作废',
    },
    {
      key: 'todayRate',
      value: statData.todayFinishRate,
      label: '当日完成率',
      suffix: '%',
      helpText: '当日已回访 / 当日计划数 的比值',
    },
    {
      key: 'weekPlan',
      value: statData.lastDaysOutTimePlanNum,
      label: '当周计划数',
      helpText:
        '1. 选中日期为本周时：计划日期 在 周一至今日的计划数量，排除作废 \n 2. 选中日期非本周时：计划日期在 选中日期当周的周一至周日的计划数',
    },
    {
      key: 'weekFinish',
      value: statData.lastDaysFinishPlanNum,
      label: '当周已回访',
      helpText:
        '1. 选中日期为本周时：计划日期在 周一至今日，且完成日期为 周一至今日的计划数，排除作废 \n 2.选中日期非本周时：计划日期在选中日期当周的周一至周日，且完成日期在周一至周日的计划数',
    },
    {
      key: 'weekRate',
      value: statData.lastDaysOnTimeFinishRate,
      label: '当周完成率',
      suffix: '%',
      helpText: '当周已回访 / 当周计划数 的比值',
    },
  ];

  const handleItemClick = (item: any) => {
    const callbackId = Bridge.getUuid();
    js2Native(
      'showToast',
      {
        seqId: callbackId,
        request: {
          toast: item.helpText,
          lines: 0,
        },
      },
      () => {}
    );
  };

  return (
    <View style={styles.visitedDashboardContainer}>
      {/* {loading ? (
        <SkeletonItem style={styles.visitedDashboard} />
      ) : (
       
      )} */}
      <View style={styles.visitedDashboard}>
        {dashboardItems.map(item => (
          <TouchableOpacity
            key={item.key}
            style={styles.dashboardItem}
            onPress={() => handleItemClick(item)}
            activeOpacity={0.8}
          >
            <View style={styles.itemContent}>
              <View style={styles.itemNum}>
                <Text style={styles.itemNumText}>{item.value}</Text>
                {item.suffix ? (
                  <Text style={styles.itemSuffix}>{item.suffix}</Text>
                ) : null}
              </View>
              <View style={styles.itemText}>
                <Text style={styles.itemTextLabel}>{item.label}</Text>
                <Image
                  style={styles.itemIcon}
                  resizeMode='contain'
                  source={{
                    uri: 'https://static.soyoung.com/sy-pre/question-1752484200633.png',
                  }}
                />
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  visitedDashboardContainer: {
    backgroundColor: '#fff',
    padding: getRealSize(15),
  },
  visitedDashboard: {
    height: getRealSize(130),
    paddingVertical: getRealSize(5),
    paddingHorizontal: 0,
    backgroundColor: '#a9ea6a',
    flexDirection: 'row',
    flexWrap: 'wrap',
    position: 'relative',
  },
  dashboardItem: {
    width: '33.33%',
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemContent: {
    alignItems: 'center',
  },
  itemNum: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemNumText: {
    fontSize: getRealSize(22),
    color: '#030303',
    fontWeight: '700',
  },
  itemSuffix: {
    fontSize: getRealSize(14),
    marginLeft: getRealSize(5),
    color: '#030303',
  },
  itemText: {
    opacity: 0.8,
    alignItems: 'center',
    marginTop: getRealSize(4),
    height: getRealSize(14),
    flexDirection: 'row',
  },
  itemTextLabel: {
    fontWeight: '400',
    fontSize: getRealSize(10),
    lineHeight: getRealSize(14),
    color: '#030303',
    opacity: 0.8,
  },
  itemIcon: {
    width: getRealSize(10),
    height: getRealSize(10),
    marginLeft: getRealSize(2),
  },
});

export default VisitedDashboard;
