import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TextInput,
  Image,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import Modal from 'react-native-modal';
import { getRealSize } from '@/common/utils';
import { Bridge } from '@/common/bridge';
import { SafeAreaView } from 'react-native-safe-area-context';

interface WxModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (reason: string) => void;
  wxText: string;
}
/**
 * 通用选择弹窗组件
 * 支持单选/多选模式，包含搜索功能和完整的UI交互
 */
const WxModal: React.FC<WxModalProps> = ({
  visible,
  onCancel,
  onConfirm,
  wxText,
}) => {
  const [text, setText] = useState(wxText);
  /**
   * 取消操作
   */
  const handleCancel = useCallback(() => {
    onCancel?.();
  }, [onCancel]);

  /**
   * 确认操作
   */
  const handleConfirm = useCallback(() => {
    if (!text) {
      Bridge.showToast('请输入回访内容');
      return;
    }
    onConfirm?.(text);
    onCancel?.();
    setText('');
  }, [onConfirm, text]);

  return (
    <Modal
      isVisible={visible}
      onBackdropPress={handleCancel}
      animationIn='slideInUp'
      animationOut='slideOutDown'
      style={styles.modal}
    >
      <View style={styles.modalContent}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>确认回访内容</Text>
          <TouchableOpacity style={styles.modalHeaderButton}>
            <Image
              source={{
                uri: 'https://static.soyoung.com/sy-design/bzsokyai5osd1753416667708.png',
              }}
              style={styles.closeButton}
            />
          </TouchableOpacity>
        </View>
        <View style={styles.modalBody}>
          <View style={styles.modalBodyTextContainer}>
            <TextInput
              style={styles.modalBodyText}
              multiline
              maxLength={1000}
              placeholder='请输入回访内容'
              placeholderTextColor='#AAABB3'
              value={text}
              onChangeText={setText}
            />
            <Text style={styles.textLength}>{text.length}/1000</Text>
          </View>
        </View>
        <View style={styles.modalFooter}>
          <TouchableOpacity
            style={styles.modalFooterButton}
            onPress={handleConfirm}
          >
            <Image
              source={{
                uri: 'https://static.soyoung.com/sy-pre/37t94ct7aupg9-1712736600705.png',
              }}
              style={styles.modalFooterButtonIcon}
            />
            <Text style={styles.modalFooterButtonText}>发送并完成</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    height: getRealSize(52),
  },
  modalHeaderButton: {
    position: 'absolute',
    right: getRealSize(15),
    top: getRealSize(16),
  },
  closeButton: {
    width: getRealSize(20),
    height: getRealSize(20),
  },
  modalTitle: {
    fontSize: getRealSize(17),
    color: '#030303',
    fontWeight: '500',
  },
  modalBody: {
    marginBottom: getRealSize(10),
    flexDirection: 'row',
    paddingHorizontal: getRealSize(15),
  },
  modalBodyTextContainer: {
    backgroundColor: '#f6f9f9',
    borderRadius: getRealSize(4),
    marginTop: getRealSize(15),
    padding: getRealSize(10),
    height: getRealSize(140),
    position: 'relative',
    flex: 1,
    paddingBottom: getRealSize(25),
  },
  modalBodyText: {
    flex: 1,
    fontSize: getRealSize(13),
    color: '#333333',
    textAlignVertical: 'top',
    lineHeight: getRealSize(18),
  },
  textLength: {
    fontSize: getRealSize(12),
    color: '#aaabb3',
    position: 'absolute',
    right: getRealSize(10),
    bottom: getRealSize(5),
  },
  modalFooter: {
    flexDirection: 'row',
    height: getRealSize(62),
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalFooterButton: {
    width: getRealSize(255),
    height: getRealSize(40),
    borderWidth: getRealSize(1),
    borderColor: '#333',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  modalFooterButtonIcon: {
    width: getRealSize(16),
    height: getRealSize(16),
    marginRight: getRealSize(5),
  },
  modalFooterButtonText: {
    fontSize: getRealSize(13),
    color: '#333333',
    fontWeight: '500',
  },
});

export default WxModal;
