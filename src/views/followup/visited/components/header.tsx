import React, { memo, useEffect } from 'react';
import {
  View,
  ImageBackground,
  StyleSheet,
  Text,
  StatusBar,
  Platform,
  TouchableOpacity,
} from 'react-native';
import { getRealSize } from '../../../../common/utils';
import { ATrack } from '@soyoung/react-native-container';
import { back } from '@soyoung/react-native-base';
import { useNavigation } from '@react-navigation/native';

interface HeaderProps {
  title: string;
  bgColor?: string;
  hideBack?: boolean;
  rightSlot?: JSX.Element;
  // 新增回访计划相关props
  followupTab?: 0 | 1;
  permissionList?: string[];
  onFollowupTabChange?: (tab: 0 | 1) => void;
}

export default memo((props: HeaderProps) => {
  const navigation = useNavigation();
  const {
    title,
    bgColor = '#fff', // 默认绿色背景
    hideBack = false,
    rightSlot,
    followupTab = 0,
    permissionList = [],
    onFollowupTabChange,
  } = props;

  useEffect(() => {
    if (Platform.OS === 'ios') {
      return;
    }
    const unsubscribe = navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
      StatusBar.setBackgroundColor(bgColor);
    });

    return unsubscribe;
  }, [navigation, bgColor]);

  // 处理Tab切换
  const handleTabPress = (tab: 0 | 1) => {
    onFollowupTabChange?.(tab);
  };

  return (
    <View
      style={{
        ...styles.headerMain,
        backgroundColor: bgColor,
      }}
    >
      <View style={styles.container}>
        {!hideBack ? (
          <ATrack style={styles.backButton} onPress={() => back()}>
            <ImageBackground
              source={{
                uri: 'https://static.soyoung.com/sy-design/3cj8rc3ipek931726715721972.png',
              }}
              style={styles.backButtonImage}
            />
          </ATrack>
        ) : null}

        {/* 回访计划切换Tab */}
        {permissionList.includes('followup:mine') ||
        permissionList.includes('followup:all') ? (
          <View style={styles.tabContainer}>
            {permissionList.includes('followup:mine') ? (
              <TouchableOpacity
                activeOpacity={1}
                style={styles.tabItem}
                onPress={() => handleTabPress(0)}
              >
                <Text
                  style={{
                    ...styles.tabText,
                    color: followupTab === 0 ? '#030303' : '#646464',
                    fontWeight: followupTab === 0 ? '600' : '400',
                  }}
                >
                  我的计划
                </Text>
                <View
                  style={{
                    ...styles.tabUnderline,
                    backgroundColor: followupTab === 0 ? '#000' : '#fff',
                  }}
                />
              </TouchableOpacity>
            ) : null}
            {permissionList.includes('followup:all') ? (
              <TouchableOpacity
                activeOpacity={1}
                style={styles.tabItem}
                onPress={() => handleTabPress(1)}
              >
                <Text
                  style={{
                    ...styles.tabText,
                    color: followupTab === 1 ? '#030303' : '#646464',
                    fontWeight: followupTab === 1 ? '600' : '400',
                  }}
                >
                  门店计划
                </Text>
                <View
                  style={{
                    ...styles.tabUnderline,
                    backgroundColor: followupTab === 1 ? '#000' : '#fff',
                  }}
                />
              </TouchableOpacity>
            ) : null}
          </View>
        ) : null}

        {rightSlot ? <View style={styles.rightSlot}>{rightSlot}</View> : null}
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  headerMain: {
    width: '100%',
    overflow: 'hidden',
  },
  container: {
    height: getRealSize(44),
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  backButton: {
    position: 'absolute',
    left: 0,
    top: 0,
    zIndex: 10,
  },
  backButtonImage: {
    width: getRealSize(44),
    height: getRealSize(44),
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  tabItem: {
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingHorizontal: getRealSize(20),
    height: getRealSize(44),
    position: 'relative',
  },
  tabText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(14),
    color: '#646464',
    letterSpacing: 0,
    fontWeight: '500',
    lineHeight: getRealSize(20),
  },
  tabUnderline: {
    width: getRealSize(20),
    height: getRealSize(2),
    marginTop: getRealSize(10),
    backgroundColor: '#000000',
  },
  rightSlot: {
    position: 'absolute',
    right: getRealSize(15),
    top: 0,
    height: getRealSize(44),
    justifyContent: 'center',
    alignItems: 'center',
  },
});
