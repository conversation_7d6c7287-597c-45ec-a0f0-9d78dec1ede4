import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  StatusBar,
  Platform,
  Modal,
} from 'react-native';
// import { Picker } from '@react-native-picker/picker';
import { SafeAreaView } from 'react-native-safe-area-context';
import api, { FetchModule } from '@/common/api';
import { getRealSize } from '@/common/utils';
import Header from '@/components/header';
import ButtonSelect from '../../components/ButtonSelect';
import CommonSelect, { ActiveItem } from '@/components/CommonSelect';

// 选项类型定义
interface Option {
  label: string;
  value: string | number;
}

interface TemplateOption {
  option_type: string;
  list: Option[];
}

// 表单数据类型
interface FormData {
  followup_type?: string | number;
  followup_type_desc?: string;
  followup_outline: string;
  followup_result?: string | number;
  followup_result_desc?: string;
  satisfied_yn?: string | number;
  satisfied_yn_desc?: string;
  followup_way?: string | number;
  followup_way_desc?: string;
}

// 组件Props类型
interface FollowupEditProps {
  visible: boolean;
  editData: FormData;
  onSave: (data: FormData) => void;
  onCancel: () => void;
}

const FollowupEdit: React.FC<FollowupEditProps> = ({
  visible,
  editData,
  onSave,
  onCancel,
}) => {
  const [formData, setFormData] = useState<FormData>({
    ...editData,
    followup_outline: editData.followup_outline || '',
  });
  const [templateOptions, setTemplateOptions] = useState<TemplateOption[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (visible) {
      loadTemplateOptions();
      // 重置表单数据
      setFormData({
        ...editData,
        followup_outline: editData.followup_outline || '',
      });
    }
  }, [visible, editData]);

  // 加载模板选项数据
  const loadTemplateOptions = async () => {
    try {
      const response = await api.pagefetch({
        path: '/chain-wxapp/v1/followup-plan/optionPlan',
        params: {},
        method: FetchModule.Method.POST,
      });
      if (response.errorCode === 0 && response.responseData) {
        setTemplateOptions(response.responseData);
      } else {
        Alert.alert('错误', response.errorMsg || '加载选项失败');
      }
    } catch (error) {
      Alert.alert('网络异常', '请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 使用 useMemo 缓存选项数据，避免重复计算
  const followupTypeOptions = useMemo(() => {
    const option = templateOptions.find(
      item => item.option_type === 'followup_type'
    );
    return option?.list || [];
  }, [templateOptions]);

  const followupResultOptions = useMemo(() => {
    const option = templateOptions.find(
      item => item.option_type === 'followup_result'
    );
    return option?.list || [];
  }, [templateOptions]);

  const satisfiedYnOptions = useMemo(() => {
    const option = templateOptions.find(
      item => item.option_type === 'satisfied_yn'
    );
    return option?.list || [];
  }, [templateOptions]);

  const followupWayOptions = useMemo(() => {
    const option = templateOptions.find(
      item => item.option_type === 'followup_way'
    );
    return option?.list || [];
  }, [templateOptions]);

  // 获取回访类型描述
  const getFollowupTypeDesc = (): string => {
    const option = followupTypeOptions.find(
      item => item.value === formData.followup_type
    );
    return option?.label || '';
  };

  // 处理回访类型变化
  const handleFollowupTypeChange = (value: ActiveItem) => {
    setFormData({
      ...formData,
      followup_type: value.id,
      followup_type_desc: value.name,
    });
  };

  // 处理回访提纲变化
  const handleOutlineChange = (text: string) => {
    setFormData({
      ...formData,
      followup_outline: text,
    });
  };

  // 处理回访结果变化
  const handleFollowupResultChange = (
    value: string | number | (string | number)[] | null
  ) => {
    const singleValue = Array.isArray(value) ? value[0] : value;
    const selectedOption = followupResultOptions.find(
      item => item.value === singleValue
    );

    setFormData({
      ...formData,
      followup_result: singleValue as string | number,
      followup_result_desc: selectedOption?.label || '',
    });
  };

  // 处理是否满意变化
  const handleSatisfiedChange = (
    value: string | number | (string | number)[] | null
  ) => {
    const singleValue = Array.isArray(value) ? value[0] : value;
    const selectedOption = satisfiedYnOptions.find(
      item => item.value === singleValue
    );

    setFormData({
      ...formData,
      satisfied_yn: singleValue as string | number,
      satisfied_yn_desc: selectedOption?.label || '',
    });
  };

  // 处理回访方式变化
  const handleFollowupWayChange = (
    value: string | number | (string | number)[] | null
  ) => {
    const singleValue = Array.isArray(value) ? value[0] : value;
    const selectedOption = followupWayOptions.find(
      item => item.value === singleValue
    );

    setFormData({
      ...formData,
      followup_way: singleValue as string | number,
      followup_way_desc: selectedOption?.label || '',
    });
  };

  // 保存数据
  const handleSave = () => {
    onSave(formData);
  };

  // 取消编辑
  const handleCancel = () => {
    onCancel();
  };

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType='slide'
      transparent={false}
      statusBarTranslucent={true}
      onRequestClose={handleCancel}
    >
      <SafeAreaView style={styles.container} edges={['bottom']}>
        <Header
          title='编辑回访'
          bgColor='#fff'
          hideBack={true}
          rightSlot={
            <TouchableOpacity
              activeOpacity={1}
              onPress={handleCancel}
              style={styles.closeButton}
            >
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          }
        />

        {loading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>加载中...</Text>
          </View>
        ) : (
          <>
            <ScrollView
              style={styles.scrollView}
              showsVerticalScrollIndicator={false}
            >
              {/* 回访类型 */}
              <Text style={styles.title}>回访类型</Text>
              <View style={styles.selectContainer}>
                <CommonSelect
                  active={{
                    id: formData.followup_type,
                    name: getFollowupTypeDesc(),
                  }}
                  options={followupTypeOptions}
                  title='回访类型'
                  multiple={false}
                  onChange={value =>
                    handleFollowupTypeChange(value as ActiveItem)
                  }
                />
              </View>
              {/* 回访提纲 */}
              <Text style={styles.title}>回访提纲</Text>
              <View style={styles.textareaContainer}>
                <TextInput
                  style={styles.textarea}
                  multiline
                  maxLength={1000}
                  placeholder='请输入回访提纲'
                  placeholderTextColor='#AAABB3'
                  value={formData.followup_outline}
                  onChangeText={handleOutlineChange}
                  textAlignVertical='top'
                />
                <Text style={styles.textLength}>
                  {formData.followup_outline.length}/1000
                </Text>
              </View>

              {/* 回访结果 */}
              <Text style={styles.title}>回访结果</Text>
              <View style={styles.selectContainer}>
                <ButtonSelect
                  options={followupResultOptions}
                  active={formData.followup_result || ''}
                  multiple={false}
                  noCancle={true}
                  onChange={handleFollowupResultChange}
                />
              </View>

              {/* 是否满意 */}
              <Text style={styles.title}>是否满意</Text>
              <View style={styles.selectContainer}>
                <ButtonSelect
                  options={satisfiedYnOptions}
                  active={formData.satisfied_yn || ''}
                  multiple={false}
                  noCancle={true}
                  onChange={handleSatisfiedChange}
                />
              </View>

              {/* 回访方式 */}
              <Text style={styles.title}>回访方式</Text>
              <View style={styles.selectContainer}>
                <ButtonSelect
                  options={followupWayOptions}
                  active={formData.followup_way || ''}
                  multiple={false}
                  noCancle={true}
                  onChange={handleFollowupWayChange}
                />
              </View>

              <View style={styles.bottomSpacing} />
            </ScrollView>

            {/* 底部按钮 */}
            <View style={styles.footer}>
              <TouchableOpacity
                activeOpacity={1}
                style={styles.cancelButton}
                onPress={handleCancel}
              >
                <Text style={styles.cancelButtonText}>取消</Text>
              </TouchableOpacity>
              <TouchableOpacity
                activeOpacity={1}
                style={styles.saveButton}
                onPress={handleSave}
              >
                <Text style={styles.saveButtonText}>保存</Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  closeButton: {
    width: getRealSize(44),
    height: getRealSize(44),
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: getRealSize(20),
    color: '#666666',
    fontWeight: '400',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: getRealSize(16),
    color: '#666666',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: getRealSize(15),
    paddingTop: getRealSize(28),
  },
  title: {
    fontSize: getRealSize(13),
    fontWeight: '500',
    color: '#161616',
    marginBottom: getRealSize(15),
  },
  pickerContainer: {
    backgroundColor: '#f5f5f5',
    borderRadius: getRealSize(4),
    height: getRealSize(38),
    marginBottom: getRealSize(31),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: getRealSize(16),
  },
  pickerText: {
    fontSize: getRealSize(13),
    color: '#161616',
    fontWeight: '400',
  },
  pickerArrow: {
    fontSize: getRealSize(9),
    color: '#161616',
  },
  textareaContainer: {
    backgroundColor: '#f6f9f9',
    borderRadius: getRealSize(4),
    height: getRealSize(160),
    padding: getRealSize(10),
    marginBottom: getRealSize(20),
    position: 'relative',
  },
  textarea: {
    flex: 1,
    fontSize: getRealSize(13),
    color: '#161616',
    textAlignVertical: 'top',
    fontWeight: '400',
  },
  textLength: {
    position: 'absolute',
    bottom: getRealSize(10),
    right: getRealSize(10),
    fontSize: getRealSize(12),
    color: '#aaabb3',
    fontWeight: '400',
  },
  selectContainer: {
    marginBottom: getRealSize(20),
  },
  bottomSpacing: {
    height: getRealSize(93),
  },
  footer: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    borderTopWidth: 0.5,
    borderTopColor: '#f0f0f0',
    paddingHorizontal: getRealSize(15),
    paddingTop: getRealSize(10),
    paddingBottom: Platform.OS === 'ios' ? getRealSize(34) : getRealSize(10),
    height: getRealSize(83),
  },
  cancelButton: {
    width: getRealSize(75),
    height: getRealSize(40),
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: getRealSize(10),
  },
  cancelButtonText: {
    fontSize: getRealSize(13),
    fontWeight: '500',
    color: '#666666',
  },
  saveButton: {
    width: getRealSize(260),
    height: getRealSize(40),
    backgroundColor: '#161616',
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: getRealSize(13),
    fontWeight: '500',
    color: '#ffffff',
  },
});

export default FollowupEdit;
