import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import ProjectPopup, {
  ProjectPopupRef,
  ActiveItem,
  Active,
} from '@/components/ProjectPopup';
import { getRealSize } from '@/common/utils';
import { MultiSelectItem } from '@/components/MultiSelect';

/**
 * 项目选择组件
 * 支持单选/多选模式，整合了 SingleSelect、MultiSelect 和 ProjectPopup
 */

export interface ProjectSelectProps {
  /** 当前选中的项目 */
  active?: ActiveItem[];
  /** 选择器标题 */
  title?: string;
  /** 选中值变化回调 */
  onActiveChange?: (active: ActiveItem[]) => void;
  /** 选择变化回调（与 onActiveChange 相同，提供兼容性） */
  onChange?: (active: ActiveItem[]) => void;
}

const ProjectSelect: React.FC<ProjectSelectProps> = ({
  active,
  title = '',
  onActiveChange,
  onChange,
}) => {
  const [compActive, setCompActive] = useState<ActiveItem[]>(() => {
    return Array.isArray(active) && active.length > 0 ? active : [];
  });

  const [openPanel, setOpenPanel] = useState(false);
  const projectPopupRef = useRef<ProjectPopupRef>(null);

  // 监听外部 active 变化并同步到内部状态
  useEffect(() => {
    setCompActive(Array.isArray(active) && active.length > 0 ? active : []);
  }, [active]);

  // 处理打开弹窗
  const handleOpen = useCallback(() => {
    setOpenPanel(true);
    projectPopupRef.current?.open();
  }, []);

  // 处理关闭弹窗
  const handleClose = useCallback(() => {
    setOpenPanel(false);
  }, []);

  // 处理多选模式下的删除操作
  const handleRemove = useCallback(
    (id: number) => {
      const newActive = compActive.filter(
        (item: MultiSelectItem) => item.id !== id
      );
      setCompActive(newActive);

      // 触发回调
      onActiveChange?.(newActive);
      onChange?.(newActive);
    },
    [compActive, onActiveChange, onChange]
  );

  // 处理弹窗确认选择
  const handlePopupChange = useCallback(
    (newActive: Active) => {
      const newActiveItems = newActive as ActiveItem[];
      setCompActive(newActiveItems);

      // 触发回调
      onActiveChange?.(newActiveItems);
      onChange?.(newActiveItems);
    },
    [onActiveChange, onChange]
  );

  return (
    <View>
      <View style={styles.container}>
        {/* 添加按钮 */}
        <TouchableOpacity
          activeOpacity={1}
          style={styles.addButton}
          onPress={handleOpen}
        >
          <Image
            source={{
              uri: 'https://static.soyoung.com/sy-pre/a21xeugzis9k-1713863400669.png',
            }}
            style={styles.addButtonImage}
            resizeMode='contain'
          />
          <Text style={styles.addButtonText}>{title}</Text>
        </TouchableOpacity>

        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.labelContent}
        >
          {/* 已选标签列表 */}
          {compActive.length > 0 ? (
            compActive.map(item => (
              <TouchableOpacity
                key={item.id}
                activeOpacity={1}
                style={styles.selectedItem}
                onPress={() => handleRemove(item.id)}
              >
                <Text style={styles.selectedItemText}>{item.name}</Text>
                {/* <Image
                  source={{
                    uri: 'https://static.soyoung.com/sy-pre/3mzhissd573e2-1712131800686.png',
                  }}
                  style={styles.selectedItemImage}
                  resizeMode='contain'
                /> */}
                <Text style={styles.removeText}>×</Text>
              </TouchableOpacity>
            ))
          ) : (
            <Text style={styles.placeholder}>请选择{title}</Text>
          )}
        </ScrollView>
      </View>

      {/* 项目选择弹窗 */}
      <ProjectPopup
        ref={projectPopupRef}
        active={compActive}
        title={title}
        multiple={true}
        onActiveChange={handlePopupChange}
        onClose={handleClose}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginTop: getRealSize(10), // 20rpx
    alignItems: 'center',
  },
  addButton: {
    height: getRealSize(38), // 76rpx
    backgroundColor: '#F8F8F8',
    paddingHorizontal: getRealSize(10), // 20rpx
    flexDirection: 'row',
    width: getRealSize(85),
    alignItems: 'center',
    borderWidth: getRealSize(1), // 2rpx
    borderColor: 'transparent',
  },
  addButtonImage: {
    width: getRealSize(8), // 16rpx
    height: getRealSize(8), // 16rpx
    marginRight: getRealSize(5), // 10rpx
  },
  addButtonText: {
    fontSize: getRealSize(13), // 26rpx
    color: '#333333',
    fontWeight: '500',
  },
  labelContent: {
    flex: 1,
    marginLeft: getRealSize(10),
  },
  placeholder: {
    fontSize: getRealSize(13),
    color: '#AAABB3',
  },
  selectedItem: {
    backgroundColor: '#EBFBDC',
    paddingVertical: getRealSize(10), // 20rpx
    paddingHorizontal: getRealSize(15), // 30rpx
    marginRight: getRealSize(10), // 20rpx
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: getRealSize(1), // 2rpx
    borderColor: '#61B43E',
  },
  selectedItemText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13), // 26rpx
    color: '#61B43E',
    fontWeight: '400',
  },

  removeText: {
    fontSize: getRealSize(12),
    marginLeft: getRealSize(5),
    color: '#61B43E',
  },
});

export default ProjectSelect;

// 导出类型供外部使用
export type { ActiveItem, Active };
