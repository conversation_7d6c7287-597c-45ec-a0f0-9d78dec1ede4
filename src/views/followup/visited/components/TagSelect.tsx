import React, { useCallback, useRef, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  TextInput,
  ScrollView,
  Image,
  Alert,
} from 'react-native';
import { getRealSize } from '@/common/utils';
import TagPopup, { TagPopupRef } from '@/components/TagPopup';

// 使用TagPopup内部的ActiveItem类型定义，而不是CommonSelect的
interface TagPopupActiveItem {
  tag_id: number;
  name: string;
  tag_color: string;
  select_type: number;
  tag_pid: number;
}

export interface TagItem {
  id: number;
  name: string;
  tag_id?: number;
  tag_name?: string;
}

interface TagSelectProps {
  active: TagItem[];
  title: string;
  type: number; // 1: 客户标签, 2: 过敏标签, 3: 动态标签
  multiple?: boolean;
  onSelectionChange: (items: TagItem[]) => void;
}

const TagSelect: React.FC<TagSelectProps> = ({
  active,
  title,
  type,
  multiple = true,
  onSelectionChange,
}) => {
  const tagPopupRef = useRef<TagPopupRef>(null);

  const [compActive, setCompActive] = useState<number[]>([]);

  // 打开标签弹窗
  const openTagPop = useCallback(() => {
    console.log('openTagPop', active);
    // 修复属性访问问题，支持tag_id和id两种字段名
    const activeIds = active
      .map(item => item.tag_id || item.id)
      .filter(id => id != null);
    setCompActive(activeIds);
    setTimeout(() => {
      tagPopupRef.current?.open(type);
    }, 0);
  }, [active, type]);

  // 处理标签变更 - 改为同步函数
  const handleTagChange = useCallback(
    (data: TagPopupActiveItem | TagPopupActiveItem[]) => {
      try {
        // 将TagPopup的数据格式转换为TagSelect需要的格式
        const dataArray = Array.isArray(data) ? data : [data];
        const newItems: TagItem[] = dataArray.map(item => ({
          id: item.tag_id,
          name: item.name,
          tag_id: item.tag_id,
          tag_name: item.name,
        }));

        onSelectionChange(newItems);

        // 异步处理API调用，不阻塞UI
        setTimeout(async () => {
          try {
            // 这里可以添加API调用逻辑
            console.log('标签更新成功:', newItems);
          } catch (error) {
            console.error('标签更新失败:', error);
            Alert.alert('提示', '标签更新失败，请稍后重试');
          }
        }, 0);
      } catch (error) {
        console.error('标签处理失败:', error);
        Alert.alert('提示', '操作失败，请稍后重试');
      }
    },
    [onSelectionChange]
  );

  // 处理标签弹窗关闭
  const handleTagPopupClose = useCallback(() => {
    // 弹窗关闭时的逻辑
  }, []);

  const removeItem = (id: number) => {
    const newItems = active.filter(item => (item.tag_id || item.id) !== id);
    onSelectionChange(newItems);
  };

  const renderSelectedItems = () => {
    if (active.length === 0) {
      return <Text style={styles.placeholder}>请选择{title}</Text>;
    }

    return (
      <View style={styles.selectedContainer}>
        {active.map(item => (
          <View key={item.id || item.tag_id} style={styles.selectedItem}>
            <Text style={styles.selectedText}>
              {item.name || item.tag_name}
            </Text>
            <TouchableOpacity
              activeOpacity={1}
              onPress={() => removeItem(item.tag_id || item.id)}
              style={styles.removeButton}
            >
              <Text style={styles.removeText}>×</Text>
            </TouchableOpacity>
          </View>
        ))}
      </View>
    );
  };

  return (
    <>
      <View style={styles.container}>
        <TouchableOpacity
          activeOpacity={1}
          style={styles.label}
          onPress={openTagPop}
        >
          <Image
            source={{
              uri: 'https://static.soyoung.com/sy-pre/a21xeugzis9k-1713863400669.png',
            }}
            style={styles.labelIcon}
          />
          <Text style={styles.title}>{title}</Text>
        </TouchableOpacity>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.labelContent}
        >
          {renderSelectedItems()}
        </ScrollView>
      </View>

      <TagPopup
        ref={tagPopupRef}
        active={compActive}
        type={type}
        title={type === 1 ? '客户标签' : '敏感标签'}
        multiple={multiple}
        onChange={handleTagChange}
        onClose={handleTagPopupClose}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: getRealSize(8),
    // padding: getRealSize(15),
    marginTop: getRealSize(10),
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    width: getRealSize(85),
    height: getRealSize(38),
    backgroundColor: '#F8F8F8',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  labelIcon: {
    width: getRealSize(8),
    height: getRealSize(8),
    marginRight: getRealSize(5),
  },
  title: {
    fontSize: getRealSize(13),
    fontWeight: '500',
    color: '#333',
    // marginBottom: getRealSize(8),
  },
  labelContent: {
    flex: 1,
    marginLeft: getRealSize(10),
  },
  content: {
    minHeight: getRealSize(20),
  },
  placeholder: {
    fontSize: getRealSize(13),
    color: '#AAABB3',
  },
  selectedContainer: {
    flexDirection: 'row',
  },
  selectedItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EBFBDC',
    borderWidth: getRealSize(1),
    borderColor: '#61B43E',
    marginRight: getRealSize(10),
    paddingHorizontal: getRealSize(12.5),
    height: getRealSize(38),
    boxSizing: 'border-box',
  },
  selectedText: {
    fontSize: getRealSize(13),
    color: '#61B43E',
    fontWeight: '400',
  },
  removeButton: {
    marginLeft: getRealSize(4),
    width: getRealSize(16),
    height: getRealSize(16),
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeText: {
    fontSize: getRealSize(12),
    color: '#61B43E',
  },
});

export default TagSelect;
