import React, {
  useState,
  useEffect,
  useCallback,
  useRef,
  useMemo,
} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  ScrollView,
  RefreshControl,
  AppState,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import dayjs from 'dayjs';
import Api from '../../../common/api';
import Header from './components/header';
import DateSelect from '../../../components/DateSelect';
import VisitedDashboard from './components/VisitedDashboard';
import FollowupCard from './components/FollowupCard';
import { getRealSize } from '../../../common/utils';
import { jumpReactNativePage } from '../../../common/jumpPage';
import { checkPermission } from '@/common/checkPermission';
import { Bridge } from '@/common/bridge';
import { SkeletonItem, VisitedSkeleton } from '../components/Skeleton';

interface StatData {
  todayPlanNum: number;
  todayFinishPlanNum: number;
  todayFinishRate: number;
  lastDaysOutTimePlanNum: number;
  lastDaysFinishPlanNum: number;
  lastDaysOnTimeFinishRate: number;
}

interface PageState {
  // 权限相关
  permissionList: string[];
  tenantId: string;

  // 回访数据
  followupTab: 0 | 1; // 0: 我的计划, 1: 门店计划
  statData: StatData;

  // 日历相关
  curDate: string;
  startDate: string;
  endDate: string;
  planList: string[];

  // 筛选和列表相关
  tabList: any[];
  currentFilterTab: number;
  followupList: any[];
  currentPage: number;
  hasMore: boolean;
  hasSearch: boolean;
  filterNumber: number;
  filterParams: any;

  // 加载状态
  refreshing: boolean;
  loading: boolean;
  planLoading: boolean;
}

const INITIAL_STAT_DATA: StatData = {
  todayPlanNum: 0,
  todayFinishPlanNum: 0,
  todayFinishRate: 0,
  lastDaysOutTimePlanNum: 0,
  lastDaysFinishPlanNum: 0,
  lastDaysOnTimeFinishRate: 0,
};

const INITIAL_STATE: PageState = {
  permissionList: [],
  tenantId: '',
  followupTab: 0,
  statData: INITIAL_STAT_DATA,
  curDate: dayjs().format('YYYY-MM-DD'),
  startDate: dayjs().subtract(14, 'day').format('YYYY-MM-DD'),
  endDate: dayjs().add(14, 'day').format('YYYY-MM-DD'),
  planList: [],
  tabList: [],
  currentFilterTab: 1,
  followupList: [],
  currentPage: 0,
  hasSearch: false,
  hasMore: false,
  filterNumber: 0,
  filterParams: {},
  refreshing: false,
  loading: false,
  planLoading: false,
};

const urlMap = {
  stat: {
    0: '/chain-wxapp/v1/followup-plan/getSelfFollowupPlanStatNum',
    1: '/chain-wxapp/v1/followup-plan/getTenantFollowupPlanStatNum',
  },
  point: {
    0: '/chain-wxapp/v1/followup-plan/getSelfFollowupPlanNumByDate',
    1: '/chain-wxapp/v1/followup-plan/getTenantFollowupPlanNumByDate',
  },
  tab: {
    0: '/chain-wxapp/v1/followup-plan/getSelfFollowupPlanDataTab',
    1: '/chain-wxapp/v1/followup-plan/getTenantFollowupPlanDataTab',
  },
  list: {
    0: '/chain-wxapp/v1/followup-plan/getSelfFollowupPlanList',
    1: '/chain-wxapp/v1/followup-plan/getTenantFollowupPlanList',
  },
};

const LIMIT = 10;

const VisitedPage: React.FC<{ pageShow: boolean }> = ({ pageShow }) => {
  const scrollViewRef = useRef<ScrollView>(null);
  const previousPageShowRef = useRef<boolean>(false);
  const [state, setState] = useState<PageState>(INITIAL_STATE);
  const followupListRef = useRef<any[]>(INITIAL_STATE.followupList); // 保存最新的followupList

  // 保存上一次的筛选参数，用于比较是否需要刷新
  const previousParamsRef = useRef<{
    filterParams: any;
    filterNumber: number;
    curDate: string;
  } | null>(null);

  // 统一的状态更新函数
  const updateState = useCallback((updates: Partial<PageState>) => {
    setState(prev => {
      const newState = { ...prev, ...updates };
      // 同步更新followupList的ref
      if (updates.followupList !== undefined) {
        followupListRef.current = updates.followupList;
      }
      return newState;
    });
  }, []);

  // 检查筛选参数是否发生变化（不使用 useCallback 避免重复执行）
  const checkFilterParamsChanged = async () => {
    try {
      // 获取当前的筛选参数
      const [storedData, storedNumber, followupCalendarDate] =
        await Promise.all([
          AsyncStorage.getItem('followupFilterData'),
          AsyncStorage.getItem('followupFilterNumber'),
          AsyncStorage.getItem('followupCalendarDate'),
        ]);

      const currentFilterParams = storedData ? JSON.parse(storedData) : {};
      const currentFilterNumber = storedNumber ? parseInt(storedNumber, 10) : 0;
      const currentDate = followupCalendarDate || dayjs().format('YYYY-MM-DD');

      // 如果是第一次访问，直接返回 true
      if (!previousParamsRef.current) {
        previousParamsRef.current = {
          filterParams: currentFilterParams,
          filterNumber: currentFilterNumber,
          curDate: currentDate,
        };
        return true;
      }
      console.log(
        'currentDate',
        currentDate,
        previousParamsRef.current.curDate
      );
      // 比较参数是否发生变化
      const paramsChanged =
        JSON.stringify(previousParamsRef.current.filterParams) !==
          JSON.stringify(currentFilterParams) ||
        previousParamsRef.current.filterNumber !== currentFilterNumber ||
        previousParamsRef.current.curDate !== currentDate;

      // 更新缓存的参数
      if (paramsChanged) {
        previousParamsRef.current = {
          filterParams: currentFilterParams,
          filterNumber: currentFilterNumber,
          curDate: currentDate,
        };
      }

      return paramsChanged;
    } catch (error) {
      console.error('比较筛选参数失败:', error);
      return true; // 出错时默认刷新
    }
  };

  // 构建请求参数的工具函数
  const buildRequestParams = useCallback((filterParams: any) => {
    const params: any = {};
    Object.keys(filterParams).forEach(item => {
      if (filterParams[item] instanceof Object) {
        params[item] = filterParams[item].id;
      } else {
        params[item] = filterParams[item];
      }
    });
    return params;
  }, []);

  // 统一的错误处理
  const handleError = useCallback((error: any, fallbackMessage: string) => {
    console.error(fallbackMessage, error);
    Bridge.showToast(fallbackMessage);
  }, []);

  // 获取统计数据
  const fetchStatData = useCallback(
    async (tab: 0 | 1, date: string) => {
      try {
        const res = await Api.pagefetch({
          isLoading: false,
          path: urlMap.stat[tab],
          params: { date_time: date + ' 00:00:00' },
        });

        if (res.errorCode === 0) {
          const { responseData } = res;
          const newStatData: StatData = {
            todayPlanNum: responseData?.stat_num?.today_plan_num || 0,
            todayFinishPlanNum:
              responseData?.stat_num?.today_finish_plan_num || 0,
            todayFinishRate: responseData?.stat_num?.today_finish_rate || 0,
            lastDaysOutTimePlanNum:
              responseData?.stat_num?.last_days_plan_num || 0,
            lastDaysFinishPlanNum:
              responseData?.stat_num?.last_days_finish_plan_num || 0,
            lastDaysOnTimeFinishRate:
              responseData?.stat_num?.last_days_on_time_finish_rate || 0,
          };
          updateState({ statData: newStatData });
        }
      } catch (error) {
        handleError(error, '获取统计数据失败');
      }
    },
    [updateState, handleError]
  );

  // 获取计划数据
  const fetchPlanData = useCallback(
    async (tab: 0 | 1, startDate: string, endDate: string) => {
      try {
        updateState({ planLoading: true });
        const res = await Api.pagefetch({
          path: urlMap.point[tab],
          isLoading: false,
          params: { start_time: startDate, end_time: endDate },
        });

        if (res.errorCode === 0) {
          const { responseData } = res;
          updateState({
            planList: (responseData || []).map((item: any) => item.date),
            planLoading: false,
          });
        } else {
          updateState({ planList: [], planLoading: false });
        }
      } catch (error) {
        updateState({ planList: [], planLoading: false });
        handleError(error, '获取计划数据失败');
      }
    },
    [updateState, handleError]
  );

  // 获取Tab数据
  const fetchTabData = useCallback(
    async (tab: 0 | 1, date: string, filterParams: any) => {
      try {
        const params = buildRequestParams(filterParams);
        const res = await Api.pagefetch({
          isLoading: false,
          path: urlMap.tab[tab],
          params: { search_date: date, ...params },
        });

        if (res.errorCode === 0) {
          const { responseData } = res;
          const tabList = responseData?.tab_list || [];
          const firstTabType = tabList[0]?.tab_type || 1;
          console.log('tabList', tabList);
          updateState({
            tabList,
            currentFilterTab: firstTabType,
            currentPage: 0,
          });

          return { tabList, firstTabType };
        } else {
          updateState({ tabList: [], followupList: [] });
          return { tabList: [], firstTabType: 1 };
        }
      } catch (error) {
        updateState({ tabList: [] });
        handleError(error, '获取Tab数据失败');
        return { tabList: [], firstTabType: 1 };
      }
    },
    [buildRequestParams, updateState, handleError]
  );

  // 获取列表数据
  const fetchListData = useCallback(
    async (
      tab: 0 | 1,
      page: number,
      tabType: number,
      date: string,
      filterParams: any,
      isLoadMore = false
    ) => {
      // 防重复调用
      if (state.loading && page > 0) return;

      try {
        updateState({ hasSearch: true });
        const params = buildRequestParams(filterParams);

        const res = await Api.pagefetch({
          path: urlMap.list[tab],
          isLoading: false,
          params: {
            page,
            limit: LIMIT,
            tab_type: tabType,
            search_date: date,
            ...params,
          },
        });
        console.log('res', {
          page,
          limit: LIMIT,
          tab_type: tabType,
          search_date: date,
          ...params,
        });
        if (res.errorCode === 0) {
          const { responseData } = res;
          const newList = (responseData?.list || []).map((item: any) => ({
            ...item,
            show: true,
            height: 0,
          }));
          console.log('newList', responseData?.list);
          updateState({
            followupList: isLoadMore
              ? [...followupListRef.current, ...newList]
              : newList,
            hasMore: responseData?.has_more === 1,
          });
        } else {
          Bridge.showToast(res.errorMsg);
          if (!isLoadMore) {
            updateState({ followupList: [], hasMore: false });
          }
        }
      } catch (error) {
        if (!isLoadMore) {
          updateState({ followupList: [], hasMore: false });
        }
        handleError(error, '获取列表数据失败');
      }
    },
    [
      state.loading,
      // 移除 state.followupList 依赖，避免循环
      buildRequestParams,
      updateState,
      handleError,
    ]
  );

  // 统一的数据获取函数
  const fetchAllData = useCallback(
    async (
      tab: 0 | 1,
      date: string,
      filterParams: any,
      startDate?: string,
      endDate?: string
    ) => {
      const sd = startDate || state.startDate;
      const ed = endDate || state.endDate;

      // 1. 并行获取所有独立的数据：统计数据、计划数据、Tab信息
      const [, , tabResult] = await Promise.all([
        fetchStatData(tab, date),
        fetchPlanData(tab, sd, ed),
        fetchTabData(tab, date, filterParams),
      ]);

      // 2. 基于Tab信息获取列表数据（依赖tabResult）
      if (tabResult.tabList.length > 0) {
        await fetchListData(tab, 0, tabResult.firstTabType, date, filterParams);
      }
    },
    [
      state.startDate,
      state.endDate,
      fetchStatData,
      fetchPlanData,
      fetchTabData,
      fetchListData,
    ]
  );

  // 初始化数据
  const initializeData = useCallback(async () => {
    try {
      updateState({ loading: true, followupList: [], tabList: [] });

      // 获取筛选参数
      const [storedData, storedNumber, followupCalendarDate] =
        await Promise.all([
          AsyncStorage.getItem('followupFilterData'),
          AsyncStorage.getItem('followupFilterNumber'),
          AsyncStorage.getItem('followupCalendarDate'),
        ]);

      const filterParams = storedData ? JSON.parse(storedData) : {};
      const filterNumber = storedNumber ? parseInt(storedNumber, 10) : 0;
      const targetDate = followupCalendarDate || state.curDate;

      // 计算日期范围
      const sd = dayjs(targetDate).subtract(14, 'day').format('YYYY-MM-DD');
      const ed = dayjs(targetDate).add(14, 'day').format('YYYY-MM-DD');

      // 获取权限
      const [hasMinePermission, hasAllPermission, tenantId] = await Promise.all(
        [
          checkPermission('followup:mine'),
          checkPermission('followup:all'),
          AsyncStorage.getItem('tenant_id'),
        ]
      );

      const permissionList = [];
      if (hasMinePermission) permissionList.push('followup:mine');
      if (hasAllPermission) permissionList.push('followup:all');
      console.log('permissionList', permissionList);
      // 根据权限确定默认tab
      let currentTab = state.followupTab;
      if (currentTab === 0 && !permissionList.includes('followup:mine')) {
        currentTab = 1;
      }
      if (currentTab === 1 && !permissionList.includes('followup:all')) {
        currentTab = 0;
      }

      // 更新状态
      updateState({
        filterParams,
        filterNumber,
        curDate: targetDate,
        startDate: sd,
        endDate: ed,
        permissionList,
        tenantId: tenantId || '',
        followupTab: currentTab,
        currentPage: 0,
        hasSearch: false,
        hasMore: false,
      });

      // 获取数据
      await fetchAllData(currentTab, targetDate, filterParams, sd, ed);
    } catch (error) {
      handleError(error, '初始化数据失败');
    } finally {
      updateState({ loading: false });
    }
  }, [
    state.curDate,
    state.followupTab,
    updateState,
    fetchAllData,
    handleError,
  ]);

  // 回访计划Tab切换
  const handleFollowupTabChange = useCallback(
    async (tab: 0 | 1) => {
      try {
        AsyncStorage.removeItem('followupFilterData');
        AsyncStorage.removeItem('followupFilterNumber');
        updateState({
          loading: true,
          currentPage: 0,
          tabList: [],
          filterParams: {},
          filterNumber: 0,
          followupList: [],
          followupTab: tab,
        });

        await fetchAllData(tab, state.curDate, {});
      } catch (error) {
        handleError(error, '切换Tab失败');
      } finally {
        updateState({ loading: false });
      }
    },
    [state.curDate, state.filterParams, fetchAllData, handleError, updateState]
  );
  const updateTaskDate = useCallback((date: string) => {
    AsyncStorage.setItem('followupCalendarDate', date);
    previousParamsRef.current!.curDate = date;
  }, []);
  // 日历点击
  const handleCalendarClick = useCallback(
    async (date: string) => {
      try {
        updateState({
          loading: true,
          curDate: date,
          currentPage: 0,
          tabList: [],
          followupList: [],
        });
        updateTaskDate(date);
        // 获取统计数据
        await fetchStatData(state.followupTab, date);

        // 获取Tab信息并更新列表
        const tabResult = await fetchTabData(
          state.followupTab,
          date,
          state.filterParams
        );
        if (tabResult.tabList.length > 0) {
          await fetchListData(
            state.followupTab,
            0,
            tabResult.firstTabType,
            date,
            state.filterParams
          );
        }
      } catch (error) {
        handleError(error, '切换日期失败');
      } finally {
        updateState({ loading: false });
      }
    },
    [
      state.followupTab,
      state.filterParams,
      fetchStatData,
      fetchTabData,
      fetchListData,
      handleError,
      updateState,
    ]
  );

  // 打开日历
  const handleOpenCalendar = useCallback(() => {
    jumpReactNativePage(
      `followup/calendar?type=${state.followupTab}&curDate=${state.curDate}&calendar_type=returnVisit`
    );
  }, [state.followupTab, state.curDate]);

  // 筛选tab点击
  const handleFilterTabClick = useCallback(
    async (tabType: number) => {
      if (tabType === state.currentFilterTab) return;

      try {
        updateState({
          loading: true,
          currentFilterTab: tabType,
          currentPage: 0,
          followupList: [],
        });

        await fetchListData(
          state.followupTab,
          0,
          tabType,
          state.curDate,
          state.filterParams
        );
      } catch (error) {
        handleError(error, '切换筛选失败');
      } finally {
        updateState({ loading: false });
      }
    },
    [
      state.currentFilterTab,
      state.followupTab,
      state.curDate,
      state.filterParams,
      fetchListData,
      handleError,
      updateState,
    ]
  );

  // 回访详情点击
  const handleFollowupClick = useCallback((id: string) => {
    jumpReactNativePage(`followup/visitedDetail?id=${id}`);
  }, []);

  // 加载更多
  const loadMore = useCallback(async () => {
    if (!state.hasMore || state.loading || state.followupList.length === 0)
      return;

    const nextPage = state.currentPage + 1;
    updateState({ currentPage: nextPage });

    await fetchListData(
      state.followupTab,
      nextPage,
      state.currentFilterTab,
      state.curDate,
      state.filterParams,
      true
    );
  }, [
    state.hasMore,
    state.loading,
    state.followupList.length,
    state.currentPage,
    state.followupTab,
    state.currentFilterTab,
    state.curDate,
    state.filterParams,
    fetchListData,
    updateState,
  ]);

  // ScrollView 滚动监听
  const handleScroll = useCallback(
    (event: any) => {
      const { layoutMeasurement, contentOffset, contentSize } =
        event.nativeEvent;
      const paddingToBottom = 20;
      const isCloseToBottom =
        layoutMeasurement.height + contentOffset.y >=
        contentSize.height - paddingToBottom;

      if (isCloseToBottom) {
        loadMore();
      }
    },
    [loadMore]
  );

  // 下拉刷新
  const onRefresh = useCallback(async () => {
    try {
      updateState({
        refreshing: true,
        loading: true,
        currentPage: 0,
        tabList: [],
        followupList: [],
      });
      await fetchAllData(state.followupTab, state.curDate, state.filterParams);
    } catch (error) {
      handleError(error, '刷新数据失败');
    } finally {
      updateState({ refreshing: false, loading: false });
    }
  }, [
    state.followupTab,
    state.curDate,
    state.filterParams,
    fetchAllData,
    handleError,
    updateState,
  ]);

  // 页面显示时检查参数变化并初始化
  useEffect(() => {
    if (pageShow !== previousPageShowRef.current) {
      previousPageShowRef.current = pageShow;
      if (pageShow) {
        setTimeout(() => {
          scrollViewRef.current?.scrollTo({ y: 0, animated: true });
        }, 100);
        // 检查筛选参数是否发生变化，只有变化时才刷新数据
        checkFilterParamsChanged().then(async (hasChanged: boolean) => {
          if (hasChanged) {
            console.log('检测到筛选参数变化，刷新回访数据');
            initializeData();
          } else {
            const tabResult = await fetchTabData(
              state.followupTab,
              state.curDate,
              state.filterParams
            );
            if (tabResult.tabList.length > 0) {
              fetchListData(
                state.followupTab,
                0,
                tabResult.firstTabType,
                state.curDate,
                state.filterParams
              );
            }
            console.log('筛选参数未变化，跳过数据刷新');
          }
        });
      }
    }
  }, [pageShow]);

  // 清理函数
  useEffect(() => {
    return () => {
      AsyncStorage.removeItem('followupCalendarDate');
    };
  }, []);

  // 渲染加载更多状态
  const renderLoadMoreFooter = useMemo(() => {
    if (!state.hasSearch || !state.followupList.length) return null;

    return (
      <View style={styles.loadMoreContainer}>
        <Text style={styles.loadMoreText}>
          {state.hasMore
            ? state.loading
              ? '加载中...'
              : '上拉加载更多'
            : '没有更多啦'}
        </Text>
      </View>
    );
  }, [
    state.hasSearch,
    state.followupList.length,
    state.hasMore,
    state.loading,
  ]);

  // 渲染回访列表
  const renderFollowupList = useMemo(() => {
    if (state.followupList.length > 0) {
      return (
        <View style={styles.followupBody}>
          {state.followupList.map((item: any, index: number) => (
            <FollowupCard
              key={
                item.customer_info?.base?.youxiang_uid ||
                item.id?.toString() ||
                index.toString()
              }
              item={item}
              onCardClick={handleFollowupClick}
            />
          ))}
          {renderLoadMoreFooter}
        </View>
      );
    }

    if (state.loading) {
      return (
        <View style={styles.followupBody}>
          <VisitedSkeleton />
          <VisitedSkeleton />
          <VisitedSkeleton />
        </View>
      );
    }

    return (
      <View style={styles.followupEmpty}>
        <View style={styles.followupEmptyCore}>
          <Image
            style={styles.followupEmptyImage}
            source={{
              uri: 'https://static.soyoung.com/sy-design/aqnomvpf3ki11753429315696.png',
            }}
          />
          <Text style={styles.followupEmptyText}>暂无相关数据</Text>
        </View>
      </View>
    );
  }, [
    state.followupList,
    state.loading,
    handleFollowupClick,
    renderLoadMoreFooter,
  ]);

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Header
        title='回访'
        followupTab={state.followupTab}
        permissionList={state.permissionList}
        onFollowupTabChange={handleFollowupTabChange}
      />

      <ScrollView
        ref={scrollViewRef}
        style={styles.mainScrollView}
        refreshControl={
          <RefreshControl refreshing={state.refreshing} onRefresh={onRefresh} />
        }
        onScroll={handleScroll}
        scrollEventThrottle={16}
        stickyHeaderIndices={[3]}
      >
        {/* 统计面板 */}
        <VisitedDashboard
          statData={state.statData}
          loading={state.planLoading}
        />

        {/* 回访计划标题 */}
        <View style={styles.followupTitle}>
          <Text style={styles.followupTitleText}>回访计划</Text>
        </View>

        {/* 日期选择器 */}
        <View style={styles.dateContainer}>
          <DateSelect
            value={state.curDate}
            showPanel={true}
            startDate={state.startDate}
            endDate={state.endDate}
            showPointList={state.planList}
            onChange={handleCalendarClick}
            onClickPanel={handleOpenCalendar}
          />
        </View>

        {/* 筛选Tab */}
        <View style={styles.filterTabs}>
          <View style={styles.filterTabsContainer}>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.filterTabsScroll}
            >
              {state.tabList.length ? (
                state.tabList.map(tab => (
                  <TouchableOpacity
                    key={tab.tab_type}
                    activeOpacity={1}
                    style={styles.filterTab}
                    onPress={() => handleFilterTabClick(tab.tab_type)}
                  >
                    <Text
                      style={[
                        styles.filterTabText,
                        state.currentFilterTab === tab.tab_type &&
                          styles.filterTabTextActive,
                      ]}
                    >
                      {tab.tab_name} {tab.num}
                    </Text>
                  </TouchableOpacity>
                ))
              ) : state.loading ? (
                <>
                  <SkeletonItem
                    style={{
                      width: getRealSize(69),
                      height: getRealSize(22),
                      marginRight: getRealSize(12),
                    }}
                  />
                  <SkeletonItem
                    style={{
                      width: getRealSize(69),
                      height: getRealSize(22),
                      marginRight: getRealSize(12),
                    }}
                  />
                  <SkeletonItem
                    style={{
                      width: getRealSize(69),
                      height: getRealSize(22),
                    }}
                  />
                </>
              ) : null}
            </ScrollView>
            <TouchableOpacity
              activeOpacity={1}
              style={styles.filterButton}
              onPress={() => {
                jumpReactNativePage(
                  `followup/filters?type=${state.followupTab}`
                );
              }}
            >
              <Image
                source={{
                  uri: 'https://static.soyoung.com/sy-pre/2hw71fsbwugfk-1712751000687.png',
                }}
                style={styles.filterButtonIcon}
              />
              {state.filterNumber > 0 ? (
                <View style={styles.filterTabBadge}>
                  <Text style={styles.filterTabBadgeText}>
                    {state.filterNumber > 99 ? '99+' : state.filterNumber}
                  </Text>
                </View>
              ) : null}
            </TouchableOpacity>
          </View>
        </View>

        {/* 回访列表 */}
        <View style={styles.followupListContainer}>{renderFollowupList}</View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  mainScrollView: {
    flex: 1,
    backgroundColor: '#fff',
  },
  followupListContainer: {
    flex: 1,
  },
  // 回访容器样式 - 已移除，因为不再需要
  followupTitle: {
    paddingHorizontal: getRealSize(15),
    backgroundColor: '#fff',
  },
  followupTitleText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(15),
    color: '#222222',
    letterSpacing: 0,
    fontWeight: '600',
  },
  dateContainer: {
    paddingVertical: getRealSize(15),
    paddingLeft: getRealSize(15),
    backgroundColor: '#fff',
  },
  // 筛选Tab样式 - 添加背景色确保sticky效果正常
  filterTabs: {},
  filterTabsScroll: {
    flex: 1,
    minWidth: 0, // 允许缩小到0宽度
  },
  filterTab: {
    height: getRealSize(48),
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: getRealSize(8), // 添加内边距
  },
  filterTabsContainer: {
    flexDirection: 'row',
    paddingLeft: getRealSize(12),
    backgroundColor: '#fff',
    alignItems: 'center',
    height: getRealSize(48),
  },
  filterTabText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(15),
    lineHeight: getRealSize(48),
    color: '#AAABB3',
    letterSpacing: 0,
    textAlign: 'center',
    fontWeight: '500',
  },
  filterTabTextActive: {
    color: '#030303',
  },
  filterButton: {
    position: 'relative',
    marginHorizontal: getRealSize(8),
    flexDirection: 'row',
    flexShrink: 0, // 防止被压缩
  },
  filterButtonIcon: {
    width: getRealSize(44),
    height: getRealSize(44),
  },
  filterTabBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: '#ff4040',
    borderRadius: getRealSize(10),
    minWidth: getRealSize(20),
    height: getRealSize(20),
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  filterTabBadgeText: {
    fontSize: getRealSize(12),
    color: '#fff',
    fontWeight: '500',
  },
  // 移除原来的scrollView样式，因为不再需要
  // 回访列表样式
  followupBody: {
    paddingHorizontal: getRealSize(15),
    paddingVertical: getRealSize(10),
    backgroundColor: '#F8F8F8',
  },
  // 加载更多样式
  loadMoreContainer: {
    paddingVertical: getRealSize(20),
    alignItems: 'center',
  },
  loadMoreText: {
    fontSize: getRealSize(14),
    color: '#777777',
    fontFamily: 'PingFangSC-Regular',
    fontWeight: '400',
  },
  // 空状态样式
  followupEmpty: {
    paddingVertical: getRealSize(150),
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
  },
  followupEmptyCore: {
    alignItems: 'center',
  },
  followupEmptyImage: {
    width: getRealSize(35), // 对应Vue中的150px
    height: getRealSize(35), // 对应Vue中的120px
    marginBottom: getRealSize(20),
  },
  followupEmptyText: {
    fontSize: getRealSize(14),
    color: '#030303',
    fontWeight: '500',
  },
});

export default VisitedPage;
