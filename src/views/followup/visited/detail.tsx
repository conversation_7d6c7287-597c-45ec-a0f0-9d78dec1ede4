import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
  Image,
  Dimensions,
  Platform,
  Clipboard,
  Keyboard,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SafeAreaView } from 'react-native-safe-area-context';
import api, { FetchModule } from '../../../common/api';
import { getRealSize } from '../../../common/utils';
import { hexToRgb } from '../../../common/colorUtils';
import Header from '../../../components/header';
import TagSelect, { TagItem } from './components/TagSelect';
import FollowupEdit from './components/edit';
import { FollowupDetailSkeleton } from '../components/Skeleton';
import { jumpReactNativePage } from '@/common/jumpPage';
import ProjectSelect, { ActiveItem } from './components/ProjectSelect';
import { Bridge } from '@/common/bridge';
import { back } from '@soyoung/react-native-base';
import InvalidPop from './components/InvalidPop';
import WxModal from './components/WxModal';
import { getNativeLoginInfo } from '@/common/getNativeLoginInfo';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
interface CustomerInfo {
  base: {
    realname: string;
    gender: number;
    age: number;
    mobile: string;
    customer_id: string;
    customer_id_str: string;
  };
  belong: {
    join_c: boolean;
    staff_info: {
      external_user_id: string;
    };
  };
  tag: {
    dynamic_tag_list: Array<{
      tag_id: number;
      name: string;
      tag_color?: string;
    }>;
  };
  action: {
    pre_visit_date_format: string;
  };
}

interface FollowupPlanDetail {
  id: number;
  followup_type?: string;
  followup_type_desc: string;
  followup_outline: string;
  followup_result?: string;
  followup_result_desc: string;
  satisfied_yn?: string;
  satisfied_yn_desc: string;
  followup_user_name: string;
  followup_way_desc: string;
  plan_date: string;
  followup_record: string;
  status: number;
  user_tag_list: TagItem[];
  allergy_tag_list: TagItem[];
  crm_product_list: ActiveItem[];
  followup_way: string;
}

const FollowupDetail: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute() as any;
  const { id } = route?.params?.params as { id: string };
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo | null>(null);
  const [followupPlanDetail, setFollowupPlanDetail] =
    useState<FollowupPlanDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [isWxModalVisible, setIsWxModalVisible] = useState(false);
  const [externalUserId, setExternalUserId] = useState('');
  const [environment, setEnvironment] = useState('');
  const [isEditVisible, setIsEditVisible] = useState(false);
  const [isInvalidVisible, setIsInvalidVisible] = useState(false);
  const [editData, setEditData] = useState<any>(null);
  // 获取屏幕尺寸
  const { width: screenWidth } = Dimensions.get('window');
  const scrollViewRef = useRef<ScrollView>(null);
  useEffect(() => {
    if (id) {
      getFollowupPlanDetail();
    }
    loadEnvironment();
  }, [id]);

  // useEffect(() => {
  //   const keyboardDidShowListener = Keyboard.addListener(
  //     'keyboardDidShow',
  //     e => {
  //       setTimeout(() => {
  //         scrollViewRef.current?.scrollToEnd({ animated: false });
  //       }, 100);
  //     }
  //   );
  //   const keyboardDidHideListener = Keyboard.addListener(
  //     'keyboardDidHide',
  //     () => {
  //       setTimeout(() => {
  //         scrollViewRef.current?.scrollTo({ y: 0, animated: false });
  //       }, 0);
  //     }
  //   );
  //   return () => {
  //     keyboardDidShowListener.remove();
  //     keyboardDidHideListener.remove();
  //   };
  // }, []);

  const loadEnvironment = async () => {
    try {
      const env = await AsyncStorage.getItem('environment');
      setEnvironment(env || '');
    } catch (error) {
      console.error('加载环境信息失败:', error);
    }
  };

  const getFollowupPlanDetail = async () => {
    setLoading(true);
    try {
      const response = await api.pagefetch({
        path: '/chain-wxapp/v1/followup-plan/getFollowupPlanDetail',
        params: { id },
        method: FetchModule.Method.POST,
      });
      if (response.errorCode === 0 && response.responseData) {
        const { followup_plan_detail, customer_info } = response.responseData;
        // 处理数据格式
        if (followup_plan_detail.user_tag_list) {
          followup_plan_detail.user_tag_list =
            followup_plan_detail.user_tag_list.map((item: any) => ({
              ...item,
              id: item.tag_id || item.id,
              name: item.tag_name || item.name,
            }));
        }

        if (followup_plan_detail.allergy_tag_list) {
          followup_plan_detail.allergy_tag_list =
            followup_plan_detail.allergy_tag_list.map((item: any) => ({
              ...item,
              id: item.tag_id || item.id,
              name: item.tag_name || item.name,
            }));
        }

        if (followup_plan_detail.crm_product_list) {
          followup_plan_detail.crm_product_list =
            followup_plan_detail.crm_product_list.map((item: any) => ({
              ...item,
              id: item.tag_id || item.id,
              name: item.name,
            }));
        }
        setFollowupPlanDetail({
          ...followup_plan_detail,
          customer_id: customer_info.base.customer_id_str,
        });
        setCustomerInfo(customer_info);
      } else {
        Bridge.showToast(response.errorMsg || '获取数据失败');
      }
    } catch (error) {
      Bridge.showToast('网络异常，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const handleHome = () => {
    // 根据具体的导航结构调整
    navigation.navigate('Home');
  };

  const goToDetail = () => {
    if (customerInfo) {
      jumpReactNativePage(
        `client/detail?id=${customerInfo.base.customer_id_str}&goVisit=1`
      );
    }
  };

  const copyFollowupOutline = () => {
    if (followupPlanDetail?.followup_outline) {
      Clipboard.setString(followupPlanDetail.followup_outline);
      Bridge.showToast('复制成功');
    }
  };

  const handleEdit = () => {
    if (followupPlanDetail) {
      setEditData({
        followup_type: followupPlanDetail.followup_type,
        followup_type_desc: followupPlanDetail.followup_type_desc,
        followup_outline: followupPlanDetail.followup_outline,
        followup_result: followupPlanDetail.followup_result,
        followup_result_desc: followupPlanDetail.followup_result_desc,
        satisfied_yn: followupPlanDetail.satisfied_yn,
        satisfied_yn_desc: followupPlanDetail.satisfied_yn_desc,
        followup_way: followupPlanDetail.followup_way,
        followup_way_desc: followupPlanDetail.followup_way_desc,
      });
      setIsEditVisible(true);
    }
  };

  // 处理编辑保存
  const handleEditSave = (updatedData: any) => {
    if (followupPlanDetail) {
      setFollowupPlanDetail({
        ...followupPlanDetail,
        ...updatedData,
      });
    }
    setIsEditVisible(false);
  };

  // 处理编辑取消
  const handleEditCancel = () => {
    setIsEditVisible(false);
  };

  const sendAndFinish = async (wxText: string) => {
    if (!wxText.trim()) {
      Bridge.showToast('请输入回访内容');
      return;
    }

    // 这里需要调用企业微信发送消息的API
    // 在React Native中需要通过bridge调用原生方法
    console.log('发送微信消息:', wxText);

    // 模拟发送成功后的处理
    finish(wxText);
  };

  const finish = async (wxText?: string) => {
    if (!followupPlanDetail) return;
    const { tenant_id } = await getNativeLoginInfo();
    const p = {
      ...followupPlanDetail,
      followup_record: wxText || followupPlanDetail.followup_record,
      user_tag_id: followupPlanDetail.user_tag_list.map(item => item.id),
      followup_product_id: followupPlanDetail.crm_product_list.map(
        item => item.id
      ),
      allergy_tag_id: followupPlanDetail.allergy_tag_list.map(item => item.id),
      tenant_id: Number(tenant_id),
    };
    try {
      const response = await api.pagefetch({
        path: '/chain-wxapp/v1/followup-plan/saveFollowupPlanNew',
        params: {
          json_raw: JSON.stringify(p),
        },
        method: FetchModule.Method.POST,
      });
      if (response.errorCode === 0) {
        setIsWxModalVisible(false);
        const message =
          followupPlanDetail.status === 1 ? '恭喜您，完成计划！' : '保存成功';
        Bridge.showToast(message);
        setTimeout(() => {
          back();
        }, 1000);
      } else {
        Bridge.showToast(response.errorMsg || '操作失败');
      }
    } catch (error) {
      console.log(error);
      Bridge.showToast('网络异常，请稍后重试');
    }
  };

  const handleInvalidConfirm = async (reason: string) => {
    setIsInvalidVisible(false);
    try {
      const response = await api.pagefetch({
        path: '/chain-wxapp/v1/followup-plan/invalidFollowup',
        params: {
          id,
          invalid_reason: reason,
        },
        method: FetchModule.Method.POST,
      });
      if (response.errorCode === 0) {
        Bridge.showToast('操作成功');
        setTimeout(() => {
          back();
        }, 1000);
      } else {
        Bridge.showToast(response.errorMsg || '操作失败');
      }
    } catch (error) {
      Bridge.showToast('网络异常，请稍后重试');
    }
  };

  const updateUserTags = (tags: TagItem[]) => {
    if (followupPlanDetail) {
      setFollowupPlanDetail({
        ...followupPlanDetail,
        user_tag_list: tags,
      });
    }
  };

  const updateAllergyTags = (tags: TagItem[]) => {
    if (followupPlanDetail) {
      setFollowupPlanDetail({
        ...followupPlanDetail,
        allergy_tag_list: tags,
      });
    }
  };

  const updateProjects = (active: ActiveItem[]) => {
    if (followupPlanDetail) {
      setFollowupPlanDetail({
        ...followupPlanDetail,
        crm_product_list: active as ActiveItem[],
      });
    }
  };

  const renderCustomerInfo = () => {
    if (!customerInfo) return null;

    return (
      <TouchableOpacity
        style={styles.infoCard}
        activeOpacity={1}
        onPress={goToDetail}
      >
        <View style={styles.infoItem}>
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>
              {customerInfo.base.realname?.charAt(0)}
            </Text>
          </View>
          <View style={styles.infoText}>
            <View style={styles.infoRow}>
              <Text style={styles.name}>{customerInfo.base.realname}</Text>
              <Text style={styles.gender}>
                {customerInfo.base.gender === 1
                  ? '男'
                  : customerInfo.base.gender === 2
                    ? '女'
                    : '未知'}
              </Text>
              <View style={styles.separator} />
              <Text style={styles.age}>{customerInfo.base.age}岁</Text>
            </View>
            <View style={[styles.infoRow, styles.marginTop5]}>
              <Text style={styles.phone}>
                手机号：{customerInfo.base.mobile}
              </Text>
              {customerInfo.belong.join_c ? (
                <Text style={styles.wechat}>@微信</Text>
              ) : null}
            </View>
            <View style={[styles.infoRow, styles.marginTop5]}>
              {(customerInfo.tag.dynamic_tag_list || []).map(tag => (
                <View
                  key={tag.tag_id}
                  style={[
                    styles.tag,
                    tag.tag_color
                      ? {
                          backgroundColor: hexToRgb(tag.tag_color, 0.1),
                          borderColor: hexToRgb(tag.tag_color, 0.4),
                        }
                      : null,
                  ]}
                >
                  <Text
                    style={[
                      styles.tagText,
                      tag.tag_color ? { color: tag.tag_color } : null,
                    ]}
                  >
                    {tag.name}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        </View>
        <View style={styles.infoRight}>
          <Text style={styles.visitTime}>
            {customerInfo.action.pre_visit_date_format}
          </Text>
          <Text style={styles.detailButton}>查看详情</Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderFollowupDetail = () => {
    if (!followupPlanDetail) return null;

    return (
      <View style={styles.mainCard}>
        {/* 回访详情 */}
        <View style={styles.mainTop}>
          <View style={styles.mainTopText}>
            <Text style={styles.mainTopTitle}>回访详情</Text>
            <Text style={styles.subText}>可参考该内容对用户进行回访</Text>
          </View>
          <TouchableOpacity
            activeOpacity={1}
            style={styles.editButton}
            onPress={handleEdit}
          >
            <Text style={styles.editText}>编辑</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.mainBottom}>
          <Text style={styles.typeText}>
            回访类型：{followupPlanDetail.followup_type_desc || '暂无类型'}
          </Text>
          <View style={styles.outlineRow}>
            {followupPlanDetail.followup_outline ? (
              <Text style={styles.detailText}>
                回访提纲：{followupPlanDetail.followup_outline}
                {customerInfo?.belong.join_c &&
                followupPlanDetail.followup_outline ? (
                  <TouchableOpacity
                    activeOpacity={1}
                    style={styles.copyIconContainer}
                    onPress={copyFollowupOutline}
                  >
                    <Image
                      source={{
                        uri: 'https://static.soyoung.com/sy-design/qbwgf2lzacy21753944394952.png',
                      }}
                      style={styles.copyIcon}
                    />
                  </TouchableOpacity>
                ) : null}
              </Text>
            ) : (
              <Text style={styles.detailText}>暂无提纲</Text>
            )}
          </View>

          <View style={styles.separator2} />

          <View style={styles.resultRow}>
            <View style={styles.resultLeft}>
              <Text style={styles.resultTitle}>
                {followupPlanDetail.followup_result_desc}&
                {followupPlanDetail.satisfied_yn_desc}
              </Text>
              <Text style={styles.userName}>
                回访人：{followupPlanDetail.followup_user_name}
              </Text>
            </View>
            <View style={styles.resultRight}>
              <Text style={styles.wayTitle}>
                {followupPlanDetail.followup_way_desc}
              </Text>
              <Text style={styles.planDate}>
                计划回访时间：{followupPlanDetail.plan_date}
              </Text>
            </View>
          </View>
        </View>

        {/* 回访情况 */}
        <View style={[styles.mainTop, styles.marginTop15]}>
          <View style={styles.mainTopText}>
            <Text style={styles.required}>*</Text>
            <Text style={styles.mainTopTitle}>回访情况</Text>
            <Text style={styles.subText}>记录回访过程实际情况</Text>
          </View>
        </View>

        <View style={styles.textareaContainer}>
          <TextInput
            style={styles.textarea}
            multiline
            placeholder='请输入回访内容'
            placeholderTextColor='#AAABB3'
            value={followupPlanDetail.followup_record}
            onChangeText={text =>
              setFollowupPlanDetail({
                ...followupPlanDetail,
                followup_record: text,
              })
            }
          />
        </View>

        {/* 标签选择 */}
        <View style={styles.labelContainer}>
          <TagSelect
            active={followupPlanDetail.user_tag_list || []}
            title='客户标签'
            type={1}
            onSelectionChange={updateUserTags}
          />
          <TagSelect
            active={followupPlanDetail.allergy_tag_list || []}
            title='过敏标签'
            type={2}
            onSelectionChange={updateAllergyTags}
          />
          <ProjectSelect
            active={followupPlanDetail.crm_product_list || []}
            title='回访项目'
            onChange={updateProjects}
          />
        </View>
      </View>
    );
  };

  const renderBottomButtons = () => {
    if (!followupPlanDetail) return null;

    const isActive = followupPlanDetail.status === 1;
    const isCompleted = followupPlanDetail.status === 2;
    const canSendWx =
      customerInfo?.belong.join_c &&
      externalUserId === customerInfo?.belong?.staff_info?.external_user_id &&
      followupPlanDetail.followup_way === 'wechat';

    return (
      <View style={styles.bottomContainer}>
        {isActive ? (
          <View style={styles.buttonRow}>
            <TouchableOpacity
              activeOpacity={1}
              style={styles.cancelButton}
              onPress={() => setIsInvalidVisible(true)}
            >
              <Text style={styles.cancelButtonText}>作废</Text>
            </TouchableOpacity>
            {canSendWx ? (
              <TouchableOpacity
                activeOpacity={1}
                style={styles.sendButton}
                onPress={() => setIsWxModalVisible(true)}
              >
                <Image
                  source={{
                    uri: 'https://static.soyoung.com/sy-pre/37t94ct7aupg9-1712736600705.png',
                  }}
                  style={styles.sendIcon}
                />
                <Text style={styles.sendButtonText}>发送并完成</Text>
              </TouchableOpacity>
            ) : null}
            <TouchableOpacity
              activeOpacity={1}
              style={styles.finishButton}
              onPress={() => finish()}
            >
              <Text style={styles.finishButtonText}>完成回访</Text>
            </TouchableOpacity>
          </View>
        ) : isCompleted ? (
          <View style={styles.buttonRow}>
            <TouchableOpacity
              activeOpacity={1}
              style={styles.saveButton}
              onPress={() => finish()}
            >
              <Text style={styles.saveButtonText}>保存</Text>
            </TouchableOpacity>
          </View>
        ) : null}
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container} edges={['bottom']}>
        <Header title='回访详情' bgColor='#fff' />
        <ScrollView
          style={styles.scrollView}
          ref={scrollViewRef}
          showsVerticalScrollIndicator={false}
        >
          <FollowupDetailSkeleton />
          <View style={styles.bottomSpacing} />
        </ScrollView>
      </SafeAreaView>
    );
  }

  if (!customerInfo && !followupPlanDetail) {
    return (
      <View style={styles.emptyContainer}>
        <Image
          source={{
            uri: 'https://static.soyoung.com/sy-pre/1gf9n3o4rsc2a-1713942600671.png',
          }}
          style={styles.emptyImage}
        />
      </View>
    );
  }

  return (
    <SafeAreaView
      style={{
        ...styles.container,
      }}
      edges={['bottom']}
    >
      {/* <StatusBar barStyle='dark-content' backgroundColor='#f6f9f9' /> */}
      <Header title='回访详情' bgColor='#fff' />
      {Platform.OS === 'ios' ? (
        <KeyboardAwareScrollView
          style={{
            ...styles.scrollView,
          }}
          resetScrollToCoords={{ x: 0, y: 0 }}
          scrollEnabled={true}
          showsVerticalScrollIndicator={false}
        >
          {renderCustomerInfo()}
          {renderFollowupDetail()}
          <View
            style={{
              height: getRealSize(85),
            }}
          />
        </KeyboardAwareScrollView>
      ) : (
        <ScrollView showsVerticalScrollIndicator={false}>
          {renderCustomerInfo()}
          {renderFollowupDetail()}
          <View
            style={{
              height: getRealSize(85),
            }}
          />
        </ScrollView>
      )}

      {renderBottomButtons()}

      {/* 编辑组件 */}
      {isEditVisible && editData ? (
        <FollowupEdit
          visible={isEditVisible}
          editData={editData}
          onSave={handleEditSave}
          onCancel={handleEditCancel}
        />
      ) : null}
      <InvalidPop
        visible={isInvalidVisible}
        onCancel={() => setIsInvalidVisible(false)}
        onConfirm={handleInvalidConfirm}
      />
      <WxModal
        visible={isWxModalVisible}
        wxText={followupPlanDetail?.followup_record || ''}
        onCancel={() => setIsWxModalVisible(false)}
        onConfirm={sendAndFinish}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },

  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
  },
  emptyImage: {
    width: '100%',
    height: getRealSize(362),
  },
  header: {
    backgroundColor: '#f6f9f9',
    paddingTop: Platform.OS === 'ios' ? 0 : getRealSize(10),
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: getRealSize(15),
    paddingVertical: getRealSize(15),
  },
  backButton: {
    width: getRealSize(40),
    height: getRealSize(40),
    borderRadius: getRealSize(20),
    borderWidth: 1,
    borderColor: 'rgba(151, 151, 151, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backIcon: {
    width: getRealSize(19),
    height: getRealSize(19),
  },
  headerTitle: {
    fontSize: getRealSize(18),
    fontWeight: '500',
    color: '#333333',
    position: 'absolute',
    left: '50%',
    marginLeft: getRealSize(-36),
  },
  scrollView: {
    flex: 1,
  },
  infoCard: {
    backgroundColor: '#ffffff',
    marginTop: getRealSize(9),
    marginHorizontal: getRealSize(15),
    padding: getRealSize(15),
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  infoItem: {
    flexDirection: 'row',
    flex: 1,
  },
  avatar: {
    width: getRealSize(40),
    height: getRealSize(40),
    backgroundColor: '#61B43E',
    borderRadius: getRealSize(20),
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: getRealSize(18),
    lineHeight: getRealSize(40),
    fontWeight: '500',
    color: '#ffffff',
  },
  infoText: {
    marginLeft: getRealSize(10),
    flex: 1,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  marginTop5: {
    marginTop: getRealSize(5),
  },
  name: {
    fontSize: getRealSize(14),
    fontWeight: '500',
    color: '#333333',
  },
  separator: {
    backgroundColor: '#dedede',
    width: 1,
    height: getRealSize(8),
    marginHorizontal: getRealSize(5),
  },
  gender: {
    fontSize: getRealSize(11),
    color: '#777777',
    marginHorizontal: getRealSize(5),
  },
  age: {
    fontSize: getRealSize(11),
    color: '#777777',
  },
  phone: {
    fontSize: getRealSize(11),
    color: '#777777',
  },
  wechat: {
    fontSize: getRealSize(11),
    color: '#00c535',
    marginLeft: getRealSize(4),
  },
  tag: {
    fontSize: getRealSize(10),
    color: '#00ab84',
    backgroundColor: 'rgba(0, 171, 132, 0.1)',
    borderWidth: 1,
    borderColor: '#00ab84',
    paddingHorizontal: getRealSize(5),
    paddingVertical: getRealSize(2),
    marginRight: getRealSize(5),
  },
  tagText: {
    fontSize: getRealSize(10),
    color: '#00ab84',
  },
  infoRight: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  visitTime: {
    fontSize: getRealSize(11),
    color: '#aaabb3',
    marginTop: getRealSize(2),
  },
  detailButton: {
    fontSize: getRealSize(11),
    color: '#555555',
    borderWidth: 1,
    borderColor: '#f0f0f0',
    paddingHorizontal: getRealSize(10),
    paddingVertical: getRealSize(6),
    marginTop: getRealSize(5),
  },
  mainCard: {
    margin: getRealSize(15),
    marginTop: getRealSize(12),
    backgroundColor: '#ffffff',
    padding: getRealSize(10),
  },
  mainTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  marginTop15: {
    marginTop: getRealSize(15),
  },
  mainTopText: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  required: {
    color: '#ff4040',
    fontSize: getRealSize(14),
  },
  mainTopTitle: {
    fontSize: getRealSize(14),
    fontWeight: '500',
    color: '#161616',
    marginLeft: getRealSize(5),
  },
  subText: {
    fontSize: getRealSize(10),
    color: '#999999',
    marginLeft: getRealSize(12),
  },
  editButton: {
    borderWidth: 1,
    borderColor: '#f0f0f0',
    paddingHorizontal: getRealSize(10),
    paddingVertical: getRealSize(6),
  },
  editText: {
    fontSize: getRealSize(11),
    color: '#555555',
  },
  mainBottom: {
    borderWidth: 1,
    borderColor: '#f0f0f0',
    // borderRadius: getRealSize(8),
    padding: getRealSize(10),
    marginTop: getRealSize(10),
  },
  typeText: {
    fontSize: getRealSize(13),
    lineHeight: getRealSize(20),
    color: '#333',
    marginBottom: getRealSize(5),
  },
  detailText: {
    fontSize: getRealSize(13),
    lineHeight: getRealSize(20),
    color: '#555555',
    marginBottom: getRealSize(5),
  },
  outlineRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  copyIconContainer: {
    padding: 0,
    margin: 0,
  },
  copyIcon: {
    width: getRealSize(20),
    height: getRealSize(20),
    marginLeft: getRealSize(3),
    transform: [{ translateY: getRealSize(4) }], // 微调垂直位置
  },
  separator2: {
    height: 1,
    backgroundColor: '#f0f0f0',
    marginVertical: getRealSize(15),
  },
  resultRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  resultLeft: {
    flex: 1,
  },
  resultTitle: {
    fontSize: getRealSize(18),
    fontWeight: '600',
    color: '#161616',
    marginBottom: getRealSize(5),
  },
  userName: {
    fontSize: getRealSize(12),
    color: '#aaabb3',
  },
  resultRight: {
    alignItems: 'flex-end',
  },
  wayTitle: {
    fontSize: getRealSize(16),
    fontWeight: '600',
    color: '#161616',
    marginBottom: getRealSize(5),
  },
  planDate: {
    fontSize: getRealSize(12),
    color: '#aaabb3',
  },
  textareaContainer: {
    backgroundColor: '#F8F8F8',
    height: getRealSize(90),
    paddingHorizontal: getRealSize(15),
    marginTop: getRealSize(15),
  },
  textarea: {
    flex: 1,
    fontSize: getRealSize(13),
    color: '#161616',
    textAlignVertical: 'top',
  },
  labelContainer: {
    marginTop: getRealSize(5),
    paddingBottom: getRealSize(5),
  },
  bottomSpacing: {
    height: getRealSize(85),
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#ffffff',
    paddingHorizontal: getRealSize(10),
    paddingVertical: getRealSize(10),
  },
  buttonRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cancelButton: {
    borderWidth: 0.5,
    borderColor: '#333',
    width: getRealSize(75),
    height: getRealSize(42),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: getRealSize(10),
  },
  cancelButtonText: {
    fontSize: getRealSize(13),
    color: '#333',
    fontWeight: '500',
  },
  sendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#333',
    backgroundColor: '#fff',
    width: getRealSize(140),
    height: getRealSize(42),
    marginRight: getRealSize(8),
  },
  sendIcon: {
    width: getRealSize(18),
    height: getRealSize(18),
    marginRight: getRealSize(2),
  },
  sendButtonText: {
    fontSize: getRealSize(13),
    fontWeight: '500',
    color: '#030303',
  },
  finishButton: {
    backgroundColor: '#161616',
    flex: 1,
    height: getRealSize(42),
    justifyContent: 'center',
    alignItems: 'center',
  },
  finishButtonText: {
    fontSize: getRealSize(13),
    fontWeight: '500',
    color: '#ffffff',
  },
  saveButton: {
    backgroundColor: '#161616',
    flex: 1,
    height: getRealSize(42),
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: getRealSize(13),
    fontWeight: '500',
    color: '#ffffff',
  },
});

export default FollowupDetail;
