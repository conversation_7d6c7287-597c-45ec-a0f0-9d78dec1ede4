/* eslint-disable react-native/no-inline-styles */
import React, { Component } from 'react';
import {
  Text,
  View,
  Button,
  StyleSheet,
  ScrollView,
  Dimensions,
  EmitterSubscription,
  Image,
} from 'react-native';
import Api from '../../common/api';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Bridge } from '../../common/bridge';
import Modal from 'react-native-modal';
import { modalAnimation } from '@/constant/modal_animation';
import Svg, { Circle } from 'react-native-svg';
import { LineChart } from 'react-native-chart-kit';
import jsApi from '@soyoung/react-native-jsapi';
import DateInput from '@/components/DateInput';
import DatePicker from '@/components/DatePicker';
import { checkPermission } from '@/common/checkPermission';
import JSAPIV1 from '@soyoung/react-native-jsapi/src/v1';
import Header from '@/components/header';
import { SafeAreaView } from 'react-native-safe-area-context';
import ImagePicker from '@soyoung/react-native-image-crop-picker';
import CameraSelect from '@/components/CameraSelect';
import {
  NotifyCrossStatusChangeFunc,
  addCrossStatusListener,
  removeCrossStatusListener,
} from '@/constant/cross_event';
import { switchTab } from '@/common/jumpPage';
import { clearExtInfo } from '@/common/updateLoginExtInfo';
interface PageProps {
  route: {
    params: {
      params: {
        name: String;
      };
      tabbar: boolean;
    };
  };
  insets: {
    bottom: number;
    left: number;
    right: number;
    top: number;
  };
}

interface PageState {
  singleDate: string;
  dateRange: [string, string];
  yearOnly: string;
  monthOnly: string;
  dayOnly: string;
  yearMonth: string;
  monthDay: string;
  bottomUpModalVisible: boolean;
  datePickerVisible: boolean;
  pickerSelectedDate: string;
  selectedImages: any[];
  selectedVideos: any[];
  cameraSelectVisible: boolean;
  // 添加跨页面事件状态
  crossStatus: boolean;
  crossEventCount: number;
}

export default class Page extends Component<PageProps, PageState> {
  private data: any;
  private dataSet: any;
  private tablecolumns: any;
  private tabledata: any;
  private subscription: EmitterSubscription | null = null;
  // 添加跨页面事件监听器
  private crossStatusSubscription: any = null;

  constructor(props: PageProps) {
    super(props);
    this.state = {
      singleDate: '',
      dateRange: ['', ''],
      yearOnly: '',
      monthOnly: '',
      dayOnly: '',
      yearMonth: '',
      monthDay: '',
      bottomUpModalVisible: false,
      datePickerVisible: false,
      pickerSelectedDate: '',
      selectedImages: [],
      selectedVideos: [],
      cameraSelectVisible: false,
      // 初始化跨页面事件状态
      crossStatus: false,
      crossEventCount: 0,
    };
  }

  handleClickRequest = async () => {
    const res = await Api.pagefetch({
      path: '/chain-wxapp/v1/target/getTenantMonthTarget',
      params: {
        year_month: 202507,
      },
    });

    console.log('handleClickRequest', res);
    jsApi.toNative('showToast', {
      toast: JSON.stringify(res),
    });
  };

  handleClickNotify = async () => {
    NotifyCrossStatusChangeFunc({
      status: true,
    });
  };

  // 切换跨页面事件状态
  handleToggleCrossStatus = async () => {
    const newStatus = !this.state.crossStatus;
    NotifyCrossStatusChangeFunc({
      status: newStatus,
    });
  };

  handleClickCheckPermission = async () => {
    const permission = 'wx:tenant:target';
    const res = await checkPermission(permission);
    jsApi.toNative('showToast', {
      toast: res ? `有权限${permission}` : `无权限${permission}`,
    });
  };

  handleClickMockLogin = async () => {
    await AsyncStorage.removeItem('reactNativeCrossInfo');
    await clearExtInfo();
    jsApi.toNative('showToast', {
      toast: '清理RN协同信息成功',
    });
  };

  // 处理单日期选择
  handleSingleDateChange = (value: string | [string, string]) => {
    if (typeof value === 'string') {
      this.setState({ singleDate: value });
      Bridge.showToast(`单日期选择: ${value}`);
    }
  };

  // 处理日期范围选择
  handleDateRangeChange = (value: string | [string, string]) => {
    if (Array.isArray(value)) {
      this.setState({ dateRange: value });
      const [startDate, endDate] = value;
      Bridge.showToast(`日期范围: ${startDate} ~ ${endDate}`);
    }
  };

  // 处理只选年份
  handleYearOnlyChange = (value: string | [string, string]) => {
    if (typeof value === 'string') {
      this.setState({ yearOnly: value });
      Bridge.showToast(`选择年份: ${value}`);
    }
  };

  // 处理只选月份
  handleMonthOnlyChange = (value: string | [string, string]) => {
    if (typeof value === 'string') {
      this.setState({ monthOnly: value });
      Bridge.showToast(`选择月份: ${value}月`);
    }
  };

  // 处理只选日期
  handleDayOnlyChange = (value: string | [string, string]) => {
    if (typeof value === 'string') {
      this.setState({ dayOnly: value });
      Bridge.showToast(`选择日期: ${value}日`);
    }
  };

  // 处理年-月选择
  handleYearMonthChange = (value: string | [string, string]) => {
    if (typeof value === 'string') {
      this.setState({ yearMonth: value });
      Bridge.showToast(`选择年月: ${value}`);
    }
  };

  // 处理月-日选择
  handleMonthDayChange = (value: string | [string, string]) => {
    if (typeof value === 'string') {
      this.setState({ monthDay: value });
      Bridge.showToast(`选择月日: ${value}`);
    }
  };

  handleClickBottomUpModal = async () => {
    this.setState({
      bottomUpModalVisible: !this.state.bottomUpModalVisible,
    });
  };

  // 处理 DatePicker 显示
  handleShowDatePicker = () => {
    this.setState({ datePickerVisible: true });
  };

  // 处理 DatePicker 确认
  handleDatePickerConfirm = (date: string) => {
    this.setState({
      pickerSelectedDate: date,
      datePickerVisible: false,
    });
    Bridge.showToast(`DatePicker选择: ${date}`);
  };

  // 处理 DatePicker 取消
  handleDatePickerCancel = () => {
    this.setState({ datePickerVisible: false });
  };

  // DatePicker 禁用函数示例
  datePickerDisabled = (date: Date): boolean => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // 重置到当天开始

    // 禁用今天之前的日期
    return date.getTime() < today.getTime();
  };

  // 显示相机选择器
  handleShowCameraSelect = () => {
    this.setState({ cameraSelectVisible: true });
  };

  // 关闭相机选择器
  handleCloseCameraSelect = () => {
    this.setState({ cameraSelectVisible: false });
  };

  // 处理相机选择器结果
  handleCameraSelectResult = (result: any) => {
    if (Array.isArray(result)) {
      // 多个文件
      this.setState({ selectedImages: result, selectedVideos: [] });
    } else {
      // 单个文件
      if (result.mime && result.mime.startsWith('video/')) {
        this.setState({ selectedVideos: [result], selectedImages: [] });
      } else {
        this.setState({ selectedImages: [result], selectedVideos: [] });
      }
    }
    Bridge.showToast('选择完成');
  };
  componentDidMount() {
    JSAPIV1.addListener('login_status_change' as any, undefined, info => {
      console.log('login_status_change', info);
    });

    // 添加跨页面事件监听
    this.crossStatusSubscription = addCrossStatusListener(
      (info: { status: boolean }) => {
        console.log('收到跨页面事件:', info);
        this.setState({
          crossStatus: info.status,
          crossEventCount: this.state.crossEventCount + 1,
        });
        // 显示toast提示
        jsApi.toNative('showToast', {
          toast: `跨页面事件触发: ${info.status ? '激活' : '关闭'}, 总计: ${this.state.crossEventCount + 1}次`,
        });
      }
    );
  }

  componentWillUnmount() {
    if (this.subscription) {
      this.subscription.remove();
    }
    // 清理跨页面事件监听器
    if (this.crossStatusSubscription) {
      removeCrossStatusListener(this.crossStatusSubscription);
      this.crossStatusSubscription = null;
    }
  }

  render() {
    const {
      singleDate,
      dateRange,
      yearOnly,
      monthOnly,
      dayOnly,
      yearMonth,
      monthDay,
    } = this.state;

    return (
      <SafeAreaView
        style={{ flex: 1, backgroundColor: '#f5f5f5' }}
        edges={['bottom']}
      >
        <Header title='测试页面' bgColor='#fff' />
        <ScrollView
          style={[styles.container, { paddingTop: this.props.insets.top }]}
        >
          <View
            style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              paddingHorizontal: 10,
            }}
          >
            <Svg width={100} height={100}>
              <Circle
                cx={50}
                cy={50}
                r={40}
                stroke='black'
                strokeWidth={4}
                fill='red'
              />
            </Svg>
            <LineChart
              data={{
                labels: [
                  'January',
                  'February',
                  'March',
                  'April',
                  'May',
                  'June',
                ],
                datasets: [
                  {
                    data: [
                      Math.random() * 100,
                      Math.random() * 100,
                      Math.random() * 100,
                      Math.random() * 100,
                      Math.random() * 100,
                      Math.random() * 100,
                    ],
                  },
                ],
              }}
              width={Dimensions.get('window').width} // from react-native
              height={220}
              yAxisLabel='$'
              yAxisSuffix='k'
              yAxisInterval={1} // optional, defaults to 1
              chartConfig={{
                backgroundColor: '#e26a00',
                backgroundGradientFrom: '#fb8c00',
                backgroundGradientTo: '#ffa726',
                decimalPlaces: 2, // optional, defaults to 2dp
                color: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
                labelColor: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
                style: {
                  borderRadius: 16,
                },
                propsForDots: {
                  r: '6',
                  strokeWidth: '2',
                  stroke: '#ffa726',
                },
              }}
              bezier
              style={{
                marginVertical: 8,
                borderRadius: 16,
              }}
            />
          </View>

          <View
            style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              paddingHorizontal: 10,
            }}
          >
            <View style={{ marginRight: 10, marginBottom: 10 }}>
              <Button
                onPress={() => switchTab('RN:DATA')}
                title='切换到数据页'
              />
            </View>

            <View style={{ marginRight: 10, marginBottom: 10 }}>
              <Button
                onPress={this.handleClickNotify}
                title='测试登录态变更notify'
              />
            </View>
            <View style={{ marginRight: 10, marginBottom: 10 }}>
              <Button
                onPress={this.handleToggleCrossStatus}
                title={`跨页面事件测试 (${this.state.crossStatus ? '激活' : '未激活'}) - 触发${this.state.crossEventCount}次`}
              />
            </View>
            <View style={{ marginRight: 10, marginBottom: 10 }}>
              <Button
                onPress={this.handleClickCheckPermission}
                title="测试是否有 ('wx:tenant:target') 权限（必须要登录后）"
              />
            </View>

            <View style={{ marginRight: 10, marginBottom: 10 }}>
              <Button
                onPress={this.handleClickMockLogin}
                title='清理RN协同信息'
              />
            </View>
            <View style={{ marginRight: 10, marginBottom: 10 }}>
              <Button
                onPress={this.handleClickRequest}
                title='test请求接口数据'
              />
            </View>
            <View style={{ marginRight: 10, marginBottom: 10 }}>
              <Button
                onPress={this.handleClickBottomUpModal}
                title='从下向上半弹窗'
              />
            </View>
            <View style={{ marginRight: 10, marginBottom: 10 }}>
              <Button
                onPress={this.handleShowDatePicker}
                title='显示DatePicker'
              />
            </View>
            <View style={{ marginRight: 10, marginBottom: 10 }}>
              <Button
                onPress={this.handleShowCameraSelect}
                title='显示相机选择器'
              />
            </View>
          </View>
          <Modal
            isVisible={this.state.bottomUpModalVisible}
            {...modalAnimation}
            onBackdropPress={() =>
              this.setState({ bottomUpModalVisible: false })
            }
          >
            <View style={{ backgroundColor: 'red', height: 100 }}>
              <Text>从下向上半弹窗</Text>
            </View>
          </Modal>

          {/* 日期选择组件示例 */}
          <View style={styles.datePickerSection}>
            <Text style={styles.sectionTitle}>日期选择组件示例</Text>

            {/* 完整日期选择 */}
            <View style={styles.dateInputGroup}>
              <Text style={styles.label}>完整日期选择 (年-月-日):</Text>
              <DateInput
                value={singleDate}
                placeholder={['请选择日期']}
                type='year-month-day'
                isRange={false}
                onChange={this.handleSingleDateChange}
              />
              {singleDate ? (
                <Text style={styles.resultText}>选中日期: {singleDate}</Text>
              ) : null}
            </View>

            {/* 只选年份 */}
            <View style={styles.dateInputGroup}>
              <Text style={styles.label}>只选年份:</Text>
              <DateInput
                value={yearOnly}
                placeholder={['选择年份']}
                type='year'
                isRange={false}
                onChange={this.handleYearOnlyChange}
              />
              {yearOnly ? (
                <Text style={styles.resultText}>选中年份: {yearOnly}</Text>
              ) : null}
            </View>

            {/* 只选月份 */}
            <View style={styles.dateInputGroup}>
              <Text style={styles.label}>只选月份:</Text>
              <DateInput
                value={monthOnly}
                placeholder={['选择月份']}
                type='month'
                isRange={false}
                onChange={this.handleMonthOnlyChange}
              />
              {monthOnly ? (
                <Text style={styles.resultText}>选中月份: {monthOnly}月</Text>
              ) : null}
            </View>

            {/* 只选日期 */}
            <View style={styles.dateInputGroup}>
              <Text style={styles.label}>只选日期:</Text>
              <DateInput
                value={dayOnly}
                placeholder={['选择日期']}
                type='day'
                isRange={false}
                onChange={this.handleDayOnlyChange}
              />
              {dayOnly ? (
                <Text style={styles.resultText}>选中日期: {dayOnly}日</Text>
              ) : null}
            </View>

            {/* 年-月选择 */}
            <View style={styles.dateInputGroup}>
              <Text style={styles.label}>年-月选择:</Text>
              <DateInput
                value={yearMonth}
                placeholder={['选择年月']}
                type='year-month'
                isRange={false}
                onChange={this.handleYearMonthChange}
              />
              {yearMonth ? (
                <Text style={styles.resultText}>选中年月: {yearMonth}</Text>
              ) : null}
            </View>

            {/* 月-日选择 */}
            <View style={styles.dateInputGroup}>
              <Text style={styles.label}>月-日选择:</Text>
              <DateInput
                value={monthDay}
                placeholder={['选择月日']}
                type='month-day'
                isRange={false}
                onChange={this.handleMonthDayChange}
              />
              {monthDay ? (
                <Text style={styles.resultText}>选中月日: {monthDay}</Text>
              ) : null}
            </View>

            {/* 日期范围选择 */}
            <View style={styles.dateInputGroup}>
              <Text style={styles.label}>日期范围选择:</Text>
              <DateInput
                value={dateRange}
                placeholder={['开始日期', '结束日期']}
                type='year-month-day'
                isRange={true}
                rangeSeparator='至'
                onChange={this.handleDateRangeChange}
              />
              {dateRange[0] || dateRange[1] ? (
                <Text style={styles.resultText}>
                  选中范围: {dateRange[0]} ~ {dateRange[1]}
                </Text>
              ) : null}
            </View>

            {/* DateInput 禁用功能示例 */}
            <View style={styles.dateInputGroup}>
              <Text style={styles.label}>
                DateInput 禁用功能（禁用今天之剎的日期）:
              </Text>
              <DateInput
                value={singleDate}
                placeholder={['请选择日期']}
                type='year-month-day'
                isRange={false}
                disabled={this.datePickerDisabled}
                onChange={this.handleSingleDateChange}
              />
            </View>

            {/* DatePicker 使用示例 */}
            <View style={styles.dateInputGroup}>
              <Text style={styles.label}>
                DatePicker 组件示例（禁用今天之前的日期）:
              </Text>
              <Button
                title={
                  this.state.pickerSelectedDate
                    ? `已选择: ${this.state.pickerSelectedDate}`
                    : '点击选择日期'
                }
                onPress={this.handleShowDatePicker}
              />
            </View>

            {/* 选择图片 */}
            <View style={styles.dateInputGroup}>
              <Text style={styles.label}>资源选择:</Text>
              <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                {this.state.selectedImages.map((image, index) => (
                  <Image
                    key={`${index}-i`}
                    source={{ uri: image.path }}
                    style={{ width: 100, height: 100 }}
                  />
                ))}
                {this.state.selectedVideos.map((image, index) => (
                  <Image
                    key={`${index}-v1`}
                    source={{
                      uri: image.videoCover.path,
                    }}
                    style={{ width: 100, height: 100 }}
                  />
                ))}
                {this.state.selectedVideos.map((video, index) => (
                  <Text key={`${index}-v2`}>{`filename: ${video.filename}
localIdentifier: ${video.localIdentifier}
path: ${video.path}
mime: ${video.mime}
videoCover: ${video.videoCover.path}
`}</Text>
                ))}
              </View>
              <Button
                title={'选择图片'}
                onPress={() => {
                  ImagePicker.openPicker({
                    multiple: true,
                    maxFiles: 2,
                    minFiles: 1,
                    mediaType: 'photo',
                  }).then(images => {
                    console.log('选择图片', images);
                    this.setState({ selectedImages: images });
                    this.setState({ selectedVideos: [] });
                  });
                }}
              />
              <Button
                title={'选择视频'}
                onPress={() => {
                  ImagePicker.openPicker({
                    mediaType: 'video',
                    videoCover: {
                      enabled: true,
                    },
                  }).then(video => {
                    this.setState({ selectedImages: [] });
                    this.setState({ selectedVideos: [video] });
                  });
                }}
              />
              <Button
                title={'拍照'}
                onPress={() => {
                  ImagePicker.openCamera({
                    mediaType: 'photo',
                  }).then(image => {
                    console.log('拍照image', image);
                    this.setState({ selectedImages: [image] });
                    this.setState({ selectedVideos: [] });
                  });
                }}
              />
              <Button
                title={'拍视频'}
                onPress={() => {
                  ImagePicker.openCamera({
                    mediaType: 'video',
                    videoCover: {
                      enabled: true,
                    },
                  }).then(video => {
                    this.setState({ selectedImages: [] });
                    this.setState({ selectedVideos: [video] });
                  });
                }}
              />
            </View>

            {/* 退出登录 */}
            <View style={styles.dateInputGroup}>
              <Text style={styles.label}>退出登录:</Text>
              <Button
                title={'退出登录'}
                onPress={() => {
                  jsApi.toNative('applogout');
                }}
              />
            </View>
          </View>

          {/* DatePicker 弹窗 */}
          <DatePicker
            visible={this.state.datePickerVisible}
            value={this.state.pickerSelectedDate}
            title='选择日期（禁用今天之前）'
            type='year-month-day'
            disabled={this.datePickerDisabled}
            onConfirm={this.handleDatePickerConfirm}
            onCancel={this.handleDatePickerCancel}
          />

          {/* CameraSelect 相机选择器 */}
          <CameraSelect
            openPanel={this.state.cameraSelectVisible}
            onClose={this.handleCloseCameraSelect}
            onResult={this.handleCameraSelectResult}
            mediaType='photo'
            multiple={true}
            maxFiles={3}
            minFiles={1}
          />

          <View
            style={{ height: this.props.insets.bottom + this.props.insets.top }}
          />
        </ScrollView>
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  buttonGroup: {
    marginBottom: 30,
    gap: 10,
  },
  datePickerSection: {
    backgroundColor: '#ffffff',
    padding: 20,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 20,
  },
  dateInputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    color: '#333',
    marginBottom: 8,
    fontWeight: '500',
  },
  resultText: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
    fontStyle: 'italic',
  },
});
