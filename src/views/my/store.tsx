import React, { useState } from 'react';
import { View, Text, StyleSheet, Al<PERSON>, ScrollView } from 'react-native';
import { ATrack } from '@soyoung/react-native-container';
import { useFocusEffect } from '@react-navigation/native';
import { back } from '@soyoung/react-native-base';
import { getRealSize } from '../../common/utils';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import api, { FetchModule } from '@/common/api';
import { NotifyCrossStatusChangeFunc } from '@/constant/cross_event';
import { getNativeLoginInfo } from '@/common/getNativeLoginInfo';
import { updateExtInfo, updateParamsInfo } from '@/common/updateLoginExtInfo';
import Header from '@/components/header';
import { SafeAreaView } from 'react-native-safe-area-context';

interface TenantItem {
  id: string;
  name: string;
  is_current: number;
}

interface StoreTargetProps {
  pageShowFlag?: boolean;
}

const StoreTarget: React.FC<StoreTargetProps> = () => {
  const insets = useSafeAreaInsets();
  const [list, setList] = useState<TenantItem[]>([]);
  const [currentName, setCurrentName] = useState('');
  const [selectedIndex, setSelectedIndex] = useState<number>(-1);
  const [selectedId, setSelectedId] = useState('');
  const [num, setNum] = useState(0);
  const [loading, setLoading] = useState(false);

  // 获取门店列表（使用Mock数据）
  const getTenantList = async () => {
    setLoading(true);
    try {
      const res = await api.reactNativeFetch(
        '/chain-wxapp/v1/user/tenantList',
        {},
        FetchModule.Method.POST
      );

      const { errorCode, responseData } = res;
      let data = [];

      if (errorCode === 0 && responseData) {
        data = responseData;
        setNum(responseData.length);
        // 找到当前门店
        data.forEach((element: TenantItem, index: number) => {
          if (element.is_current === 1) {
            setCurrentName(element.name);
            setSelectedIndex(index);
            setSelectedId(element.id);
          }
        });
        setList(data);
      } else {
        Alert.alert('提示', responseData?.errorMsg || '获取门店列表失败');
      }
    } catch (error) {
      Alert.alert('获取门店列表异常');
    } finally {
      setLoading(false);
    }
  };

  // 选择门店（仅选中状态）
  const handleSelectStore = (item: TenantItem, index: number) => {
    setSelectedIndex(index);
    setSelectedId(item.id);
  };

  // 确认切换门店（使用Mock逻辑）
  const confirmSwitch = async () => {
    if (selectedId === '') {
      Alert.alert('提示', '请选择要切换的门店');
      return;
    }

    setLoading(true);
    try {
      const res = await api.reactNativeFetch(
        '/chain-wxapp/v1/user/changeTenant',
        {
          tenant_user_id: selectedId,
        },
        FetchModule.Method.POST
      );

      const { errorCode, responseData } = res;
      if (errorCode === 0 && responseData) {
        // 如果是穿越 改 cross_token
        // 如果没穿越 改 token
        const storageAppInfo = await getNativeLoginInfo();
        const extInfo = storageAppInfo?.ext_info
          ? JSON.parse(storageAppInfo?.ext_info || '{}')
          : null;
        if (extInfo?.cross_token) {
          await updateExtInfo({
            cross_token: responseData.token,
            cross_permissions: responseData.permissions,
            cross_roles: responseData.roles,
            cross_tenant_id: responseData.tenant_id,
            cross_to_uid: responseData.cross_to_uid || extInfo?.cross_to_uid,
          });
        } else {
          await updateParamsInfo({
            token: responseData.token,
            tenant_id: responseData.tenant_id.toString(),
            roles: responseData.roles,
            permissions: responseData.permissions,
            tenant_user_id: responseData.tenant_user_id.toString(),
          });
        }
        NotifyCrossStatusChangeFunc({
          status: true,
        });
        back();
      } else {
        Alert.alert('提示', responseData?.errorMsg || '门店切换失败');
      }
    } catch (error) {
      Alert.alert('网络异常', '门店切换失败，请稍后重试');
      console.error('Mock切换失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 页面聚焦时获取门店列表
  useFocusEffect(
    React.useCallback(() => {
      getTenantList();
    }, [])
  );

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Header title='切换门店' bgColor='#FFFFFF' />
      <ScrollView
        style={[
          styles.scrollView,
          { marginBottom: getRealSize(50) + Math.max(20, insets.bottom) },
        ]}
        showsVerticalScrollIndicator={false}
      >
        {/* 标题区域 */}
        <View style={styles.titleSection}>
          <View style={styles.titleRow}>
            <Text style={styles.titleLeft}>门店切换前</Text>
            <Text style={styles.titleRight} numberOfLines={1}>
              {currentName}
            </Text>
          </View>
        </View>

        {/* 门店数量提示 */}
        <Text style={styles.storeCount}>可选{num}家门店</Text>

        {/* 门店列表 */}
        <View style={styles.storeListContainer}>
          {list.map((item, index) => (
            <ATrack
              key={item.id}
              style={[
                styles.storeItem,
                selectedIndex === index && styles.storeItemSelected,
              ]}
              onPress={() => handleSelectStore(item, index)}
            >
              <Text
                style={[
                  styles.storeItemText,
                  selectedIndex === index && styles.storeItemTextSelected,
                ]}
              >
                {item.name}
              </Text>
            </ATrack>
          ))}
        </View>
      </ScrollView>

      {/* 底部确认按钮 */}
      <View
        style={[
          styles.confirmButtonContainer,
          { paddingBottom: Math.max(20, insets.bottom) },
        ]}
      >
        <ATrack style={styles.confirmButton} onPress={confirmSwitch}>
          <Text style={styles.confirmButtonText}>
            {loading ? '切换中...' : '确认'}
          </Text>
        </ATrack>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    position: 'relative',
  },
  scrollView: {
    flex: 1,
  },
  // 标题区域样式
  titleSection: {
    paddingTop: getRealSize(20),
    paddingBottom: getRealSize(20),
    marginHorizontal: getRealSize(15),
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(222, 222, 222, 0.42)',
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleLeft: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(15),
    color: '#333333',
    flexShrink: 0,
    width: getRealSize(100),
  },
  titleRight: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(15),
    color: '#aaabb3',
    flex: 1,
    textAlign: 'right',
  },
  // 门店数量提示
  storeCount: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15),
    color: '#aaabb3',
    marginHorizontal: getRealSize(15),
    marginTop: getRealSize(10),
  },
  // 门店列表容器
  storeListContainer: {
    marginHorizontal: getRealSize(15),
    paddingBottom: getRealSize(20),
  },
  // 门店项样式（根据最新设计稿：无圆角、无边框）
  storeItem: {
    backgroundColor: '#f5f5f5',
    height: getRealSize(54),
    paddingHorizontal: getRealSize(16),
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: getRealSize(15),
    // 移除圆角和边框
  },
  // 选中状态样式（严格按照设计稿CSS）
  storeItemSelected: {
    backgroundColor: '#EBFBDC',
    borderWidth: 1,
    borderColor: '#61B43E',
  },
  // 门店名称文字样式
  storeItemText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15),
    color: '#161616',
    textAlign: 'center',
    fontWeight: '400',
    letterSpacing: 0,
  },
  // 选中状态文字样式（严格按照设计稿CSS）
  storeItemTextSelected: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15),
    color: '#61B43E',
    letterSpacing: 0,
    fontWeight: '400',
  },
  // 底部确认按钮容器
  confirmButtonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#ffffff',
    paddingHorizontal: getRealSize(20),
    paddingTop: getRealSize(10),
  },
  // 确认按钮样式（非圆角、#333333背景、白字）
  confirmButton: {
    backgroundColor: '#333333',
    height: getRealSize(40),
    borderRadius: 0, // 非圆角
    alignItems: 'center',
    justifyContent: 'center',
  },
  confirmButtonText: {
    color: '#ffffff',
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(16),
  },
});

interface PageProps {}

class Store extends React.Component<PageProps> {
  constructor(props: PageProps) {
    super(props);
    this.state = {
      pageShow: true,
    };
  }

  soyoungPageName() {
    return 'Store';
  }

  /** 页面埋点 */
  soyoungPageInfo() {
    return {
      page_name: '选择门店',
      page_type: 'store_selection',
    };
  }

  didAppear() {
    this.setState({
      pageShow: true,
    });
  }

  willDisappear() {
    this.setState({
      pageShow: false,
    });
  }

  preferredStatusBarStyle() {
    // 0默认 1 白色 2 黑色
    return '2';
  }

  render() {
    return (
      <StoreTarget
        {...this.props}
        pageShowFlag={(this.state as any).pageShow}
      />
    );
  }
}

export default Store;
