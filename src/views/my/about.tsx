import React, { useState, useEffect } from 'react';
import { View, Text, Image, StyleSheet, Alert } from 'react-native';
import { go } from '@soyoung/react-native-base';
import { getRealSize } from '../../common/utils';
import jsApi from '@soyoung/react-native-jsapi';
import api, { FetchModule } from '../../common/api';
import { ATrack } from '@soyoung/react-native-container';
import { AboutSkeleton } from './components/AboutSkeleton';
import Header from '@/components/header';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Bridge } from '../../common/bridge';

interface AboutItem {
  title: string;
  url: string;
}

interface AboutTargetProps {
  pageShowFlag?: boolean;
}

const AboutTarget: React.FC<AboutTargetProps> = () => {
  const [menuList, setMenuList] = useState<AboutItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [appVersion, setAppVersion] = useState<string>('1.0.0'); // 默认版本号

  // 获取App版本信息
  const fetchAppVersion = async () => {
    try {
      const response = await Bridge.getAppInfo();
      console.log('获取App版本信息:', response);
      if (response.status === '1') {
        setAppVersion(response.lver);
      }
    } catch (error) {
      if (__DEV__) {
        console.warn('获取App版本信息失败:', error);
      }
      // 如果获取失败，保持默认版本号
    }
  };

  // 请求协议数据
  const fetchPrivacyAgreement = async () => {
    try {
      setLoading(true);
      const response = await api.pagefetch({
        path: 'chain-wxapp/v1/user/privacyAgreement',
        method: FetchModule.Method.POST,
        params: {},
        isLoading: false, // 手动管理loading状态
      });

      if (response.errorCode === 0 && response.responseData) {
        setMenuList(response.responseData);
      } else {
        if (__DEV__) {
          console.warn('获取协议数据失败:', response.errorMsg);
        }
        // 如果接口失败，使用默认数据
        setMenuList([
          {
            title: '隐私政策',
            url: 'https://m.soyoung.com/policy/OnlinePolicy?key=privacy&app_id=125',
          },
          {
            title: '个人信息收集清单',
            url: 'https://m.soyoung.com/policy/OnlinePolicy?key=information-collection&app_id=125',
          },
          {
            title: '第三方共享信息清单',
            url: 'https://m.soyoung.com/policy/OnlinePolicy?key=third-party-sharing&app_id=125',
          },
        ]);
      }
    } catch (error) {
      if (__DEV__) {
        console.error('请求协议数据异常:', error);
      }
      // 异常情况下使用默认数据
      setMenuList([
        {
          title: '隐私政策',
          url: 'https://m.soyoung.com/policy/OnlinePolicy?key=privacy&app_id=125',
        },
        {
          title: '个人信息收集清单',
          url: 'https://m.soyoung.com/policy/OnlinePolicy?key=information-collection&app_id=125',
        },
        {
          title: '第三方共享信息清单',
          url: 'https://m.soyoung.com/policy/OnlinePolicy?key=third-party-sharing&app_id=125',
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPrivacyAgreement();
    fetchAppVersion();
  }, []);

  const goh5 = (url: string) => {
    try {
      go(`app.soyoung://html?url=${encodeURIComponent(url)}`);
    } catch (error) {
      Alert.alert('提示', '页面跳转失败，请稍后重试');
    }
  };

  // 退出登录
  const logoutRequest = async () => {
    Alert.alert('确认退出', '确定要退出登录吗？', [
      { text: '取消', style: 'cancel' },
      {
        text: '确定',
        onPress: async () => {
          try {
            jsApi.toNative('applogout');
          } catch (error) {
            Alert.alert('退出失败', '请稍后重试');
          }
        },
      },
    ]);
  };

  const handleLogout = () => {
    logoutRequest();
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      {/* 顶部导航栏 */}
      <Header title='设置' bgColor='#fff' />
      {/* 内容区域 */}
      <View style={styles.content}>
        {/* 菜单项 */}
        <View style={styles.menuSection}>
          {loading ? (
            <View style={styles.aboutskeleton}>
              <AboutSkeleton />
            </View>
          ) : (
            menuList.map((item, index) => (
              <ATrack
                key={index}
                style={styles.menuItem}
                onPress={() => goh5(item.url)}
              >
                <Text style={styles.menuItemTitle}>{item.title}</Text>
                <Image
                  style={styles.menuItemIcon}
                  source={{
                    uri: 'https://static.soyoung.com/sy-design/ufwlaak4j0861744799639572.png',
                  }}
                  resizeMode='contain'
                />
              </ATrack>
            ))
          )}
        </View>

        {/* 版本信息 */}
        <View style={styles.versionSection}>
          <View style={styles.versionItem}>
            <Text style={styles.versionLabel}>当前版本</Text>
            <View style={styles.versionRight}>
              <Text style={styles.versionNumber}>{appVersion}</Text>
              {/* <View style={styles.versionDot} /> */}
            </View>
          </View>
        </View>
      </View>

      {/* 底部退出按钮 */}
      <ATrack style={styles.bottomSection} onPress={handleLogout}>
        <View style={styles.logoutButton}>
          <Text style={styles.logoutButtonText}>退出登录</Text>
        </View>
      </ATrack>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  // 内容区域
  content: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  // 菜单区域
  menuSection: {
    marginTop: getRealSize(10),
    backgroundColor: '#ffffff',
  },
  aboutskeleton: {
    height: getRealSize(226),
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: getRealSize(20),
    paddingVertical: getRealSize(20),
    borderBottomWidth: 1,
    borderBottomColor: '#f2f2f2',
  },
  menuItemTitle: {
    fontSize: getRealSize(16),
    color: '#333333',
    fontWeight: '400',
    flex: 1,
  },
  menuItemIcon: {
    width: getRealSize(6),
    height: getRealSize(10),
    marginLeft: getRealSize(12),
  },

  // 版本信息区域
  versionSection: {
    marginTop: getRealSize(10),
    backgroundColor: '#ffffff',
  },
  versionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: getRealSize(20),
    paddingVertical: getRealSize(20),
  },
  versionLabel: {
    fontSize: getRealSize(16),
    color: '#333333',
    fontWeight: '400',
  },
  versionRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  versionNumber: {
    fontSize: getRealSize(16),
    color: '#333333',
    fontWeight: '400',
    marginRight: getRealSize(8),
  },
  versionDot: {
    width: getRealSize(8),
    height: getRealSize(8),
    borderRadius: getRealSize(4),
    backgroundColor: '#ff4444',
  },

  // 底部退出按钮区域
  bottomSection: {
    paddingHorizontal: getRealSize(24),
    paddingVertical: getRealSize(24),
    backgroundColor: '#f8f8f8',
  },
  logoutButton: {
    height: getRealSize(42),
    backgroundColor: '#f8f8f8',
    borderWidth: 1,
    borderColor: '#333333',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoutButtonText: {
    fontSize: getRealSize(16),
    color: '#333333',
    fontWeight: '400',
  },
});

interface PageProps {}

interface AboutState {
  pageShow: boolean;
}

class About extends React.Component<PageProps, AboutState> {
  constructor(props: PageProps) {
    super(props);
    this.state = {
      pageShow: true,
    };
  }

  soyoungPageName() {
    return 'About';
  }

  /** 页面埋点 */
  soyoungPageInfo() {
    return {
      page_name: '关于我们',
      page_type: 'about',
    };
  }

  didAppear() {
    this.setState({
      pageShow: true,
    });
  }

  willDisappear() {
    this.setState({
      pageShow: false,
    });
  }

  preferredStatusBarStyle() {
    // 0默认 1 白色 2 黑色
    return '2';
  }

  render() {
    return <AboutTarget {...this.props} pageShowFlag={this.state.pageShow} />;
  }
}

export default About;
