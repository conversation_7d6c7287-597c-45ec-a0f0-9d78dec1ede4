import React from 'react';
import PageLayout from './page';
import {
  addCrossStatusListener,
  removeCrossStatusListener,
} from '@/constant/cross_event';
import JSAPIV1 from '@soyoung/react-native-jsapi/src/v1';

interface PageProps {}

interface PageState {
  pageShow: boolean;
  crossChange: boolean;
  loginStatus: boolean;
}

class MyPage extends React.Component<PageProps, PageState> {
  private crossStatusSubscription: any = null;
  private loginStatusSubscription: any = null;
  constructor(props: PageProps) {
    super(props);
    this.state = {
      pageShow: true,
      crossChange: false,
      loginStatus: false,
    };
  }

  soyoungPageName() {
    return 'my';
  }

  /** 页面埋点 */
  soyoungPageInfo() {
    return {
      page_name: '我的',
      page_type: 'my',
      tabName: 'RN:MINE',
    };
  }

  componentDidMount() {
    // 使用新的事件监听方式
    this.crossStatusSubscription = addCrossStatusListener(
      (info: { status: boolean }) => {
        console.log('my page,cross_status_change', info);
        this.setState({
          crossChange: !this.state.crossChange,
        });
      }
    );

    this.loginStatusSubscription = JSAPIV1.addListener(
      'login_status_change' as any,
      {},
      (info: { status: boolean }) => {
        console.log('my page,login_status_change', info);

        if (info.login_status) {
          this.setState({
            loginStatus: !info.status,
          });
        }
      }
    );
  }

  componentWillUnmount() {
    // 清理事件监听器，防止内存泄漏
    if (this.crossStatusSubscription) {
      removeCrossStatusListener(this.crossStatusSubscription);
      this.crossStatusSubscription = null;
    }
    if (this.loginStatusSubscription) {
      this.loginStatusSubscription?.remove();
      this.loginStatusSubscription = null;
    }
  }

  didAppear() {
    this.setState({
      pageShow: true,
    });
  }

  willDisappear() {
    this.setState({
      pageShow: false,
    });
  }

  preferredStatusBarStyle() {
    // 0默认 1 白色 2 黑色
    return '2';
  }

  render() {
    return (
      <PageLayout
        {...this.props}
        pageShowFlag={this.state.pageShow}
        crossChange={this.state.crossChange}
        loginStatus={this.state.loginStatus}
      />
    );
  }
}

export default MyPage;
