import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Skeleton from '../../../components/Skeleton';
import { getRealSize } from '../../../common/utils';

/**
 * 我的页面骨架图组件
 * 模拟页面加载前的占位效果，提升用户体验
 */
const MySkeleton: React.FC = () => {
  const insets = useSafeAreaInsets();

  return (
    <View style={styles.myContent}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* 头部区域 - 包含背景和用户信息 */}
        <View style={styles.headerContainer}>
          {/* 头部背景区域 - 骨架图纯色背景 */}
          <View
            style={[
              styles.myInfoBackground,
              {
                top: -insets.top,
                height: getRealSize(203) + insets.top,
              },
            ]}
          />

          {/* 用户信息区域骨架图 - 在头部背景内 */}
          <View style={styles.myInfo}>
            <View style={styles.myInfoMain}>
              {/* 头像骨架 - 圆形，与骨架图一致 */}
              <Skeleton
                width={getRealSize(74)}
                height={getRealSize(74)}
                borderRadius={getRealSize(37)}
                style={styles.avatarSkeleton}
              />
              <View style={styles.myInfoRight}>
                {/* 第一行文本骨架 - 较长 */}
                <Skeleton
                  width={getRealSize(120)}
                  height={getRealSize(16)}
                  borderRadius={getRealSize(2)}
                  marginBottom={getRealSize(12)}
                />
                {/* 第二行文本骨架 - 中等长度 */}
                <Skeleton
                  width={getRealSize(90)}
                  height={getRealSize(14)}
                  borderRadius={getRealSize(2)}
                  marginBottom={getRealSize(12)}
                />
                {/* 第三行小标签骨架 */}
                <View style={styles.rolesSkeleton}>
                  <Skeleton
                    width={getRealSize(50)}
                    height={getRealSize(20)}
                    borderRadius={getRealSize(10)}
                    marginRight={getRealSize(6)}
                  />
                  <Skeleton
                    width={getRealSize(40)}
                    height={getRealSize(20)}
                    borderRadius={getRealSize(10)}
                  />
                </View>
              </View>
            </View>
          </View>
        </View>

        {/* 医生认证组件骨架 - 白色背景，无间隙，宽度100% */}
        <View style={styles.authSkeleton}>
          {/* 第一行骨架块 */}
          <View style={styles.authSkeletonRow}>
            <Skeleton
              width={getRealSize(60)}
              height={getRealSize(14)}
              borderRadius={getRealSize(2)}
              marginBottom={0}
            />
            <Skeleton
              width={getRealSize(80)}
              height={getRealSize(14)}
              borderRadius={getRealSize(2)}
              marginBottom={0}
              style={styles.authSkeletonItem}
            />
          </View>

          {/* 第二行骨架块 */}
          <View style={styles.authSkeletonRow}>
            <Skeleton
              width={getRealSize(120)}
              height={getRealSize(14)}
              borderRadius={getRealSize(2)}
              marginBottom={0}
            />
            <Skeleton
              width={getRealSize(50)}
              height={getRealSize(14)}
              borderRadius={getRealSize(2)}
              marginBottom={0}
              style={styles.authSkeletonItem}
            />
          </View>

          {/* 第三行骨架块 */}
          <View style={styles.authSkeletonRow}>
            <Skeleton
              width={getRealSize(90)}
              height={getRealSize(14)}
              borderRadius={getRealSize(2)}
              marginBottom={0}
            />
            <Skeleton
              width={getRealSize(70)}
              height={getRealSize(14)}
              borderRadius={getRealSize(2)}
              marginBottom={0}
              style={styles.authSkeletonItem}
            />
          </View>
        </View>

        {/* 认证组件与菜单间隔 */}
        <View style={styles.authGap} />

        {/* 功能菜单列表骨架 */}
        <View style={styles.myMenu}>
          {/* 门店菜单项骨架 */}
          <View style={styles.menuItemSkeleton}>
            <View style={styles.menuItemLeft}>
              <Skeleton
                width={23}
                height={23}
                borderRadius={4}
                marginRight={12}
              />
              <Skeleton width={getRealSize(60)} height={16} />
            </View>
            <View style={styles.menuItemRight}>
              <Skeleton width={getRealSize(80)} height={14} marginRight={8} />
              <Skeleton width={getRealSize(6)} height={12} />
            </View>
          </View>

          {/* 账号协同菜单项骨架 */}
          <View style={styles.menuItemSkeleton}>
            <View style={styles.menuItemLeft}>
              <Skeleton
                width={23}
                height={23}
                borderRadius={4}
                marginRight={12}
              />
              <Skeleton width={getRealSize(60)} height={16} />
            </View>
            <View style={styles.menuItemRight}>
              <Skeleton width={getRealSize(50)} height={14} marginRight={8} />
              <Skeleton width={getRealSize(6)} height={12} />
            </View>
          </View>

          {/* 设置菜单项骨架 */}
          <View style={[styles.menuItemSkeleton, styles.menuItemSkeletonLast]}>
            <View style={styles.menuItemLeft}>
              <Skeleton
                width={23}
                height={23}
                borderRadius={4}
                marginRight={12}
              />
              <Skeleton width={getRealSize(40)} height={16} />
            </View>
            <View style={styles.menuItemRight}>
              <Skeleton width={getRealSize(6)} height={12} />
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  myContent: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  scrollView: {
    flex: 1,
  },
  headerContainer: {
    position: 'relative',
  },
  myInfoBackground: {
    position: 'absolute',
    left: 0,
    right: 0,
    width: '100%',
    backgroundColor: '#f2f2f2', // 骨架图灰色背景
    zIndex: 1,
  },
  myInfo: {
    height: getRealSize(203),
    paddingHorizontal: getRealSize(24),
    paddingTop: getRealSize(60),
    justifyContent: 'center',
    zIndex: 2,
  },
  myInfoMain: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarSkeleton: {
    marginRight: getRealSize(18),
  },
  myInfoRight: {
    flex: 1,
    justifyContent: 'center',
  },
  rolesSkeleton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  authSkeleton: {
    backgroundColor: '#ffffff',
    paddingHorizontal: getRealSize(20),
    paddingVertical: getRealSize(16),
    width: '100%',
    height: getRealSize(92),
  },
  authSkeletonRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getRealSize(12),
  },
  authSkeletonItem: {
    marginLeft: getRealSize(10),
  },
  authGap: {
    height: getRealSize(10),
    backgroundColor: '#f2f2f2',
  },
  myMenu: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: getRealSize(20),
    borderRadius: getRealSize(8),
    overflow: 'hidden',
  },
  menuItemSkeleton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: getRealSize(20),
    paddingVertical: getRealSize(16),
    borderBottomWidth: getRealSize(0.5),
    borderBottomColor: '#f2f2f2',
    height: getRealSize(67),
  },
  menuItemSkeletonLast: {
    borderBottomWidth: 0,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default MySkeleton;
