import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  Dimensions,
} from 'react-native';
import Modal from 'react-native-modal';
import { getRealSize } from '../../../common/utils';
import api, { FetchModule } from '../../../common/api';
import { ATrack } from '@soyoung/react-native-container';
import { modalAnimation } from '@/constant/modal_animation';
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// 认证项目接口 - 根据设计稿扩展
interface AuthItem {
  id: string;
  name: string;
  desc: string;
  obtain_yn: number;
  obtain_desc: string;
  service_str: string;
  service_num: string;
  service_num_source: number;
  need_service_num_source: number;
  doctor_num: string;
  type: number;
  need_service_num: number;
  showAllDesc?: boolean;
  needShowMoreBtn?: boolean;
  progress?: number; // 添加进度字段，用于显示当前完成进度
}

// 认证数据接口 - 完全对齐原工程
interface AuthData {
  doctor_tag_info: AuthItem[];
  doctor_tag_pop_info: AuthItem[];
}

// Props接口 - 完全对齐原工程Vue Props
interface DoctorAuthPopProps {
  crossChange?: boolean;
  loginStatus?: boolean;
  isDoctor?: boolean;
  pageShowFlag?: boolean;
}

const DoctorAuthPop = (props: DoctorAuthPopProps) => {
  const { crossChange, loginStatus, isDoctor, pageShowFlag } = props;
  const [showPopup, setShowPopup] = useState(false);
  const [authList, setAuthList] = useState<AuthData>({
    doctor_tag_info: [],
    doctor_tag_pop_info: [],
  });

  // 添加 ref 来跟踪组件是否已卸载
  const isMountedRef = useRef(true);
  const isInitializedRef = useRef(false);

  // 获取认证列表 - 开发环境使用Mock数据
  const getAuthList = useCallback(async () => {
    if (!isDoctor) {
      return;
    }
    try {
      const res = await api.reactNativeFetch(
        'chain-wxapp/v1/user/doctorCertifyInfo',
        {},
        FetchModule.Method.POST
      );

      // 检查组件是否已卸载
      if (!isMountedRef.current) return;

      if (res.errorCode === 0) {
        const responseData = res.responseData || {
          doctor_tag_info: [],
          doctor_tag_pop_info: [],
        };

        // 为每个数据项添加展开/折叠标志并检测文本溢出，同时计算进度
        if (
          responseData.doctor_tag_pop_info &&
          responseData.doctor_tag_pop_info.length > 0
        ) {
          responseData.doctor_tag_pop_info.forEach((item: any) => {
            item.showAllDesc = false;

            // 计算进度：使用 service_num_source 作为当前完成数
            if (
              item.service_num_source !== undefined &&
              item.need_service_num !== undefined
            ) {
              item.progress = Math.min(
                item.service_num_source,
                item.need_service_num
              );
            }

            // 检测文本是否超过两行
            if (!item.desc) {
              item.needShowMoreBtn = false;
            } else {
              const text = item.desc.trim();
              let weightedLength = 0;

              for (let i = 0; i < text.length; i++) {
                const char = text.charAt(i);
                if (/[\u4e00-\u9fa5]/.test(char)) {
                  // 中文字符
                  weightedLength += 1.8;
                } else if (/[a-zA-Z]/.test(char)) {
                  // 英文字符
                  weightedLength += 0.8;
                } else {
                  // 其他字符
                  weightedLength += 1;
                }
              }

              // 假设每行可容纳约34个权重单位(相当于约19个中文字符或约40个英文字符)
              item.needShowMoreBtn = weightedLength > 68; // 两行的权重阈值
            }
          });
        }

        setAuthList(responseData);
      }
    } catch (error) {
      // 检查组件是否已卸载
      if (!isMountedRef.current) return;
      console.error('获取认证列表失败:', error);
    }
  }, [crossChange, loginStatus, isDoctor, pageShowFlag]);

  // 切换描述展开/收起状态
  const toggleDesc = useCallback((itemId: string) => {
    setAuthList(prevState => ({
      ...prevState,
      doctor_tag_pop_info: prevState.doctor_tag_pop_info.map(item =>
        item.id === itemId ? { ...item, showAllDesc: !item.showAllDesc } : item
      ),
    }));
  }, []);

  // 弹窗控制方法
  const openDoctorAuthPop = useCallback(() => {
    // 直接打开弹窗，不需要额外的状态更新
    setShowPopup(true);
  }, []);

  const handleCancel = useCallback(() => {
    setShowPopup(false);
  }, []);

  // 组件挂载和props变化时获取数据
  useEffect(() => {
    isMountedRef.current = true;

    // 只在组件挂载时或props真正变化时调用
    if (!isInitializedRef.current) {
      // 组件首次挂载
      getAuthList();
      isInitializedRef.current = true;
    } else if (crossChange !== undefined || loginStatus !== undefined) {
      // props变化时重新获取数据
      getAuthList();
    }

    // 清理函数
    return () => {
      isMountedRef.current = false;
    };
  }, [crossChange, loginStatus, getAuthList]);

  // 如果没有认证信息，不渲染组件 - 对齐原工程v-if逻辑
  if (!authList.doctor_tag_info || authList.doctor_tag_info.length === 0) {
    return null;
  }

  return (
    <>
      {/* 新氧认证展示区域 - 完全对齐原工程template结构 */}
      <View style={styles.soyoungAuthentication}>
        <View style={styles.soyoungAuthenticationTag}>
          <View style={styles.point} />
          <Text style={styles.tagName}>新氧医生认证</Text>
        </View>

        <ATrack
          style={styles.soyoungAuthenticationBody}
          onPress={openDoctorAuthPop}
        >
          <View style={styles.soyoungAuthenticationList}>
            {authList.doctor_tag_info.slice(0, 4).map(item => {
              // 完全对齐原工程的条件渲染逻辑，使用胶囊式布局
              if (item.name === '万支大师' && +item.obtain_yn === 1) {
                return (
                  <View
                    key={item.id}
                    style={styles.soyoungAuthenticationListItem}
                  >
                    <View style={[styles.listItemTitle, styles.orangeTitle]}>
                      <Text style={[styles.titleText]}>{item.name}</Text>
                    </View>
                    <View
                      style={[
                        styles.listItemDescription,
                        styles.orangeDescription,
                      ]}
                    >
                      <Text
                        style={[
                          styles.descriptionText,
                          styles.orangeDescriptionText,
                        ]}
                      >
                        {item.service_str} {item.service_num}
                      </Text>
                      {+item.obtain_yn === 0 && (
                        <Image
                          style={styles.descriptionImage}
                          source={{
                            uri: 'https://static.soyoung.com/sy-design/tmj9hu16cksa1746600916494.png',
                          }}
                        />
                      )}
                    </View>
                  </View>
                );
              } else if (item.name !== '万支大师' && +item.obtain_yn !== 0) {
                return (
                  <View
                    key={item.id}
                    style={styles.soyoungAuthenticationListItem}
                  >
                    <View style={[styles.listItemTitle, styles.blackTitle]}>
                      <Text style={[styles.titleText]}>{item.name}</Text>
                    </View>
                    <View
                      style={[
                        styles.listItemDescription,
                        styles.blackDescription,
                      ]}
                    >
                      <Text
                        style={[
                          styles.descriptionText,
                          styles.blackDescriptionText,
                        ]}
                      >
                        {item.service_str} {item.service_num}
                      </Text>
                      {+item.obtain_yn === 0 && (
                        <Image
                          style={styles.descriptionImage}
                          source={{
                            uri: 'https://static.soyoung.com/sy-design/tmj9hu16cksa1746600916494.png',
                          }}
                        />
                      )}
                    </View>
                  </View>
                );
              } else if (+item.obtain_yn === 0) {
                return (
                  <View
                    key={item.id}
                    style={styles.soyoungAuthenticationListItem}
                  >
                    <View style={[styles.listItemTitle, styles.disabledTitle]}>
                      <Text style={[styles.titleText]}>{item.name}</Text>
                    </View>
                    <View
                      style={[
                        styles.listItemDescription,
                        styles.disabledDescription,
                      ]}
                    >
                      <Text
                        style={[
                          styles.descriptionText,
                          styles.disabledDescriptionText,
                        ]}
                      >
                        {item.service_str} {item.service_num}
                      </Text>
                      <Image
                        style={styles.descriptionImage}
                        source={{
                          uri: 'https://static.soyoung.com/sy-design/tmj9hu16cksa1746600916494.png',
                        }}
                      />
                    </View>
                  </View>
                );
              }
              return null;
            })}
          </View>

          {/* 更多按钮 */}
          <ATrack
            style={styles.soyoungAuthenticationImg}
            onPress={openDoctorAuthPop}
          >
            <Image
              style={styles.arrowImage}
              source={{
                uri: 'https://static.soyoung.com/sy-design/ufwlaak4j0861744799639572.png',
              }}
            />
          </ATrack>
        </ATrack>
      </View>

      {/* 弹窗遮罩 - 完全对齐原工程uni-popup结构 */}
      <Modal
        isVisible={showPopup}
        {...modalAnimation}
        style={styles.modal}
        animationIn='slideInUp'
        animationOut='slideOutDown'
        avoidKeyboard={false}
        onBackdropPress={handleCancel} // 点击蒙层关闭弹窗
        onBackButtonPress={handleCancel} // Android返回键关闭弹窗
      >
        <View style={styles.popBody}>
          <View style={styles.commonPopupTop}>
            <View style={styles.commonPopupTopBar}>
              <View style={styles.commonPopupCancel} />
              <Text style={styles.commonPopupTitle}>新氧医生认证</Text>
              <ATrack onPress={handleCancel} style={styles.commonPopupClose}>
                <Image
                  style={styles.commonPopupCloseIcon}
                  source={{
                    uri: 'https://static.soyoung.com/sy-design/bzsokyai5osd1753688976847.png',
                  }}
                />
              </ATrack>
            </View>
          </View>

          <ScrollView
            style={styles.commonPopupBody}
            showsVerticalScrollIndicator={false}
            bounces={false}
            nestedScrollEnabled={false}
          >
            {authList.doctor_tag_pop_info.map(item => {
              return (
                <View key={item.id} style={styles.newAuthItem}>
                  {/* 认证标题和状态 */}
                  <View style={styles.authHeader}>
                    <Text style={styles.authTitle}>{item.name}</Text>
                    <View
                      style={[
                        styles.statusTag,
                        +item.obtain_yn === 1
                          ? styles.obtainedTag
                          : styles.notObtainedTag,
                      ]}
                    >
                      <Text
                        style={[
                          styles.statusText,
                          +item.obtain_yn === 1
                            ? styles.obtainedText
                            : styles.notObtainedText,
                        ]}
                      >
                        {+item.obtain_yn === 1 ? '已获得' : '未获得'}
                      </Text>
                    </View>
                  </View>

                  {/* 认证描述 */}
                  <View style={styles.descContainer}>
                    <View style={styles.descContainerItem}>
                      <Text
                        style={[
                          styles.authDesc,
                          !item.showAllDesc &&
                            item.needShowMoreBtn &&
                            styles.authDescCollapsed,
                        ]}
                        numberOfLines={
                          item.showAllDesc || !item.needShowMoreBtn
                            ? undefined
                            : 2
                        }
                      >
                        {item.desc}
                      </Text>
                    </View>
                    {item.needShowMoreBtn && !item.showAllDesc ? (
                      <ATrack
                        onPress={() => toggleDesc(item.id)}
                        style={styles.expandButton}
                      >
                        <Text style={styles.expandButtonText}>全部</Text>
                        <Image
                          style={styles.expandButtonIcon}
                          source={{
                            uri: 'https://static.soyoung.com/sy-design/ufwlaak4j0861747205909836.png',
                          }}
                        />
                      </ATrack>
                    ) : null}
                  </View>

                  {/* 进度信息 */}
                  {+item.type === 2 &&
                    +item.service_num < +item.need_service_num && (
                      <View style={styles.progressContainer}>
                        <View style={styles.progressInfo}>
                          <Text style={styles.progressText}>完成进度</Text>
                          <Text
                            style={[styles.progressValue, { color: '#333333' }]}
                          >
                            <Text style={styles.progressCurrent}>
                              {item.service_num_source}
                            </Text>
                            /{item.need_service_num_source}
                          </Text>
                        </View>
                        <View style={styles.progressBar}>
                          <View
                            style={[
                              styles.progressBarInner,
                              {
                                width: `${Math.min(
                                  (item.service_num_source /
                                    item.need_service_num_source) *
                                    100,
                                  100
                                )}%`,
                              },
                            ]}
                          >
                            {+item.service_num !== 0 && (
                              <View style={styles.progressDot} />
                            )}
                          </View>
                        </View>
                      </View>
                    )}

                  {/* 操作信息 */}
                  <View style={styles.operationContainer}>
                    <View style={styles.operationLeft}>
                      <Text style={styles.operationText}>
                        {item.service_str}
                      </Text>
                      <Text style={styles.operationValue}>
                        {item.service_num}
                      </Text>
                    </View>
                    <View style={styles.operationDivider} />
                    <View style={styles.operationRight}>
                      <Text style={styles.operationRightText}>已有</Text>
                      <Text style={styles.operationRightValue}>
                        {item.doctor_num}
                      </Text>
                      <Text style={styles.operationRightText}>
                        名医生获得认证
                      </Text>
                    </View>
                  </View>
                </View>
              );
            })}
          </ScrollView>
        </View>
      </Modal>
    </>
  );
};

DoctorAuthPop.displayName = 'DoctorAuthPop';

const styles = StyleSheet.create({
  // 主容器样式 - 按设计稿要求设置高度90px
  soyoungAuthentication: {
    width: '100%',
    height: 90,
    flexDirection: 'column',
    paddingHorizontal: getRealSize(20),
    marginTop: getRealSize(10),
  },

  // 标题区域 - 对齐原工程.soyoung-authentication-tag
  soyoungAuthenticationTag: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginBottom: getRealSize(10),
  },
  point: {
    width: getRealSize(5), // 对齐原工程10rpx，转换为5px
    height: getRealSize(5), // 对齐原工程10rpx，转换为5px
    backgroundColor: '#00ab84',
    borderRadius: getRealSize(2.5), // 对应的圆角半径
    marginRight: getRealSize(5),
  },
  tagName: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(14), // 对齐原工程Vue组件字体大小
    color: '#303233',
    fontWeight: '400',
  },

  // 内容区域 - 对齐原工程.soyoung-authentication-body
  soyoungAuthenticationBody: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },

  // 认证列表 - 对齐原工程.soyoung-authentication-list
  soyoungAuthenticationList: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
    flex: 1,
  },

  // 认证标签项 - 对齐原工程.soyoung-authentication-list-item
  soyoungAuthenticationListItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginRight: getRealSize(10),
    marginBottom: getRealSize(12),
    overflow: 'hidden', // 确保圆角效果
  },
  listItemTitle: {
    height: getRealSize(16), // 对齐原工程32rpx，转换为16px
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: getRealSize(3), // 对齐原工程6rpx转换为3px
    // 去掉圆角，改为直角
  },
  titleText: {
    height: getRealSize(16),
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(10), // 对齐原工程20rpx转换为10px
    fontWeight: '500',
    color: '#ffffff',
    textAlignVertical: 'center', // 确保文字垂直居中
    includeFontPadding: false, // 去掉Android默认字体内边距
  },
  listItemDescription: {
    height: getRealSize(16), // 对齐原工程32rpx，转换为16px
    paddingHorizontal: getRealSize(3), // 对齐原工程6rpx转换为3px
    flexDirection: 'row',
    alignItems: 'center',
    // 去掉圆角，改为直角
  },
  descriptionText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(9), // 对齐原工程10px字体大小
    fontWeight: '400',
  },
  descriptionImage: {
    width: getRealSize(12),
    height: getRealSize(12),
    marginLeft: getRealSize(3),
  },

  // 标签样式变体 - 对齐原工程&.black, &.disabled, &.orange
  orangeTitle: {
    backgroundColor: '#ff6c0e', // 对齐原工程橙色背景
  },
  orangeDescription: {
    backgroundColor: '#ffeee2', // 对齐原工程橙色描述背景
  },
  orangeDescriptionText: {
    color: '#ff6c0e', // 对齐原工程橙色文字
  },

  blackTitle: {
    backgroundColor: '#333333', // 对齐原工程黑色背景
  },
  blackDescription: {
    backgroundColor: '#ebedea', // 对齐原工程灰色描述背景
  },
  blackDescriptionText: {
    color: '#333333', // 对齐原工程黑色文字
  },

  disabledTitle: {
    backgroundColor: '#333333',
    opacity: 0.5, // 对齐原工程禁用透明度
  },
  disabledDescription: {
    backgroundColor: '#ebedea',
  },
  disabledDescriptionText: {
    color: '#777777', // 对齐原工程禁用文字颜色
  },

  // 右侧箭头区域 - 对齐原工程.soyoung-authentication-img
  soyoungAuthenticationImg: {
    width: getRealSize(16), // 对齐原工程32rpx，转换为16px
    height: getRealSize(16), // 对齐原工程32rpx，转换为16px
    alignItems: 'center',
    justifyContent: 'center',
  },
  arrowImage: {
    width: getRealSize(6), // 对齐原工程12rpx，转换为6px
    height: getRealSize(10), // 对齐原工程20rpx，转换为10px
  },

  // react-native-modal样式
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  popBody: {
    width: screenWidth,
    maxHeight: screenHeight * 0.65, // 限制最大高度为屏幕一半
    minHeight: getRealSize(460),
    backgroundColor: '#F8F8F8',
    // 按设计稿要求去掉圆角
  },
  commonPopupTop: {
    paddingHorizontal: getRealSize(16),
    paddingVertical: getRealSize(16),
    backgroundColor: '#ffffff',
  },
  commonPopupTopBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  commonPopupCancel: {
    width: getRealSize(24),
  },
  commonPopupTitle: {
    fontSize: getRealSize(18),
    fontWeight: '600',
    color: '#333333',
  },
  commonPopupClose: {
    width: getRealSize(44),
    height: getRealSize(24),
    justifyContent: 'center',
    alignItems: 'center',
  },
  commonPopupCloseIcon: {
    width: getRealSize(20),
    height: getRealSize(20),
  },

  // 弹窗内容 - 对齐原工程.common-popup-body
  commonPopupBody: {
    height: getRealSize(602),
    paddingTop: getRealSize(10),
  },
  descContainer: {
    flexDirection: 'column',
    flexWrap: 'wrap',
    alignItems: 'center',
    marginBottom: getRealSize(8),
  },
  descContainerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  // === 根据设计稿新增的样式定义 ===
  // 新的认证项目容器
  newAuthItem: {
    paddingHorizontal: getRealSize(10),
    paddingVertical: getRealSize(10),
    marginBottom: getRealSize(15),
    marginLeft: getRealSize(15),
    width: getRealSize(345),
    backgroundColor: '#ffffff',
  },

  // 认证标题区域
  authHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: getRealSize(5),
  },

  // 认证标题文字
  authTitle: {
    fontSize: getRealSize(15),
    fontWeight: '600',
    color: '#333333',
    flex: 1,
  },

  // 状态标签基础样式
  statusTag: {
    paddingHorizontal: getRealSize(10),
    paddingVertical: getRealSize(4),
    minWidth: getRealSize(56),
    alignItems: 'center',
  },

  // 已获得状态标签
  obtainedTag: {
    backgroundColor: '#EBFBDC',
  },

  // 未获得状态标签
  notObtainedTag: {
    backgroundColor: '#f0f0f0',
  },

  // 状态文字基础样式
  statusText: {
    fontSize: getRealSize(12),
    fontWeight: '500',
  },

  // 已获得状态文字
  obtainedText: {
    color: '#61B43E',
  },

  // 未获得状态文字
  notObtainedText: {
    color: '#999999',
  },

  // 认证描述
  authDesc: {
    fontSize: getRealSize(12),
    color: '#61B43E',
    marginBottom: getRealSize(5),
  },

  // 收起状态的描述文本
  authDescCollapsed: {
    flex: 1,
  },

  // 展开/收起按钮
  expandButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  expandButtonText: {
    fontSize: getRealSize(12),
    color: '#333333',
  },
  expandButtonIcon: {
    width: getRealSize(10),
    height: getRealSize(6),
    marginLeft: getRealSize(4),
  },
  // 进度数值
  progressValue: {
    fontSize: getRealSize(11),
    color: '#61B43E',
    fontWeight: '600',
  },

  // 进度容器 - 对齐Vue版本的.common-popup-item-progress
  progressContainer: {
    marginBottom: getRealSize(10),
  },

  // 进度信息行
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getRealSize(12),
  },

  // 进度文本
  progressText: {
    fontSize: getRealSize(12),
    color: '#333333',
  },

  // 当前进度数值
  progressCurrent: {
    color: '#61B43E',
  },

  // 进度条容器
  progressBar: {
    height: getRealSize(7),
    backgroundColor: '#F2F2F2',
    borderRadius: getRealSize(3.5),
    position: 'relative',
  },

  // 进度条填充
  progressBarInner: {
    height: '100%',
    backgroundColor: '#A9EA6A',
    borderRadius: getRealSize(3.5),
    position: 'relative',
  },

  // 进度条圆点
  progressDot: {
    position: 'absolute',
    right: getRealSize(-1.75),
    top: '50%',
    transform: [{ translateY: getRealSize(-3.5) }],
    width: getRealSize(7),
    height: getRealSize(7),
    backgroundColor: '#ffffff',
    borderWidth: getRealSize(0.5),
    borderColor: '#61B43E',
    borderRadius: getRealSize(3.5),
  },

  // 操作信息容器 - 对齐Vue版本的.common-popup-item-operation
  operationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  // 操作左侧
  operationLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  // 操作文本
  operationText: {
    fontSize: getRealSize(12),
    color: '#aaabb3',
    marginRight: getRealSize(4),
  },

  // 操作数值
  operationValue: {
    fontSize: getRealSize(12),
    color: '#303233',
  },

  // 操作分隔符
  operationDivider: {
    width: getRealSize(1),
    height: getRealSize(10),
    backgroundColor: '#d8d8d8',
    marginHorizontal: getRealSize(10),
  },

  // 操作右侧
  operationRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  // 操作右侧文本
  operationRightText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#aaabb3',
    fontWeight: '400',
  },

  // 操作右侧数值
  operationRightValue: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#aaabb3',
    fontWeight: '400',
    marginHorizontal: getRealSize(2),
  },
});

export default DoctorAuthPop;
