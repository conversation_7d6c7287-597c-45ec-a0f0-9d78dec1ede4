import React, { useState, useCallback } from 'react';
import { View, Text, Image, ScrollView, StyleSheet, Alert } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { go } from '@soyoung/react-native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ATrack } from '@soyoung/react-native-container';
import api, { FetchModule } from '../../../common/api';
import { getRealSize } from '../../../common/utils';
import DoctorAuthPop from '../components/DoctorAuthPop';
import MySkeleton from '../components/MySkeleton';
import { getNativeLoginInfo } from '@/common/getNativeLoginInfo';
import jsApi from '@soyoung/react-native-jsapi';
import { NotifyCrossStatusChangeFunc } from '@/constant/cross_event';
import { clearExtInfo } from '@/common/updateLoginExtInfo';
interface UserInfo {
  name?: string;
  mobile?: string;
  avatar?: string;
  tenant_name?: string;
  user_id?: string;
  tenant_id?: string;
  tenant_user_id?: string;
  roles?: Array<{ id: string; name: string }>;
}

interface CrossInfo {
  canCross: number;
  crossId: string;
  crossUserName: string;
  crossUserId: string;
}

interface ApiResponse<T = any> {
  errorCode: number;
  errorMsg: string;
  responseData: T;
}

interface MyProps {
  pageShowFlag?: boolean;
  crossChange?: boolean;
  loginStatus?: boolean;
}

const MyPageLayout: React.FC<MyProps> = props => {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [crossInfo, setCrossInfo] = useState<CrossInfo>({
    canCross: 0,
    crossId: '',
    crossUserName: '',
    crossUserId: '',
  });
  const [isCross, setIsCross] = useState<boolean>(false);
  const [loading, setLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLogin, setIsLogin] = useState(false);

  // 组件引用
  const insets = useSafeAreaInsets();

  // 获取用户信息
  const getUserInfo = async () => {
    setLoading(true);
    try {
      const response: ApiResponse<UserInfo> = await api.reactNativeFetch(
        'chain-wxapp/v1/user/info',
        {},
        FetchModule.Method.POST,
        false
      );

      // console.log('*** 获取用户信息响应 ***', response);

      if (response.errorCode === 0 && response.responseData) {
        const userData = response.responseData;
        const appLoginInfo = await getNativeLoginInfo();
        const extInfo = appLoginInfo?.ext_info
          ? JSON.parse(appLoginInfo?.ext_info || '{}')
          : null;
        setIsCross(extInfo?.cross_token ? true : false);
        setUserInfo(userData);

        // 获取协同信息 - 移到try-catch外部，避免影响主流程
        const originUserId = await AsyncStorage.getItem('origin_user_id');
        const userId = userData.user_id;

        // 异步获取协同信息，不阻塞主流程
        setTimeout(() => {
          getCrossInfo({
            origin_user_id: originUserId || userId,
          });
        }, 0);
      } else {
        Alert.alert('提示', response.errorMsg || '获取用户信息失败');
      }
    } catch (error: any) {
      Alert.alert('网络异常', `请稍后重试: ${error.message || error}`);
    } finally {
      setLoading(false);
    }
  };

  // 获取协同信息
  const getCrossInfo = async (params: { origin_user_id?: string }) => {
    try {
      const response: ApiResponse<any> = await api.reactNativeFetch(
        'chain-wxapp/v1/user/crossInfo',
        params,
        FetchModule.Method.POST,
        false
      );

      if (response.errorCode === 0 && response.responseData) {
        setCrossInfo({
          canCross: response.responseData.cross_yn,
          crossId: response.responseData.cross_id,
          crossUserName: response.responseData.cross_user_name,
          crossUserId: response.responseData.cross_user_id,
        });
      }
    } catch (error) {
      if (__DEV__) {
        console.error('获取协同信息失败:', error);
      }
    }
  };

  // 菜单项点击处理 - 使用@soyoung/react-native-base的go方法
  const handleMenuClick = useCallback((type: string) => {
    try {
      switch (type) {
        case 'store':
          // 跳转到选择门店页面
          go('app.soyoung://rn/staff/store');
          break;
        case 'about':
          // 跳转到关于页面
          go('app.soyoung://rn/staff/about');
          break;
        case 'cross':
          // 跳转到账号协同页面
          go('app.soyoung://rn/staff/cross');
          break;
        default:
          break;
      }
    } catch (error) {
      if (__DEV__) {
        console.error('页面跳转失败:', error);
      }
      Alert.alert('提示', '页面跳转失败，请稍后重试');
    }
  }, []);

  // 退出协同
  const handleCancelCross = useCallback(async () => {
    const appLoginInfo = await getNativeLoginInfo();
    const extInfo = JSON.parse(appLoginInfo?.ext_info || '{}');

    if (!extInfo?.cross_token) {
      Alert.alert('提示', '未找到cross_token');
      return;
    }
    try {
      await clearExtInfo();
      jsApi.toNative('showToast', {
        toast: '退出协同成功',
      });
      getUserInfo();
      if (__DEV__) {
        console.log('updateLoginExtInfo success');
      }
    } catch (e) {
      jsApi.toNative('showToast', {
        toast: '退出协同失败',
      });
      if (__DEV__) {
        console.log('updateLoginExtInfo error', e);
      }
    }

    NotifyCrossStatusChangeFunc({
      status: false,
    });
  }, []);

  // 页面聚焦时刷新数据
  useFocusEffect(
    React.useCallback(() => {
      console.log('MyPage useFocusEffect, pageShowFlag:', props);
      getNativeLoginInfo().then(res => {
        if (res?.uid && res?.uid !== '0') {
          setIsLogin(true);
          // 只在首次加载或props变化时获取用户信息，避免弹窗打开时重复加载
          if (
            !isInitialized ||
            props.crossChange !== undefined ||
            props.loginStatus !== undefined
          ) {
            getUserInfo();
            setIsInitialized(true);
          }
        } else {
          setIsLogin(false);
        }
      });
    }, [props.crossChange, props.loginStatus, isInitialized])
  );

  // 如果没有用户信息或正在加载，显示骨架图
  if (!userInfo || loading) {
    return <MySkeleton />;
  }

  return (
    <View style={styles.myContent}>
      {/* 全屏背景图 - 延伸到状态栏，总高度保持203px */}
      <Image
        style={[
          styles.myInfoBackground,
          {
            top: -insets.top,
            height: getRealSize(203) + insets.top,
          },
        ]}
        source={{
          uri: 'https://static.soyoung.com/sy-design/2kfqh6xtc906d1753263811623.png',
        }}
        resizeMode='cover'
      />
      <ScrollView
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        {/* 用户信息区域 - 添加背景图，高度203px */}
        <View style={styles.myInfo}>
          <View style={styles.myInfoMain}>
            <Image
              style={styles.myInfoAvatar}
              source={{
                uri:
                  userInfo.avatar ||
                  'https://static.soyoung.com/sy-pre/trdenyu27i2z-1711350600709.png',
              }}
              resizeMode='cover'
            />
            <View
              style={[
                styles.myInfoRight,
                { width: isCross ? getRealSize(240) : getRealSize(320) },
              ]}
            >
              <Text style={styles.myInfoName}>{userInfo.name}</Text>
              <Text style={styles.myInfoMobile}>{userInfo.mobile}</Text>
              <View
                style={[
                  styles.myInfoRoles,
                  { width: isCross ? getRealSize(160) : getRealSize(200) },
                ]}
              >
                {userInfo.roles?.map(role => (
                  <View
                    key={role.id}
                    style={[
                      styles.myInfoRoleBox,
                      role.name === '店长'
                        ? styles.myInfoRoleManager
                        : styles.myInfoRole,
                    ]}
                  >
                    <Text
                      style={{
                        color: '#FFFFFF',
                        fontSize: getRealSize(10),
                        lineHeight: getRealSize(18),
                      }}
                    >
                      {role.name}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          </View>

          {/* 退出协同按钮 */}
          {isCross ? (
            <View style={styles.myInfoLogout}>
              <ATrack
                style={styles.myInfoLogoutBox}
                onPress={handleCancelCross}
                hitSlop={{
                  top: 30,
                  bottom: 30,
                  left: 30,
                  right: 30,
                }}
              >
                <Image
                  style={styles.myInfoLogoutIcon}
                  source={{
                    uri: 'https://static.soyoung.com/sy-pre/3o57pepxjqh9b-1711357800679.png',
                  }}
                  resizeMode='contain'
                />
                <Text style={styles.myInfoLogoutText}>退出协同</Text>
              </ATrack>
            </View>
          ) : null}
        </View>

        {/* 新氧认证组件 */}
        {isLogin ? (
          <DoctorAuthPop
            crossChange={props.crossChange}
            loginStatus={props.loginStatus}
            pageShowFlag={props.pageShowFlag}
            isDoctor={userInfo.roles?.some(
              role => role.name === '医生' && role.id === 'doctor'
            )}
          />
        ) : null}

        {/* 认证组件与下方的间隔 */}
        <View style={styles.authGap} />

        {/* 功能菜单列表 - 按设计稿调整顺序和样式 */}
        <View style={styles.myMenu}>
          {/* 门店 */}
          <View style={styles.myMenuItemLine}>
            <ATrack
              style={styles.myMenuItem}
              onPress={() => handleMenuClick('store')}
            >
              <View style={styles.myMenuItemLeft}>
                <Image
                  style={styles.myMenuItemLeftIcon}
                  source={{
                    uri: 'https://static.soyoung.com/sy-design/9idhpmii49m41753263811817.png',
                  }}
                  resizeMode='contain'
                />
                <Text style={styles.myMenuItemLeftText}>切换门店</Text>
              </View>
              <View style={styles.myMenuItemRight}>
                <Text
                  style={styles.myMenuItemName}
                  numberOfLines={1}
                  ellipsizeMode='tail'
                >
                  {userInfo.tenant_name}
                </Text>
                <Image
                  style={styles.myMenuItemIcon}
                  source={{
                    uri: 'https://static.soyoung.com/sy-design/ufwlaak4j0861744799639572.png',
                  }}
                  resizeMode='contain'
                />
              </View>
            </ATrack>
          </View>

          {/* 账号协同 */}
          {crossInfo.canCross ? (
            <View style={styles.myMenuItemLine}>
              <ATrack
                style={styles.myMenuItem}
                onPress={() => handleMenuClick('cross')}
              >
                <View style={styles.myMenuItemLeft}>
                  <Image
                    style={styles.myMenuItemLeftIcon}
                    source={{
                      uri: 'https://static.soyoung.com/sy-design/13bdoz1jztnyd1753263811782.png',
                    }}
                    resizeMode='contain'
                  />
                  <Text style={styles.myMenuItemLeftText}>账号协同</Text>
                </View>
                <View style={styles.myMenuItemRight}>
                  <Text style={styles.myMenuItemName}>
                    {isCross ? userInfo.name : ''}
                  </Text>
                  <Image
                    style={styles.myMenuItemIcon}
                    source={{
                      uri: 'https://static.soyoung.com/sy-design/ufwlaak4j0861744799639572.png',
                    }}
                    resizeMode='contain'
                  />
                </View>
              </ATrack>
            </View>
          ) : null}

          {/* 设置（原关于我们） */}
          <View style={styles.myMenuItemLine}>
            <ATrack
              style={[styles.myMenuItem, styles.myMenuItemLast]}
              onPress={() => handleMenuClick('about')}
            >
              <View style={styles.myMenuItemLeft}>
                <Image
                  style={styles.myMenuItemLeftIcon}
                  source={{
                    uri: 'https://static.soyoung.com/sy-design/34dco7xf0m0sd1753263811788.png',
                  }}
                  resizeMode='contain'
                />
                <Text style={styles.myMenuItemLeftText}>设置</Text>
              </View>
              <View style={styles.myMenuItemRight}>
                <Image
                  style={styles.myMenuItemIcon}
                  source={{
                    uri: 'https://static.soyoung.com/sy-design/ufwlaak4j0861744799639572.png',
                  }}
                  resizeMode='contain'
                />
              </View>
            </ATrack>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  // 主容器样式 - 完全对齐原始设计
  myContent: {
    flex: 1,
    backgroundColor: '#ffffff',
    flexDirection: 'column' as 'column',
    position: 'relative' as 'relative',
    width: '100%',
  },
  // 用户信息区域 - 添加背景图，高度203px
  myInfo: {
    height: getRealSize(203),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'relative',
    width: '100%',
  },

  // 背景图样式
  myInfoBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
  },

  myInfoMain: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    position: 'absolute',
    bottom: getRealSize(20),
    left: getRealSize(15),
    flex: 1,
  },

  myInfoAvatar: {
    width: getRealSize(80),
    height: getRealSize(80),
    borderRadius: getRealSize(40),
    borderWidth: getRealSize(1),
    borderColor: '#ffffff',
  },

  myInfoRight: {
    marginLeft: getRealSize(20),
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },

  myInfoName: {
    fontSize: getRealSize(20),
    color: '#161616',
    fontWeight: '500',
  },

  myInfoMobile: {
    fontSize: getRealSize(15),
    color: '#646464',
    marginTop: getRealSize(5),
  },

  myInfoRoles: {
    width: getRealSize(150),
    flexDirection: 'row',
    marginTop: getRealSize(8),
    flexWrap: 'wrap',
    maxHeight: getRealSize(58), // 精确计算：3行角色标签高度 (18+2+18+2+18=58px)
    overflow: 'hidden',
  },
  myInfoRoleBox: {
    marginRight: getRealSize(2),
    marginBottom: getRealSize(2),
    height: getRealSize(18), // 固定高度，确保每个角色标签高度一致
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flexWrap: 'wrap',
  },
  myInfoRole: {
    fontSize: getRealSize(10),
    color: '#ffffff',
    backgroundColor: '#A9EA6A',
    paddingHorizontal: getRealSize(3),
    alignItems: 'center',
    justifyContent: 'center',
  },

  myInfoRoleManager: {
    fontSize: getRealSize(10),
    color: '#ffffff',
    backgroundColor: '#030303',
    paddingVertical: getRealSize(1),
    paddingHorizontal: getRealSize(3),
    alignItems: 'center',
    justifyContent: 'center',
  },

  myInfoLogout: {
    borderWidth: 1,
    borderColor: '#f0f0f0',
    borderRadius: getRealSize(4),
    padding: getRealSize(10),
    flexDirection: 'row',
    alignItems: 'center',
    position: 'absolute',
    bottom: getRealSize(40),
    right: getRealSize(15),
    flex: 1,
  },
  myInfoLogoutBox: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  myInfoLogoutIcon: {
    width: getRealSize(13),
    height: getRealSize(14),
    marginRight: getRealSize(5),
  },

  myInfoLogoutText: {
    fontSize: getRealSize(12),
    color: '#555555',
    fontWeight: '500',
  },

  // 认证组件与下方的间隔
  authGap: {
    height: getRealSize(10),
    backgroundColor: '#f8f8f8',
  },

  // 功能菜单样式 - 按设计稿调整
  myMenu: {
    paddingHorizontal: getRealSize(20),
    marginHorizontal: 0,
    flexDirection: 'column',
    backgroundColor: '#ffffff',
  },

  myMenuItemLine: {
    borderBottomWidth: 1,
    borderBottomColor: '#f2f2f2',
  },

  myMenuItem: {
    paddingVertical: getRealSize(20),
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#f2f2f2',
  },

  myMenuItemLast: {
    borderBottomWidth: 0,
  },

  myMenuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  myMenuItemLeftIcon: {
    width: getRealSize(23),
    height: getRealSize(23),
    marginRight: getRealSize(12),
  },

  myMenuItemLeftText: {
    fontSize: getRealSize(16),
    color: '#333333',
    fontWeight: '500',
  },

  myMenuItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: getRealSize(10),
    justifyContent: 'flex-end',
  },

  myMenuItemName: {
    maxWidth: getRealSize(200),
    fontSize: getRealSize(14),
    color: '#030303',
    textAlign: 'right',
    lineHeight: getRealSize(20),
    fontWeight: '500',
  },

  myMenuItemIcon: {
    width: getRealSize(6), // 统一为医生认证组件箭头样式
    height: getRealSize(10), // 统一为医生认证组件箭头样式
    marginLeft: getRealSize(7),
  },
});

export default MyPageLayout;
