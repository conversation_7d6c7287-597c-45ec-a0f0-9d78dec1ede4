import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
  Image,
  TextInput,
  Platform,
} from 'react-native';
import Modal from 'react-native-modal';
import { getRealSize } from '../../../../common/utils';
import { UserItem, CurrentUser } from '../types';
import { modalAnimation } from '@/constant/modal_animation';
import { ATrack } from '@soyoung/react-native-container';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface UserModalProps {
  visible: boolean;
  userList: UserItem[];
  currentUser: CurrentUser;
  onClose: () => void;
  onSelect: (user: UserItem) => void;
}

const UserModal: React.FC<UserModalProps> = ({
  visible,
  userList,
  currentUser,
  onClose,
  onSelect,
}) => {
  const [selectedUser, setSelectedUser] = React.useState<UserItem | null>(null);
  const [searchText, setSearchText] = React.useState<string>('');

  // 当弹窗打开时，初始化选中状态
  React.useEffect(() => {
    if (visible) {
      const current = userList.find(item => item.value === currentUser.id);
      setSelectedUser(current || null);
    }
  }, [visible, userList, currentUser.id]);

  const handleItemSelect = (item: UserItem) => {
    setSelectedUser(item);
  };

  const handleConfirm = () => {
    if (selectedUser) {
      onSelect(selectedUser);
    }
    onClose();
  };

  const handleCancel = () => {
    setSearchText(''); // 清空搜索文本
    onClose();
  };

  // 过滤员工列表
  const filteredUserList = React.useMemo(() => {
    if (!searchText.trim()) {
      return userList;
    }
    return userList.filter(
      item =>
        item.label.toLowerCase().includes(searchText.toLowerCase()) ||
        (item.user_id && String(item.user_id).includes(String(searchText))) ||
        (item.tenant_name &&
          item.tenant_name.toLowerCase().includes(searchText.toLowerCase()))
    );
  }, [userList, searchText]);

  const renderContent = () => {
    return (
      <>
        {/* 弹窗头部 */}
        <View style={styles.commonPopupTop}>
          <View style={styles.commonPopupTopBar}>
            <ATrack style={styles.commonPopupCancel} onPress={handleCancel}>
              <Text style={styles.cancelText}>取消</Text>
            </ATrack>
            <Text style={styles.commonPopupTitle}>选择员工</Text>
            <ATrack style={styles.commonPopupClose} onPress={handleConfirm}>
              <Text style={styles.confirmText}>确认</Text>
            </ATrack>
          </View>
        </View>

        {/* 搜索框 */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Image
              source={{
                uri: 'https://static.soyoung.com/sy-pre/1y4a2d4fr2l31-1711955400686.png',
              }}
              style={styles.searchIcon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder='请输入员工姓名'
              placeholderTextColor='#bababa'
              value={searchText}
              onChangeText={setSearchText}
              clearButtonMode='while-editing'
            />
          </View>
        </View>

        {/* 弹窗内容 */}
        <ScrollView
          style={styles.commonPopupBody}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          overScrollMode='never'
          bounces={false}
          nestedScrollEnabled={false}
          scrollEventThrottle={16}
        >
          {filteredUserList.map((item, index) => (
            <ATrack
              key={`record_${item.value}_${index}`}
              style={styles.commonPopupItem}
              onPress={() => handleItemSelect(item)}
            >
              <View
                style={[
                  styles.userCard,
                  selectedUser?.value === item.value && styles.activeItem,
                ]}
              >
                <Image
                  source={{
                    uri:
                      item.avatar ||
                      'https://img2.baidu.com/it/u=1978192862,2048448374&fm=253&fmt=auto&app=138&f=JPEG?w=504&h=500',
                  }}
                  style={styles.userAvatar}
                />
                <View style={styles.userInfo}>
                  <Text style={styles.userName}>{item.label}</Text>
                  <Text style={styles.userSubTitle}>
                    员工号：{item.user_id || ''}
                  </Text>
                  {item.tenant_name ? (
                    <Text style={styles.userStore}>
                      {item.tenant_name || ''}
                    </Text>
                  ) : null}
                </View>
              </View>
            </ATrack>
          ))}
        </ScrollView>
      </>
    );
  };

  return (
    <Modal
      isVisible={visible}
      {...modalAnimation}
      onBackdropPress={handleCancel}
      animationIn='slideInUp'
      animationOut='slideOutDown'
      useNativeDriverForBackdrop={true}
      style={styles.modal}
      avoidKeyboard={false}
      statusBarTranslucent={Platform.OS === 'android'}
    >
      {Platform.OS === 'android' ? (
        <View
          style={{
            flex: 1,
            justifyContent: 'flex-end', // 使用固定定位避免键盘影响
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
          }}
        >
          <View
            style={{
              ...styles.popBody,
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
            }}
          >
            {renderContent()}
          </View>
        </View>
      ) : (
        <View style={styles.popBody}>{renderContent()}</View>
      )}
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  popBody: {
    width: screenWidth,
    maxHeight: screenHeight * 0.65,
    minHeight: getRealSize(560),
    backgroundColor: '#ffffff',
  },
  commonPopupTop: {
    paddingHorizontal: getRealSize(16),
    paddingTop: getRealSize(16),
  },
  commonPopupTopBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  commonPopupCancel: {
    width: getRealSize(44),
    height: getRealSize(24),
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  commonPopupTitle: {
    fontSize: getRealSize(18),
    fontWeight: '600',
    color: '#333333',
    fontFamily: 'PingFangSC-Medium',
  },
  commonPopupClose: {
    width: getRealSize(44),
    height: getRealSize(24),
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelText: {
    fontSize: getRealSize(14),
    color: '#999999',
    fontWeight: '400',
  },
  confirmText: {
    fontSize: getRealSize(14),
    color: '#61B43E',
    fontWeight: '500',
  },
  commonPopupBody: {
    maxHeight: getRealSize(400),
    backgroundColor: '#F8F8F8',
  },
  scrollContent: {
    alignItems: 'center',
    paddingBottom: getRealSize(10),
    paddingHorizontal: getRealSize(15),
  },
  commonPopupItem: {
    backgroundColor: '#ffffff',
    marginTop: getRealSize(10),
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeItem: {
    backgroundColor: '#EBFBDC',
    borderColor: '#61B43E',
  },
  userCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    width: '100%',
    paddingHorizontal: getRealSize(15),
    paddingVertical: getRealSize(15),
    borderWidth: 1,
    borderColor: '#FFFFFF',
  },
  userAvatar: {
    width: getRealSize(40),
    height: getRealSize(40),
    borderRadius: getRealSize(20),
    marginRight: getRealSize(10),
  },
  userInfo: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  userName: {
    fontSize: getRealSize(14),
    color: '#333333',
    fontFamily: 'PingFangSC-Medium',
    fontWeight: '500',
    letterSpacing: 0,
    marginBottom: getRealSize(4),
  },
  userSubTitle: {
    fontSize: getRealSize(11),
    color: '#777777',
    fontFamily: 'PingFangSC-Regular',
    fontWeight: '400',
    letterSpacing: 0,
  },
  userStore: {
    fontSize: getRealSize(11),
    color: '#999999',
    fontFamily: 'PingFangSC-Regular',
    marginTop: getRealSize(4),
  },
  searchContainer: {
    paddingHorizontal: getRealSize(15),
    paddingVertical: getRealSize(12),
    backgroundColor: '#ffffff',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: getRealSize(36),
    backgroundColor: '#f8f8f8',
    paddingHorizontal: getRealSize(10),
  },
  searchIcon: {
    width: getRealSize(16),
    height: getRealSize(16),
    marginRight: getRealSize(5),
    tintColor: '#999999',
  },
  searchInput: {
    flex: 1,
    fontSize: getRealSize(15),
    color: '#333333',
    fontFamily: 'PingFangSC-Regular',
    textAlignVertical: 'center',
    includeFontPadding: false,
    padding: 0,
  },
});

export default UserModal;
