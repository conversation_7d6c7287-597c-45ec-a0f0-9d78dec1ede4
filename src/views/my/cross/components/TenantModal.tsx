import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
  TextInput,
  Image,
  Platform,
} from 'react-native';
import Modal from 'react-native-modal';
import { getRealSize } from '../../../../common/utils';
import { TenantItem, CurrentTenant } from '../types';
import { modalAnimation } from '../../../../constant/modal_animation';
import { ATrack } from '@soyoung/react-native-container';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface TenantModalProps {
  visible: boolean;
  tenantList: TenantItem[];
  currentTenant: CurrentTenant;
  onClose: () => void;
  onSelect: (tenant: TenantItem) => void;
}

const TenantModal: React.FC<TenantModalProps> = ({
  visible,
  tenantList,
  currentTenant,
  onClose,
  onSelect,
}) => {
  const [selectedTenant, setSelectedTenant] = React.useState<TenantItem | null>(
    null
  );
  const [searchText, setSearchText] = React.useState<string>('');

  // 当弹窗打开时，初始化选中状态
  React.useEffect(() => {
    if (visible) {
      const current = tenantList.find(item => item.value === currentTenant.id);
      setSelectedTenant(current || null);
    }
  }, [visible, tenantList, currentTenant.id]);

  const handleItemSelect = (item: TenantItem) => {
    setSelectedTenant(item);
  };

  const handleConfirm = () => {
    if (selectedTenant) {
      onSelect(selectedTenant);
    }
    onClose();
  };

  const handleCancel = () => {
    setSearchText(''); // 清空搜索文本
    onClose();
  };

  // 过滤门店列表
  const filteredTenantList = React.useMemo(() => {
    if (!searchText.trim()) {
      return tenantList;
    }
    return tenantList.filter(item =>
      item.label.toLowerCase().includes(searchText.toLowerCase())
    );
  }, [tenantList, searchText]);

  const renderContent = () => {
    return (
      <>
        {/* 弹窗头部 */}
        <View style={styles.commonPopupTop}>
          <View style={styles.commonPopupTopBar}>
            <ATrack style={styles.commonPopupCancel} onPress={handleCancel}>
              <Text style={styles.cancelText}>取消</Text>
            </ATrack>
            <Text style={styles.commonPopupTitle}>选择门店</Text>
            <ATrack style={styles.commonPopupClose} onPress={handleConfirm}>
              <Text style={styles.confirmText}>确认</Text>
            </ATrack>
          </View>
        </View>

        {/* 搜索框 */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Image
              source={{
                uri: 'https://static.soyoung.com/sy-pre/1y4a2d4fr2l31-1711955400686.png',
              }}
              style={styles.searchIcon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder='请输入门店名称'
              placeholderTextColor='#bababa'
              value={searchText}
              onChangeText={setSearchText}
              clearButtonMode='while-editing'
            />
          </View>
        </View>

        {/* 弹窗内容 */}
        <ScrollView
          style={styles.commonPopupBody}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          overScrollMode='never'
          bounces={false}
          nestedScrollEnabled={false}
          scrollEventThrottle={16}
        >
          {filteredTenantList.map((item, _index) => (
            <ATrack
              key={item.value}
              style={[styles.commonPopupItem]}
              onPress={() => handleItemSelect(item)}
            >
              <View
                style={[
                  styles.tenantItem,
                  selectedTenant?.value === item.value && styles.activeItem,
                ]}
              >
                <Text
                  style={[
                    styles.itemText,
                    selectedTenant?.value === item.value && styles.activeText,
                  ]}
                  numberOfLines={1}
                  ellipsizeMode='tail'
                >
                  {item.label}
                </Text>
              </View>
            </ATrack>
          ))}
        </ScrollView>
      </>
    );
  };
  return (
    <Modal
      isVisible={visible}
      {...modalAnimation}
      onBackdropPress={handleCancel}
      animationIn='slideInUp'
      animationOut='slideOutDown'
      style={styles.modal}
      avoidKeyboard={false}
      statusBarTranslucent={Platform.OS === 'android'}
    >
      {Platform.OS === 'android' ? (
        <View
          style={{
            flex: 1,
            justifyContent: 'flex-end', // 使用固定定位避免键盘影响
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
          }}
        >
          <View
            style={{
              ...styles.popBody,
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
            }}
          >
            {renderContent()}
          </View>
        </View>
      ) : (
        <View style={styles.popBody}>{renderContent()}</View>
      )}
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  popBody: {
    width: screenWidth,
    maxHeight: screenHeight * 0.65,
    minHeight: getRealSize(560),
    backgroundColor: '#ffffff',
  },
  commonPopupTop: {
    paddingHorizontal: getRealSize(16),
    paddingTop: getRealSize(16),
  },
  commonPopupTopBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  commonPopupCancel: {
    width: getRealSize(44),
    height: getRealSize(24),
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  commonPopupTitle: {
    fontSize: getRealSize(18),
    fontWeight: '600',
    color: '#333333',
    fontFamily: 'PingFangSC-Medium',
  },
  commonPopupClose: {
    width: getRealSize(44),
    height: getRealSize(24),
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelText: {
    fontSize: getRealSize(14),
    color: '#999999',
    fontWeight: '400',
  },
  confirmText: {
    fontSize: getRealSize(14),
    color: '#61B43E',
    fontWeight: '500',
  },
  commonPopupBody: {
    maxHeight: getRealSize(400),
    backgroundColor: '#F8F8F8',
  },
  scrollContent: {
    alignItems: 'center',
    paddingBottom: getRealSize(10),
  },
  commonPopupItem: {
    backgroundColor: '#ffffff',
    height: getRealSize(54),
    width: getRealSize(345),
    marginTop: getRealSize(10),
    alignItems: 'center',
    justifyContent: 'center',
  },
  tenantItem: {
    width: '100%',
    height: '100%',
    paddingHorizontal: getRealSize(15),
    paddingVertical: getRealSize(15),
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#FFFFFF',
  },
  activeItem: {
    backgroundColor: '#EBFBDC',
    borderWidth: 1,
    borderColor: '#61B43E',
  },
  itemText: {
    fontSize: getRealSize(15),
    color: '#333333',
    fontFamily: 'PingFangSC-Regular',
  },
  activeText: {
    color: '#61B43E',
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15),
    fontWeight: '400',
  },
  searchContainer: {
    paddingHorizontal: getRealSize(16),
    paddingVertical: getRealSize(12),
    backgroundColor: '#ffffff',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: getRealSize(36),
    backgroundColor: '#f8f8f8',
    paddingHorizontal: getRealSize(10),
  },
  searchIcon: {
    width: getRealSize(16),
    height: getRealSize(16),
    marginRight: getRealSize(5),
    tintColor: '#999999',
  },
  searchInput: {
    flex: 1,
    fontSize: getRealSize(15),
    color: '#333333',
    fontFamily: 'PingFangSC-Regular',
    textAlignVertical: 'center',
    includeFontPadding: false,
    padding: 0,
  },
});

export default TenantModal;
