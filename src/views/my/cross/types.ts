/*
 * 账号协同页面类型定义
 */

export interface TenantItem {
  label: string;
  value: number | string;
  tenant_name?: string;
  tenant_id?: number | string;
}

export interface UserItem {
  label: string;
  value: number | string;
  user_id: string;
  avatar?: string;
  tenant_name?: string;
}

export interface CurrentTenant {
  id: number | string;
  name: string;
}

export interface CurrentUser {
  id: number | string;
  name: string;
}

export interface CrossInRequest {
  cross_id: number | string;
  origin_user_id: number | string;
  select_tenant_id: number | string;
}

export interface CrossInResponse {
  token: string;
  permissions: any[];
  cross_to_uid: number | string;
}

export interface ApiResponse<T = any> {
  errorCode: number;
  errorMsg: string;
  responseData: T;
}
