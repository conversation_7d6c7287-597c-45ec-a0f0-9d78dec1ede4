import React, { useState, useCallback, useEffect } from 'react';
import { View, Text, StyleSheet, Image, Alert } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { back } from '@soyoung/react-native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import api, { FetchModule } from '../../../common/api';
import { getRealSize } from '../../../common/utils';
import TenantModal from './components/TenantModal';
import UserModal from './components/UserModal';
import jsApi from '@soyoung/react-native-jsapi';
import { ATrack } from '@soyoung/react-native-container';
import Header from '@/components/header';
import { SafeAreaView } from 'react-native-safe-area-context';

import {
  TenantItem,
  UserItem,
  CurrentTenant,
  CurrentUser,
  CrossInResponse,
  ApiResponse,
} from './types';
import { getNativeLoginInfo } from '../../../common/getNativeLoginInfo';
import { NotifyCrossStatusChangeFunc } from '@/constant/cross_event';
import { updateExtInfo } from '@/common/updateLoginExtInfo';

interface CrossTargetProps {
  pageShowFlag?: boolean;
}

const CrossTarget: React.FC<CrossTargetProps> = _props => {
  const [tenantList, setTenantList] = useState<TenantItem[]>([]);
  const [userList, setUserList] = useState<UserItem[]>([]);
  const [curTenant, setCurTenant] = useState<CurrentTenant>({
    id: NaN,
    name: '',
  });
  const [curUser, setCurUser] = useState<CurrentUser>({
    id: NaN,
    name: '',
  });
  const [tenantModalVisible, setTenantModalVisible] = useState(false);
  const [userModalVisible, setUserModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);

  // 获取门店列表 - 对应Vue的getTenantList方法
  const getTenantList = useCallback(async () => {
    try {
      const appLoginInfo = await getNativeLoginInfo();
      const userId = appLoginInfo.user_id;

      const response: ApiResponse = await api.reactNativeFetch(
        'chain-wxapp/v1/user/crossTenantList',
        {
          origin_user_id: userId,
        },
        FetchModule.Method.POST,
        false
      );

      if (response.errorCode === 0 && response.responseData) {
        const mappedList = response.responseData.map((item: any) => ({
          label: item.tenant_name,
          value: item.tenant_id,
        }));
        setTenantList(mappedList);

        // 门店列表获取完成后，检查穿越状态
        await checkCrossStatus(mappedList);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      Alert.alert('提示', '获取门店列表失败，请重试');
    }
  }, []);

  // 获取员工列表 - 对应Vue的getCrossUserList方法
  const getCrossUserList = useCallback(
    async (isFromCheckStatus = false) => {
      // console.log(
      //   'getCrossUserList 被调用，isFromCheckStatus:',
      //   isFromCheckStatus
      // );
      // console.log('curTenant.id:', curTenant.id);
      if (!curTenant.id || isNaN(Number(curTenant.id))) {
        // console.log('curTenant.id 无效，退出');
        return;
      }

      await getCrossUserListWithTenantId(
        Number(curTenant.id),
        isFromCheckStatus
      );
    },
    [curTenant.id]
  );

  // 根据门店ID获取员工列表
  const getCrossUserListWithTenantId = useCallback(
    async (tenantId: number, isFromCheckStatus = false) => {
      // console.log('getCrossUserListWithTenantId 被调用，tenantId:', tenantId);
      if (!tenantId || isNaN(Number(tenantId))) {
        // console.log('tenantId 无效，退出');
        return;
      }

      try {
        const appLoginInfo = await getNativeLoginInfo();
        const userId = appLoginInfo.user_id;

        const response: ApiResponse = await api.reactNativeFetch(
          'chain-wxapp/v1/user/crossUserList',
          {
            tenant_id: curTenant.id,
            origin_user_id: userId,
          },
          FetchModule.Method.POST,
          false
        );

        if (response.errorCode === 0 && response.responseData) {
          console.log('用户列表API响应:', response.responseData);
          const mappedList = response.responseData.map((item: any) => ({
            label: item.user_name,
            value: item.cross_id,
            user_id: item.user_id,
            avatar: item.avatar,
          }));
          // console.log('映射后的用户列表:', mappedList, isFromCheckStatus);
          setUserList(mappedList);

          // 如果是从checkCrossStatus调用的，需要设置curUser
          if (isFromCheckStatus) {
            // console.log('设置curUser，mappedList:', mappedList);
            const extInfo = appLoginInfo?.ext_info
              ? typeof appLoginInfo.ext_info === 'string'
                ? JSON.parse(appLoginInfo.ext_info)
                : appLoginInfo.ext_info
              : null;
            const crossId = extInfo?.cross_to_uid;
            // console.log('crossId:', crossId);

            if (
              crossId &&
              mappedList.some(
                (item: UserItem) => item.user_id === Number(crossId)
              )
            ) {
              const userName =
                mappedList.find(
                  (item: UserItem) => item.user_id === Number(crossId)
                )?.label || '';
              // console.log('设置curUser:', {
              //   id: Number(crossId),
              //   name: userName,
              // });
              setCurUser({
                id: Number(crossId) || NaN,
                name: userName,
              });
            } else {
              console.log('未找到匹配的用户或crossId为空');
            }
          }
        }
      } catch (error) {
        console.error('获取员工列表失败:', error);
        Alert.alert('提示', '获取员工列表失败，请重试');
      }
    },
    [curTenant.id]
  );

  // 处理门店选择 - 对应Vue的handleSelectTenant
  const handleSelectTenant = useCallback(() => {
    setTenantModalVisible(true);
  }, []);

  // 处理员工选择 - 对应Vue的handleSelectUser
  const handleSelectUser = useCallback(() => {
    if (curTenant.id && !isNaN(Number(curTenant.id))) {
      setUserModalVisible(true);
    } else {
      jsApi.toNative('showToast', {
        toast: '请先选择门店',
      });
    }
  }, [curTenant.id]);

  // 切换账号 - 对应Vue的handleCross
  const handleCross = useCallback(async () => {
    if (
      !curTenant.id ||
      !curUser.id ||
      isNaN(Number(curTenant.id)) ||
      isNaN(Number(curUser.id))
    ) {
      Alert.alert('提示', '请选择门店和员工');
      return;
    }
    try {
      setLoading(true);

      const userInfo = await jsApi.toNative('getLoginInfo');

      console.log('userInfo', userInfo);

      const response: ApiResponse<CrossInResponse> = await api.reactNativeFetch(
        'chain-wxapp/v1/user/crossIn',
        {
          cross_id: curUser.id,
          origin_user_id: userInfo.user_id,
          select_tenant_id: curTenant.id,
        },
        FetchModule.Method.POST
      );

      if (response.errorCode === 0 && response.responseData) {
        const { cross_to_uid, token, permissions, roles, tenant_id } =
          response.responseData;

        await updateExtInfo({
          cross_to_uid: cross_to_uid,
          cross_token: token,
          cross_permissions: permissions,
          cross_roles: roles,
          cross_tenant_id: tenant_id,
        });

        NotifyCrossStatusChangeFunc({
          status: true,
        });
        // 返回上一页
        back();
      } else {
        Alert.alert('提示', response.errorMsg || '切换账号失败');
      }
    } catch (error) {
      console.error('切换账号失败:', error);
      Alert.alert('提示', '切换账号失败，请重试');
    } finally {
      setLoading(false);
    }
  }, [curTenant.id, curUser.id]);

  // 门店选择回调
  const handleTenantSelect = useCallback((tenant: TenantItem) => {
    setCurTenant({
      id: tenant.value,
      name: tenant.label,
    });
    // 重置用户选择
    setCurUser({
      id: NaN,
      name: '',
    });
  }, []);

  // 员工选择回调
  const handleUserSelect = useCallback((user: UserItem) => {
    setCurUser({
      id: user.value,
      name: user.label,
    });
  }, []);

  const checkCrossStatus = useCallback(
    async (tenantListData?: TenantItem[]) => {
      const appLoginInfo = await getNativeLoginInfo();

      const extInfo = appLoginInfo?.ext_info
        ? typeof appLoginInfo.ext_info === 'string'
          ? JSON.parse(appLoginInfo.ext_info)
          : appLoginInfo.ext_info
        : null;
      if (extInfo?.cross_token) {
        // console.log('有穿越', tenantListData || tenantList);
        // console.log('extInfo:', extInfo);
        const crossTenantId = extInfo?.cross_tenant_id;
        const currentTenantList = tenantListData || tenantList;

        // console.log('crossTenantId:', crossTenantId);
        // console.log('currentTenantList:', currentTenantList);

        // 设置当前门店
        const tenantName =
          currentTenantList.find(
            (item: TenantItem) => item.value === Number(crossTenantId)
          )?.label || '';

        // console.log('tenantName:', tenantName);

        // 如果有门店信息，先设置门店，然后获取用户列表
        if (crossTenantId && tenantName) {
          // console.log('开始获取用户列表');
          // 先设置门店
          setCurTenant({
            id: Number(crossTenantId) || NaN,
            name: tenantName,
          });

          // 直接获取用户列表，不依赖curTenant状态
          try {
            const response: ApiResponse = await api.reactNativeFetch(
              'chain-wxapp/v1/user/crossUserList',
              {
                tenant_id: Number(crossTenantId),
                origin_user_id: appLoginInfo.user_id,
              },
              FetchModule.Method.POST,
              false
            );

            if (response.errorCode === 0 && response.responseData) {
              // console.log('用户列表API响应:', response.responseData);
              const mappedList = response.responseData.map((item: any) => ({
                label: item.user_name,
                value: item.cross_id,
                user_id: item.user_id,
                avatar: item.avatar,
              }));
              // console.log('映射后的用户列表:', mappedList, true);
              setUserList(mappedList);

              // 设置curUser
              const crossId = extInfo?.cross_to_uid;
              // console.log('crossId:', crossId);

              if (
                crossId &&
                mappedList.some(
                  (item: UserItem) => item.user_id === Number(crossId)
                )
              ) {
                const userName =
                  mappedList.find(
                    (item: UserItem) => item.user_id === Number(crossId)
                  )?.label || '';
                // console.log('设置curUser:', {
                //   id: Number(crossId),
                //   name: userName,
                // });
                setCurUser({
                  id: Number(crossId) || NaN,
                  name: userName,
                });
              } else {
                console.log('未找到匹配的用户或crossId为空');
              }
            }
          } catch (error) {
            console.error('获取员工列表失败:', error);
          }
        }
      }
    },
    [tenantList, getCrossUserList]
  );

  // 生命周期 - 对应Vue的onLoad
  useFocusEffect(
    useCallback(() => {
      setIsInitializing(true);
      getTenantList().finally(() => {
        setIsInitializing(false);
      });
    }, [getTenantList])
  );

  // 监听门店变化 - 对应Vue的watch
  useEffect(() => {
    if (curTenant.id && !isNaN(Number(curTenant.id)) && !isInitializing) {
      getCrossUserList(false);
    }
  }, [curTenant.id, getCrossUserList, isInitializing]);

  // 检查按钮是否可用
  const isButtonDisabled = !curUser.id || isNaN(Number(curUser.id));

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Header bgColor='#FFFFFF' title='账号协同选择' />
      {/* 门店选择 */}
      <ATrack style={styles.list} onPress={handleSelectTenant}>
        <Text style={styles.title}>协同门店</Text>
        <View style={styles.main}>
          <Text style={styles.name} numberOfLines={1} ellipsizeMode='tail'>
            {curTenant?.name || ''}
          </Text>
          <Image
            source={{
              uri: 'https://static.soyoung.com/sy-design/6eboz32amcrz1755145878468.png',
            }}
            style={styles.icon}
          />
        </View>
      </ATrack>

      {/* 分割线 */}
      <View style={styles.divider} />

      {/* 员工选择 */}
      <ATrack style={styles.list} onPress={handleSelectUser}>
        <Text style={styles.title}>协同员工</Text>
        <View style={styles.main}>
          <Text style={styles.name}>{curUser?.name || ''}</Text>
          <Image
            source={{
              uri: 'https://static.soyoung.com/sy-design/6eboz32amcrz1755145878468.png',
            }}
            style={styles.icon}
          />
        </View>
      </ATrack>

      {/* 切换账号按钮 */}
      <ATrack
        style={[styles.button, isButtonDisabled && styles.buttonDisabled]}
        onPress={handleCross}
      >
        <View style={styles.buttonContent}>
          <Text
            style={[
              styles.buttonText,
              isButtonDisabled && styles.buttonTextDisabled,
            ]}
          >
            {loading ? '切换中...' : '切换账号'}
          </Text>
        </View>
      </ATrack>

      {/* 门店选择弹窗 */}
      <TenantModal
        visible={tenantModalVisible}
        tenantList={tenantList}
        currentTenant={curTenant}
        onClose={() => setTenantModalVisible(false)}
        onSelect={handleTenantSelect}
      />

      {/* 员工选择弹窗 */}
      <UserModal
        visible={userModalVisible}
        userList={userList}
        currentUser={curUser}
        onClose={() => setUserModalVisible(false)}
        onSelect={handleUserSelect}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    flex: 1,
  },
  list: {
    flexDirection: 'row',
    paddingHorizontal: getRealSize(30),
    height: getRealSize(60),
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#ffffff',
  },
  title: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15),
    color: '#333333',
    flexShrink: 0,
  },
  main: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    maxWidth: getRealSize(270), // 540rpx转换
  },
  name: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(15), // 30rpx转换
    color: '#aaabb3',
    textAlign: 'right',
    lineHeight: getRealSize(20), // 40rpx转换
    fontWeight: '500',
    flex: 1,
  },
  icon: {
    width: getRealSize(6), // 16rpx转换
    height: getRealSize(9), // 28rpx转换
    marginLeft: getRealSize(8), // 22rpx转换
  },
  button: {
    position: 'absolute',
    bottom: getRealSize(33), // 66rpx转换
    left: getRealSize(23), // 46rpx转换
    right: getRealSize(23), // 46rpx转换
    height: getRealSize(42), // 80rpx转换
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonDisabled: {
    borderColor: '#333333',
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#333333',
    width: '100%',
    height: getRealSize(42),
  },
  buttonText: {
    color: '#333333',
    fontSize: getRealSize(13), // 32rpx转换
    fontFamily: 'PingFangSC-Medium',
    fontWeight: '500',
  },
  buttonTextDisabled: {
    color: '#333333',
  },
  divider: {
    width: getRealSize(345),
    height: getRealSize(0.5),
    backgroundColor: '#f0f0f0',
    alignSelf: 'center',
    marginVertical: 0,
  },
});

interface PageProps {}

class Cross extends React.Component<PageProps> {
  constructor(props: PageProps) {
    super(props);
    this.state = {
      pageShow: true,
    };
  }

  soyoungPageName() {
    return 'Cross';
  }

  /** 页面埋点 */
  soyoungPageInfo() {
    return {
      page_name: '账号协同',
      page_type: 'cross_account',
    };
  }

  didAppear() {
    this.setState({
      pageShow: true,
    });
  }

  willDisappear() {
    this.setState({
      pageShow: false,
    });
  }

  preferredStatusBarStyle() {
    // 0默认 1 白色 2 黑色
    return '2';
  }

  render() {
    return (
      <CrossTarget
        {...this.props}
        pageShowFlag={(this.state as any).pageShow}
      />
    );
  }
}

export default Cross;
