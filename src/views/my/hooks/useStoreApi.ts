import { useState, useCallback } from 'react';
import api, { FetchModule } from '../../../common/api';

/**
 * 门店相关API调用 Hook
 *
 * @description 管理门店列表获取、门店切换等API调用
 * @version 1.0.0 - 移除 mock 数据，使用真实接口
 */
export const useStoreApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取门店列表
  const fetchTenantList = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // 使用真实接口：/chain-wxapp/v1/user/tenantList
      const response = await api.reactNativeFetch(
        'chain-wxapp/v1/user/tenantList',
        {},
        FetchModule.Method.POST
      );

      if (response.errorCode === 0) {
        return response.responseData || [];
      } else {
        throw new Error(response.errorMsg || '获取门店列表失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '网络错误';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // 切换门店
  const changeTenant = useCallback(async (tenantUserId: string) => {
    setLoading(true);
    setError(null);

    try {
      // 使用真实接口：/chain-wxapp/v1/user/changeTenant
      const response = await api.reactNativeFetch(
        'chain-wxapp/v1/user/changeTenant',
        {
          tenant_user_id: tenantUserId,
        },
        FetchModule.Method.POST
      );

      if (response.errorCode === 0) {
        return response.responseData;
      } else {
        throw new Error(response.errorMsg || '切换门店失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '网络错误';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // 获取用户信息
  const fetchUserInfo = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // 使用真实接口：/chain-wxapp/v1/user/info
      const response = await api.reactNativeFetch(
        'chain-wxapp/v1/user/info',
        {},
        FetchModule.Method.POST
      );

      if (response.errorCode === 0) {
        return response.responseData;
      } else {
        throw new Error(response.errorMsg || '获取用户信息失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '网络错误';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // 获取权限列表
  const fetchPermissionList = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // 使用真实接口：/chain-wxapp/v1/user/permissionList
      const response = await api.reactNativeFetch(
        'chain-wxapp/v1/user/permissionList',
        {},
        FetchModule.Method.POST
      );

      if (response.errorCode === 0) {
        return response.responseData;
      } else {
        throw new Error(response.errorMsg || '获取权限列表失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '网络错误';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // 完整的门店切换流程
  const switchStore = useCallback(
    async (tenantUserId: string) => {
      // 防止重复调用
      if (loading) {
        console.warn('门店切换正在进行中，忽略重复调用');
        return;
      }

      setLoading(true);
      setError(null);

      try {
        console.log('开始门店切换流程，目标门店ID:', tenantUserId);

        // 1. 切换门店（不使用changeTenant的loading状态）
        const changeResponse = await api.reactNativeFetch(
          'chain-wxapp/v1/user/changeTenant',
          {
            tenant_user_id: tenantUserId,
          },
          FetchModule.Method.POST
        );

        if (changeResponse.errorCode !== 0) {
          throw new Error(changeResponse.errorMsg || '切换门店失败');
        }

        const changeResult = changeResponse.responseData;
        console.log('门店切换API调用成功');

        // 2. 更新token（如果后端返回了新token）
        if (changeResult?.token) {
          console.log('门店切换成功，新token:', changeResult.token);
        }

        // 3. 获取用户信息（不使用fetchUserInfo的loading状态）
        const userResponse = await api.reactNativeFetch(
          'chain-wxapp/v1/user/info',
          {},
          FetchModule.Method.POST
        );

        let userInfo = null;
        if (userResponse.errorCode === 0) {
          userInfo = userResponse.responseData;
          console.log('获取用户信息成功');
        }

        // 4. 获取权限列表（不使用fetchPermissionList的loading状态）
        const permissionResponse = await api.reactNativeFetch(
          'chain-wxapp/v1/user/permissionList',
          {},
          FetchModule.Method.POST
        );

        let permissionList = null;
        if (permissionResponse.errorCode === 0) {
          permissionList = permissionResponse.responseData;
          console.log('获取权限列表成功');
        }

        console.log('门店切换流程全部完成');

        return {
          success: true,
          userInfo,
          permissionList,
          token: changeResult?.token,
        };
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : '门店切换失败';
        console.error('门店切换失败:', err);
        setError(errorMessage);
        throw err;
      } finally {
        console.log('重置loading状态');
        setLoading(false);
      }
    },
    [loading]
  );

  return {
    loading,
    error,
    fetchTenantList,
    changeTenant,
    fetchUserInfo,
    fetchPermissionList,
    switchStore,
  };
};
