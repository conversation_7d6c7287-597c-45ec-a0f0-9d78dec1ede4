import React from 'react';
import PerformanceScorePage from './page/performanceScore';

interface PageProps {}

interface PageState {
  pageShow: boolean;
}

class DetailPage extends React.Component<PageProps, PageState> {
  constructor(props: PageProps) {
    super(props);
    this.state = {
      pageShow: true,
    };
  }

  soyoungPageName() {
    return '';
  }

  /** 页面埋点 */
  soyoungPageInfo() {
    return {};
  }

  didAppear() {
    this.setState({
      pageShow: true,
    });
  }

  willDisappear() {
    this.setState({
      pageShow: false,
    });
  }
  componentWillUnmount() {
    console.log('componentWillUnmount');
  }

  preferredStatusBarStyle() {
    return '2';
  }

  render() {
    return (
      <PerformanceScorePage
        {...this.props}
        pageShowFlag={this.state.pageShow}
      />
    );
  }
}

export default DetailPage;
