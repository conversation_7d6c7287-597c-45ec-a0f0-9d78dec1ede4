import React, { Component } from 'react';
import FeeExplainPage from './page/feeExplain';

interface PageProps {
  route: {
    params: {
      params: {
        name: string;
      };
    };
  };
  insets: {
    bottom: number;
    left: number;
    right: number;
    top: number;
  };
}

export default class Page extends Component<PageProps> {
  constructor(props: PageProps) {
    super(props);
    this.state = {
      pageShow: true,
    };
  }

  soyoungPageName() {
    return '';
  }

  /** 页面埋点 */
  soyoungPageInfo() {
    return {};
  }

  didAppear() {
    this.setState({
      pageShow: true,
    });
  }

  preferredStatusBarStyle() {
    return '2';
  }

  willDisappear() {
    this.setState({
      pageShow: false,
    });
  }
  render() {
    return <FeeExplainPage {...this.props} pageShow={this.state.pageShow} />;
  }
}
