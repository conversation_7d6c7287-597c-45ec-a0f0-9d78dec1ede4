import { getRealSize } from '@/common/utils';
import Header from '@/components/header';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { flatListProps } from '@/constant/flatlist_props';
import dayjs from 'dayjs';
import PermissionWrapper from '@/components/PermissionWrapper';
import { SafeAreaView } from 'react-native-safe-area-context';
import Api, { FetchModule } from '@/common/api';
import PopupModalTime from '@/components/PopupModalTime';
import CommonPopup, { CommonPopupRef, Active } from '@/components/CommonPopup';
import { IncomeHeaderSkeleton, IncomeSkeleton } from './components/Skeleton';
import { pushChangeTenant } from '@/common/pushChangeTenant';
import { jumpReactNativePage } from '@/common/jumpPage';
import { useRoute } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';

// 定义TenantItem类型
export interface TenantItem {
  name: string;
  tenant_id: number;
}

// 定义收入项类型
export interface IncomeItem {
  amount: string;
  code: string;
  created_at: string;
  cust_channel: string;
  cust_channel_type: string;
  customer_name: string;
  detail_id: number;
  discard_time: string;
  exec_orderno: string;
  exec_time: string;
  exec_times: number;
  fee_role_id: number;
  id: number;
  is_new_cust: number;
  is_sd: number;
  member_number: string;
  notes: string;
  operating_times: string;
  order_amount: string;
  order_id: number;
  performance_tag_id: number;
  performance_time: string;
  product_code: string;
  product_id: number;
  product_name: string;
  product_performance_tags: string;
  product_type: string;
  role_id: number;
  status: number;
  status_id: number;
  status_id_des: string;
  tenant_id: number;
  type: number;
  type_des: string;
  type_des_pre: string;
  type_des_suffix: string;
  updated_at: string;
  user_id: number;
  visit_id: number;
}

const TaskPage: React.FC<{ pageShow: boolean }> = ({ pageShow }) => {
  const router: any = useRoute();
  const [tenant, setTenant] = useState<TenantItem>({
    tenant_id: 0,
    name: '全部门店',
  });
  const [tenantList, setTenantList] = useState<TenantItem[]>([]);
  const [dateVisible, setDateVisible] = useState<boolean>(false);
  const [openPanel, setOpenPanel] = useState(false);
  const [isShowRule, setIsShowRule] = useState(false);
  const commonPopupRef = useRef<CommonPopupRef>(null);
  const [startDate, setStartDate] = useState<string>(
    dayjs().format('YYYY-MM-DD')
  );
  const [endDate, setEndDate] = useState<string>(dayjs().format('YYYY-MM-DD'));
  const [dateType, setDateType] = useState<number>(1);
  const [incomeList, setIncomeList] = useState<IncomeItem[]>([]);

  // 添加下拉刷新和加载更多相关状态
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [page, setPage] = useState<number>(1);
  const [totalIncome, setTotalIncome] = useState<number>(0);
  const [hasPermission, setHasPermission] = useState<boolean>(true);

  const getTenantList = useCallback(async (tenantId?: number) => {
    const response = await Api.pagefetch({
      path: '/chain-wxapp/v1/user/tenantList',
      params: {},
      method: FetchModule.Method.POST,
    });
    if (response.errorCode === 0) {
      const tenantList = [
        {
          tenant_id: 0,
          name: '全部门店',
        },
        ...(response.responseData as TenantItem[]),
      ];
      setTenantList(tenantList);
      handleTenantChange(
        tenantList.find(item => item.tenant_id === tenantId) || tenant
      );
    } else if (response.errorCode === 10002) {
      setHasPermission(false);
    }
  }, []);

  const getList = useCallback(
    async (
      pageNum: number = 1,
      isRefresh: boolean = false,
      customParams?: {
        sdate?: string;
        edate?: string;
        tenant_id?: number | string;
      }
    ) => {
      if (loading && !isRefresh) return;

      setLoading(true);

      try {
        const response = await Api.pagefetch({
          path: '/chain-wxapp/v1/index/incomeListAll',
          isLoading: false,
          params: {
            tenant_id: customParams?.tenant_id ?? tenant.tenant_id,
            sdate: customParams?.sdate ?? startDate,
            edate: customParams?.edate ?? endDate,
            page: pageNum,
            page_size: 10,
          },
          method: FetchModule.Method.POST,
        });

        if (response.errorCode === 0) {
          console.log(response.responseData);
          const {
            list = [],
            income_total,
            total = 0,
            is_show_rule = false,
          } = response.responseData || {};

          let incomes = [];
          if (isRefresh) {
            incomes = list || [];
          } else {
            incomes = [...incomeList, ...list];
          }
          setIncomeList(incomes);
          setTotalIncome(income_total);
          setHasMore(incomes.length < total);
          setIsShowRule(is_show_rule);
          setPage(pageNum);
        } else if (response.errorCode === 10002) {
          setHasPermission(false);
        }
      } catch (error) {
        console.error('获取收入列表失败:', error);
      } finally {
        setLoading(false);
        if (isRefresh) {
          setRefreshing(false);
        }
      }
    },
    [
      tenant.tenant_id,
      startDate,
      endDate,
      loading,
      incomeList,
      router.params?.params?.tenant_user_id,
    ]
  );

  // 下拉刷新
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    setIncomeList([]);
    setPage(1);
    await getList(1, true);
  }, [getList]);

  // 加载更多
  const onLoadMore = useCallback(() => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      getList(nextPage, false);
    }
  }, [loading, hasMore, page, getList]);

  useEffect(() => {
    const initializePage = async () => {
      const tenantId = router.params?.params?.tenant_id;
      const tenantUserId = router.params?.params?.tenant_user_id;
      try {
        if (tenantId && tenantUserId) {
          await pushChangeTenant(tenantId, tenantUserId);
        }
      } finally {
        getTenantList(parseInt(tenantId, 10));
      }
    };

    initializePage();
  }, []);

  /**
   * 打开弹窗
   */
  const handleOpen = useCallback(() => {
    setOpenPanel(true);
    commonPopupRef.current?.open();
  }, []);

  const handleDateConfirm = (data: any) => {
    console.log('data', data);
    setStartDate(data.start);
    setEndDate(data.end);
    setDateType(data.type);
    setIncomeList([]);
    setPage(1);
    // 使用新的日期参数立即请求数据
    getList(1, true, {
      sdate: data.start,
      edate: data.end,
    });
  };

  const handleTenantChange = (active: TenantItem) => {
    setTenant(active);
    setPage(1);
    setIncomeList([]);
    // 使用新的租户参数立即请求数据
    getList(1, true, {
      tenant_id: active.tenant_id,
    });
  };

  const renderFilter = () => {
    return (
      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={styles.filterDate}
          activeOpacity={1}
          onPress={() => setDateVisible(true)}
        >
          <Text style={styles.filterItemText}>
            {dateType === 1
              ? '今日'
              : dateType === 2
                ? '本月'
                : `${dayjs(startDate).format('MM-DD')} - ${dayjs(
                    endDate
                  ).format('MM-DD')}`}
          </Text>
          <Image
            source={{
              uri: 'https://static.soyoung.com/sy-pre/djxi3futvcns-1713508561411.png',
            }}
            resizeMode='contain'
            style={styles.calendarIcon}
          />
        </TouchableOpacity>
        <TouchableOpacity
          activeOpacity={1}
          style={styles.filterRight}
          onPress={handleOpen}
        >
          <View style={styles.filterRightTextContainer}>
            <Text
              style={styles.filterItemText}
              numberOfLines={1}
              ellipsizeMode='tail'
            >
              {tenant.name}
            </Text>
          </View>
          <Image
            source={{
              uri: 'https://static.soyoung.com/sy-design/3u5f7j35qkn711753259920865.png',
            }}
            resizeMode='contain'
            style={styles.calendarIcon}
          />
        </TouchableOpacity>
      </View>
    );
  };

  // 收入合计组件
  const renderIncomeHeader = () => {
    return (
      <View style={styles.incomeHeaderContainer}>
        <View style={styles.incomeHeader}>
          <View style={styles.incomeHeaderContent}>
            <View style={styles.incomeHeaderValueContainer}>
              <Text style={styles.incomeHeaderLabel}>收入合计</Text>
              <Text style={styles.incomeHeaderValue}>¥{totalIncome}</Text>
            </View>
            {isShowRule ? (
              <TouchableOpacity
                activeOpacity={1}
                style={styles.incomeHeaderRule}
                onPress={() => {
                  jumpReactNativePage(`income/feeExplain`);
                }}
              >
                <Image
                  source={{
                    uri: 'https://static.soyoung.com/sy-design/icon1753259861586.png',
                  }}
                  style={styles.incomeHeaderRuleIcon}
                />
                <Text style={styles.incomeHeaderRuleText}>提成规则</Text>
              </TouchableOpacity>
            ) : null}
          </View>
        </View>
        <LinearGradient
          colors={['#fff', '#F8F8F8']}
          style={styles.incomeHeaderGradient}
        />
      </View>
    );
  };

  const IncomeCard = ({ item }: { item: IncomeItem }) => (
    <View style={styles.incomeCard}>
      <View style={styles.incomeCardHeader}>
        <View style={styles.incomeCardTitleContainer}>
          <Text style={styles.incomeCardTitle}>
            {item.type_des_pre}
            {item.type_des_suffix ? (
              <Text style={styles.incomeCardTitle}>
                ｜ {item.type_des_suffix}
              </Text>
            ) : null}
          </Text>
          {item.type !== 6 ? (
            <View
              style={[
                styles.incomeCardTag,
                +item.amount < 0 && styles.incomeCardTagNegative,
              ]}
            >
              <Text
                style={[
                  styles.incomeCardTagText,
                  +item.amount < 0 && styles.incomeCardTagTextNegative,
                ]}
              >
                {item.status_id_des}
              </Text>
            </View>
          ) : null}
        </View>
        <TouchableOpacity
          activeOpacity={1}
          style={styles.incomeCardValueContainer}
          onPress={() => {
            if (item.type !== 6) {
              jumpReactNativePage(`record/detail?visit_id=${item.visit_id}`);
            }
          }}
        >
          <Text
            style={{
              ...styles.incomeCardValuePrefix,
              ...(+item.amount < 0 && styles.incomeCardValuePrefixNegative),
            }}
          >
            {+item.amount >= 0 ? '+' : ''}
          </Text>
          <Text
            style={[
              styles.incomeCardValue,
              +item.amount < 0 && styles.incomeCardValueNegative,
            ]}
          >
            {item.amount}
          </Text>
          {item.type !== 6 ? (
            <Image
              source={{
                uri: 'https://static.soyoung.com/sy-design/34v6bz73z2nf81753259861215.png',
              }}
              style={styles.incomeCardArrow}
            />
          ) : null}
        </TouchableOpacity>
      </View>
      <View style={styles.incomeCardDetails}>
        <Text style={styles.incomeCardDescription}>
          {item.product_name}
          {item.exec_times > 0 && item.type !== 6 && `*${item.exec_times}`}
        </Text>
        {item.customer_name ? (
          <Text style={styles.incomeCardDescription}>
            用户名称：{item.customer_name}
          </Text>
        ) : null}
        <Text style={styles.incomeCardTimestamp}>{item.performance_time}</Text>
      </View>
    </View>
  );

  // 渲染加载更多组件
  const renderFooter = () => {
    if (!hasMore && incomeList.length > 0) {
      return (
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>没有更多数据了</Text>
        </View>
      );
    }

    if (loading && !refreshing) {
      return (
        <View style={styles.footerContainer}>
          <ActivityIndicator size='small' color='#61B43E' />
          <Text style={styles.footerText}>加载中...</Text>
        </View>
      );
    }

    return null;
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      {/* <StatusBar
        barStyle='dark-content'
        backgroundColor='#fff'
        translucent={true}
      /> */}
      <Header title='提成管理' bgColor='#fff' />
      {renderFilter()}
      {incomeList.length > 0 ? (
        <FlatList
          style={styles.incomeList}
          data={incomeList}
          renderItem={({ item }) => <IncomeCard item={item} />}
          keyExtractor={(item, index) => `income_${item.id}_${index}`}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#61B43E']}
              tintColor='#61B43E'
            />
          }
          {...flatListProps}
          onEndReached={onLoadMore}
          ListHeaderComponent={renderIncomeHeader}
          onEndReachedThreshold={0.3}
          ListFooterComponent={renderFooter}
          showsVerticalScrollIndicator={false}
        />
      ) : !hasPermission ? (
        <PermissionWrapper hasPermission={false} />
      ) : loading ? (
        <View style={styles.incomeList}>
          <IncomeHeaderSkeleton />
          <IncomeSkeleton />
          <IncomeSkeleton />
          <IncomeSkeleton />
          <IncomeSkeleton />
        </View>
      ) : (
        <ScrollView
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#61B43E']}
              tintColor='#61B43E'
            />
          }
        >
          {renderIncomeHeader()}
          <View style={styles.incomeEmpty}>
            <View style={styles.incomeEmptyCore}>
              <Image
                style={styles.incomeEmptyImage}
                source={{
                  uri: 'https://static.soyoung.com/sy-design/aqnomvpf3ki11753429315696.png',
                }}
              />
              <Text style={styles.incomeEmptyText}>暂无相关数据</Text>
            </View>
          </View>
        </ScrollView>
      )}

      <PopupModalTime
        visible={dateVisible}
        onClose={() => setDateVisible(false)}
        onConfirm={handleDateConfirm}
      />
      <CommonPopup
        ref={commonPopupRef}
        active={{
          id: tenant.tenant_id,
          name: tenant.name,
        }}
        title='选择门店'
        multiple={false}
        options={tenantList.map(item => ({
          label: item.name,
          value: item.tenant_id,
        }))}
        maxLines={1}
        onClose={() => setOpenPanel(false)}
        onChange={(active: Active) => {
          const selectedTenant = Array.isArray(active) ? active[0] : active;
          handleTenantChange({
            tenant_id: selectedTenant.id as number,
            name: selectedTenant.name,
          });
        }}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  filterContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: getRealSize(57),
    paddingHorizontal: getRealSize(15),
    backgroundColor: '#fff',
  },
  filterDate: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: getRealSize(27),
    paddingHorizontal: getRealSize(10),
  },
  calendarIcon: {
    width: getRealSize(8),
    height: getRealSize(8),
    marginLeft: getRealSize(5),
  },
  filterItemText: {
    fontSize: getRealSize(13),
    lineHeight: getRealSize(27),
    letterSpacing: 0,
    fontWeight: '400',
    color: '#333333',
  },
  filterRight: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginLeft: getRealSize(15),
  },
  filterRightTextContainer: {
    flex: 1,
    height: getRealSize(27),
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  incomeHeaderContainer: {
    backgroundColor: '#fff',
  },
  // 新增收入合计样式
  incomeHeader: {
    backgroundColor: '#EBFBDC',
    marginTop: getRealSize(15),
    paddingHorizontal: getRealSize(15),
    marginHorizontal: getRealSize(15),
    height: getRealSize(58),
    justifyContent: 'center',
  },
  incomeHeaderGradient: {
    height: getRealSize(15),
  },
  incomeHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  incomeHeaderValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  incomeHeaderLabel: {
    fontSize: getRealSize(15),
    color: '#333333',
    fontWeight: '400',
    lineHeight: getRealSize(21),
  },
  incomeHeaderValue: {
    fontSize: getRealSize(15),
    color: '#333333',
    fontWeight: '500',
    marginLeft: getRealSize(10),
  },
  incomeHeaderRule: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  incomeHeaderRuleIcon: {
    width: getRealSize(12),
    height: getRealSize(12),
    marginRight: getRealSize(5),
  },
  incomeHeaderRuleText: {
    fontSize: getRealSize(13),
    color: '#333333',
    fontWeight: '400',
    lineHeight: getRealSize(18),
  },
  incomeList: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  incomeCard: {
    backgroundColor: '#ffffff',
    padding: getRealSize(15),
    marginHorizontal: getRealSize(15),
    marginBottom: getRealSize(10),
  },
  incomeCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: getRealSize(9),
  },
  incomeCardTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: getRealSize(26),
    flex: 1,
  },
  incomeCardTitle: {
    fontSize: getRealSize(15),
    color: '#333333',
    fontWeight: '500',
    lineHeight: getRealSize(21),
    marginRight: getRealSize(8),
  },
  incomeCardTag: {
    backgroundColor: '#EBFBDC',
    height: getRealSize(20),
    paddingHorizontal: getRealSize(5),
  },
  incomeCardTagNegative: {
    backgroundColor: '#F2F2F2',
  },
  incomeCardTagText: {
    fontSize: getRealSize(12),
    lineHeight: getRealSize(20),
    fontWeight: '400',
    color: '#345F22',
  },
  incomeCardTagTextNegative: {
    color: '#646464',
  },
  incomeCardValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  incomeCardValuePrefix: {
    fontSize: getRealSize(15),
    color: '#61B43E',
    fontWeight: '500',
    lineHeight: getRealSize(26),
    marginRight: getRealSize(1),
  },
  incomeCardValuePrefixNegative: {
    color: '#333',
  },
  incomeCardValue: {
    fontSize: getRealSize(16),
    lineHeight: getRealSize(26),
    color: '#61B43E',
    fontWeight: '700',
    marginRight: getRealSize(5),
  },
  incomeCardValueNegative: {
    color: '#333',
  },
  incomeCardArrow: {
    width: getRealSize(6),
    height: getRealSize(10),
  },
  incomeCardDetails: {},

  incomeCardDescription: {
    fontSize: getRealSize(14),
    color: '#646464',
    fontWeight: '400',
    lineHeight: getRealSize(20),
    marginBottom: getRealSize(5),
  },

  incomeCardTimestamp: {
    fontSize: getRealSize(13),
    color: '#8c8c8c',
    fontWeight: '400',
    lineHeight: getRealSize(18),
  },
  // 加载更多相关样式
  footerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: getRealSize(20),
  },
  footerText: {
    fontSize: getRealSize(14),
    color: '#999999',
    marginLeft: getRealSize(8),
  },
  // 空状态样式
  incomeEmpty: {
    paddingVertical: getRealSize(150),
    alignItems: 'center',
  },
  incomeEmptyCore: {
    alignItems: 'center',
  },
  incomeEmptyImage: {
    width: getRealSize(35), // 对应Vue中的150px
    height: getRealSize(35), // 对应Vue中的120px
    marginBottom: getRealSize(20),
  },
  incomeEmptyText: {
    fontSize: getRealSize(14),
    color: '#030303',
    fontWeight: '500',
  },
});

export default TaskPage;
