import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TextInput,
  FlatList,
  Dimensions,
} from 'react-native';
import Modal from 'react-native-modal';
import { modalAnimation } from '@/constant/modal_animation';
import { getRealSize } from '@/common/utils';
import { ATrack } from '@soyoung/react-native-container';
import { flatListProps } from '@/constant/flatlist_props';

interface Employee {
  id: string;
  user_id: string;
  name: string;
}

interface PopupModalProps {
  visible: boolean;
  employeeList: Employee[];
  onClose: () => void;
  onConfirm: (confirmData: Employee) => void;
}

const PopupModal: React.FC<PopupModalProps> = ({
  visible,
  employeeList,
  onClose,
  onConfirm,
}) => {
  const [fuzzySearchCustomerText, setFuzzySearchCustomerText] =
    useState<string>('');
  const [fuzzySearchEmployeeList, setFuzzySearchEmployeeList] = useState<
    Employee[]
  >([]);
  // 搜索员工
  const searchStaff = (text: string) => {
    setFuzzySearchCustomerText(text);
    if (text) {
      setFuzzySearchEmployeeList(
        employeeList.filter(item => item.name.includes(text))
      );
    } else {
      setFuzzySearchEmployeeList(employeeList);
    }
  };

  const onClickItem = (item: Employee) => {
    setFuzzySearchCustomerText(item.name);
    onClose();
    onConfirm(item);
  };

  const renderItem = ({ item }: { item: Employee }) => (
    <ATrack style={styles.employeeItem} onPress={() => onClickItem(item)}>
      <Text style={styles.employeeItemText}>{item.name}</Text>
    </ATrack>
  );

  useEffect(() => {
    setFuzzySearchEmployeeList(employeeList);
  }, [employeeList]);

  if (!visible) return null;
  return (
    <Modal
      isVisible={visible}
      {...modalAnimation}
      onBackdropPress={onClose}
      animationIn='slideInUp'
      animationOut='slideOutDown'
      deviceWidth={Dimensions.get('window').width}
      useNativeDriverForBackdrop={true}
      style={styles.modal}
    >
      <View style={styles.popupOverlay}>
        {/* <ATrack style={styles.popupMask} onPress={onClose} /> */}
        <View style={styles.popupContent}>
          <View style={styles.performancePopup}>
            <View style={styles.performancePopupTitle}>
              <Text style={styles.performancePopupTitleText}>选择员工</Text>
            </View>
            <ATrack style={styles.performancePopupClose} onPress={onClose}>
              <Image
                style={styles.performancePopupCloseIcon}
                source={{
                  uri: 'https://static.soyoung.com/sy-design/bzsokyai5osd1753688976847.png',
                }}
              />
            </ATrack>
            <View style={styles.filterSearch}>
              <Image
                source={{
                  uri: 'https://static.soyoung.com/sy-design/jsc2u0zt10171753259744187.png',
                }}
                style={styles.searchIcon}
              />
              <TextInput
                style={styles.searchInput}
                value={fuzzySearchCustomerText}
                onChangeText={searchStaff}
                placeholder='搜索员工'
                placeholderTextColor='#AAABB3'
              />
            </View>
            <FlatList
              style={styles.employeeList}
              data={fuzzySearchEmployeeList}
              renderItem={renderItem}
              keyExtractor={(item, index) => `record_${item.id}_${index}`}
              {...flatListProps}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  // 弹窗样式
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  popupOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
  },
  popupContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  performancePopup: {
    backgroundColor: '#fff',
    paddingHorizontal: getRealSize(15),
    paddingBottom: getRealSize(20),
    height: getRealSize(400),
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  performancePopupClose: {
    position: 'absolute',
    right: getRealSize(15),
    top: getRealSize(15),
    width: getRealSize(20),
    height: getRealSize(20),
  },
  performancePopupCloseIcon: {
    width: getRealSize(20),
    height: getRealSize(20),
  },
  performancePopupTitle: {
    paddingVertical: getRealSize(15),
    justifyContent: 'center',
    alignItems: 'center',
  },
  performancePopupTitleText: {
    fontSize: getRealSize(17),
    color: '#030303',
    fontWeight: '500',
    textAlign: 'center',
    fontFamily: 'PingFangSC-Medium',
  },
  filterSearch: {
    height: getRealSize(36),
    paddingHorizontal: getRealSize(10),
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
  },
  searchIcon: {
    width: getRealSize(16),
    height: getRealSize(16),
    marginRight: getRealSize(5),
  },
  searchInput: {
    flex: 1,
    fontSize: getRealSize(13),
    color: '#333333',
    padding: 0,
  },
  employeeList: {
    flex: 1,
    marginTop: getRealSize(10),
  },
  employeeItem: {
    padding: getRealSize(10),
  },
  employeeItemText: {
    fontSize: getRealSize(13),
    color: '#333333',
  },
});

export default PopupModal;
