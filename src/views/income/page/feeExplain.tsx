import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  RefreshControl,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import { ATrack } from '@soyoung/react-native-container';
import { getRealSize } from '@/common/utils';
import jsApi from '@soyoung/react-native-jsapi';
import api from '@/common/api';
import Header from '@/components/header';
import { SafeAreaView } from 'react-native-safe-area-context';
import DashedLine from '@/components/DashedLine';

interface CategoryItem {
  id: number;
  name: string;
}

interface FeeDetail {
  label: string;
  value: string;
}

interface FeeItem {
  sku_id: string;
  sku_name: string;
  details: FeeDetail[];
}

interface PageProps {
  pageShowFlag?: boolean;
}

const FeeExplainPage: React.FC<PageProps> = ({ pageShowFlag }) => {
  const [query, setQuery] = useState<string>('');
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [page, setPage] = useState<number>(1);
  const [categoryId, setCategoryId] = useState<number>(0);
  const [categoryList, setCategoryList] = useState<CategoryItem[]>([]);
  const [feeList, setFeeList] = useState<FeeItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  // 获取分类列表
  const getFeeConfigCategoryList = useCallback(async () => {
    try {
      const response = await api.pagefetch({
        path: '/chain-wxapp/v1/performance/feeConfigCategoryList',
        params: {},
      });

      if (response.errorCode === 0) {
        console.log(response.responseData);
        setCategoryList(response.responseData || []);
      }
    } catch (error) {
      console.error('getFeeConfigCategoryList error:', error);
    }
  }, []);

  // 获取费用配置列表
  const getFeeConfigList = useCallback(
    async (
      isRefresh = false,
      customParams?: { page?: number; categoryId?: number; query?: string }
    ) => {
      setLoading(true);
      if (isRefresh) {
        setRefreshing(true);
      }

      try {
        const currentPage = customParams?.page ?? (isRefresh ? 1 : page);
        const currentCategoryId = customParams?.categoryId ?? categoryId;
        const currentQuery = customParams?.query ?? query;

        const response = await api.pagefetch({
          path: '/chain-wxapp/v1/performance/feeConfigList',
          params: {
            page: currentPage,
            size: 10,
            category_id: currentCategoryId,
            query: currentQuery,
          },
          isLoading: false,
        });

        if (response.errorCode === 0) {
          console.log(response.responseData);
          const responseData = response.responseData;
          setHasMore(responseData?.has_more || false);

          if (isRefresh || currentPage === 1) {
            setFeeList(responseData?.list || []);
          } else {
            setFeeList(prev => [...prev, ...(responseData?.list || [])]);
          }
        } else {
          jsApi.toNative('showToast', {
            toast: response.errorMsg || '获取数据失败',
          });
        }
      } catch (error) {
        console.error('getFeeConfigList error:', error);
        jsApi.toNative('showToast', {
          toast: '获取数据异常',
        });
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },
    [page, categoryId, query]
  );

  // 处理搜索
  const handleSearch = useCallback(() => {
    setPage(1);
    getFeeConfigList(true);
  }, [getFeeConfigList]);

  // 清除搜索
  const handleClearQuery = useCallback(() => {
    setQuery('');
    setPage(1);
    getFeeConfigList(true, { page: 1, categoryId, query: '' });
  }, [getFeeConfigList, categoryId]);

  // 切换分类
  const handleClickCategory = useCallback(
    (id: number) => {
      setPage(1);
      setCategoryId(id);
      // 切换分类后自动搜索
      getFeeConfigList(true, { page: 1, categoryId: id, query });
    },
    [getFeeConfigList, query]
  );

  // 下拉刷新
  const onRefresh = useCallback(() => {
    setPage(1);
    getFeeConfigList(true);
  }, [getFeeConfigList]);

  // 加载更多
  const loadMore = useCallback(() => {
    if (!loading && hasMore && feeList.length > 0) {
      setPage(prev => prev + 1);
    }
  }, [loading, hasMore, feeList.length]);

  // 当page变化时加载数据
  useEffect(() => {
    if (page > 1) {
      getFeeConfigList();
    }
  }, [page]);

  // 初始化
  useEffect(() => {
    getFeeConfigCategoryList();
    getFeeConfigList(true);
  }, []);

  return (
    <SafeAreaView style={{ flex: 1 }} edges={['bottom']}>
      <Header title='手工费说明' bgColor='#fff' />
      <View style={styles.container}>
        {/* 表单区域 */}
        <View style={styles.feeForm}>
          <Text style={styles.feeFormTitle}>手工费单价查询</Text>

          {/* 搜索框 */}
          <View style={styles.feeSearch}>
            <Image
              style={styles.feeSearchIcon}
              source={{
                uri: 'https://static.soyoung.com/sy-design/2o36ex5wcu3qi1753259861187.png',
              }}
              resizeMode='contain'
            />
            <TextInput
              style={styles.feeSearchInput}
              value={query}
              onChangeText={setQuery}
              placeholder='请输入商品名称/skuid搜索'
              placeholderTextColor='#DEDEDE'
              onEndEditing={handleSearch}
              returnKeyType='done'
            />
            {query.length > 0 && (
              <TouchableOpacity
                style={styles.feeSearchClearWrapper}
                onPress={handleClearQuery}
              >
                <Image
                  style={styles.feeSearchClear}
                  source={{
                    uri: 'https://static.soyoung.com/sy-pre/219l297us0uts-1711617000689.png',
                  }}
                />
              </TouchableOpacity>
            )}
          </View>

          {/* 分类按钮 */}
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.feeBtns}
            contentContainerStyle={styles.feeBtnsContent}
          >
            <ATrack
              style={[
                styles.feeBtnsItem,
                categoryId === 0 && styles.feeBtnsItemActive,
              ]}
              onPress={() => handleClickCategory(0)}
            >
              <Text
                style={[
                  styles.feeBtnsItemText,
                  categoryId === 0 && styles.feeBtnsItemTextActive,
                ]}
              >
                全部
              </Text>
            </ATrack>
            {categoryList.map(item => (
              <ATrack
                key={item.id}
                style={[
                  styles.feeBtnsItem,
                  categoryId === item.id && styles.feeBtnsItemActive,
                ]}
                onPress={() => handleClickCategory(item.id)}
              >
                <Text
                  style={[
                    styles.feeBtnsItemText,
                    categoryId === item.id && styles.feeBtnsItemTextActive,
                  ]}
                >
                  {item.name}
                </Text>
              </ATrack>
            ))}
          </ScrollView>
        </View>

        {/* 列表内容 */}
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          onMomentumScrollEnd={event => {
            const { layoutMeasurement, contentOffset, contentSize } =
              event.nativeEvent;
            const isCloseToBottom =
              layoutMeasurement.height + contentOffset.y >=
              contentSize.height - 20;
            if (isCloseToBottom) {
              loadMore();
            }
          }}
        >
          {feeList.length > 0 ? (
            <View style={styles.feeList}>
              {feeList.map(item => (
                <View key={item.sku_id} style={styles.feeItem}>
                  <Text style={styles.feeItemTitle}>{item.sku_name}</Text>
                  <Text style={styles.feeItemSkuid}>skuid：{item.sku_id}</Text>
                  <View style={styles.feeItemPriceWrapper}>
                    {item.details.map((detail, index) => (
                      <View key={index} style={styles.feeItemPrice}>
                        <Text style={styles.feeItemPriceLabel}>
                          {detail.label}
                        </Text>
                        <Text style={styles.feeItemPriceValue}>
                          {detail.value}
                        </Text>
                        <DashedLine
                          style={{
                            top: getRealSize(10),
                            left: getRealSize(115),
                            width: getRealSize(150),
                            height: getRealSize(1),
                            position: 'absolute',
                          }}
                          dashLength={2}
                          dashGap={2}
                          dashThickness={1}
                          dashColor='#bababa'
                        />
                      </View>
                    ))}
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <View style={styles.feeEmpty}>
              <Text style={styles.feeEmptyText}>暂无数据</Text>
            </View>
          )}
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  feeForm: {
    backgroundColor: '#ffffff',
    paddingHorizontal: getRealSize(15),
    paddingTop: getRealSize(15),
    borderTopLeftRadius: getRealSize(10),
    borderTopRightRadius: getRealSize(10),
    position: 'relative',
  },
  feeFormTitle: {
    fontSize: getRealSize(15),
    color: '#030303',
    fontWeight: '400',
    fontFamily: 'PingFangSC-Medium',
  },
  feeSearch: {
    marginTop: getRealSize(16),
    height: getRealSize(36),
    backgroundColor: '#f6f9f9',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getRealSize(15),
    position: 'relative',
  },
  feeSearchIcon: {
    width: getRealSize(16),
    height: getRealSize(16),
    marginRight: getRealSize(8),
  },
  feeSearchInput: {
    flex: 1,
    fontSize: getRealSize(15),
    color: '#161616',
    fontWeight: '400',
    paddingVertical: 0,
  },
  feeSearchClearWrapper: {
    width: getRealSize(38),
    height: getRealSize(38),
    position: 'absolute',
    right: 0,
    top: -getRealSize(1),
    justifyContent: 'center',
    alignItems: 'center',
  },
  feeSearchClear: {
    width: getRealSize(16),
    height: getRealSize(16),
  },
  feeBtns: {
    marginTop: getRealSize(15),
    marginBottom: getRealSize(15),
  },
  feeBtnsContent: {
    paddingRight: getRealSize(15),
  },
  feeBtnsItem: {
    height: getRealSize(28),
    paddingHorizontal: getRealSize(15),
    marginRight: getRealSize(10),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: getRealSize(7),
  },
  feeBtnsItemActive: {
    backgroundColor: '#333',
  },
  feeBtnsItemText: {
    fontSize: getRealSize(13),
    color: '#333333',
    fontWeight: '400',
  },
  feeBtnsItemTextActive: {
    color: '#ffffff',
  },
  scrollView: {
    flex: 1,
    marginTop: getRealSize(15),
  },
  feeList: {
    paddingHorizontal: getRealSize(15),
  },
  feeItem: {
    backgroundColor: '#ffffff',
    padding: getRealSize(15),
    marginBottom: getRealSize(10),
  },
  feeItemTitle: {
    fontSize: getRealSize(15),
    color: '#333333',
    fontWeight: '500',
    fontFamily: 'PingFangSC-Medium',
  },
  feeItemSkuid: {
    fontSize: getRealSize(12),
    color: '#8c8c8c',
    fontWeight: '400',
    marginTop: getRealSize(5),
    marginBottom: getRealSize(10),
  },
  feeItemPriceWrapper: {
    position: 'relative',
    justifyContent: 'space-between',
  },
  feeItemPrice: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: getRealSize(5),
    position: 'relative',
    minHeight: getRealSize(20),
  },
  feeItemPriceLabel: {
    fontSize: getRealSize(14),
    color: '#354052',
    fontWeight: '400',
  },
  feeItemPriceValue: {
    fontSize: getRealSize(14),
    color: '#61B43E',
    fontWeight: '500',
    fontFamily: 'PingFangSC-Medium',
  },
  feeEmpty: {
    paddingTop: getRealSize(50),
    alignItems: 'center',
  },
  feeEmptyText: {
    fontSize: getRealSize(14),
    color: '#999',
  },
});

export default FeeExplainPage;
