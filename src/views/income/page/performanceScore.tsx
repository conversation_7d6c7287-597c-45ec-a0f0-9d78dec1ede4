import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from 'react';
import {
  View,
  Text,
  Image,
  FlatList,
  StyleSheet,
  Alert,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import Modal from 'react-native-modal';
import Header from '@/components/header';
import PermissionWrapper from '@/components/PermissionWrapper';
import Api from '@/common/api';
import { getNativeLoginInfo } from '@/common/getNativeLoginInfo';
import { SafeAreaView } from 'react-native-safe-area-context';
import { getRealSize } from '../../../common/utils';
import { pushChangeTenant } from '../../../common/pushChangeTenant';
import { useRoute } from '@react-navigation/native';
import { ATrack } from '@soyoung/react-native-container';
import DashedLine from '../../../components/DashedLine';
import { modalAnimation } from '@/constant/modal_animation';
import { flatListProps } from '@/constant/flatlist_props';

// 样式定义移到组件外部
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  noPermissionState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: getRealSize(375),
    height: '100%',
    backgroundColor: '#fff',
  },
  // 日期选择器
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: getRealSize(20),
    backgroundColor: '#ffffff',
  },
  dateArrow: {
    width: getRealSize(10),
    height: getRealSize(10),
  },
  dateText: {
    fontSize: getRealSize(16),
    color: '#333333',
    fontWeight: '500',
    marginHorizontal: getRealSize(20),
    minWidth: getRealSize(100),
    textAlign: 'center',
  },

  scoreCardContainer: {
    backgroundColor: '#fff',
    width: '100%',
    paddingHorizontal: getRealSize(15),
    paddingBottom: getRealSize(15),
    marginBottom: getRealSize(10),
  },

  // 绩效分卡片
  scoreCard: {
    backgroundColor: '#EBFBDC',
    paddingVertical: getRealSize(20),
    paddingHorizontal: getRealSize(20),
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  scoreLabel: {
    fontSize: getRealSize(15),
    color: '#333333',
    fontWeight: '400',
  },
  scoreValue: {
    fontSize: getRealSize(15),
    color: '#333333',
    fontWeight: '500',
    marginLeft: getRealSize(10),
  },

  // 绩效分条目
  performanceItem: {
    backgroundColor: '#ffffff',
    padding: getRealSize(15),
    marginBottom: getRealSize(10),
    marginHorizontal: getRealSize(10),
  },
  performanceItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getRealSize(8),
  },
  performanceItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  performanceItemType: {
    fontSize: getRealSize(16),
    color: '#333333',
    fontWeight: '500',
  },
  performanceItemStatus: {
    fontSize: getRealSize(12),
    color: '#999999',
    marginLeft: getRealSize(8),
  },
  performanceItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  performanceItemScore: {
    marginRight: getRealSize(5),
    fontFamily: 'DINAlternate-Bold',
    fontSize: getRealSize(22),
    color: '#030303',
    letterSpacing: 0,
    fontWeight: '700',
  },
  scorePositive: {
    color: '#61B43E',
  },
  scoreNegative: {
    color: '#333333',
  },
  arrowIcon: {
    width: getRealSize(6),
    height: getRealSize(9),
    tintColor: '#CCCCCC',
  },
  performanceItemDescription: {
    fontSize: getRealSize(14),
    color: '#666666',
    lineHeight: getRealSize(20),
    marginBottom: getRealSize(4),
  },
  performanceItemTime: {
    fontSize: getRealSize(12),
    color: '#999999',
  },
  // 空状态
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: getRealSize(60),
  },
  emptyIcon: {
    width: getRealSize(35),
    height: 35,
  },
  emptyText: {
    fontSize: getRealSize(14),
    color: '#030303',
    textAlign: 'center',
    fontWeight: '500',
    marginTop: getRealSize(20),
  },
  // 加载更多
  loadMore: {
    alignItems: 'center',
    paddingVertical: getRealSize(15),
  },
  loadMoreText: {
    fontSize: getRealSize(12),
    color: '#999999',
    fontWeight: '400',
  },
  // 弹窗样式
  modal: {
    justifyContent: 'flex-end',
    margin: getRealSize(0),
  },
  popupContent: {
    backgroundColor: '#ffffff',
    borderTopLeftRadius: getRealSize(16),
    borderTopRightRadius: getRealSize(16),
    maxHeight: '80%',
  },
  performancePopupContent: {
    backgroundColor: '#ffffff',
  },
  performancePopupHeader: {
    paddingHorizontal: getRealSize(15),
    paddingTop: getRealSize(15),
    paddingBottom: getRealSize(20),
  },
  performancePopupTitleBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  performancePopupTitle: {
    fontSize: getRealSize(16),
    color: '#333333',
    fontWeight: '500',
  },
  performancePopupCancel: {
    width: getRealSize(24),
  },
  performancePopupClose: {
    width: getRealSize(24),
    height: getRealSize(24),
    alignItems: 'center',
    justifyContent: 'center',
  },
  performancePopupCloseIcon: {
    width: getRealSize(20),
    height: getRealSize(20),
  },
  performancePopupTable: {
    borderWidth: 1,
    borderColor: '#eee',
    borderRadius: getRealSize(8),
    marginHorizontal: getRealSize(15),
    marginBottom: getRealSize(20),
    overflow: 'hidden',
  },
  performancePopupTableHeader: {
    flexDirection: 'row',
    backgroundColor: '#f8f9fa',
    paddingVertical: getRealSize(12),
    paddingHorizontal: getRealSize(15),
  },
  performancePopupTableRow: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingVertical: getRealSize(12),
    paddingHorizontal: getRealSize(15),
  },
  performancePopupTableCell: {
    flex: 1,
    fontSize: getRealSize(14),
    color: '#666666',
    fontWeight: '400',
    textAlign: 'center',
  },
  performancePopupTableCellNegative: {
    color: '#4CAF50',
  },
  performancePopupTableCellHeader: {
    flex: 1,
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '500',
    textAlign: 'center',
  },
  performancePopupTableCellHeaderLeft: {
    flex: 1,
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '500',
    textAlign: 'left',
  },
  performancePopupTableCellLeft: {
    flex: 1,
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '400',
    textAlign: 'left',
  },
  performancePopupTableEmpty: {
    paddingVertical: getRealSize(30),
    borderTopWidth: getRealSize(1),
    borderTopColor: '#eee',
  },
  performancePopupTableEmptyText: {
    textAlign: 'center',
    fontSize: getRealSize(14),
    color: '#999999',
    fontWeight: '400',
  },
  // 绩效详情样式
  performanceDetailContent: {
    paddingHorizontal: getRealSize(15),
    paddingBottom: getRealSize(20),
    maxHeight: getRealSize(400),
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: getRealSize(15),
    alignItems: 'flex-start',
  },
  detailLabel: {
    width: getRealSize(80),
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '400',
  },
  detailValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'flex-end',
  },
  detailValue: {
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  detailValueLong: {
    maxWidth: '100%',
    flex: 1,
  },
  detailValueNegative: {
    fontSize: getRealSize(18),
    color: '#61B43E',
    fontWeight: '700',
    fontFamily: 'DINAlternate-Bold',
    textAlign: 'right',
  },
  detailStatusCanceled: {
    marginLeft: getRealSize(10),
    fontSize: getRealSize(12),
    color: '#999999',
    backgroundColor: '#f5f5f5',
    paddingHorizontal: getRealSize(6),
    paddingVertical: getRealSize(2),
    borderRadius: getRealSize(4),
    fontWeight: '400',
  },
  // 流程管理样式
  processContainer: {
    marginTop: getRealSize(10),
    backgroundColor: '#f8f8f8',
    padding: getRealSize(15),
    paddingBottom: getRealSize(1),
  },
  processItem: {
    marginBottom: getRealSize(18),
  },
  processHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  processTitle: {
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '400',
    marginRight: getRealSize(5),
  },
  processLine: {
    flex: 1,
    marginHorizontal: getRealSize(5),
  },
  processUser: {
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '500',
    marginLeft: getRealSize(5),
    textAlign: 'right',
  },
  processTime: {
    fontSize: getRealSize(12),
    color: '#999999',
    marginTop: getRealSize(5),
    textAlign: 'right',
  },
});

// 绩效分条目组件
const PerformanceItem = React.memo(
  ({ item, onClick }: { item: any; onClick: (item: any) => void }) => {
    const handlePress = useCallback(() => {
      onClick(item);
    }, [onClick, item]);

    return (
      <ATrack style={styles.performanceItem} onPress={handlePress}>
        <View style={styles.performanceItemHeader}>
          <View style={styles.performanceItemLeft}>
            <Text style={styles.performanceItemType}>{item.title}</Text>
            {item.status === 2 ? (
              <Text style={styles.performanceItemStatus}>已撤销</Text>
            ) : null}
          </View>
          <View style={styles.performanceItemRight}>
            <Text
              style={[
                styles.performanceItemScore,
                item?.sjvb_deduct_score?.startsWith('+')
                  ? styles.scorePositive
                  : styles.scoreNegative,
              ]}
            >
              {item.sjvb_deduct_score}
            </Text>
            <Image
              source={{
                uri: 'https://static.soyoung.com/sy-design/6eboz32amcrz1755055437690.png',
              }}
              style={styles.arrowIcon}
            />
          </View>
        </View>
        <Text style={styles.performanceItemDescription}>
          {item.description}
        </Text>
        <Text style={styles.performanceItemTime}>{item.created_at}</Text>
      </ATrack>
    );
  }
);

const Popup = ({
  visible,
  children,
  onClose,
}: {
  visible: boolean;
  children: React.ReactNode;
  onClose: () => void;
}) => {
  return (
    <Modal
      {...modalAnimation}
      isVisible={visible}
      onBackdropPress={onClose}
      onBackButtonPress={onClose}
      style={styles.modal}
      backdropOpacity={0.5}
      animationIn='slideInUp'
      animationOut='slideOutDown'
      swipeDirection='down'
      onSwipeComplete={onClose}
    >
      <View style={styles.popupContent}>{children}</View>
    </Modal>
  );
};

interface PerformanceScorePageProps {
  pageShowFlag?: boolean;
}

const PerformanceScorePage: React.FC<PerformanceScorePageProps> = () => {
  // 状态管理
  const [switchDateValue, setSwitchDateValue] = useState('');
  const [performanceList, setPerformanceList] = useState<any[]>([]);
  const [performanceData, setPerformanceData] = useState({
    select_month: '',
    score_total: '-',
    score_describe: {
      title: '绩效分说明',
      current_score_total: 0,
      list: [] as Array<{
        name: string;
        score_total: string;
        current_score: string;
      }>,
    },
  });

  // 分页相关状态
  const [pagination, setPagination] = useState({
    page: 1,
    total: 0,
    loading: false,
  });
  const [hasPermission, setHasPermission] = useState<boolean>(true);
  const [hasSearch, setHasSearch] = useState(false);
  const [performancePopupVisible, setPerformancePopupVisible] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [detailData, setDetailData] = useState<any>({});

  // 防抖计时器
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 取消请求的 ref
  const cancelRequestRef = useRef<boolean>(false);
  // 进入页面通过参数自动弹出详情的标记，避免重复触发
  const hasAutoOpenedRef = useRef<boolean>(false);
  // FlatList 的 ref，用于控制滚动
  const flatListRef = useRef<FlatList>(null);
  // 存储需要滚动的 detailId，等待列表数据加载完成后滚动
  const pendingScrollDetailIdRef = useRef<string | null>(null);

  const router: any = useRoute();

  // 使用 useMemo 缓存计算结果
  const hasMoreData = useMemo(() => {
    return performanceList.length < pagination.total;
  }, [performanceList.length, pagination.total]);

  const formattedDate = useMemo(() => {
    return switchDateValue.replace('-', '年') + '月';
  }, [switchDateValue]);

  const canLoadMore = useMemo(() => {
    return hasMoreData && !pagination.loading;
  }, [hasMoreData, pagination.loading]);

  // 获取租户用户ID的辅助函数
  const getTenantUserId = useCallback(async (): Promise<string> => {
    try {
      const res = await getNativeLoginInfo();

      let returnID = res.tenant_user_id || '';
      if (res.ext_info && typeof res.ext_info === 'object') {
        const ext = res.ext_info as { cross_tenant_user_id?: string };
        if (ext.cross_tenant_user_id) {
          returnID = ext.cross_tenant_user_id;
        }
      }

      return returnID;
    } catch (error) {
      console.error('获取tenant_user_id失败:', error);
      return 'test_user_id';
    }
  }, []);

  // 获取绩效分明细数据 - 优化版本
  const fetchPerformanceData = async (params: any, retryCount = 0) => {
    setPagination(prev => {
      if (prev.loading) return prev;
      return { ...prev, loading: true };
    });

    try {
      const response: any = await Api.pagefetch({
        path: '/chain-wxapp/v1/index/scoreListAll',
        params: {
          month: params.month,
          page: params.page,
          page_size: params.page_size,
        },
        isLoading: false,
      }).catch(error => {
        return {
          errorCode: 1,
          errorMsg: error.message || '获取绩效数据失败',
        };
      });

      if (response.errorCode === 0 && response.responseData) {
        const responseData = response.responseData;

        // 如果是切换月份，直接更新数据，不清空列表
        if (params.isMonthSwitch) {
          // 直接更新数据，避免闪烁
          setPerformanceData(responseData);
          setPerformanceList(responseData.list || []);
        } else {
          // 正常的分页加载
          setPerformanceData(responseData);
          if (params.page === 1) {
            setPerformanceList(responseData.list || []);
          } else {
            setPerformanceList(prev => [...prev, ...(responseData.list || [])]);
          }
        }

        setPagination(prev => ({
          ...prev,
          total: responseData.total || 0,
          loading: false,
        }));
        setHasSearch(true);
      } else if (response.errorCode === 10002) {
        setHasPermission(false);
      } else {
        throw new Error(response.errorMsg || '获取绩效数据失败');
      }
    } catch (error) {
      if (retryCount < 3) {
        setTimeout(
          () => {
            fetchPerformanceData(params, retryCount + 1);
          },
          1000 * (retryCount + 1)
        );
      } else {
        Alert.alert('错误', '获取绩效数据失败，请稍后重试');
      }
    } finally {
      setPagination(prev => ({ ...prev, loading: false }));
    }
  };

  // 获取绩效分详情
  const fetchPerformanceDetail = async (params: any) => {
    try {
      const response = await Api.pagefetch({
        path: '/chain-wxapp/v1/index/scoreDetailNew',
        params: {
          tenant_user_id: params.tenant_user_id,
          detail_id: params.detail_id,
        },
        isLoading: false,
      });

      if (response.errorCode === 0 && response.responseData) {
        setDetailData(response.responseData);
        setPerformancePopupVisible(true);

        // 存储需要滚动的 detailId，等待列表数据加载完成后滚动
        pendingScrollDetailIdRef.current = params.detail_id;
      } else if (response.errorCode === 10002) {
        setHasPermission(false);
      } else {
        Alert.alert('错误', response.errorMsg || '获取绩效详情失败');
      }
    } catch (error) {
      Alert.alert('错误', '获取绩效详情失败');
    }
  };

  const getPushDataChangeTenant = useCallback(async (): Promise<void> => {
    const tenantId = router.params?.params?.tenant_id;
    const tenantUserId = router.params?.params?.tenant_user_id;

    if (tenantId && tenantUserId) {
      await pushChangeTenant(tenantId, tenantUserId).catch(() => {
        return false;
      });
    }
  }, [router]);

  // 初始化默认日期和推送租户变更
  useEffect(() => {
    let isMounted = true;

    const initializePage = async () => {
      try {
        if (isMounted) {
          await getPushDataChangeTenant();
        }
      } finally {
        if (isMounted) {
          const params: any = (router as any)?.params?.params || {};
          const paramsMonth = params?.month;

          if (paramsMonth) {
            setSwitchDateValue(paramsMonth);
          } else {
            // 设置默认日期
            const date = new Date();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            setSwitchDateValue(`${date.getFullYear()}-${month}`);
          }
        }
      }
    };

    initializePage();

    return () => {
      isMounted = false;
    };
  }, [getPushDataChangeTenant]);

  // 页面初始化
  useEffect(() => {
    let isMounted = true;

    const initPage = async () => {
      if (switchDateValue && isMounted) {
        try {
          const tenantUserId = await getTenantUserId();
          setPagination({ page: 1, total: 0, loading: false });
          setPerformanceList([]);

          const params = {
            tenant_user_id: tenantUserId,
            month: switchDateValue,
            page: 1,
            page_size: 10,
            isMonthSwitch: false, // 初始化不算切换月份
          };

          await fetchPerformanceData(params);
        } catch (error) {
          // 获取数据失败
        }
      }
    };

    initPage();

    return () => {
      isMounted = false;
      // 清理防抖计时器
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      // 取消正在进行的请求
      cancelRequestRef.current = true;
    };
  }, [switchDateValue]);

  // 根据路由参数自动弹出绩效分详情
  useEffect(() => {
    // 已经弹过则不再处理
    if (hasAutoOpenedRef.current) return;

    // 确保列表数据已经加载完成后再处理弹窗
    if (!hasSearch || performanceList.length === 0) return;

    const params: any = (router as any)?.params?.params || {};
    const detailId = params?.detail_id || params?.detailId;
    if (detailId) {
      (async () => {
        try {
          const tenantUserId = await getTenantUserId();
          await fetchPerformanceDetail({
            tenant_user_id: tenantUserId,
            detail_id: detailId,
          });
          hasAutoOpenedRef.current = true;
        } catch (e) {
          // ignore; 错误已在 fetchPerformanceDetail 内部处理
        }
      })();
    }
  }, [router, getTenantUserId, hasSearch, performanceList.length]); // 添加依赖确保数据加载完成后再处理

  // 监听 performanceList 变化，当有待滚动的 detailId 时执行滚动
  useEffect(() => {
    if (pendingScrollDetailIdRef.current && performanceList.length > 0) {
      const detailId = pendingScrollDetailIdRef.current;
      pendingScrollDetailIdRef.current = null; // 清空待滚动的 detailId

      // 延迟执行滚动，确保组件已完全渲染
      setTimeout(() => {
        if (flatListRef.current) {
          const targetIndex = performanceList.findIndex(
            item => item.id.toString() === detailId.toString()
          );
          if (targetIndex !== -1) {
            const headerHeight = getRealSize(120);
            const itemHeight = getRealSize(104 + 10);
            const offset = headerHeight + targetIndex * itemHeight;
            const targetOffset = Math.max(0, offset);
            flatListRef.current.scrollToOffset({
              offset: targetOffset,
              animated: true,
            });
          }
        }
      }, 200);
    }
  }, [performanceList]);

  // 下拉刷新
  const onRefresh = async () => {
    setRefreshing(true);
    const tenantUserId = await getTenantUserId();
    const params = {
      tenant_user_id: tenantUserId,
      month: switchDateValue,
      page: 1,
      page_size: 10,
      isMonthSwitch: false,
    };

    await fetchPerformanceData(params);
    setRefreshing(false);
  };

  // 加载更多
  const loadMore = async () => {
    if (canLoadMore) {
      const nextPage = pagination.page + 1;
      setPagination(prev => ({ ...prev, page: nextPage }));
      const tenantUserId = await getTenantUserId();
      await fetchPerformanceData({
        tenant_user_id: tenantUserId,
        month: switchDateValue,
        page: nextPage,
        page_size: 10,
        isMonthSwitch: false,
      });
    }
  };

  // 切换月份 - 防抖优化版本
  const switchDate = useCallback(
    (type: 'left' | 'right') => {
      // 清除之前的防抖计时器
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      // 设置防抖，300ms 内多次点击只执行最后一次
      debounceTimerRef.current = setTimeout(async () => {
        // 取消之前的请求
        cancelRequestRef.current = true;

        const date = new Date(switchDateValue);
        if (type === 'left') {
          date.setMonth(date.getMonth() - 1);
        } else if (type === 'right') {
          date.setMonth(date.getMonth() + 1);
        }
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const newDateValue = `${date.getFullYear()}-${month}`;

        // 先更新日期和加载状态，但不立即清空列表
        setSwitchDateValue(newDateValue);
        setPagination({ page: 1, total: 0, loading: true }); // 显示加载状态
        // 不清空列表，保持旧数据直到新数据加载完成
        setHasSearch(false); // 重置搜索状态

        // 重置取消标志
        cancelRequestRef.current = false;

        const tenantUserId = await getTenantUserId();
        const params = {
          tenant_user_id: tenantUserId,
          month: newDateValue,
          page: 1,
          page_size: 10,
          isMonthSwitch: true, // 标记为月份切换
        };

        // 检查是否需要取消请求
        if (!cancelRequestRef.current) {
          await fetchPerformanceData(params);
        }
      }, 200); // 增加防抖时间到200ms
    },
    [switchDateValue, getTenantUserId]
  );

  const handlePerformancePopupClose = useCallback(() => {
    setPerformancePopupVisible(false);
    setDetailData({});
  }, []);

  const popupPerformance = useCallback(
    async (item: any) => {
      const tenantUserId = await getTenantUserId();
      await fetchPerformanceDetail({
        tenant_user_id: tenantUserId,
        detail_id: item.id,
      });
    },
    [getTenantUserId]
  );

  // 缓存图片源对象，避免重复创建
  const emptyImageSource = useMemo(
    () => ({
      uri: 'https://static.soyoung.com/sy-design/3q7kvrkruzbs01752650189618.png',
    }),
    []
  );

  const leftArrowSource = useMemo(
    () => ({
      uri: 'https://static.soyoung.com/sy-design/3eee8e6gu7wq01755055437870.png',
    }),
    []
  );

  const rightArrowSource = useMemo(
    () => ({
      uri: 'https://static.soyoung.com/sy-design/xilq5a444xh1755055437917.png',
    }),
    []
  );

  // 渲染空状态
  const renderEmptyState = useCallback(() => {
    return (
      <View style={styles.emptyState}>
        <Image
          source={emptyImageSource}
          style={styles.emptyIcon}
          resizeMode='contain'
        />
        <Text style={styles.emptyText}>暂无绩效明细</Text>
      </View>
    );
  }, [emptyImageSource]);

  // 渲染加载更多
  const renderLoadMore = useCallback(() => {
    if (!hasSearch || performanceList.length === 0) return null;

    return (
      <View style={styles.loadMore}>
        {pagination.loading ? (
          <ActivityIndicator size='small' color='#999999' />
        ) : (
          <Text style={styles.loadMoreText}>
            {hasMoreData ? '上拉加载更多' : '没有更多啦'}
          </Text>
        )}
      </View>
    );
  }, [hasSearch, performanceList.length, hasMoreData, pagination.loading]);

  // 渲染列表项
  const renderPerformanceItem = useCallback(
    ({ item, index }: { item: any; index: number }) => (
      <PerformanceItem
        key={`performance-${item.id}-${index}`}
        item={item}
        onClick={popupPerformance}
      />
    ),
    [popupPerformance]
  );

  // 渲染列表头部
  const renderListHeader = useCallback(
    () => (
      <>
        {/* 日期选择器 */}
        <View style={styles.dateContainer}>
          <ATrack
            onPress={() => switchDate('left')}
            hitSlop={{
              top: 30,
              bottom: 30,
              left: 30,
              right: 30,
            }}
          >
            <Image
              source={leftArrowSource}
              resizeMode='contain'
              style={styles.dateArrow}
            />
          </ATrack>
          <Text style={styles.dateText}>{formattedDate}</Text>
          <ATrack
            onPress={() => switchDate('right')}
            hitSlop={{
              top: 30,
              bottom: 30,
              left: 30,
              right: 30,
            }}
          >
            <Image
              source={rightArrowSource}
              resizeMode='contain'
              style={styles.dateArrow}
            />
          </ATrack>
        </View>

        {/* 绩效分卡片 */}
        <View style={styles.scoreCardContainer}>
          <View style={styles.scoreCard}>
            <Text style={styles.scoreLabel}>绩效分</Text>
            <Text style={styles.scoreValue}>
              {performanceData.score_total}分
            </Text>
          </View>
        </View>
      </>
    ),
    [
      formattedDate,
      performanceData.score_total,
      switchDate,
      leftArrowSource,
      rightArrowSource,
    ]
  );

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Header hideBack={false} title={'绩效分'} bgColor='#fff' />
      {!hasPermission ? (
        <View style={styles.noPermissionState}>
          <PermissionWrapper hasPermission={false} />
        </View>
      ) : (
        <View style={{ flex: 1 }}>
          <FlatList
            ref={flatListRef}
            {...flatListProps}
            style={styles.scrollView}
            data={performanceList}
            keyExtractor={(item, index) => `${item.id}-${index}`}
            renderItem={renderPerformanceItem}
            ListHeaderComponent={renderListHeader}
            ListEmptyComponent={hasSearch ? renderEmptyState() : null}
            ListFooterComponent={renderLoadMore()}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                enabled={true}
              />
            }
            onEndReached={loadMore}
            getItemLayout={(data, index) => ({
              length: getRealSize(104 + 10), // 实际item高度 + marginBottom
              offset: getRealSize(104 + 10) * index,
              index,
            })}
          />
        </View>
      )}

      {/* 绩效说明弹窗 */}
      <Popup
        visible={performancePopupVisible}
        onClose={handlePerformancePopupClose}
      >
        <View style={styles.performancePopupContent}>
          <View style={styles.performancePopupHeader}>
            <View style={styles.performancePopupTitleBar}>
              <View style={styles.performancePopupCancel} />
              <Text style={styles.performancePopupTitle}>
                {detailData.type_des
                  ? '绩效分详情'
                  : performanceData.score_describe.title}
              </Text>
              <ATrack
                style={styles.performancePopupClose}
                onPress={handlePerformancePopupClose}
              >
                <Image
                  source={{
                    uri: 'https://static.soyoung.com/sy-design/bzsokyai5osd1753260407943.png',
                  }}
                  style={styles.performancePopupCloseIcon}
                />
              </ATrack>
            </View>
          </View>

          {/* 如果有详情数据，显示详情内容 */}
          {detailData.type_des ? (
            <View style={styles.performanceDetailContent}>
              {/* 绩效类型和状态 */}
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>绩效类型</Text>
                <View style={styles.detailValueContainer}>
                  <Text style={styles.detailValue}>{detailData.type_des}</Text>
                  {detailData.status === 2 && (
                    <Text style={styles.detailStatusCanceled}>已撤销</Text>
                  )}
                </View>
              </View>

              {/* 扣分项 */}
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>扣分项</Text>
                <Text style={[styles.detailValue, styles.detailValueLong]}>
                  {detailData.sjvb_name}
                </Text>
              </View>

              {/* 扣分 */}
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>扣分</Text>
                <Text style={styles.detailValueNegative}>
                  {detailData.sjvb_deduct_score}
                </Text>
              </View>

              {/* 备注 */}
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>备注</Text>
                <Text style={styles.detailValue} numberOfLines={10}>
                  {detailData.remark}
                </Text>
              </View>

              {/* 流程管理 */}
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>流程管理</Text>
              </View>

              {/* 流程管理详情 */}
              {detailData.operate_flow_list &&
                detailData.operate_flow_list.length > 0 && (
                  <View style={styles.processContainer}>
                    {detailData.operate_flow_list.map(
                      (item: any, index: number) => (
                        <View key={index} style={styles.processItem}>
                          <View style={styles.processHeader}>
                            <Text style={styles.processTitle}>
                              {item.type_des}
                            </Text>
                            <DashedLine
                              style={styles.processLine}
                              dashColor='#cccccc'
                              dashLength={4}
                              dashGap={4}
                              dashThickness={1}
                            />
                            <Text style={styles.processUser}>{item.name}</Text>
                          </View>
                          <Text style={styles.processTime}>
                            {item.operate_time}
                          </Text>
                        </View>
                      )
                    )}
                  </View>
                )}
            </View>
          ) : (
            /* 原有的绩效分说明表格 */
            <View style={styles.performancePopupTable}>
              {/* 表头 */}
              <View style={styles.performancePopupTableHeader}>
                <Text style={styles.performancePopupTableCellHeaderLeft}>
                  绩效类型
                </Text>
                <Text style={styles.performancePopupTableCellHeader}>
                  岗位规范
                </Text>
                <Text style={styles.performancePopupTableCellHeader}>
                  已撤销
                </Text>
              </View>
              {/* 表格内容 */}
              {performanceData.score_describe.list.length > 0 ? (
                performanceData.score_describe.list.map((row, index) => (
                  <View key={index} style={styles.performancePopupTableRow}>
                    <Text style={styles.performancePopupTableCellLeft}>
                      {row.name || ''}
                    </Text>
                    <Text style={styles.performancePopupTableCell}>
                      {row.score_total || ''}
                    </Text>
                    <Text
                      style={[
                        styles.performancePopupTableCell,
                        row &&
                        row.current_score &&
                        row.current_score?.startsWith('-')
                          ? styles.performancePopupTableCellNegative
                          : null,
                      ]}
                    >
                      {row.current_score || ''}
                    </Text>
                  </View>
                ))
              ) : (
                <View style={styles.performancePopupTableEmpty}>
                  <Text style={styles.performancePopupTableEmptyText}>
                    暂无配置信息
                  </Text>
                </View>
              )}
            </View>
          )}
        </View>
      </Popup>
    </SafeAreaView>
  );
};

export default PerformanceScorePage;
