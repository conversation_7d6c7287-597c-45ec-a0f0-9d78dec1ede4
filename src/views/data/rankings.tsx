import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from 'react-native';
import Api from '../../common/api';
import jsApi from '@soyoung/react-native-jsapi';
import { RouteProp, useRoute } from '@react-navigation/native';
import Modal from 'react-native-modal';
import { modalAnimation } from '@/constant/modal_animation';
import { styles, tabStyle } from './rankingsPage/rankings.style';
import {
  PageProps,
  RankingsTabItem,
  RankingsTypeItem,
  RouteParams,
  ShortcutDate,
  SortItem,
  MarketItem,
  RoleItem,
} from './rankingsPage/rankings.interfaces';
import { commonFilterStyle } from './rankingsPage/rankings.style';
import { modalStyle } from './indexPage/index.style';
import dayjs from 'dayjs';
import CommonPopup, { CommonPopupRef } from '../../components/CommonPopup';
import DatePicker, { DatePickerType } from '../../components/DatePicker';
import { shortcutDateConfig } from './rankingsPage/rankings.config';
import { Bridge } from '../../common/bridge';
import RankingsTable, {
  ColumnItem,
  RankListItem,
} from './components/rankingsTable';
import DateInput from '../../components/DateInput';
import { SafeAreaView } from 'react-native-safe-area-context';

// 排行榜页面内容组件
const RankingsPageContent: React.FC = () => {
  const rolePopupRef = useRef<CommonPopupRef>(null);
  const sortPopupRef = useRef<CommonPopupRef>(null);
  const marketPopupRef = useRef<CommonPopupRef>(null);

  // 获取url参数
  const route = useRoute<RouteProp<Record<string, RouteParams>, string>>();
  const {
    role_id: routeRoleId = 2,
    cur_tab: routeCurTab = 1,
    date_type = 'month',
    start_date = dayjs().startOf('month').format('YYYY-MM-DD'),
    end_date = dayjs().endOf('month').format('YYYY-MM-DD'),
  } = route.params.params;

  // 确保类型为 number，处理可能的字符串输入
  const role_id =
    typeof routeRoleId === 'number' ? routeRoleId : Number(routeRoleId) || 2;
  const cur_tab =
    typeof routeCurTab === 'number' ? routeCurTab : Number(routeCurTab) || 1;

  const [dateRange, setDateRange] = useState<[string, string]>([
    start_date,
    end_date,
  ]);
  //tab
  const [tabs, setTabs] = useState<Array<RankingsTabItem>>([]);
  const [activeTab, setActiveTab] = useState<number>(cur_tab);
  // 门店或者城市
  const [activeType, setActiveType] = useState<RankingsTypeItem>({
    id: 1,
    name: '门店',
  });
  const [rankTypeList, setRankTypeList] = useState<RankingsTypeItem[]>([]);

  // 表格数据
  const [list, setList] = useState<RankListItem[]>([]);
  const [columns, setColumns] = useState<ColumnItem[]>([]);

  // 角色列表
  const [searchRoles, setSearchRoles] = useState<RoleItem[]>([]);
  const [searchRole, setSearchRole] = useState<RoleItem>({
    id: 0,
    name: '',
  });
  // 市场列表
  const [marketList, setMarketList] = useState<MarketItem[]>([]);
  const [curMarket, setCurMarket] = useState<MarketItem>({} as MarketItem);

  // 排序列表
  const [sortList, setSortList] = useState<SortItem[]>([]);
  const [curSort, setCurSort] = useState<SortItem>({} as SortItem);

  const isInitializing = useRef<boolean>(true);

  const [refresh, setRefresh] = useState<number>(0);

  const [loading, setLoading] = useState<boolean>(true);

  const [noPermission] = useState<boolean>(role_id === 8);

  // 初始化调用
  useEffect(() => {
    if (noPermission) {
      return;
    }
    const getMarketList = async () => {
      try {
        const { errorCode, responseData } = await Api.pagefetch({
          path: '/chain-wxapp/v1/index/getMarketList',
          params: {},
        });

        if (errorCode === 0 && responseData) {
          const newMarketList = [
            {
              id: '',
              name: '全部',
            },
          ].concat(
            responseData.map((item: string) => ({
              id: item,
              name: item,
            }))
          );
          setMarketList(newMarketList);
          setCurMarket(newMarketList[0]);
        }
      } catch (error) {
        console.error('获取市场列表失败:', error);
      }
    };
    // 初始化时也获取配置数据
    const fetchInitialConfig = async () => {
      try {
        const res = await Api.pagefetch({
          path: '/chain-wxapp/v1/index/getRankConfig',
          isLoading: false,
          params: {
            role_id,
          },
        });
        if (res.errorCode !== 0) {
          setTabs([]);
          return;
        }
        setTabs(res.responseData.tabs || []);
        setActiveTab(activeTab || res.responseData.tabs[0]?.tab_id || 1);
        const types = res.responseData.types.map((item: any) => ({
          id: item.type_id,
          name: item.type_name,
        }));
        setRankTypeList(types);
        setActiveType(types[0]);
      } catch (error) {
        setTabs([]);
      }
    };
    getMarketList();
    fetchInitialConfig();
  }, []);

  // 其他参数变化监听
  useEffect(() => {
    if (noPermission) {
      return;
    }
    // 获取排行榜数据
    const fetchRankDataList = async () => {
      if (curMarket.id === undefined) {
        return;
      }

      const [startDate, endDate] = dateRange;

      let search_role_id = role_id === 2 ? '' : role_id;

      if (searchRole.id) {
        search_role_id = searchRole.id;
      }

      console.log(
        'rankings params',
        `[isInitializing: ${isInitializing.current}]`,
        `[日期: ${dateRange[0]}至${dateRange[1]}]`,
        `[类型: ${activeType.name}]`,
        `[tab: ${activeTab}]`,
        `[search_role_id: ${search_role_id}]`,
        `[role_id: ${role_id}]`,
        `[market_name: ${curMarket.id}]`
      );
      /**
     *  tab_id：tab id，必填，只能传1,2,3
        market_name：赛道名称,tab_id=3时选填，其它不需要传
        user_id：不需要传
        tenant_user_id：不需要传
        role_id：工作台-用户选择的角色id,tab_id=2和3时需要穿
        search_role_id：
          tab_id=1时，该值不能为5，排行榜详情页-用户选择的角色id
          tab_id=2时，如果是店长，可以穿0,3,4,5；非店长必须和role_id一致
          tab_id=3时，只能传3
        role_key：不需要传，完全没用的字段，用户选择的角色key
        start_date：必填：开始时间
        end_date：必填：结束时间
        cur_user_is_store_manager：不需要传，安全漏洞！！当前登陆人是否店长
        type_id：选填，类型 1门店（默认） 2城市
     * 
     */
      try {
        const res = await Api.pagefetch({
          path: '/chain-wxapp/v1/index/getRankDataList',
          isLoading: false,
          params: {
            role_id,
            search_role_id,
            tab_id: activeTab,
            start_date: startDate,
            end_date: endDate,
            type_id: activeType.id,
            market_name: activeTab === 3 ? curMarket.id : '',
          },
        });
        console.log('rankings data', res);
        if (res.errorCode !== 0) {
          setList([]);
          setColumns([]);
          return;
        }
        setList((res.responseData.list as RankListItem[]) || []);
        setColumns((res.responseData.columns as ColumnItem[]) || []);
        setSearchRoles(
          res.responseData.search_roles.map((item: any) => ({
            id: item.role_id,
            name: item.role_name,
          }))
        );
        if (isInitializing.current) {
          // 处理排序列表
          const newSortList = (res.responseData.columns as ColumnItem[])
            .filter(item => item.is_participate_sort)
            .map(item => ({
              id: item.name || '',
              name: `按${item.value}`,
            }));
          setSortList(newSortList as SortItem[]);

          const currentSort = (res.responseData.columns as ColumnItem[])
            .filter(item => item.is_participate_sort)
            .find(item => item.is_current_sort);

          setCurSort({
            id: currentSort?.name || '',
            name: currentSort?.value ? `按${currentSort?.value}` : '',
          });

          // 处理当前角色
          const role = res.responseData.search_roles[0];
          if (role) {
            const { role_id: id, role_name: name } = role;
            setSearchRole({
              id,
              name,
            });
            isInitializing.current = false;
          }
        }
      } catch (error) {
        setList([]);
        setColumns([]);
      } finally {
        setLoading(false);
      }
    };
    fetchRankDataList();
  }, [activeTab, dateRange, activeType, role_id, curMarket, refresh]);

  const defaultSortBy = useMemo(() => {
    return columns.find(item => item.is_current_sort)?.name;
  }, [columns]);

  return (
    <View style={styles.container}>
      {/* 头部导航 */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() =>
            jsApi.toNative('backAnimated', {
              transitionType: '0',
              disableAnimation: '0',
            })
          }
          activeOpacity={0.9}
        >
          <Image
            source={{
              uri: 'https://static.soyoung.com/sy-design/3cj8rc3ipek931753771823399.png',
            }}
            style={styles.backIcon}
          />
        </TouchableOpacity>
        <Image
          source={{
            uri: 'https://static.soyoung.com/sy-design/bg1753771823561.png',
          }}
          style={styles.bgContainer}
        />
      </View>
      {noPermission ? (
        <View style={styles.noPermissionContainer}>
          <Text style={styles.noPermissionText}>暂无排行榜</Text>
        </View>
      ) : (
        <>
          {/* 通用选择器 */}
          <CommonFilter
            type={activeType}
            types={rankTypeList}
            setType={setActiveType}
            dateType={date_type}
            dateRange={dateRange}
            setDateRange={setDateRange}
          />
          {/* 排行榜tab */}
          <View style={tabStyle.container}>
            {tabs.map(tab => (
              <TouchableOpacity
                key={tab.tab_id}
                style={tabStyle.tabItem}
                onPress={() => {
                  if (tab.tab_id === activeTab) {
                    return;
                  }
                  isInitializing.current = true;
                  setSearchRole({
                    id: 0,
                    name: '',
                  });
                  setActiveTab(tab.tab_id);
                }}
                activeOpacity={0.9}
              >
                <Text
                  style={[
                    tabStyle.tabText,
                    activeTab === tab.tab_id && tabStyle.tabTextActive,
                  ]}
                >
                  {tab.tab_name}
                </Text>
                <View
                  style={[
                    tabStyle.tabIndicator,
                    activeTab !== tab.tab_id && tabStyle.tabIndicatorInactive,
                  ]}
                />
              </TouchableOpacity>
            ))}
          </View>
          {/* 员工排行标题和排序 */}
          <View style={styles.subHeader}>
            <Text style={styles.subTitle}>员工排行</Text>
            <View style={styles.sortSelector}>
              {/* 角色筛选 */}
              <TouchableOpacity
                style={styles.sortSelector}
                onPress={() => {
                  rolePopupRef.current?.open();
                }}
                activeOpacity={0.9}
              >
                <Text style={styles.sortText}>
                  {searchRole.name || '请选择'}
                </Text>
                <Image
                  source={{
                    uri: 'https://static.soyoung.com/sy-design/3u5f7j35qkn711753771823571.png',
                  }}
                  style={styles.sortIcon}
                />
              </TouchableOpacity>
              {/* 筛选 */}
              {[1, 2].includes(activeTab) && curSort.id ? (
                <TouchableOpacity
                  style={[styles.sortSelector]}
                  onPress={() => {
                    sortPopupRef.current?.open();
                  }}
                  activeOpacity={0.9}
                >
                  <Text style={styles.sortText}>
                    {curSort.name || '请选择'}
                  </Text>
                  <Image
                    source={{
                      uri: 'https://static.soyoung.com/sy-design/3u5f7j35qkn711753771823571.png',
                    }}
                    style={styles.sortIcon}
                  />
                </TouchableOpacity>
              ) : null}
              {/* 市场数据排序 */}
              {activeTab === 3 && curMarket.name && (
                <TouchableOpacity
                  style={styles.sortSelector}
                  onPress={() => {
                    marketPopupRef.current?.open();
                  }}
                  activeOpacity={0.9}
                >
                  <Text style={styles.sortText}>
                    {curMarket.name || '请选择'}
                  </Text>
                  <Image
                    source={{
                      uri: 'https://static.soyoung.com/sy-design/3u5f7j35qkn711753771823571.png',
                    }}
                    style={styles.sortIcon}
                  />
                </TouchableOpacity>
              )}
            </View>
          </View>
          {/* 表格内容 */}
          <View style={styles.tableContainer}>
            <RankingsTable
              loading={false}
              list={list}
              columns={columns}
              sortBy={curSort.id || defaultSortBy}
              headerBackgroundColor='#f8f8f8'
            />

            {/* 绝对定位的 loading 覆盖层 */}
            {loading && (
              <View style={styles.loadingOverlay}>
                <ActivityIndicator
                  size='small'
                  color='#61B43E'
                  style={styles.loadingSpinner}
                />
                <Text style={styles.loadingText}>加载中...</Text>
              </View>
            )}
          </View>

          {/* 角色筛选弹窗 */}
          <CommonPopup
            active={searchRole}
            multiple={false}
            ref={rolePopupRef}
            title=''
            options={searchRoles.map(role => ({
              label: role.name,
              value: role.id,
            }))}
            onActiveChange={selected => {
              if (selected && 'id' in selected) {
                setSearchRole({
                  id: selected.id as number,
                  name: selected.name,
                });
                setRefresh(Date.now());
              }
            }}
          />

          {/* sort popup */}
          <CommonPopup
            active={{
              id: curSort.id,
              name: curSort.name || '',
            }}
            multiple={false}
            ref={sortPopupRef}
            title=''
            options={sortList.map(sort => ({
              label: sort.name,
              value: sort.id,
            }))}
            onActiveChange={selected => {
              if (selected && 'id' in selected) {
                setCurSort({
                  id: selected.id as string,
                  name: selected.name,
                });
              }
            }}
          />

          {/* 市场数据排序弹窗 */}
          <CommonPopup
            active={{
              id: curMarket.id,
              name: curMarket.name,
            }}
            multiple={false}
            ref={marketPopupRef}
            title=''
            options={marketList.map(market => ({
              label: market.name,
              value: market.id,
            }))}
            onActiveChange={selected => {
              if (selected && 'id' in selected) {
                setCurMarket({
                  id: selected.id.toString(),
                  name: selected.name,
                });
              }
            }}
          />
        </>
      )}
    </View>
  );
};

// 日期范围选择器
const CommonFilter: React.FC<{
  type: RankingsTypeItem;
  types: RankingsTypeItem[];
  setType: (type: RankingsTypeItem) => void;
  dateType: 'day' | 'month' | 'custom';
  dateRange: [string, string];
  setDateRange: (dateRange: [string, string]) => void;
}> = ({ type, types, setType, dateType, dateRange, setDateRange }) => {
  const rankScopePopupRef = useRef<CommonPopupRef>(null);

  const [dateVisible, setDateVisible] = useState(false);
  const [datePickerVisible, setDatePickerVisible] = useState<boolean>(false);
  const [datePickerType, setDatePickerType] = useState<DatePickerType>(
    dateType === 'month' ? 'year-month' : 'year-month-day'
  );
  const [localDateRange, setLocalDateRange] = useState<[string, string]>([
    '',
    '',
  ]);

  // 类型名称
  const typeName = useMemo(() => {
    return types.find(item => item.id === type.id)?.name || '';
  }, [type, types]);

  // 快捷日期
  const shortcutDate = useMemo<ShortcutDate[]>(() => {
    return shortcutDateConfig();
  }, []);

  // 日期选择器值
  const [datePickerValue, setDatePickerValue] = useState<ShortcutDate>(
    (() => {
      switch (dateType) {
        case 'day':
          const today = shortcutDate.find(item => item.name === '今日');
          return {
            ...today!,
            start: dayjs().startOf('day').format('YYYY-MM-DD'),
            end: dayjs().endOf('day').format('YYYY-MM-DD'),
          };
        case 'month':
          const month = shortcutDate.find(item => item.name === '本月');
          return {
            ...month!,
            start: dayjs().startOf('month').format('YYYY-MM-DD'),
            end: dayjs().endOf('month').format('YYYY-MM-DD'),
          };
        case 'custom':
          const custom = shortcutDate[shortcutDate.length - 1];
          return {
            ...custom!,
            start: dayjs(dateRange[0]).format('YYYY-MM-DD'),
            end: dayjs(dateRange[1]).format('YYYY-MM-DD'),
          };
        default:
          return {
            ...shortcutDate[0],
          };
      }
    })()
  );

  const lastDatePickerValue = useRef<ShortcutDate>(datePickerValue);

  const [showTypeName, setShowTypeName] = useState<string>(
    dateType === 'day' ? '日榜' : dateType === 'month' ? '月榜' : '自定义'
  );

  const [showDateRange, setShowDateRange] = useState<string>(
    dateType === 'day'
      ? dayjs().format('YYYY-MM-DD')
      : dateType === 'month'
        ? dayjs().format('YYYY-MM')
        : `${dateRange[0]} 至 ${dateRange[1]}`
  );

  const setShowInfo = (value: ShortcutDate) => {
    if (value.type === 'day') {
      setShowTypeName('日榜');
      setShowDateRange(dayjs(value.start).format('YYYY-MM-DD'));
    } else if (value.type === 'month') {
      setShowTypeName('月榜');
      setShowDateRange(dayjs(value.start).format('YYYY-MM'));
    } else {
      setShowTypeName('自定义');
      setShowDateRange(`${value.start} 至 ${value.end}`);
    }
  };

  // 当 datePickerValue 变化且弹窗关闭时，自动同步 dateRange
  useEffect(() => {
    if (dateVisible) return;
    if (!datePickerValue.start || !datePickerValue.end) {
      return;
    }
    const [start, end] = dateRange;
    if (start === datePickerValue.start && end === datePickerValue.end) {
      return;
    }
    setDateRange([datePickerValue.start, datePickerValue.end]);
  }, [dateVisible, datePickerValue, setDateRange]);

  return (
    <>
      <View style={commonFilterStyle.datePickerContainer}>
        <TouchableOpacity
          style={commonFilterStyle.dateSelector}
          onPress={() => {
            setDateVisible(true);
          }}
          activeOpacity={0.9}
        >
          <Text style={commonFilterStyle.label}>{showTypeName}</Text>
          <View style={commonFilterStyle.line} />
          <Text style={commonFilterStyle.text}>{showDateRange}</Text>
          <Image
            source={{
              uri: 'https://static.soyoung.com/sy-design/3u5f7j35qkn711753771823308.png',
            }}
            style={[
              commonFilterStyle.arrowIcon,
              {
                transform: [{ rotate: dateVisible ? '180deg' : '0deg' }],
              },
            ]}
          />
        </TouchableOpacity>
        <TouchableOpacity
          style={commonFilterStyle.dateSelector}
          onPress={() => {
            rankScopePopupRef.current?.open();
          }}
          activeOpacity={0.9}
        >
          <Text style={commonFilterStyle.text}>{typeName}</Text>
          <Image
            source={{
              uri: 'https://static.soyoung.com/sy-design/3u5f7j35qkn711753771823308.png',
            }}
            style={[
              commonFilterStyle.arrowIcon,
              {
                transform: [{ rotate: dateVisible ? '180deg' : '0deg' }],
              },
            ]}
          />
        </TouchableOpacity>
      </View>
      {/* 日期选择器 */}
      <Modal
        isVisible={dateVisible}
        {...modalAnimation}
        style={modalStyle.modal}
        animationIn='slideInUp'
        animationOut='slideOutDown'
        onModalShow={() => {
          // 弹窗显示时保存当前状态
          lastDatePickerValue.current = datePickerValue;
          if (datePickerValue.name === '自定义') {
            setLocalDateRange([
              dayjs(datePickerValue.start).format('YYYY-MM-DD'),
              dayjs(datePickerValue.end).format('YYYY-MM-DD'),
            ]);
          }
        }}
        onModalHide={() => {
          // 弹窗隐藏时重置本地状态
          setLocalDateRange(['', '']);
        }}
      >
        <View style={modalStyle.modalContainer}>
          <View style={modalStyle.modalHeader}>
            <Text style={modalStyle.modalTitle}>选择时间</Text>
            <TouchableOpacity
              style={modalStyle.closeButton}
              onPress={() => {
                // 恢复到上一次的状态
                setDatePickerValue(lastDatePickerValue.current);
                setLocalDateRange(['', '']);
                setDateVisible(false);
              }}
            >
              <Image
                style={modalStyle.closeImage}
                source={{
                  uri: 'https://static.soyoung.com/sy-design/bzsokyai5osd1753416667877.png',
                }}
              />
            </TouchableOpacity>
          </View>
          <View style={modalStyle.modalContent}>
            <View style={commonFilterStyle.dateRangeContainer}>
              {shortcutDate.map(item => (
                <TouchableOpacity
                  key={item.name}
                  style={commonFilterStyle.checkItem}
                  activeOpacity={0.9}
                  onPress={() => {
                    setDatePickerValue(item);
                    if (item.start && item.end) {
                      setDateVisible(false);
                      setShowInfo(item);
                      setLocalDateRange(['', '']);
                      return;
                    }
                    if (item.type === 'day') {
                      setDatePickerType('year-month-day');
                      setDatePickerVisible(true);
                      return;
                    }
                    if (item.type === 'month') {
                      setDatePickerType('year-month');
                      setDatePickerVisible(true);
                      return;
                    }
                  }}
                >
                  <Text style={commonFilterStyle.checkLabel}>{item.name}</Text>

                  <Image
                    source={{
                      uri:
                        datePickerValue.name === item.name
                          ? 'https://static.soyoung.com/sy-design/1i3fb7pl05wlg1753755992345.png'
                          : 'https://static.soyoung.com/sy-design/8k1ijrc526id1753755992331.png',
                    }}
                    style={commonFilterStyle.checkIcon}
                  />
                </TouchableOpacity>
              ))}
            </View>
          </View>
          {/* 日期选择器 */}
          <DatePicker
            title={
              datePickerType === 'year-month-day' ? '选择日期' : '选择月份'
            }
            visible={datePickerVisible}
            value={datePickerValue.start}
            type={datePickerType}
            onConfirm={dateStr => {
              if (datePickerType === 'year-month-day') {
                // 验证是否选择未来日期
                const selectedDate = dayjs(dateStr);
                const today = dayjs();

                if (selectedDate.isAfter(today, 'day')) {
                  Bridge.showToast('不能选择未来日期');
                  return;
                }
                setDatePickerValue(prev => ({
                  ...prev,
                  start: dateStr,
                  end: dateStr,
                }));
                setShowInfo({
                  ...datePickerValue,
                  start: dateStr,
                  end: dateStr,
                });
                // 如果是选择月份
              } else if (datePickerType === 'year-month') {
                // 验证是否选择未来月份
                const selectedMonth = dayjs(dateStr);
                const currentMonth = dayjs().startOf('month');

                if (selectedMonth.isAfter(currentMonth, 'month')) {
                  Bridge.showToast('不能选择未来月份');
                  return;
                }

                const start = dayjs(dateStr)
                  .startOf('month')
                  .format('YYYY-MM-DD');
                const end = dayjs(dateStr).endOf('month').format('YYYY-MM-DD');
                setDatePickerValue(prev => ({
                  ...prev,
                  start,
                  end,
                }));
                setShowInfo({
                  ...datePickerValue,
                  start,
                  end,
                });
              }
              setDateVisible(false);
              setDatePickerVisible(false);
              setLocalDateRange(['', '']);
            }}
            onCancel={() => {
              setDatePickerVisible(false);
            }}
          />
          {/* 底部按钮 */}
          {datePickerValue.name === '自定义' && (
            <>
              <View style={commonFilterStyle.timePickerContainer}>
                <DateInput
                  value={localDateRange}
                  isRange={true}
                  type='year-month-day'
                  onChange={value => {
                    setLocalDateRange(value as [string, string]);
                  }}
                />
              </View>
              <View style={modalStyle.modalFooter}>
                <TouchableOpacity
                  style={modalStyle.cancelButton}
                  onPress={() => {
                    setLocalDateRange(['', '']);
                  }}
                  activeOpacity={0.9}
                >
                  <Text style={modalStyle.cancelButtonText}>重置</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={modalStyle.confirmButton}
                  onPress={() => {
                    if (!localDateRange[0] || !localDateRange[1]) {
                      Bridge.showToast('请选择日期');
                      return;
                    }
                    // 验证时间间隔是否超过3个月
                    const startDate = dayjs(localDateRange[0]);
                    const endDate = dayjs(localDateRange[1]);
                    const diffInMonths = endDate.diff(startDate, 'month', true);

                    if (diffInMonths > 3) {
                      Bridge.showToast('起止时间间隔不能超过3个月');
                      return;
                    }

                    setDatePickerValue(prev => ({
                      ...prev,
                      start: localDateRange[0],
                      end: localDateRange[1],
                    }));
                    setDateVisible(false);
                    setDatePickerVisible(false);
                    setShowInfo({
                      ...datePickerValue,
                      start: localDateRange[0],
                      end: localDateRange[1],
                    });
                  }}
                  activeOpacity={0.9}
                >
                  <Text style={modalStyle.confirmButtonText}>确定</Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>
      </Modal>
      {/* 门店还是城市 维度 */}
      <CommonPopup
        ref={rankScopePopupRef}
        title=''
        active={type}
        multiple={false}
        searchable={false}
        options={types.map(item => ({
          label: item.name,
          value: item.id,
        }))}
        onActiveChange={item => {
          setType(item as RankingsTypeItem);
        }}
        onClose={() => {}}
      />
    </>
  );
};

class RankingsPage extends React.Component<PageProps> {
  constructor(props: PageProps) {
    super(props);
  }

  soyoungPageName() {
    return 'dataRankingsPage';
  }

  /** 页面埋点 */
  soyoungPageInfo() {
    return {
      pageName: 'dataRankingsPage',
    };
  }
  preferredStatusBarStyle() {
    return '0';
  }
  didAppear() {}

  willDisappear() {}

  render() {
    return (
      <SafeAreaView style={styles.container} edges={['bottom']}>
        <RankingsPageContent />
      </SafeAreaView>
    );
  }
}

export default RankingsPage;
