import React, { useEffect, useState } from 'react';
import { DataPageProvider, useDataPage } from './indexPage/index.context';
import { PageProps } from './indexPage/index.interfaces';
import { View, ScrollView, RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { indexStyle } from './indexPage/index.style';
import ErrorView from './components/Error';
import {
  Skeleton,
  RankingList,
  StickyHeader,
  StatisticsGrid,
  IndicatorAnalysis,
  EmployeeStatistics,
  NewOldCustomerAnalysis,
  ServicePointAnalysis,
  NegativeReviewDistribution,
  StoreManagerSatisfactionStatCard,
  NurseSatisfactionStatCard,
} from './indexPage/components';
import PermissionWrapper from '../../components/PermissionWrapper';
// import { useFocusEffect } from '@react-navigation/native';

// 使用 Context 的内容组件
const DataPageContent: React.FC<{ pageShow: boolean }> = ({ pageShow }) => {
  const {
    hasPermission,
    showSkeleton,
    ctxRole,
    ctxDateRange,
    pageData,
    errorMessage,
    refresh,
  } = useDataPage();

  const [refreshTimer, setRefreshTimer] = useState(0);

  useEffect(() => {
    console.log('数据页面显示了:', pageShow);
    if (pageShow) {
      setRefreshTimer(Date.now());
      refresh();
    }
  }, [pageShow]);

  const {
    // 当前月份
    current_month,
    // 绩效目标
    performance_target,
    // 数据统计网格
    core_index,
    // 满意度统计
    customer_reviews,
    // 指标分析
    performance_analysis,
    // 新老客分析
    customers_analysis,
    // 服务点分析
    service_point_analysis,
    // 差评分布
    bad_evaluation_analysis,
  } = pageData;
  // 正常情况下
  const workWell = hasPermission && errorMessage === '';
  return (
    <View style={indexStyle.container}>
      {showSkeleton && workWell ? <Skeleton /> : null}
      {hasPermission ? (
        errorMessage ? (
          <ErrorView text={errorMessage} onPress={refresh} />
        ) : (
          <>
            {/* 吸顶Header */}
            <StickyHeader />
            {/* 内容区域 */}
            <ScrollView
              refreshControl={
                <RefreshControl
                  refreshing={false}
                  onRefresh={() => {
                    refresh();
                    setRefreshTimer(Date.now());
                  }}
                />
              }
              style={indexStyle.contentContainer}
              contentContainerStyle={indexStyle.scrollContent}
              showsVerticalScrollIndicator={false}
            >
              {/* 满意度统计卡片 */}
              {['store-manager', 'consultant'].includes(ctxRole.key) ? (
                // 店长的卡片
                <StoreManagerSatisfactionStatCard
                  current_month={current_month}
                  performance_target={performance_target}
                  customer_reviews={customer_reviews}
                />
              ) : (
                // 护士的卡片
                <NurseSatisfactionStatCard
                  customer_reviews={customer_reviews}
                />
              )}
              {/* 数据统计网格组件 */}
              {core_index && core_index.length > 0 ? (
                <StatisticsGrid core_index={core_index} />
              ) : null}
              {/* 排行榜组件 */}
              <RankingList refresh={refreshTimer} />
              {/* 指标分析组件 */}
              {performance_analysis ? (
                <IndicatorAnalysis
                  ctxRole={ctxRole}
                  performance_analysis={performance_analysis}
                >
                  {ctxRole.key === 'store-manager' && (
                    <EmployeeStatistics
                      refresh={refreshTimer}
                      ctxRole={ctxRole}
                      ctxDateRange={ctxDateRange}
                    />
                  )}
                </IndicatorAnalysis>
              ) : null}
              {/* 新老客分析组件 */}
              {customers_analysis && customers_analysis.length > 0 ? (
                <NewOldCustomerAnalysis
                  customers_analysis={customers_analysis}
                />
              ) : null}
              {/* 服务点分析组件 */}
              {service_point_analysis ? (
                <ServicePointAnalysis
                  service_point_analysis={service_point_analysis}
                />
              ) : null}
              {/* 差评分布组件 */}
              {bad_evaluation_analysis ? (
                <NegativeReviewDistribution
                  bad_evaluation_analysis={bad_evaluation_analysis}
                />
              ) : null}
            </ScrollView>
          </>
        )
      ) : (
        <PermissionWrapper hasPermission={false} tabBar={true} />
      )}
    </View>
  );
};

class DataPage extends React.Component<PageProps> {
  state = {
    pageShow: false,
  };
  constructor(props: PageProps) {
    super(props);
  }

  soyoungPageName() {
    return 'dataPage';
  }

  /** 页面埋点 */
  soyoungPageInfo() {
    return {
      pageName: 'dataPage',
      pageTitle: '数据页面',
      tabName: 'RN:DATA',
    };
  }

  didAppear() {
    this.setState({
      pageShow: true,
    });
  }

  willDisappear() {
    this.setState({
      pageShow: false,
    });
  }

  preferredStatusBarStyle() {
    return '2';
  }

  render() {
    return (
      <DataPageProvider>
        <SafeAreaView style={indexStyle.container} edges={['bottom']}>
          <DataPageContent pageShow={this.state.pageShow} />
        </SafeAreaView>
      </DataPageProvider>
    );
  }
}

export default DataPage;
