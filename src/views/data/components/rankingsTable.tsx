import { ScrollView, StyleSheet, Text, View } from 'react-native';
import { getRealSize } from '../../../common/utils';

import React, { memo, useMemo } from 'react';
import Svg, { Defs, Path, Text as SvgText } from 'react-native-svg';

export interface RankListItem {
  column: {
    name: string;
    value: string;
  };
  data: Array<{
    is_me: boolean;
    value: string;
  }>;
}

export interface ColumnItem {
  is_current_sort: boolean;
  is_format: boolean;
  is_participate_sort: boolean;
  name: string;
  unit: string;
  value: string;
}

// 转换列式数据为行式数据的函数
const convertColumnToRowData = (rankList: RankListItem[]) => {
  if (!rankList.length) return [];

  const allData = rankList[0]?.data || [];
  return allData.map((item, index) => {
    const rowData: any = {
      is_me: item.is_me,
      name: item.value,
    };

    // 将其他列的数据添加到行数据中
    rankList.forEach(col => {
      if (col.column.name !== 'name') {
        rowData[col.column.name] = col.data[index]?.value || '0';
      }
    });

    return rowData;
  });
};

// 格式化大数字的函数
const formatLargeNumber = (num: number, unit: string = '') => {
  // 如果数字不是有效数值，返回默认值
  if (typeof num !== 'number' || isNaN(num)) {
    return '0' + unit;
  }

  // 处理函数：保留两位小数（截断处理）
  const truncateToTwoDecimals = (value: number): string => {
    const parts = value.toString().split('.');
    const integerPart = parts[0];
    let decimalPart = '';
    if (parts.length > 1 && parts[1].length > 0) {
      // 取小数点后最多两位，直接截断
      decimalPart = '.' + parts[1].substring(0, Math.min(2, parts[1].length));
    }
    return integerPart + decimalPart;
  };

  // 根据unit参数决定不同的处理逻辑
  if (unit === '万') {
    // 如果单位是'万'且数值大于10000，则除以10000并保留两位小数
    if (num >= 10000) {
      const divided = num / 10000;
      return truncateToTwoDecimals(divided) + unit;
    } else {
      // 小于10000不处理的截断保留两位小数
      return truncateToTwoDecimals(num);
    }
  } else {
    // 其他情况，直接保留两位小数
    return truncateToTwoDecimals(num) + unit;
  }
};

export default function RankingsTable({
  list = [],
  columns = [],
  headerBackgroundColor,
  sortBy = '',
  loading = false,
  onlyTop3 = false,
}: {
  list: RankListItem[];
  columns: ColumnItem[];
  headerBackgroundColor?: string;
  sortBy?: string;
  loading?: boolean;
  onlyTop3?: boolean;
}) {
  // 转换并排序数据
  const convertedRankList = useMemo(() => {
    if (!sortBy) {
      // 如果没有排序字段，按默认顺序并添加排名字段
      const convertedList = convertColumnToRowData(list);
      return convertedList.map((item, index) => ({
        ...item,
        rank: index + 1,
      }));
    }

    // 先将列式数据转换为行式数据并排序
    const sortedList = convertColumnToRowData(list).sort((a, b) => {
      return Number(b[sortBy]) - Number(a[sortBy]);
    });

    // 添加排名字段，处理并列排名的情况
    let currentRank = 1;
    let prevValue: any = null;

    return sortedList.map((item, index) => {
      if (index === 0) {
        // 第一项的排名始终为1
        prevValue = item[sortBy];
        item.rank = 1;
      } else {
        // 如果当前值与前一个值相同，则排名保持不变
        if (item[sortBy] === prevValue) {
          item.rank = currentRank;
        } else {
          // 不同值时，排名为当前索引位置+1
          currentRank = index + 1;
          item.rank = currentRank;
          prevValue = item[sortBy];
        }
      }
      return item;
    });
  }, [list, sortBy]);

  // 显示前50条数据
  const showRankList = useMemo(() => {
    if (onlyTop3) {
      const index = convertedRankList.findIndex(item => item.is_me);
      return [
        ...convertedRankList.slice(0, 3),
        index > 2
          ? {
              ...convertedRankList[index],
            }
          : null,
      ].filter(Boolean);
    }
    return convertedRankList.slice(0, 50);
  }, [convertedRankList, onlyTop3]);

  // 找到自己的数据
  const mySelfData = useMemo(() => {
    return convertedRankList.find(item => item.is_me);
  }, [convertedRankList]);

  return (
    <View style={styles.container}>
      {/* 表格头部 */}
      {onlyTop3 && list.length === 0 ? null : (
        <View
          style={[
            styles.tableHeader,
            { backgroundColor: headerBackgroundColor },
          ]}
        >
          <Text style={[styles.headerItem, styles.leftHeaderItem]}>
            {columns.length > 0 ? '排名' : ''}
          </Text>
          {columns.map((item, index) => (
            <Text
              key={`header_${item.name}`}
              style={[
                styles.headerItem,
                columns.length - 1 === index && styles.textRight,
                // item.value.length > 6 && {
                //   fontSize: getRealSize(11),
                // },
              ]}
            >
              {item.value}
            </Text>
          ))}
        </View>
      )}

      <ScrollView style={styles.tableBody} showsVerticalScrollIndicator={false}>
        {showRankList.length > 0 ? (
          <>
            {showRankList.map((item, index) => (
              <View
                key={`row_${index}`}
                style={[styles.tableRow, item.is_me && styles.isMeRow]}
              >
                {/* 排名列 */}
                <View style={[styles.dataColumn, styles.leftColumn]}>
                  {item.rank <= 3 || onlyTop3 ? (
                    <RankSvg rank={item.rank} />
                  ) : (
                    <Text style={styles.rankText}>{item.rank}</Text>
                  )}
                </View>

                {/* 数据列 */}
                {columns.map((column, columnIndex) => (
                  <View
                    key={`column_${column.name}_${columnIndex}`}
                    style={[
                      styles.dataColumn,
                      columns.length - 1 === columnIndex && styles.rightColumn,
                    ]}
                  >
                    <Text
                      style={[
                        styles.dataText,
                        column.name === sortBy && styles.sortByText,
                        column.name === sortBy && styles.highlight,
                        columns.length - 1 === columnIndex
                          ? styles.textRight
                          : {},
                      ]}
                    >
                      {column.is_format
                        ? formatLargeNumber(
                            Number(item[column.name] || 0),
                            column.unit || ''
                          )
                        : `${item[column.name] || ''}${column.unit || ''}`}
                    </Text>
                  </View>
                ))}
              </View>
            ))}
            {!onlyTop3 && <View style={styles.bottomBlock} />}
          </>
        ) : (
          <Block text='暂无数据' />
        )}
      </ScrollView>

      {/* 加载状态覆盖层 */}
      {loading && (
        <View style={styles.loadingOverlay}>
          {/* <Block text='加载中...' /> */}
        </View>
      )}

      {/* 自己的数据固定在底部 */}
      {!loading && !onlyTop3 && mySelfData && (
        <View style={styles.myDataContainer}>
          {/* 排名列 */}
          <View style={[styles.dataColumn, styles.leftColumn]}>
            <Text style={styles.rankText}>{mySelfData.rank}</Text>
          </View>

          {/* 数据列 */}
          {columns.map((column, columnIndex) => (
            <View
              key={`column_${column.name}_${columnIndex}`}
              style={[
                styles.dataColumn,
                columns.length - 1 === columnIndex && styles.rightColumn,
              ]}
            >
              <Text
                style={[
                  styles.dataText,
                  column.name === sortBy && styles.sortByText,
                  columns.length - 1 === columnIndex ? styles.textRight : {},
                ]}
              >
                {column.is_format
                  ? formatLargeNumber(
                      Number(mySelfData[column.name] || 0),
                      column.unit || ''
                    )
                  : `${mySelfData[column.name] || ''}${column.unit || ''}`}
              </Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  tableHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getRealSize(10),
    height: getRealSize(28),
    backgroundColor: '#fff',
  },
  leftHeaderItem: {
    flex: 0.8,
  },
  headerItem: {
    height: getRealSize(28),
    lineHeight: getRealSize(28),
    // fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#777',
    flex: 1.5,
    textAlign: 'left',
  },
  textRight: {
    textAlign: 'right',
  },
  tableBody: {
    flex: 1,
    paddingVertical: getRealSize(5),
  },
  bottomBlock: {
    height: getRealSize(30),
  },
  tableRow: {
    height: getRealSize(40),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: getRealSize(10),
    borderColor: '#fff',
    borderWidth: 1,
  },
  rankText: {
    paddingLeft: getRealSize(3),
    fontSize: getRealSize(15),
    color: '#465478',
    fontWeight: '500',
  },
  dataColumn: {
    flex: 1.5,
    alignItems: 'flex-start',
  },
  leftColumn: {
    flex: 0.8,
    alignItems: 'flex-start',
  },
  rightColumn: {
    alignItems: 'flex-end',
  },
  dataText: {
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '500',
  },
  sortByText: {
    color: '#61B43E',
    fontWeight: 'bold',
  },
  myDataContainer: {
    marginLeft: getRealSize(-15),
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getRealSize(25),
    height: getRealSize(50),
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -1 },
    shadowOpacity: 0.1,
    shadowRadius: getRealSize(8),
    elevation: 3,
    width: getRealSize(375),
  },
  // 高亮
  highlight: {
    color: '#61B43E',
  },
  isMeRow: {
    backgroundColor: '#EBFBDC',
    borderWidth: 1,
    borderColor: '#61B43E',
  },
  blockData: {
    height: getRealSize(130),
    paddingVertical: getRealSize(40),
    alignItems: 'center',
  },
  blockDataText: {
    fontSize: getRealSize(14),
    color: '#BABABA',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    // backgroundColor: 'rgba(255, 255, 255, 0.7)',
    opacity: 0,
    zIndex: 1,
  },
});

// 排名图标
const RankSvg = memo(
  ({ rank }: { rank: number }) => {
    const color = useMemo(() => {
      switch (rank) {
        case 1:
          return '#FF694F';
        case 2:
          return '#F9AA4D';
        case 3:
          return '#6DA8E8';
        default:
          return '#00ab84';
      }
    }, [rank]);
    return (
      <Svg width='15' height='19' viewBox='0 0 15 19'>
        <Defs>
          {/* <SvgLinearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <Stop offset="0%" stopColor={props.color} />
          <Stop offset="100%" stopColor={props.color} />
        </SvgLinearGradient> */}
        </Defs>
        {/* 主形状：上面矩形，下面一个角的五边形 */}
        <Path
          d='M2,0 L13,0 C14.1045695,0 15,0.8954305 15,2 L15,12.7217199 C15,13.3752783 14.6806746,13.9876075 14.1447656,14.3616931 L8.64476555,18.2009088 C7.95707376,18.6809446 7.04292624,18.6809446 6.35523445,18.2009088 L0.85523445,14.3616931 C0.319325363,13.9876075 -4.4408921e-16,13.3752783 6.66133815e-16,12.7217199 L0,2 C0,0.8954305 0.8954305,2.22044605e-16 2,0 Z'
          fill={color}
          stroke={color}
          strokeWidth='0.2'
        />
        {/* 数字文本 */}
        <SvgText
          x='8'
          y='12'
          fontFamily='Arial, sans-serif'
          fontSize='10'
          fontWeight='bold'
          textAnchor='middle'
          fill='white'
        >
          {rank}
        </SvgText>
      </Svg>
    );
  },
  (prevProps, nextProps) => {
    return prevProps.rank === nextProps.rank;
  }
);

const Block: React.FC<{ text: string }> = ({ text }) => {
  return (
    <View style={styles.blockData}>
      <Text style={styles.blockDataText}>{text}</Text>
    </View>
  );
};
