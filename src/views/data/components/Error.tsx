import React from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity } from 'react-native';
import { getRealSize } from '@/common/utils';

interface ErrorViewProps {
  /**
   * 错误文本内容
   */
  text?: string;
  /**
   * 自定义错误图片URI
   */
  imageUri?: string;
  /**
   * 自定义样式
   */
  style?: any;
  /**
   * 刷新按钮点击事件
   */
  onPress?: () => void;
}

/**
 * 错误状态组件
 * 用于显示错误信息时的错误状态提示
 */
const ErrorView: React.FC<ErrorViewProps> = ({
  text = '网络异常，请稍后重试',
  imageUri = 'https://static.soyoung.com/sy-design/aqnomvpf3ki11753429315696.png',
  style,
  onPress,
}) => {
  return (
    <View style={[styles.errorContainer, style]}>
      <Image
        source={{
          uri: imageUri,
        }}
        style={styles.errorImg}
        resizeMode='contain'
      />
      <Text style={styles.errorText}>{text}</Text>
      {onPress && (
        <TouchableOpacity style={styles.errorButton} onPress={onPress}>
          <Text style={styles.errorButtonText}>刷新</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: getRealSize(20),
  },
  errorImg: {
    width: getRealSize(60),
    height: getRealSize(60),
    marginBottom: getRealSize(16),
  },
  errorText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(14),
    color: '#999999',
    letterSpacing: 0,
    textAlign: 'center',
    lineHeight: getRealSize(20),
    fontWeight: '400',
  },
  errorButton: {
    marginTop: getRealSize(16),
    backgroundColor: '#007AFF',
    paddingHorizontal: getRealSize(24),
    paddingVertical: getRealSize(8),
    borderRadius: getRealSize(6),
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorButtonText: {
    fontSize: getRealSize(14),
    color: '#FFFFFF',
    fontFamily: 'PingFangSC-Medium',
    fontWeight: '500',
  },
});

export default ErrorView;
