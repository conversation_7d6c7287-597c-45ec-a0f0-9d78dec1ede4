import { getRealSize } from '../../../common/utils';
import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  noPermissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noPermissionText: {
    fontSize: getRealSize(14),
    color: '#777',
  },
  header: {
    position: 'relative',
    width: getRealSize(375),
    height: getRealSize(100),
  },
  backButton: {
    position: 'absolute',
    top: getRealSize(45),
    left: 0,
    height: getRealSize(40),
    width: getRealSize(40),
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  backIcon: {
    width: getRealSize(9),
    height: getRealSize(17),
  },
  bgContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: getRealSize(375),
    height: getRealSize(100),
  },

  tabContainer: {
    flexDirection: 'row',
  },
  tab: {
    paddingHorizontal: getRealSize(12),
    paddingVertical: getRealSize(6),
    marginLeft: getRealSize(8),
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabActive: {
    borderBottomColor: '#333333',
  },
  tabText: {
    fontSize: getRealSize(14),
    color: '#666666',
    fontWeight: '400',
  },
  tabTextActive: {
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '500',
  },
  subHeader: {
    height: getRealSize(50),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: getRealSize(15),
    backgroundColor: '#fff',
  },
  subTitle: {
    fontSize: getRealSize(14),
    color: '#333333',
  },
  subHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  sortSelector: {
    height: getRealSize(40),
    marginLeft: getRealSize(30),
    flexDirection: 'row',
    alignItems: 'center',
  },
  sortText: {
    fontSize: getRealSize(14),
    color: '#333333',
    marginRight: getRealSize(6),
  },
  sortIcon: {
    width: getRealSize(9),
    height: getRealSize(6),
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#F0F0F0',
    paddingVertical: getRealSize(12),
    paddingHorizontal: getRealSize(15),
  },
  rankHeader: {
    width: getRealSize(60),
    alignItems: 'center',
  },
  nameHeader: {
    flex: 1,
    alignItems: 'flex-start',
    paddingLeft: getRealSize(10),
  },
  goodReviewsHeader: {
    width: getRealSize(80),
    alignItems: 'center',
  },
  badReviewsHeader: {
    width: getRealSize(80),
    alignItems: 'center',
  },
  satisfactionHeader: {
    width: getRealSize(80),
    alignItems: 'center',
  },
  headerText: {
    fontSize: getRealSize(14),
    fontWeight: '500',
    color: '#333333',
  },
  tableContainer: {
    flex: 1,
    position: 'relative', // 为绝对定位的 loading 提供定位上下文
    paddingHorizontal: getRealSize(15),
    backgroundColor: '#fff',
  },
  // 绝对定位的 loading 样式
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingSpinner: {
    width: getRealSize(40),
    height: getRealSize(40),
  },
  loadingText: {
    marginTop: getRealSize(10),
    fontSize: getRealSize(14),
    color: '#999999',
  },
  tableRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: getRealSize(12),
    paddingHorizontal: getRealSize(15),
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  highlightedRow: {
    backgroundColor: '#EBFBDC',
    borderWidth: 1,
    borderColor: '#61B43E',
  },
  rankCell: {
    width: getRealSize(60),
    alignItems: 'center',
  },
  nameCell: {
    flex: 1,
    alignItems: 'flex-start',
    paddingLeft: getRealSize(10),
  },
  goodReviewsCell: {
    width: getRealSize(80),
    alignItems: 'center',
  },
  badReviewsCell: {
    width: getRealSize(80),
    alignItems: 'center',
  },
  satisfactionCell: {
    width: getRealSize(80),
    alignItems: 'center',
  },
  rankBadge: {
    width: getRealSize(24),
    height: getRealSize(24),
    borderRadius: getRealSize(12),
    justifyContent: 'center',
    alignItems: 'center',
  },
  rankBadgeText: {
    fontSize: getRealSize(12),
    fontWeight: '600',
    color: '#fff',
  },
  nameText: {
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '400',
  },
  goodReviewsText: {
    fontSize: getRealSize(14),
    color: '#61B43E',
    fontWeight: '500',
  },
  badReviewsText: {
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '400',
  },
  satisfactionText: {
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '400',
  },
  loadingContainer: {
    paddingVertical: getRealSize(40),
    alignItems: 'center',
  },
  // loadingText: {
  //   fontSize: getRealSize(14),
  //   color: '#999999',
  // },
  emptyContainer: {
    paddingVertical: getRealSize(40),
    alignItems: 'center',
  },
  emptyText: {
    fontSize: getRealSize(14),
    color: '#999999',
  },
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: getRealSize(12),
    borderTopRightRadius: getRealSize(12),
    paddingBottom: getRealSize(20),
  },
  modalHeader: {
    paddingVertical: getRealSize(15),
    paddingHorizontal: getRealSize(20),
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  modalTitle: {
    fontSize: getRealSize(16),
    fontWeight: '500',
    color: '#333333',
    textAlign: 'center',
  },
  modalContent: {
    paddingHorizontal: getRealSize(20),
    paddingTop: getRealSize(15),
  },
  sortOption: {
    paddingVertical: getRealSize(12),
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  sortOptionText: {
    fontSize: getRealSize(14),
    color: '#333333',
  },
  sortOptionTextActive: {
    color: '#61B43E',
    fontWeight: '500',
  },
});

// 通用选择器样式
export const commonFilterStyle = StyleSheet.create({
  datePickerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: getRealSize(5),
    width: getRealSize(375),
    height: getRealSize(50),
    backgroundColor: '#fff',
  },
  dateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    height: getRealSize(50),
    paddingHorizontal: getRealSize(15),
  },
  label: {
    marginRight: getRealSize(10),
    fontSize: getRealSize(14),
    color: '#333333',
  },
  line: {
    marginRight: getRealSize(10),
    width: 1,
    height: getRealSize(12),
    backgroundColor: '#dedede',
  },
  text: {
    marginRight: getRealSize(10),
    fontSize: getRealSize(14),
    color: '#333333',
  },
  arrowIcon: {
    width: getRealSize(9),
    height: getRealSize(6),
  },
  dateRangeContainer: {},
  checkItem: {
    height: getRealSize(40),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  checkLabel: {
    fontSize: getRealSize(15),
    color: '#333333',
  },
  checkIcon: {
    width: getRealSize(17),
    height: getRealSize(17),
  },
  timePickerContainer: {
    paddingHorizontal: getRealSize(20),
    paddingVertical: getRealSize(15),
  },
  dateInputContainer: {
    flex: 1,
    alignItems: 'center',
  },
  dateInput: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: getRealSize(12),
    paddingVertical: getRealSize(8),
  },
  dateInputText: {
    fontSize: getRealSize(14),
    color: '#333',
  },
  datePlaceholder: {
    fontSize: getRealSize(14),
    color: '#999',
  },
  calendarIcon: {
    width: getRealSize(17),
    height: getRealSize(16),
  },
  inputLine: {
    height: getRealSize(1),
    backgroundColor: '#E5E5E5',
    width: '100%',
    marginTop: getRealSize(4),
  },
  dateSeparator: {
    paddingHorizontal: getRealSize(15),
    alignItems: 'center',
  },
  separatorText: {
    fontSize: getRealSize(14),
    color: '#666',
  },
});

// Tab 组件样式
export const tabStyle = StyleSheet.create({
  container: {
    flexDirection: 'row',
    height: getRealSize(42),
    paddingHorizontal: getRealSize(15),
    backgroundColor: '#fff',
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    height: getRealSize(42),
  },
  tabText: {
    fontSize: getRealSize(14),
    color: '#646464',
    fontWeight: '400',
    textAlign: 'center',
  },
  tabTextActive: {
    fontSize: getRealSize(14),
    color: '#030303',
    fontWeight: '500',
    textAlign: 'center',
  },
  tabIndicator: {
    position: 'absolute',
    bottom: 0,
    left: '50%',
    width: getRealSize(20),
    height: getRealSize(2),
    backgroundColor: '#030303',
    transform: [{ translateX: getRealSize(-10) }],
  },
  tabIndicatorInactive: {
    display: 'none',
  },
});
