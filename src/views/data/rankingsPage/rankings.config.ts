import dayjs from 'dayjs';
import { ShortcutDate } from './rankings.interfaces';

export const shortcutDateConfig: () => ShortcutDate[] = () => [
  {
    name: '今日',
    type: 'day',
    start: dayjs().startOf('day').format('YYYY-MM-DD'),
    end: dayjs().endOf('day').format('YYYY-MM-DD'),
  },
  {
    name: '昨日',
    type: 'day',
    start: dayjs().subtract(1, 'day').startOf('day').format('YYYY-MM-DD'),
    end: dayjs().subtract(1, 'day').endOf('day').format('YYYY-MM-DD'),
  },
  {
    name: '本周',
    type: 'custom',
    start: dayjs().startOf('week').format('YYYY-MM-DD'),
    end: dayjs().endOf('week').format('YYYY-MM-DD'),
  },
  {
    name: '上周',
    type: 'custom',
    start: dayjs().subtract(1, 'week').startOf('week').format('YYYY-MM-DD'),
    end: dayjs().subtract(1, 'week').endOf('week').format('YYYY-MM-DD'),
  },
  {
    name: '本月',
    type: 'month',
    start: dayjs().startOf('month').format('YYYY-MM-DD'),
    end: dayjs().endOf('month').format('YYYY-MM-DD'),
  },
  {
    name: '上月',
    type: 'month',
    start: dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
    end: dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD'),
  },
  {
    name: '日榜',
    type: 'day',
    start: '',
    end: '',
  },
  {
    name: '月榜',
    type: 'month',
    start: '',
    end: '',
  },
  {
    name: '自定义',
    type: 'custom',
    start: '',
    end: '',
  },
];
