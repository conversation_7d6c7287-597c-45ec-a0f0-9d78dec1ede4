export interface PageProps {
  navigation?: any;
}

export type DateType = 'day' | 'month' | 'custom';
export interface RouteParams {
  params: {
    role_id: string | number;
    cur_tab: string | number;
    date_type: DateType;
    start_date: string;
    end_date: string;
  };
}

// 定义类型
export interface MarketItem {
  id: string;
  name: string;
}
export interface RankingsTypeItem {
  id: number;
  name: string;
}

export interface RankingsTabItem {
  tab_id: number;
  tab_name: string;
}

export interface ShortcutDate {
  name: string;
  type: DateType;
  start: string;
  end: string;
}

export interface SortItem {
  id: string;
  name: string;
}

export interface RoleItem {
  id: number;
  name: string;
}
