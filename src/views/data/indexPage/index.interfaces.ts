import { ReactNode } from 'react';

export interface PageProps {
  navigation?: any;
}

export interface RouteParams {
  params: {
    tenant_id: string | number;
    tenant_user_id: string | number;
  };
}

export interface PageState {}

export interface RoleItem {
  id: number;
  name: string;
  key: string;
  is_select: number;
}

export interface PerformanceTarget {
  performance_execution_amount: string;
  performance_execution_target_amount: string;
  performance_completion_rate: string;
}

export interface CustomerReviews {
  customer_satisfaction_rate: string;
  customer_evaluate_rate: string;
  customer_bad_evaluation_num: number;
}

export interface CoreIndexItem {
  core_index_name: string;
  core_index_key: string;
  core_index_value: string;
}

export interface PerformanceRankItem {
  performance_rank: number;
  performance_name: string;
  performance_performance_execution_amount: string;
  performance_up_order_rate: string;
}

export interface CommentRankItem {
  evaluate_rank: number;
  evaluate_name: string;
  evaluate_score: string;
}

export interface RankList {
  performance_rank: PerformanceRankItem[];
  comment_rank: CommentRankItem[];
}

export interface PerformanceAnalysisData {
  performance_analysis_amount: string;
  performance_analysis_amount_avg: string;
  performance_analysis_date_time: string;
}

export interface PerformanceAnalysis {
  performance_analysis_execution_amount: string;
  performance_analysis_execution_avg: string;
  performance_analysis_data: PerformanceAnalysisData[];
}

export interface EmployeeStatisticsListItem {
  employee_statistics_list_name: string;
  employee_statistics_list_key: string;
  employee_statistics_list_list: string[];
}

export interface EmployeeStatisticsRoleItem {
  role_id: number;
  role_key: string;
  role_name: string;
  is_select: number;
}

export interface EmployeeStatistics {
  employee_statistics_list_data: EmployeeStatisticsListItem[];
  employee_statistics_role_list: Array<EmployeeStatisticsRoleItem>;
}

// Provider 组件
export interface DataPageProviderProps {
  children: ReactNode;
}

export interface CustomerAnalysisListItem {
  customers_analysis_name: string;
  customers_analysis_key: string;
  customers_analysis_old: string;
  customers_analysis_new: string;
}

export interface BadEvaluationAnalysis {
  bad_evaluation_total: number;
  bad_evaluation_list: Array<{
    bad_evaluation_name: string;
    bad_evaluation_total: number;
  }>;
}

export interface ServicePointAnalysisProduct {
  product_name: string;
  product_performance_execution_amount: string;
  product_verification_service_point_num: number;
  product_service_point_rate: string;
}

export interface ServicePointAnalysisPieData {
  name: string;
  population: number;
  color: string;
  legendFontColor: string;
  legendFontSize: number;
  amount: string;
  rate: string;
}

export interface ServicePointAnalysis {
  service_point_total: number;
  service_point_analysis_product: Array<ServicePointAnalysisProduct>;
}

export interface NegativeReviewPieData {
  name: string;
  population: number;
  color: string;
  legendFontColor: string;
  legendFontSize: number;
}

// 主数据接口
export interface PageData {
  start_date: string;
  end_date: string;
  current_month: string;
  current_role_has_index: number;
  role_list: RoleItem[];
  performance_target: PerformanceTarget | null;
  customer_reviews: CustomerReviews | null;
  core_index: CoreIndexItem[] | null;
  rank_list: RankList | null;
  performance_analysis: PerformanceAnalysis | null;
  employee_statistics: null;
  customers_analysis: Array<CustomerAnalysisListItem> | null;
  bad_evaluation_analysis: BadEvaluationAnalysis | null;
  service_point_analysis: ServicePointAnalysis | null;
  my_rank_list: null;
}

export type TimeTab = {
  label: string;
  value: number;
  type: 'day' | 'month' | 'custom';
};

export interface DataPageContextType {
  hasPermission: boolean;
  loading: boolean;
  timeTabs: TimeTab[];
  // 状态管理
  ctxRole: RoleItem;
  setCurrentRole: (role: RoleItem) => void;
  activeTimeTab: number;
  setActiveTimeTab: (tab: number) => void;
  ctxDateRange: [string, string];
  setCurrentDateRange: (dateRange: [string, string]) => void;

  pageData: PageData;

  // 错误信息
  errorMessage: string;

  showSkeleton: boolean;
  setIsInitializing: (isInitializing: any) => void;
  // 刷新
  refresh: () => void;
}
