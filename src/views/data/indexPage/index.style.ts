import { StyleSheet } from 'react-native';
import { getRealSize } from '../../../common/utils';

export const indexStyle = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  // 内容区域样式
  contentContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: getRealSize(15),
    paddingTop: getRealSize(10),
    paddingBottom: getRealSize(15),
  },
});

export const modalStyle = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
    padding: 0,
  },
  modalContainer: {
    position: 'relative',
    paddingBottom: getRealSize(30),
    backgroundColor: '#fff',
  },
  modalHeader: {
    position: 'relative',
    height: getRealSize(52),
    paddingHorizontal: getRealSize(15),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButton: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: getRealSize(52),
    height: getRealSize(52),
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeImage: {
    height: getRealSize(20),
    width: getRealSize(20),
  },
  modalTitle: {
    fontSize: getRealSize(16),
    fontWeight: '500',
    color: '#333',
    textAlign: 'center',
  },
  modalContent: {
    paddingHorizontal: getRealSize(25),
    paddingBottom: getRealSize(15),
  },
  modalFooter: {
    height: getRealSize(42),
    width: getRealSize(375),
    paddingHorizontal: getRealSize(25),
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    height: getRealSize(42),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#333',
    marginRight: getRealSize(15),
  },
  cancelButtonText: {
    fontSize: getRealSize(13),
    color: '#333',
    fontWeight: '500',
  },
  confirmButton: {
    flex: 1,
    height: getRealSize(42),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#333',
    borderWidth: 1,
    borderColor: '#333',
  },
  confirmButtonText: {
    fontSize: getRealSize(13),
    color: '#fff',
    fontWeight: '500',
  },
});
