import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useRef,
} from 'react';
import {
  DataPageContextType,
  DataPageProviderProps,
  PageData,
  RoleItem,
  RouteParams,
  TimeTab,
} from './index.interfaces';
import dayjs from 'dayjs';
import Api from '@/common/api';
import {
  addCrossStatusListener,
  removeCrossStatusListener,
} from '@/constant/cross_event';
import { RouteProp, useRoute } from '@react-navigation/native';
import { pushChangeTenant } from '../../../common/pushChangeTenant';
// import { getNativeLoginInfo } from '../../../common/getNativeLoginInfo';

function formatDate(date: dayjs.Dayjs) {
  return dayjs(date).format('YYYY-MM-DD');
}

const timeTabs: Array<TimeTab> = [
  {
    label: '今日',
    value: 0,
    type: 'day',
  },
  {
    label: '本月',
    value: 1,
    type: 'month',
  },
  {
    label: '自定义',
    value: 2,
    type: 'custom',
  },
];

/**
 * 获取默认数据
 * @returns 默认数据
 */
function getDefaultPageData(): PageData {
  return {
    start_date: '',
    end_date: '',
    current_month: '',
    current_role_has_index: 0,
    role_list: [
      {
        id: 0,
        name: '',
        key: '',
        is_select: 0,
      },
    ],
    performance_target: null,
    customer_reviews: null,
    core_index: null,
    rank_list: null,
    employee_statistics: null,
    performance_analysis: null,
    customers_analysis: null,
    bad_evaluation_analysis: null,
    service_point_analysis: null,
    my_rank_list: null,
  };
}

// 创建 Context
const DataPageContext = createContext<DataPageContextType | undefined>(
  undefined
);

export const DataPageProvider: React.FC<DataPageProviderProps> = ({
  children,
}) => {
  const route = useRoute<RouteProp<Record<string, RouteParams>, string>>();
  const { tenant_id: RouteTenantId, tenant_user_id: RouteTenantUserId } =
    route.params.params;

  // 状态管理
  const [pageData, setPageData] = useState<PageData>(getDefaultPageData());
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [ctxRole, setCtxRole] = useState<RoleItem>({
    id: 0,
    name: '',
    key: '',
    is_select: 0,
  });
  const [isInitializing, setIsInitializing] = useState(true);

  // 默认时间使用当前月
  const [activeTimeTab, setActiveTimeTab] = useState(1);
  const [ctxDateRange, setCtxDateRange] = useState<[string, string]>([
    dayjs().startOf('month').format('YYYY-MM-DD'),
    dayjs().endOf('month').format('YYYY-MM-DD'),
  ]);

  const [loading, setLoading] = useState(false);

  const isInitializedRef = useRef(false);

  const [refreshTimer, setRefreshTimer] = useState(0);

  const [hasPermission, setHasPermission] = useState(true);

  //*  获取初始页面数据
  useEffect(() => {
    const [startDate, endDate] = ctxDateRange;
    const fetchInitPageData = async (start_date: string, end_date: string) => {
      console.log(
        '【获取初始页面数据】',
        `[start_date: ${start_date}]`,
        `[end_date: ${end_date}]`
      );
      setErrorMessage('');
      let st = Date.now();
      try {
        if (RouteTenantId && RouteTenantUserId) {
          await pushChangeTenant(
            String(RouteTenantId),
            String(RouteTenantUserId)
          );
        }
      } catch (error) {
        console.log('pushChangeTenant error', error);
      }
      try {
        const res = await Api.pagefetch({
          path: '/chain-wxapp/v1/index/workbenchNew',
          params: {
            employee_stat_role_id: '',
            role_id: '',
            role_key: '',
            start_date,
            end_date,
          },
        });
        console.log('workbenchNew init done!, time:', Date.now() - st);
        st = Date.now();
        if (res.errorCode === 10002) {
          console.log(res.errorMsg);
          setHasPermission(false);
          return;
        } else if (res.errorCode !== 0) {
          throw new Error(res.errorMsg);
        }
        const responseData = res.responseData as PageData;
        setHasPermission(responseData.current_role_has_index === 1);
        // 设置默认角色
        const defaultRole = (roleList: RoleItem[]) =>
          roleList?.find(role => role.is_select === 1) || roleList[0];
        const newRole = defaultRole(responseData.role_list);
        if (newRole) setCtxRole(newRole);
        const {
          role_list,
          current_month,
          performance_target,
          customer_reviews,
          core_index,
          ...others
        } = responseData;
        // 设置页面数据
        setPageData(prev => ({
          ...prev,
          performance_target,
          current_month,
          role_list,
          customer_reviews,
          core_index,
        }));
        // 区分时间片设置
        setTimeout(() => {
          setPageData(prev => ({
            ...prev,
            ...others,
          }));
        }, 500);
      } catch (error) {
        setErrorMessage(error instanceof Error ? error.message : '未知错误');
      } finally {
        setIsInitializing(false);
        console.log('setPageData stat done! time:', Date.now() - st);
        setTimeout(() => {
          isInitializedRef.current = true; // 标记初始化完成
        });
      }
    };

    fetchInitPageData(startDate, endDate);
    const subscription = addCrossStatusListener(async () => {
      // const nativeLoginInfo = await getNativeLoginInfo();
      // const extInfo = JSON.parse((nativeLoginInfo?.ext_info as string) || '{}');
      console.log('数据页面账号协同句柄触发！！');
      isInitializedRef.current = false;
      const resetStart = formatDate(dayjs().startOf('month'));
      const resetEnd = formatDate(dayjs().endOf('month'));

      await fetchInitPageData(resetStart, resetEnd);
      // 将时间默认为月
      setActiveTimeTab(1);
      setCtxDateRange([resetStart, resetEnd]);
    });
    return () => {
      removeCrossStatusListener(subscription);
    };
  }, []);

  //*  根据参数获取页面数据
  useEffect(() => {
    setErrorMessage('');
    const [startDate, endDate] = ctxDateRange;
    const { id, key } = ctxRole;
    if (!isInitializedRef.current) {
      return;
    }
    if (id === 0 && key === '') {
      return;
    }
    if (startDate === '' || endDate === '') {
      return;
    }
    console.log(
      '【获取数据页面数据】',
      `isInitializedRef: ${isInitializedRef.current}`,
      `时间: ${startDate} - ${endDate}`,
      `ctxRole: ID=${id}, key=${key}`
    );
    //*  获取页面数据
    const fetchPageData = async () => {
      try {
        setLoading(true);
        const res = await Api.pagefetch({
          path: '/chain-wxapp/v1/index/workbenchNew',
          params: {
            // 不需要传递
            employee_stat_role_id: '',
            role_id: id,
            role_key: key,
            start_date: startDate,
            end_date: endDate,
          },
        });
        console.log('workbenchNew done!!');
        if (res.errorCode !== 0) {
          throw new Error(res.errorMsg);
        }
        const responseData = res.responseData as PageData;
        setPageData(responseData);
      } catch (error) {
        setErrorMessage(error instanceof Error ? error.message : '未知错误');
      } finally {
        setLoading(false);
      }
    };
    fetchPageData();
  }, [ctxDateRange, ctxRole, refreshTimer]);

  const contextValue: DataPageContextType = {
    hasPermission,
    loading,
    // 错误信息
    errorMessage,
    // 骨架屏
    showSkeleton: isInitializing,
    setIsInitializing,
    // 页面数据
    pageData,
    // 角色管理
    ctxRole,
    setCurrentRole: (role: RoleItem) => {
      setCtxRole(role);
    },
    // 时间管理
    ctxDateRange,
    setCurrentDateRange: (dateRange: [string, string]) => {
      const [startDate, endDate] = dateRange;
      setCtxDateRange([startDate, endDate]);
    },
    // 时间管理
    timeTabs,
    activeTimeTab,
    setActiveTimeTab: (timeTab: number) => {
      setActiveTimeTab(timeTab);
    },
    refresh: () => {
      setRefreshTimer(Date.now());
    },
  };

  return (
    <DataPageContext.Provider value={contextValue}>
      {children}
    </DataPageContext.Provider>
  );
};

export const useDataPage = () => {
  const context = useContext(DataPageContext);
  if (context === undefined) {
    throw new Error('useDataPage must be used within a DataPageProvider');
  }
  return context;
};
