import React, { useState, useEffect, memo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Image,
  StyleSheet,
} from 'react-native';
import Svg, {
  Defs,
  Stop,
  Rect,
  LinearGradient as SvgLinearGradient,
} from 'react-native-svg';
import { getRealSize } from '@/common/utils';
import Api from '@/common/api';
import {
  EmployeeStatisticsListItem,
  EmployeeStatisticsRoleItem,
  RoleItem,
} from '../index.interfaces';
import { compareDateRange, deepEqual } from '../index.utils';
import Block from './Block';

const styles = StyleSheet.create({
  container: {},
  roleTabs: {
    flexDirection: 'row',
  },
  roleTab: {
    paddingBottom: getRealSize(15),
    marginRight: getRealSize(22),
  },
  roleTabText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(14),
    color: '#555555',
    fontWeight: '400',
  },
  roleTabTextActive: {
    color: '#61B43E',
    fontWeight: '500',
  },
  // 表格容器
  tableContainer: {
    position: 'relative',
    flexDirection: 'row',
    flex: 1,
  },
  // Header 背景
  headerBgc: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    width: getRealSize(315),
    height: getRealSize(38),
    overflow: 'hidden',
    zIndex: 1,
  },
  // 固定列
  fixedColumn: {
    width: getRealSize(80),
    backgroundColor: 'transparent',
    zIndex: 2,
  },
  // 固定列头部文字
  fixedHeaderText: {
    height: getRealSize(38),
    lineHeight: getRealSize(38),
    paddingHorizontal: getRealSize(10),
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#555555',
    fontWeight: '400',
    textAlignVertical: 'center',
    backgroundColor: 'transparent',
  },
  // 固定列单元格
  fixedCell: {
    height: getRealSize(28),
    paddingVertical: getRealSize(5),
    paddingHorizontal: getRealSize(10),
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#333333',
    fontWeight: '400',
    textAlignVertical: 'center',
  },
  // 滚动容器
  scrollContainer: {
    flex: 1,
    paddingRight: getRealSize(10),
    zIndex: 2,
  },
  // 滚动内容
  scrollContent: {
    flexDirection: 'row',
  },
  column: {
    minWidth: getRealSize(100),
    paddingHorizontal: getRealSize(10),
    alignItems: 'flex-end',
  },
  sortable: {
    height: getRealSize(38),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'transparent',
  },
  th: {
    height: getRealSize(28),
    paddingVertical: getRealSize(5),
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#555555',
    textAlign: 'center',
    fontWeight: '400',
  },
  sortIcon: {
    width: getRealSize(8),
    height: getRealSize(8),
    marginLeft: getRealSize(4),
  },
  td: {
    height: getRealSize(28),
    paddingVertical: getRealSize(5),
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#333333',
    textAlign: 'center',
    fontWeight: '400',
  },
  // 骨架屏样式
  skeletonBlock: {
    backgroundColor: '#f8f8f8',
  },
  skeletonBlockSmall: {
    backgroundColor: '#f8f8f8',
  },
});

// 骨架屏组件
// 暂时不用
// const EmployeeStatisticsSkeleton: React.FC = () => {
//   return (
//     <View style={styles.container}>
//       {/* 角色标签骨架 */}
//       <View style={styles.roleTabs}>
//         {[1, 2, 3].map((_, index) => (
//           <View key={index} style={styles.roleTab}>
//             <View
//               style={[
//                 styles.skeletonBlock,
//                 {
//                   width: getRealSize(30),
//                   height: getRealSize(14),
//                 },
//               ]}
//             />
//           </View>
//         ))}
//       </View>

//       {/* 表格骨架 */}
//       <View style={styles.tableContainer}>
//         {/* 固定列骨架 */}
//         {[1, 2, 3].map(v => (
//           <View style={styles.column} key={v}>
//             <View
//               style={[
//                 styles.skeletonBlock,
//                 {
//                   width: getRealSize(40),
//                   height: getRealSize(16),
//                 },
//               ]}
//             />
//             {[1, 2, 3].map(sub => (
//               <View
//                 key={sub}
//                 style={[
//                   styles.skeletonBlockSmall,
//                   {
//                     width: getRealSize(70 + sub * 5),
//                     height: getRealSize(16),
//                     marginTop: getRealSize(5),
//                   },
//                 ]}
//               />
//             ))}
//           </View>
//         ))}
//       </View>
//     </View>
//   );
// };

const EmployeeStatistics: React.FC<{
  ctxRole: RoleItem;
  ctxDateRange: [string, string];
  refresh: number;
}> = memo(
  ({ ctxRole, ctxDateRange, refresh: PRefreshTimer }) => {
    const [employeeStatRoleId, setEmployeeStatRoleId] = useState(0);
    const [employeeStatisticsListData, setEmployeeStatisticsListData] =
      useState<EmployeeStatisticsListItem[]>([]);
    const [employeeStatisticsRoleList, setEmployeeStatisticsRoleList] =
      useState<EmployeeStatisticsRoleItem[]>([]);

    // 排序状态管理
    const [ascending, setAscending] = useState<Record<string, string>>({});
    const [sortedData, setSortedData] = useState<EmployeeStatisticsListItem[]>(
      []
    );

    const [refreshTimer, setRefreshTimer] = useState(0);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
      const fetchEmployeeStatistics = async () => {
        const { id, key } = ctxRole;
        const [startDate, endDate] = ctxDateRange;
        if (id === 0 && key === '') {
          return;
        }
        console.log(
          '【获取数据页面-员工统计数据】',
          `[startDate: ${startDate}]`,
          `[endDate: ${endDate}]`,
          `[role_id: ${id}]`,
          `[role_key: ${key}]`
        );
        try {
          const res = await Api.pagefetch({
            path: '/chain-wxapp/v1/index/employeeStatistics',
            params: {
              employee_stat_role_id: employeeStatRoleId,
              role_id: id,
              role_key: key,
              start_date: startDate,
              end_date: endDate,
            },
          });
          console.log('employeeStatistics done!');
          if (res.errorCode !== 0 || !res.responseData.employee_statistics) {
            return;
          }
          const {
            employee_statistics_list_data,
            employee_statistics_role_list,
          } = res.responseData.employee_statistics;
          setEmployeeStatisticsListData(
            (employee_statistics_list_data as EmployeeStatisticsListItem[]) ||
              []
          );
          setEmployeeStatisticsRoleList(
            (employee_statistics_role_list as EmployeeStatisticsRoleItem[]) ||
              []
          );
          setEmployeeStatRoleId(
            employee_statistics_role_list.find(
              (item: EmployeeStatisticsRoleItem) => item.is_select === 1
            )?.role_id || ''
          );
        } catch (error) {
          setEmployeeStatisticsListData([]);
          setEmployeeStatisticsRoleList([]);
        } finally {
          setLoading(false);
        }
      };
      fetchEmployeeStatistics();
    }, [ctxRole, ctxDateRange, refreshTimer, PRefreshTimer]);

    useEffect(() => {
      setSortedData(employeeStatisticsListData);
    }, [employeeStatisticsListData]);

    const defaultIcon =
      'https://static.soyoung.com/sy-design/2ub72u3coax3o1753771823408.png';
    const upIcon =
      'https://static.soyoung.com/sy-design/2u7oe1ppig1jm1753771823213.png';
    const dwIcon =
      'https://static.soyoung.com/sy-design/2u7oefqtfa3k51753771823228.png';

    // 提取数字的辅助函数
    const extractNumber = (value: string | number): number => {
      if (typeof value === 'number') return value;
      if (typeof value === 'string') {
        const num = parseFloat(value.replace(/[^\d.-]/g, ''));
        return isNaN(num) ? 0 : num;
      }
      return 0;
    };

    // 排序方法
    const sortStatistics = (item: any, flag: boolean) => {
      // 重置所有排序状态，只保留当前点击的列
      const newAscending: Record<string, string> = {};
      newAscending[item.employee_statistics_list_key] = flag ? 'up' : 'down';
      setAscending(newAscending);

      // 提取各列数据
      const data = employeeStatisticsListData;
      const key = item.employee_statistics_list_key;
      const nameColumn = data.find(
        i => i.employee_statistics_list_name === '姓名'
      );
      const targetColumn = data.find(
        i => i.employee_statistics_list_key === key
      );

      if (!nameColumn || !targetColumn) {
        setSortedData(data);
        return;
      }

      // 合并数据用于排序
      const mergedData = nameColumn.employee_statistics_list_list.map(
        (name, i) => ({
          name,
          value: targetColumn.employee_statistics_list_list[i],
          row: data.map(column => column.employee_statistics_list_list[i]), // 包含所有列的行数据
        })
      );

      // 排序数据
      mergedData.sort((a, b) => {
        const aValue = extractNumber(a.value);
        const bValue = extractNumber(b.value);
        return flag ? aValue - bValue : bValue - aValue;
      });

      // 按排序后的数据重组各列
      const newSortedData = data.map(column => ({
        ...column,
        employee_statistics_list_list: mergedData.map(
          i => i.row[data.indexOf(column)]
        ),
      }));

      setSortedData(newSortedData);
    };

    return loading ? (
      <Block text='加载中...' />
    ) : sortedData.length > 0 ? (
      <View style={styles.container}>
        {/* 角色筛选标签 */}
        <View style={styles.roleTabs}>
          {employeeStatisticsRoleList.map(role => (
            <TouchableOpacity
              key={role.role_key}
              style={styles.roleTab}
              onPress={() => {
                setEmployeeStatRoleId(role.role_id);
                setRefreshTimer(Date.now());
              }}
              activeOpacity={0.9}
            >
              <Text
                style={[
                  styles.roleTabText,
                  employeeStatRoleId === role.role_id &&
                    styles.roleTabTextActive,
                ]}
              >
                {role.role_name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        <View style={styles.tableContainer}>
          <View style={styles.headerBgc}>
            <Svg width='100%' height='100%' style={StyleSheet.absoluteFill}>
              <Defs>
                <SvgLinearGradient id='headerGrad' x1='0' y1='0' x2='0' y2='1'>
                  <Stop offset='0%' stopColor='#F9F9F9' />
                  <Stop offset='100%' stopColor='#F9F9F9' />
                </SvgLinearGradient>
              </Defs>
              <Rect
                x='0'
                y='0'
                width={getRealSize(315)}
                height={getRealSize(38)}
                fill='url(#headerGrad)'
                rx={0}
                ry={0}
              />
            </Svg>
          </View>

          {/* 固定第一列 */}
          <View style={styles.fixedColumn}>
            <Text style={styles.fixedHeaderText}>姓名</Text>
            {sortedData[0]?.employee_statistics_list_list.map((data, index) => (
              <Text style={styles.fixedCell} key={`fixed_${index}`}>
                {data}
              </Text>
            ))}
          </View>

          {/* 横向滚动的内容 */}
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.scrollContainer}
            contentContainerStyle={styles.scrollContent}
          >
            {sortedData.map(
              (
                {
                  employee_statistics_list_name,
                  employee_statistics_list_key,
                  employee_statistics_list_list,
                },
                columnIndex
              ) =>
                columnIndex > 0 ? (
                  <View
                    style={styles.column}
                    key={employee_statistics_list_key}
                  >
                    <TouchableOpacity
                      style={styles.sortable}
                      onPress={() => {
                        const currentSort =
                          ascending[employee_statistics_list_key];
                        const isAscending = currentSort === 'up';
                        sortStatistics(
                          {
                            employee_statistics_list_key,
                            employee_statistics_list_name,
                          },
                          !isAscending
                        );
                      }}
                      activeOpacity={0.7}
                    >
                      <Text style={styles.th}>
                        {employee_statistics_list_name}
                      </Text>
                      <Image
                        source={{
                          uri:
                            ascending[employee_statistics_list_key] === 'up'
                              ? upIcon
                              : ascending[employee_statistics_list_key] ===
                                  'down'
                                ? dwIcon
                                : defaultIcon,
                        }}
                        style={styles.sortIcon}
                      />
                    </TouchableOpacity>
                    {employee_statistics_list_list.map((data, index) => (
                      <Text
                        style={styles.td}
                        key={`${employee_statistics_list_key}_${index}`}
                      >
                        {data}
                      </Text>
                    ))}
                  </View>
                ) : null
            )}
          </ScrollView>
        </View>
      </View>
    ) : (
      <Block />
    );
  },
  (prevProps, nextProps) => {
    return (
      deepEqual(prevProps.ctxRole, nextProps.ctxRole) &&
      compareDateRange(prevProps.ctxDateRange, nextProps.ctxDateRange) &&
      prevProps.refresh === nextProps.refresh
    );
  }
);

export default EmployeeStatistics;
