/* eslint-disable react-native/no-inline-styles */
import React, { memo, useRef, useEffect } from 'react';
import { View, Animated, StyleSheet, Dimensions } from 'react-native';
import { getRealSize } from '@/common/utils';

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: Dimensions.get('window').height,
    backgroundColor: '#f2f2f2',
    zIndex: 1000,
  },
  header: {
    height: getRealSize(57),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: getRealSize(15),
    backgroundColor: '#fff',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  block: {
    width: getRealSize(46),
    height: getRealSize(27),
    backgroundColor: '#f2f2f2',
    marginRight: getRealSize(5),
  },
  headerIcon: {
    width: getRealSize(13),
    height: getRealSize(13),
    backgroundColor: '#f2f2f2',
    marginLeft: getRealSize(6),
  },
  content: {
    flex: 1,
    paddingHorizontal: getRealSize(15),
    paddingTop: getRealSize(10),
  },
  firstRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: getRealSize(10),
  },
  card: {
    width: getRealSize(167),
    height: getRealSize(140),
    backgroundColor: '#fff',
  },
  bigBlock: {
    width: getRealSize(345),
    height: getRealSize(300),
    backgroundColor: '#fff',
    marginBottom: getRealSize(10),
  },
});

// 单个骨架块组件
const SkeletonBlock: React.FC<{
  style: any;
  delay?: number;
}> = memo(({ style, delay = 0 }) => {
  const fadeAnim = useRef(new Animated.Value(0.3)).current;
  const shimmerAnim = useRef(new Animated.Value(0)).current;
  const isMountedRef = useRef(true);
  const animationRefs = useRef<{
    fadeAnimation?: Animated.CompositeAnimation;
    shimmerAnimation?: Animated.CompositeAnimation;
    timer?: NodeJS.Timeout;
  }>({});

  useEffect(() => {
    // 延迟启动动画
    const timer = setTimeout(() => {
      if (!isMountedRef.current) return;

      // 闪烁动画
      const startFadeAnimation = () => {
        if (!isMountedRef.current) return;

        const fadeAnimation = Animated.sequence([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(fadeAnim, {
            toValue: 0.3,
            duration: 800,
            useNativeDriver: true,
          }),
        ]);

        animationRefs.current.fadeAnimation = fadeAnimation;
        fadeAnimation.start(finished => {
          if (finished && isMountedRef.current) {
            startFadeAnimation();
          }
        });
      };

      // 光泽效果动画
      const startShimmerAnimation = () => {
        if (!isMountedRef.current) return;

        shimmerAnim.setValue(0);
        const shimmerAnimation = Animated.timing(shimmerAnim, {
          toValue: 1,
          duration: 1200,
          useNativeDriver: true,
        });

        animationRefs.current.shimmerAnimation = shimmerAnimation;
        shimmerAnimation.start(finished => {
          if (finished && isMountedRef.current) {
            startShimmerAnimation();
          }
        });
      };

      startFadeAnimation();
      startShimmerAnimation();
    }, delay);

    animationRefs.current.timer = timer;

    return () => {
      // 标记组件已卸载
      isMountedRef.current = false;

      // 清理定时器
      if (animationRefs.current.timer) {
        clearTimeout(animationRefs.current.timer);
      }

      // 停止所有动画
      if (animationRefs.current.fadeAnimation) {
        animationRefs.current.fadeAnimation.stop();
      }
      if (animationRefs.current.shimmerAnimation) {
        animationRefs.current.shimmerAnimation.stop();
      }

      // 停止动画值的监听
      fadeAnim.stopAnimation();
      shimmerAnim.stopAnimation();

      // 清理引用
      animationRefs.current = {};
    };
  }, [fadeAnim, shimmerAnim, delay]);

  // 计算闪烁透明度
  const opacity = fadeAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 1],
  });

  // 计算光泽位移
  const translateX = shimmerAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [-100, 100],
  });

  // 计算光泽透明度
  const shimmerOpacity = shimmerAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0, 0.6, 0],
  });

  return (
    <View style={[style, { overflow: 'hidden' }]}>
      {/* 基础背景 */}
      <Animated.View style={[StyleSheet.absoluteFill, { opacity }]} />

      {/* 光泽效果层 */}
      <Animated.View
        style={[
          StyleSheet.absoluteFill,
          {
            backgroundColor: 'transparent',
            opacity: shimmerOpacity,
            transform: [{ translateX }],
          },
        ]}
      >
        <View
          style={{
            width: '100%',
            height: '100%',
            backgroundColor: 'transparent',
            position: 'absolute',
            left: 0,
            top: 0,
            transform: [{ skewX: '-20deg' }],
          }}
        >
          <View
            style={{
              width: '30%',
              height: '100%',
              backgroundColor: '#FFFFFF',
              opacity: 0.8,
            }}
          />
        </View>
      </Animated.View>
    </View>
  );
});

// Skeleton 组件
const Skeleton: React.FC = memo(() => {
  return (
    <View style={styles.container}>
      <View style={{ height: getRealSize(44) }} />
      {/* 顶部导航栏 */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <SkeletonBlock style={styles.block} delay={0} />
          <SkeletonBlock style={styles.block} delay={100} />
          <SkeletonBlock style={styles.block} delay={200} />
        </View>
        <View style={styles.headerRight}>
          <SkeletonBlock style={styles.block} delay={300} />
          <SkeletonBlock style={styles.headerIcon} delay={400} />
        </View>
      </View>

      {/* 主要内容区域 */}
      <View style={styles.content}>
        {/* 第一行：两个卡片 */}
        <View style={styles.firstRow}>
          <SkeletonBlock style={styles.card} delay={500} />
          <SkeletonBlock style={styles.card} delay={600} />
        </View>

        {/* 第二行：大块内容区域 */}
        <SkeletonBlock style={styles.bigBlock} delay={700} />

        {/* 底部区域 */}
        <SkeletonBlock style={styles.bigBlock} delay={800} />
      </View>
    </View>
  );
});

export default Skeleton;
