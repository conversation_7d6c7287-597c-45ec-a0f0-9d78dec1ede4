import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
} from 'react-native';
import { LineChart } from 'react-native-chart-kit';
import Svg, { Line } from 'react-native-svg';

import { getRealSize } from '@/common/utils';
import { formattedInt, formattedNumber } from '../index.utils';
import Block from './Block';
import { PerformanceAnalysis, RoleItem } from '../index.interfaces';

const isIOS = Platform.OS === 'ios';

const styles = StyleSheet.create({
  container: {
    marginBottom: getRealSize(10),
    padding: getRealSize(15),
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getRealSize(15),
  },
  title: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(15),
    color: '#333333',
    fontWeight: '500',
  },
  tabRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  tab: {
    paddingHorizontal: getRealSize(15),
    paddingVertical: getRealSize(5),
    marginLeft: getRealSize(10),
  },
  tabActive: {
    backgroundColor: '#EBFBDC',
  },
  tabInactive: {
    backgroundColor: '#f5f5f5',
  },
  tabText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#333333',
    fontWeight: '400',
  },
  tabTextActive: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#61B43E',
    fontWeight: '400',
  },
  subtitle: {
    marginBottom: getRealSize(10),
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '400',
  },
  summaryLine: {
    paddingLeft: getRealSize(10),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    height: getRealSize(38),
    backgroundColor: '#EBFBDC',
  },
  summaryLineSymbol: {
    marginRight: getRealSize(5),
    width: getRealSize(10),
    height: getRealSize(2),
    backgroundColor: '#61B43E',
    borderRadius: getRealSize(2),
  },
  summaryLineText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#555555',
    letterSpacing: -0.5,
    fontWeight: '400',
  },
  summaryLineHighlight: {
    paddingLeft: getRealSize(5),
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(14),
    color: '#61B43E',
    fontWeight: '700',
  },
  chartContainer: {
    marginTop: getRealSize(10),
    paddingHorizontal: getRealSize(10),
    overflow: 'hidden',
  },
  chart: {
    alignSelf: 'center',
  },
  // 数据点信息样式
  dataPointInfo: {
    position: 'absolute',
    top: getRealSize(10),
    zIndex: 1000,
  },
  dataPointCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: getRealSize(12),
    shadowColor: isIOS ? 'rgba(0,0,0,0.1)' : 'rgba(0,0,0,0.5)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    minWidth: getRealSize(50),
  },
  dataPointTitle: {
    fontSize: getRealSize(12),
    fontWeight: '500',
    color: '#666',
  },
  // SVG 垂直虚线容器
  verticalLineContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 2,
    height: '100%',
    zIndex: 999,
  },
  // 数据金额样式
  dataPointAmount: {
    fontSize: getRealSize(12),
    fontWeight: 'bold',
    color: '#666',
  },
});

const IndicatorAnalysis: React.FC<{
  ctxRole: RoleItem;
  performance_analysis: PerformanceAnalysis;
  children?: React.ReactNode;
}> = ({ children, ctxRole, performance_analysis }) => {
  const [activeTab, setActiveTab] = useState(0); // 默认选中"业绩分析"
  const [selectedDataPoint, setSelectedDataPoint] = useState<{
    date: string;
    amount: number;
    index: number;
    x: number;
  } | null>(null);
  const [dates, setDates] = useState<string[]>([]);
  const [amounts, setAmounts] = useState<number[]>([]);
  const [total, setTotal] = useState<string>('');
  const indicatorAnalysisTabs = useMemo(() => {
    setActiveTab(0);
    return [{ key: 0, title: '业绩分析' }].concat(
      ctxRole.key === 'store-manager' ? [{ key: 1, title: '员工统计' }] : []
    );
  }, [ctxRole]);

  useEffect(() => {
    if (!performance_analysis) {
      setDates([]);
      setAmounts([]);
      return;
    }
    const { performance_analysis_data, performance_analysis_execution_amount } =
      performance_analysis;
    setDates(
      performance_analysis_data?.map(
        item => item.performance_analysis_date_time
      ) || []
    );
    setAmounts(
      performance_analysis_data?.map(item =>
        Number(item.performance_analysis_amount)
      ) || []
    );
    setTotal(performance_analysis_execution_amount);
  }, [performance_analysis]);

  const timeRange = useMemo(() => {
    return dates.length > 0 && dates.length > 0
      ? `${dates.slice(0, 1)} - ${dates.slice(-1)}`
      : dates.length === 1
        ? dates[0]
        : '';
  }, [dates]);

  return (
    <View style={styles.container}>
      {/* 头部标题和标签 */}
      <View style={styles.header}>
        <Text style={styles.title}>指标分析</Text>
        <View style={styles.tabRow}>
          {indicatorAnalysisTabs.map(tab => (
            <TouchableOpacity
              key={tab.key}
              style={[
                styles.tab,
                activeTab === tab.key ? styles.tabActive : styles.tabInactive,
              ]}
              onPress={() => setActiveTab(tab.key)}
              activeOpacity={0.9}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === tab.key && styles.tabTextActive,
                ]}
              >
                {tab.title}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* 业绩分析内容 */}
      <View
        style={
          activeTab === 0
            ? {
                position: 'relative',
              }
            : {
                position: 'absolute',
                transform: [{ translateX: 10000 }],
              }
        }
      >
        {amounts.length > 0 ? (
          <>
            <Text style={styles.subtitle}>收入趋势</Text>
            <View style={styles.summaryLine}>
              <View style={styles.summaryLineSymbol} />
              <Text style={styles.summaryLineText}>{timeRange}</Text>
              <Text style={styles.summaryLineHighlight}>{total}</Text>
            </View>

            {/* 趋势图表 */}
            <View style={styles.chartContainer}>
              <LineChart
                data={{
                  labels: dates,
                  datasets: [
                    {
                      data: amounts,
                      color: () => `#00AB84`,
                      strokeWidth: 1,
                    },
                  ],
                }}
                formatYLabel={(value: string) => formattedInt(value)}
                width={getRealSize(315)}
                height={getRealSize(150)}
                chartConfig={{
                  backgroundGradientFrom: '#ffffff',
                  backgroundGradientTo: '#ffffff',
                  fillShadowGradientFrom: 'rgba(97,180,62,0.30)',
                  fillShadowGradientTo: 'rgba(235,251,220,0.30)',
                  decimalPlaces: 0,
                  color: (opacity = 1) => `rgba(61, 213, 152, ${opacity})`,
                  labelColor: () => `#777`,
                  style: {},
                  propsForDots: {
                    r: '0',
                    strokeWidth: '1',
                    stroke: '#00ab84',
                    fill: '#ffffff',
                  },
                  propsForBackgroundLines: {
                    strokeDasharray: '',
                    stroke: '#dedede',
                    strokeWidth: 0.5,
                  },
                  propsForLabels: {
                    fontSize: getRealSize(12),
                    fontWeight: '400',
                  },
                }}
                bezier
                style={styles.chart}
                withDots={true}
                withShadow={true}
                withInnerLines={true}
                withOuterLines={false}
                withVerticalLines={false}
                withHorizontalLines={true}
                withVerticalLabels={false}
                withHorizontalLabels={true}
                fromZero={true}
                yAxisSuffix=''
                yAxisInterval={0}
                segments={5}
                onDataPointClick={({ index, x }) => {
                  // 处理点击事件
                  setSelectedDataPoint({
                    date: dates[index],
                    amount: amounts[index],
                    index: index,
                    x: x,
                  });
                  setTimeout(() => {
                    setSelectedDataPoint(null);
                  }, 3000);
                }}
              />

              {/* SVG 垂直虚线标记 */}
              {selectedDataPoint && (
                <View
                  style={[
                    styles.verticalLineContainer,
                    { left: selectedDataPoint.x - 1 },
                  ]}
                >
                  <Svg width={2} height='100%'>
                    <Line
                      x1='1'
                      y1='0'
                      x2='1'
                      y2='100%'
                      stroke='#61B43E'
                      strokeWidth={1}
                      strokeDasharray='4 2'
                      strokeLinecap='butt'
                    />
                  </Svg>
                </View>
              )}

              {/* 点击数据显示 */}
              {selectedDataPoint && (
                <View
                  style={[
                    styles.dataPointInfo,
                    { left: selectedDataPoint.x - getRealSize(60) },
                  ]}
                >
                  <View style={styles.dataPointCard}>
                    <Text style={styles.dataPointTitle}>
                      {selectedDataPoint.date}：
                    </Text>
                    <Text style={styles.dataPointAmount}>
                      {formattedNumber(selectedDataPoint.amount)}
                    </Text>
                  </View>
                </View>
              )}
            </View>
          </>
        ) : (
          <Block />
        )}
      </View>
      {/* 员工统计内容 */}
      <View
        style={
          activeTab === 1
            ? {
                position: 'relative',
              }
            : {
                position: 'absolute',
                transform: [{ translateX: -10000 }],
              }
        }
      >
        {children}
      </View>
    </View>
  );
};

export default IndicatorAnalysis;
