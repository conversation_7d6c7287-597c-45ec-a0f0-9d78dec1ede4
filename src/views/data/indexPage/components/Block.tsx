import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { getRealSize } from '@/common/utils';

interface BlockProps {
  text?: string;
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: getRealSize(20),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  text: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(14),
    color: '#BABABA',
    letterSpacing: 0,
    textAlign: 'center',
    fontWeight: '400',
  },
});

const Block: React.FC<BlockProps> = ({ text = '暂无数据' }) => {
  return (
    <View style={styles.container}>
      <Text style={styles.text}>{text}</Text>
    </View>
  );
};

export default Block;
