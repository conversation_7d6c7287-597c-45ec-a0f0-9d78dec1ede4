import React, { useState, useEffect, memo } from 'react';
import { View, Text, StyleSheet, Platform } from 'react-native';
import { PieChart } from 'react-native-chart-kit';
import { getRealSize } from '@/common/utils';
import {
  BadEvaluationAnalysis,
  NegativeReviewPieData,
} from '../index.interfaces';
import Block from './Block';
import { deepEqual } from '../index.utils';

const isIOS = Platform.OS === 'ios';

const COLORS = [
  '#6BE7A9',
  '#97A3FF',
  '#8DD7FF',
  '#FFA183',
  '#FF77A8',
  '#4CC2AF',
  '#FFD462',
  '#E2939A',
  '#BAA498',
  '#79A4FF',
  '#9692AD',
  'green',
];

const getColor = (index: number): string => {
  return COLORS[index % COLORS.length] || '#cccccc'; // 循环使用颜色，默认灰色
};

const styles = StyleSheet.create({
  container: {
    marginBottom: getRealSize(10),
    paddingBottom: getRealSize(10),
    backgroundColor: '#fff',
    overflow: 'hidden',
  },
  title: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(16),
    color: '#333',
    fontWeight: '500',
    marginTop: getRealSize(15),
    marginLeft: getRealSize(15),
  },
  content: {
    paddingHorizontal: getRealSize(15),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  chartContainer: {
    position: 'relative',
    marginVertical: getRealSize(20),
    alignItems: 'center',
    justifyContent: 'center',
  },
  chartWrap: {
    width: getRealSize(140),
    height: getRealSize(140),
    borderRadius: getRealSize(110),
    backgroundColor: '#fff',
    // iOS 阴影
    shadowColor: isIOS ? 'rgba(0,0,0,0.1)' : 'rgba(0,0,0,0.5)',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 1,
    shadowRadius: getRealSize(7),
    // Android 阴影
    elevation: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  chartCenter: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [
      { translateX: -getRealSize(45) },
      { translateY: -getRealSize(45) },
    ],
    height: getRealSize(90),
    width: getRealSize(90),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: getRealSize(90),
    backgroundColor: '#fff',
  },
  chartCenterText: {
    paddingTop: getRealSize(10),
    fontFamily: 'DINAlternate-Bold',
    fontSize: getRealSize(25),
    lineHeight: getRealSize(25),
    color: '#333333',
    fontWeight: '700',
    textAlign: 'center',
  },
  chartCenterSub: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#AAABB3',
    fontWeight: '400',
    textAlign: 'center',
  },
  legendList: {
    marginLeft: getRealSize(50),
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  legendItem: {
    lineHeight: getRealSize(20),
    marginBottom: getRealSize(5),
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    width: getRealSize(7),
    height: getRealSize(7),
    borderRadius: getRealSize(7),
    marginRight: getRealSize(5),
  },
  name: {
    width: getRealSize(50),
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(14),
    color: '#555555',
    fontWeight: '400',
    overflow: 'hidden',
  },
  count: {
    marginLeft: getRealSize(12),
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '500',
  },
});

const NegativeReviewDistribution: React.FC<{
  bad_evaluation_analysis: BadEvaluationAnalysis;
}> = memo(
  ({ bad_evaluation_analysis }) => {
    const [badEvaluationTotal, setBadEvaluationTotal] = useState(0);
    const [pieData, setPieData] = useState<Array<NegativeReviewPieData>>([]);

    useEffect(() => {
      if (bad_evaluation_analysis) {
        const { bad_evaluation_list, bad_evaluation_total: total } =
          bad_evaluation_analysis;
        setBadEvaluationTotal(total);
        setPieData(
          bad_evaluation_list?.map(
            ({ bad_evaluation_name, bad_evaluation_total }, index) => ({
              name: bad_evaluation_name,
              population: bad_evaluation_total,
              color: getColor(index),
              legendFontColor: '#333',
              legendFontSize: 12,
            })
          ) || []
        );
      } else {
        setBadEvaluationTotal(0);
        setPieData([]);
      }
    }, [bad_evaluation_analysis]);

    return pieData.length > 0 ? (
      <View style={styles.container}>
        <Text style={styles.title}>差评分析</Text>
        <View style={styles.content}>
          {/* 环形图 */}
          <View style={styles.chartContainer}>
            <View style={styles.chartWrap}>
              <PieChart
                data={pieData}
                width={getRealSize(140)}
                height={getRealSize(140)}
                chartConfig={{
                  color: () => '#fff',
                }}
                accessor='population'
                backgroundColor='transparent'
                paddingLeft={'0'}
                center={[getRealSize(35), 0]}
                hasLegend={false}
                absolute
                avoidFalseZero
              />
            </View>
            {/* 中心文字 */}
            <View style={styles.chartCenter}>
              <Text style={styles.chartCenterText}>{badEvaluationTotal}</Text>
              <Text style={styles.chartCenterSub}>总差评数</Text>
            </View>
          </View>
          {/* 图例列表 */}
          <View style={styles.legendList}>
            {pieData.map(item => (
              <View key={item.name} style={styles.legendItem}>
                <View style={[styles.dot, { backgroundColor: item.color }]} />
                <Text numberOfLines={1} style={styles.name}>
                  {item.name}
                </Text>
                <Text style={styles.count}>{item.population}条</Text>
              </View>
            ))}
          </View>
        </View>
      </View>
    ) : (
      <Block />
    );
  },
  (prevProps, nextProps) => {
    return deepEqual(
      prevProps.bad_evaluation_analysis,
      nextProps.bad_evaluation_analysis
    );
  }
);

export default NegativeReviewDistribution;
