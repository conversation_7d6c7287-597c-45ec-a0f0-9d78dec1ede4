import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet } from 'react-native';
import { useDataPage } from '../index.context';
import { getRealSize } from '@/common/utils';
import Api from '@/common/api';
import { jumpReactNativePage } from '@/common/jumpPage';
import RankingsTable, {
  ColumnItem,
  RankListItem,
} from '../../components/rankingsTable';

const styles = StyleSheet.create({
  container: {
    marginBottom: getRealSize(10),
    paddingHorizontal: getRealSize(15),
    paddingTop: getRealSize(15),
    overflow: 'hidden',
    backgroundColor: '#fff',
  },
  tabRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginBottom: getRealSize(15),
  },
  tab: {
    marginRight: getRealSize(20),
    paddingTop: getRealSize(2),
    paddingBottom: getRealSize(5),
    alignItems: 'center',
    position: 'relative',
  },

  tabActiveIndicator: {
    position: 'absolute',
    bottom: 0,
    left: '50%',
    marginLeft: getRealSize(-10),
    width: getRealSize(20),
    height: getRealSize(2),
    backgroundColor: '#030303',
  },
  tabText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15),
    lineHeight: getRealSize(22),
    color: '#555555',
    fontWeight: '500',
  },
  tabTextActive: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(15),
    color: '#333333',
    fontWeight: '500',
  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: getRealSize(15),
  },
  footerText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#61B43E',
    fontWeight: '400',
  },
  arrow: {
    width: getRealSize(6),
    height: getRealSize(9),
    marginLeft: getRealSize(5),
  },
});

const RankingList: React.FC<{
  refresh: number;
}> = ({ refresh }) => {
  const { ctxRole, ctxDateRange, timeTabs, activeTimeTab } = useDataPage();
  const [activeTab, setActiveTab] = useState(-1); // 默认选中"评价榜"
  const [tabs, setTabs] = useState<
    Array<{
      tab_id: number;
      tab_name: string;
    }>
  >([]);
  const [list, setList] = useState<Array<RankListItem>>([]);
  const [columns, setColumns] = useState<Array<ColumnItem>>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const { id } = ctxRole;
    if (id === 0) {
      return;
    }
    const fetchRankConfig = async () => {
      try {
        const res = await Api.pagefetch({
          path: '/chain-wxapp/v1/index/getRankConfig',
          params: {
            role_id: id,
          },
        });
        if (res.errorCode !== 0) {
          setTabs([]);
          return;
        }
        setTabs(res.responseData.tabs || []);
        setActiveTab(res.responseData.tabs?.[0]?.tab_id || 1);
      } catch (error) {
        setTabs([]);
      }
    };
    fetchRankConfig();
  }, [ctxRole]);

  useEffect(() => {
    const [start_date, end_date] = ctxDateRange;
    /**
     *  tab_id：tab id，必填，只能传1,2,3
        market_name：赛道名称,tab_id=3时选填，其它不需要传
        user_id：不需要传
        tenant_user_id：不需要传
        role_id：工作台-用户选择的角色id,tab_id=2和3时需要穿
        search_role_id：
          tab_id=1时，该值不能为5，排行榜详情页-用户选择的角色id
          tab_id=2时，如果是店长，可以穿0,3,4,5；非店长必须和role_id一致
          tab_id=3时，只能传3
        role_key：不需要传，完全没用的字段，用户选择的角色key
        start_date：必填：开始时间
        end_date：必填：结束时间
        cur_user_is_store_manager：不需要传，安全漏洞！！当前登陆人是否店长
        type_id：选填，类型 1门店（默认） 2城市
     * 
     */
    if (start_date === '' || end_date === '') {
      return;
    }
    if (ctxRole.id === 0) {
      return;
    }
    if (activeTab === -1) {
      return;
    }
    console.log(
      '【获取数据页面-排行榜数据】',
      `[日期: ${start_date}至${end_date}]`,
      `[tab: ${activeTab}]`,
      `[role_id: ${ctxRole.id}]`,
      `[search_role_id: ${ctxRole.id === 2 ? '' : ctxRole.id}]`
    );
    const fetchRankDataList = async () => {
      try {
        setLoading(true);
        const res = await Api.pagefetch({
          path: '/chain-wxapp/v1/index/getRankDataList',
          params: {
            tab_id: activeTab,
            role_id: ctxRole.id,
            search_role_id: ctxRole.id === 2 ? '' : ctxRole.id,
            start_date,
            end_date,
            type_id: 1,
          },
        });
        console.log('getRankDataList done!');
        if (res.errorCode !== 0) {
          setList([]);
          setColumns([]);
          return;
        }
        setList(res.responseData.list || []);
        setColumns(res.responseData.columns || []);
      } catch (error) {
        setList([]);
        setColumns([]);
      } finally {
        setLoading(false);
      }
    };
    fetchRankDataList();
  }, [ctxRole, activeTab, ctxDateRange, refresh]);

  return (
    <View style={styles.container}>
      {/* 标签页 */}
      <View style={styles.tabRow}>
        {tabs.map(tab => (
          <TouchableOpacity
            key={tab.tab_id}
            style={styles.tab}
            onPress={() => setActiveTab(tab.tab_id)}
            activeOpacity={0.9}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === tab.tab_id && styles.tabTextActive,
              ]}
            >
              {tab.tab_name}
            </Text>
            {activeTab === tab.tab_id && (
              <View style={styles.tabActiveIndicator} />
            )}
          </TouchableOpacity>
        ))}
      </View>
      <RankingsTable
        onlyTop3={true}
        sortBy={columns.find(item => item.is_current_sort)?.name || ''}
        loading={loading}
        list={list}
        columns={columns}
      />
      {/* 底部查看详情 */}
      <TouchableOpacity
        style={styles.footer}
        activeOpacity={0.9}
        onPress={() => {
          const query = [
            `role_id=${ctxRole.id}`,
            `cur_tab=${activeTab}`,
            `date_type=${timeTabs.find(item => item.value === activeTimeTab)?.type}`,
            `start_date=${ctxDateRange[0]}`,
            `end_date=${ctxDateRange[1]}`,
          ].join('&');
          console.log('data/rankings?query=>', query);
          jumpReactNativePage(`data/rankings?${query}`);
        }}
      >
        <Text style={styles.footerText}>查看详情</Text>
        <Image
          source={{
            uri: 'https://static.soyoung.com/sy-design/3u5f7j35qkn711753259920870.png',
          }}
          style={styles.arrow}
        />
      </TouchableOpacity>
    </View>
  );
};

export default RankingList;
