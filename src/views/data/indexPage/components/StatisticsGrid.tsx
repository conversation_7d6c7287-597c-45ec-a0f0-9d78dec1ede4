import React, { memo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { getRealSize } from '@/common/utils';
import { CoreIndexItem } from '../index.interfaces';
import { deepCompareArray } from '../index.utils';

const styles = StyleSheet.create({
  container: {
    width: getRealSize(345),
    alignSelf: 'center',
    backgroundColor: '#fff',
    overflow: 'hidden',
    marginBottom: getRealSize(10),
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  cell: {
    height: getRealSize(60),
    width: getRealSize(114),
    alignItems: 'center',
    justifyContent: 'center',
  },
  value: {
    lineHeight: getRealSize(23),
    marginBottom: getRealSize(2),
    fontFamily: 'Outfit-Medium',
    fontSize: getRealSize(18),
    color: '#333333',
    fontWeight: '500',
    textAlign: 'center',
  },
  label: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#333333',
    fontWeight: '400',
    textAlign: 'center',
  },
});

const StatisticsGrid: React.FC<{
  core_index: CoreIndexItem[];
}> = memo(
  ({ core_index }) => {
    return core_index.length > 0 ? (
      <View style={styles.container}>
        {core_index.map(
          ({ core_index_value, core_index_name, core_index_key }) => (
            <View key={core_index_key} style={[styles.cell]}>
              <Text style={styles.value}>{core_index_value || ''}</Text>
              <Text style={styles.label}>{core_index_name || ''}</Text>
            </View>
          )
        )}
      </View>
    ) : null;
  },
  (prevProps, nextProps) => {
    return deepCompareArray(prevProps.core_index, nextProps.core_index);
  }
);

export default StatisticsGrid;
