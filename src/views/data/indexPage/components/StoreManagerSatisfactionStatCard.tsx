import React, { memo, useMemo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Svg, {
  Defs,
  Stop,
  Rect,
  Circle,
  LinearGradient as SvgLinearGradient,
} from 'react-native-svg';
import { getRealSize } from '@/common/utils';
import { deepEqual, formatted, formattedNumber } from '../index.utils';
import { CustomerReviews, PerformanceTarget } from '../index.interfaces';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginBottom: getRealSize(10),
  },
  card: {
    width: getRealSize(167),
    height: getRealSize(140),
    flex: 1,
    overflow: 'hidden',
    backgroundColor: 'transparent',
  },
  cardRight: {
    marginLeft: getRealSize(10),
  },
  content: {
    flex: 1,
    padding: getRealSize(15),
    justifyContent: 'space-between',
  },
  title: {
    lineHeight: getRealSize(20),
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(14),
    color: '#FFFFFF',
    fontWeight: '500',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: getRealSize(8),
  },
  value: {
    marginBottom: getRealSize(-2),
    lineHeight: getRealSize(35),
    fontFamily: 'Outfit-Medium',
    fontSize: getRealSize(28),
    color: '#FFFFFF',
    fontWeight: '500',
  },
  valueSub: {
    fontFamily: 'PingFangSC-Semibold',
    fontSize: getRealSize(16),
    color: '#FFFFFF',
    fontWeight: '600',
  },
  progressBarWrap: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressLabel: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(14),
    color: '#FFFFFF',
    fontWeight: '400',
  },
  progressValue: {
    fontSize: getRealSize(18),
    color: '#fff',
    fontWeight: '500',
  },
  rightValue: {
    fontFamily: 'Outfit-Medium',
    fontSize: getRealSize(28),
    color: '#FFFFFF',
    fontWeight: '500',
  },
  rightRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: getRealSize(8),
  },
  rightRowItem: {
    alignItems: 'flex-start',
  },
  rightLabel: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(14),
    color: '#FFFFFF',
    fontWeight: '400',
  },
  rightValueSmall: {
    fontFamily: 'DINAlternate-Bold',
    fontSize: getRealSize(18),
    color: '#FFFFFF',
    fontWeight: '700',
  },
  rightValueTiny: {
    fontFamily: 'Outfit-Medium',
    fontSize: getRealSize(16),
    color: '#FFFFFF',
    fontWeight: '500',
    letterSpacing: getRealSize(2),
  },
  rowSpaceBetween: {
    justifyContent: 'space-between',
  },
});

const StoreManagerSatisfactionStatCard: React.FC<{
  current_month: string;
  performance_target: PerformanceTarget | null;
  customer_reviews: CustomerReviews | null;
}> = memo(
  ({ current_month, performance_target, customer_reviews }) => {
    const performanceExecutionAmount = useMemo(
      () =>
        performance_target?.performance_execution_amount
          ? formatted(performance_target?.performance_execution_amount)
          : 0,
      [performance_target]
    );

    const performanceExecutionTargetAmount = useMemo(
      () =>
        performance_target?.performance_execution_target_amount
          ? formattedNumber(
              performance_target?.performance_execution_target_amount
            )
          : 0,
      [performance_target]
    );

    const completionRate = useMemo(() => {
      return (
        Number(
          performance_target?.performance_completion_rate?.replace('%', '') || 0
        ) / 100
      );
    }, [performance_target]);

    const percent = useMemo(
      () => getRealSize(128) * Math.max(completionRate, 0.07),
      [completionRate]
    );

    const customerEvaluateRate = useMemo(() => {
      return Number(
        customer_reviews?.customer_evaluate_rate?.replace('%', '') || 0
      );
    }, [customer_reviews]);

    const customerSatisfactionRate = useMemo(
      () => customer_reviews?.customer_satisfaction_rate || 0,
      [customer_reviews]
    );

    const customerBadEvaluationNum = useMemo(
      () => customer_reviews?.customer_bad_evaluation_num || 0,
      [customer_reviews]
    );

    return (
      <View style={styles.container}>
        {/* 左侧SVG渐变卡片 */}
        <View style={styles.card}>
          <Svg width='100%' height='100%' style={StyleSheet.absoluteFill}>
            <Defs>
              <SvgLinearGradient id='cardGrad' x1='0' y1='0' x2='1' y2='1'>
                <Stop offset='0%' stopColor='#09ca94' />
                <Stop offset='100%' stopColor='#00B58C' />
              </SvgLinearGradient>
            </Defs>
            <Rect
              x='0'
              y='0'
              width='100%'
              height='100%'
              fill='url(#cardGrad)'
            />
          </Svg>
          <View style={styles.content}>
            <Text style={[styles.title, { marginBottom: getRealSize(5) }]}>
              {current_month}月业绩/目标（元）
            </Text>
            <View style={[styles.row, { marginBottom: getRealSize(10) }]}>
              <Text style={styles.value}>{performanceExecutionAmount}</Text>
              <Text style={styles.valueSub}>
                /{performanceExecutionTargetAmount}
              </Text>
            </View>
            {/* 渐变进度条 */}
            <View style={styles.progressBarWrap}>
              <Svg width={getRealSize(137)} height={getRealSize(18)}>
                {/* 背景条 */}
                <Rect
                  x='0'
                  y={getRealSize(5)}
                  width={getRealSize(137)}
                  height={getRealSize(8)}
                  rx={getRealSize(4)}
                  fill='rgba(255,255,255,0.5)'
                />
                {/* 渐变进度条 */}
                <Rect
                  x='0'
                  y={getRealSize(5)}
                  width={percent}
                  height={getRealSize(8)}
                  rx={getRealSize(4)}
                  fill='rgba(255,255,255,1)'
                />
                {/* 滑块 */}
                <Circle
                  cx={percent}
                  cy={getRealSize(9)}
                  r={getRealSize(7)}
                  fill='#fff'
                  stroke='#00AB84'
                  strokeWidth={3}
                  opacity={percent > 0 ? 1 : 0}
                />
              </Svg>
            </View>
            <View style={[styles.row, styles.rowSpaceBetween]}>
              <Text style={styles.progressLabel}>完成率</Text>
              <Text style={styles.progressValue}>
                {performance_target?.performance_completion_rate}
              </Text>
            </View>
          </View>
        </View>
        {/* 右侧SVG渐变卡片 */}
        <View style={[styles.card, styles.cardRight]}>
          <Svg width='100%' height='100%' style={StyleSheet.absoluteFill}>
            <Defs>
              <SvgLinearGradient id='cardGradRight' x1='0' y1='0' x2='1' y2='1'>
                <Stop offset='0%' stopColor='#4EB4FF' />
                <Stop offset='100%' stopColor='#3590E9' />
              </SvgLinearGradient>
            </Defs>
            <Rect
              x='0'
              y='0'
              width='100%'
              height='100%'
              fill='url(#cardGradRight)'
            />
          </Svg>
          <View style={styles.content}>
            <Text style={styles.title}>满意度得分</Text>
            <Text style={styles.rightValue}>{customerSatisfactionRate}</Text>
            <View style={styles.rightRow}>
              <View style={styles.rightRowItem}>
                <Text style={styles.rightLabel}>参评率</Text>
                <Text style={styles.rightValueSmall}>
                  {customerEvaluateRate}
                  <Text style={styles.rightValueTiny}>%</Text>
                </Text>
              </View>
              <View style={styles.rightRowItem}>
                <Text style={styles.rightLabel}>差评数</Text>
                <Text style={styles.rightValueSmall}>
                  {customerBadEvaluationNum}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    );
  },
  (prevProps, nextProps) => {
    return deepEqual(prevProps.customer_reviews, nextProps.customer_reviews);
  }
);

export default StoreManagerSatisfactionStatCard;
