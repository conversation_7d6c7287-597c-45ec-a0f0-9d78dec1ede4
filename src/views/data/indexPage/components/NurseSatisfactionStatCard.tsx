import React, { memo, useMemo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Svg, {
  Defs,
  Stop,
  Rect,
  LinearGradient as SvgLinearGradient,
} from 'react-native-svg';
import { getRealSize } from '@/common/utils';
import { CustomerReviews } from '../index.interfaces';
import { deepEqual } from '../index.utils';

const styles = StyleSheet.create({
  container: {
    width: getRealSize(345),
    height: getRealSize(92),
    overflow: 'hidden',
    marginBottom: getRealSize(10),
    alignSelf: 'center',
    backgroundColor: 'transparent',
  },
  row: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    padding: getRealSize(15),
  },
  col: {
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  title: {
    marginBottom: getRealSize(10),
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(14),
    color: '#FFFFFF',
    fontWeight: '500',
  },
  value: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(28),
    color: '#FFFFFF',
    fontWeight: '700',
  },
});

const NurseSatisfactionStatCard: React.FC<{
  customer_reviews: CustomerReviews | null;
}> = memo(
  ({ customer_reviews }) => {
    return (
      <View style={styles.container}>
        {/* SVG 渐变背景 */}
        <Svg width='100%' height='100%' style={StyleSheet.absoluteFill}>
          <Defs>
            <SvgLinearGradient id='satisGrad' x1='0' y1='0' x2='1' y2='1'>
              <Stop offset='0' stopColor='#09CA94' />
              <Stop offset='100%' stopColor='#00B58C' />
            </SvgLinearGradient>
          </Defs>
          <Rect x='0' y='0' width='100%' height='100%' fill='url(#satisGrad)' />
        </Svg>
        <View style={styles.row}>
          <View style={[styles.col, { flex: 1.5 }]}>
            <Text style={styles.title}>满意度</Text>
            <Text style={styles.value}>
              {customer_reviews?.customer_satisfaction_rate || 0}
            </Text>
          </View>
          <View style={[styles.col, { flex: 1.5 }]}>
            <Text style={styles.title}>参评率</Text>
            <Text style={styles.value}>
              {customer_reviews?.customer_evaluate_rate || 0}
            </Text>
          </View>
          <View style={[styles.col, { flex: 1 }]}>
            <Text style={styles.title}>差评数</Text>
            <Text style={styles.value}>
              {customer_reviews?.customer_bad_evaluation_num || 0}
            </Text>
          </View>
        </View>
      </View>
    );
  },
  (prevProps, nextProps) => {
    return deepEqual(prevProps.customer_reviews, nextProps.customer_reviews);
  }
);

export default NurseSatisfactionStatCard;
