import React, { useState, useEffect, memo } from 'react';
import { View, Text, StyleSheet, Platform } from 'react-native';
import { PieChart } from 'react-native-chart-kit';
import { getRealSize } from '@/common/utils';
import {
  ServicePointAnalysisPieData,
  ServicePointAnalysis,
} from '../index.interfaces';
import Block from './Block';
import { deepEqual } from '../index.utils';

const isIOS = Platform.OS === 'ios';

const COLORS = [
  '#6BE7A9',
  '#97A3FF',
  '#8DD7FF',
  '#FFA183',
  '#FF77A8',
  '#4CC2AF',
  '#FFD462',
  '#E2939A',
  '#BAA498',
  '#79A4FF',
  '#9692AD',
  'green',
];
const getColor = (index: number): string => {
  return COLORS[index % COLORS.length] || '#cccccc'; // 循环使用颜色，默认灰色
};

const styles = StyleSheet.create({
  container: {
    marginBottom: getRealSize(10),
    paddingBottom: getRealSize(10),
    backgroundColor: '#fff',
    overflow: 'hidden',
  },
  title: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(16),
    color: '#333',
    fontWeight: '500',
    marginTop: getRealSize(15),
    marginLeft: getRealSize(15),
  },
  chartContainer: {
    position: 'relative',
    marginVertical: getRealSize(20),
    alignItems: 'center',
    justifyContent: 'center',
  },
  chartWrap: {
    width: getRealSize(140),
    height: getRealSize(140),
    borderRadius: getRealSize(110),
    backgroundColor: '#fff',
    // iOS 阴影
    shadowColor: isIOS ? 'rgba(0,0,0,0.1)' : 'rgba(0,0,0,0.5)',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 1,
    shadowRadius: getRealSize(7),
    // Android 阴影
    elevation: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  chartCenter: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [
      { translateX: -getRealSize(45) },
      { translateY: -getRealSize(45) },
    ],
    height: getRealSize(90),
    width: getRealSize(90),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: getRealSize(90),
    backgroundColor: '#fff',
  },
  chartCenterText: {
    paddingTop: getRealSize(10),
    fontFamily: 'DINAlternate-Bold',
    fontSize: getRealSize(25),
    lineHeight: getRealSize(25),
    color: '#333333',
    fontWeight: '700',
    textAlign: 'center',
  },
  chartCenterSub: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#AAABB3',
    fontWeight: '400',
    textAlign: 'center',
  },
  tableContainer: {
    paddingHorizontal: getRealSize(15),
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#EBFBDC',
    height: getRealSize(28),
    marginBottom: getRealSize(5),
    alignItems: 'center',
    paddingHorizontal: getRealSize(10),
  },
  th: {
    flex: 1,
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#555',
    fontWeight: '400',
    textAlign: 'left',
  },
  thFirst: {
    flex: 1.5,
  },
  thRight: {
    textAlign: 'right',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: getRealSize(5),
    paddingHorizontal: getRealSize(10),
  },
  td: {
    flex: 1,
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(16),
    color: '#333',
    fontWeight: '400',
    textAlign: 'left',
    lineHeight: getRealSize(18),
  },
  tdFirst: {
    flex: 1.5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  tdFirstText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    fontWeight: '400',
    color: '#333',
  },
  dot: {
    width: getRealSize(5),
    height: getRealSize(5),
    borderRadius: getRealSize(5),
    marginRight: getRealSize(4),
  },
  tdRight: {
    color: '#333',
    fontWeight: '700',
    textAlign: 'right',
  },
});

const ServicePointAnalysisFC: React.FC<{
  service_point_analysis: ServicePointAnalysis | null;
}> = memo(
  ({ service_point_analysis }) => {
    const [servicePointTotal, setServicePointTotal] = useState(0);
    const [servicePieData, setServicePieData] = useState<
      Array<ServicePointAnalysisPieData>
    >([]);

    useEffect(() => {
      if (service_point_analysis) {
        const { service_point_total, service_point_analysis_product } =
          service_point_analysis;
        setServicePointTotal(service_point_total);
        setServicePieData(
          service_point_analysis_product?.map(
            (
              {
                product_name,
                product_performance_execution_amount,
                product_verification_service_point_num,
                product_service_point_rate,
              },
              index
            ) => ({
              name: product_name,
              population: product_verification_service_point_num,
              color: getColor(index),
              legendFontColor: '#333',
              legendFontSize: 12,
              amount: product_performance_execution_amount,
              rate: product_service_point_rate,
            })
          ) || []
        );
      } else {
        setServicePointTotal(0);
        setServicePieData([]);
      }
    }, [service_point_analysis]);

    return (
      <View style={styles.container}>
        <Text style={styles.title}>服务点分析</Text>
        {servicePieData.length > 0 ? (
          <>
            {/* 环形图 */}
            <View style={styles.chartContainer}>
              <View style={styles.chartWrap}>
                <PieChart
                  data={servicePieData}
                  width={getRealSize(140)}
                  height={getRealSize(140)}
                  chartConfig={{
                    color: () => '#fff',
                  }}
                  accessor='population'
                  backgroundColor='transparent'
                  paddingLeft={'0'}
                  center={[getRealSize(35), 0]}
                  hasLegend={false}
                  absolute
                  avoidFalseZero
                />
              </View>
              {/* 中心文字 */}
              <View style={styles.chartCenter}>
                <Text style={styles.chartCenterText}>{servicePointTotal}</Text>
                <Text style={styles.chartCenterSub}>总服务点</Text>
              </View>
            </View>
            <View style={styles.tableContainer}>
              {/* 表头 */}
              <View style={styles.tableHeader}>
                <Text style={[styles.th, styles.thFirst]}>项目名称</Text>
                {/* <Text style={[styles.th, styles.thRight]}>
          执行业绩
        </Text> */}
                <Text style={[styles.th, styles.thRight]}>核销服务点</Text>
                <Text style={[styles.th, styles.thRight]}>服务点占比</Text>
              </View>
              {/* 数据行 */}
              {servicePieData.map(
                (row: ServicePointAnalysisPieData, index: number) => (
                  <View key={row.name + index} style={styles.row}>
                    <View style={[styles.td, styles.tdFirst]}>
                      <View
                        style={[styles.dot, { backgroundColor: row.color }]}
                      />
                      <Text numberOfLines={2} style={styles.tdFirstText}>
                        {row.name}
                      </Text>
                    </View>
                    {/* <Text style={[styles.td, styles.tdRight]}>
              {row.amount}
            </Text> */}
                    <Text style={[styles.td, styles.tdRight]}>
                      {row.population}
                    </Text>
                    <Text style={[styles.td, styles.tdRight]}>{row.rate}</Text>
                  </View>
                )
              )}
            </View>
          </>
        ) : (
          <Block />
        )}
      </View>
    );
  },
  (prevProps, nextProps) => {
    return deepEqual(
      prevProps.service_point_analysis,
      nextProps.service_point_analysis
    );
  }
);

export default ServicePointAnalysisFC;
