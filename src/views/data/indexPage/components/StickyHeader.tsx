import React, { useState, useEffect, useMemo, useRef } from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native';
import Modal from 'react-native-modal';
import { modalAnimation } from '@/constant/modal_animation';
import { getRealSize } from '@/common/utils';
import DateInput from '@/components/DateInput';
import dayjs from 'dayjs';
import { Bridge } from '@/common/bridge';
import { useDataPage } from '../index.context';
import { modalStyle } from '../index.style';
import Header from '@/components/header';

const styles = StyleSheet.create({
  stickyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: getRealSize(57),
    paddingHorizontal: getRealSize(15),
    backgroundColor: '#fff',
  },
  timeFilterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeTab: {
    paddingHorizontal: getRealSize(10),
    paddingVertical: getRealSize(4),
    marginRight: getRealSize(5),
  },
  timeTabActive: {
    borderWidth: 1,
    backgroundColor: '#EBFBDC',
    borderColor: '#61B43E',
  },
  timeTabInactive: {
    borderWidth: 1,
    backgroundColor: 'transparent',
    borderColor: '#fff',
  },
  timeTabText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#333333',
    fontWeight: '400',
    textAlign: 'center',
  },
  timeTabTextActive: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(13),
    color: '#61B43E',
    fontWeight: '500',
  },
  roleDropdown: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  roleText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#333333',
    fontWeight: '400',
    textAlign: 'center',
  },
  arrow: {
    marginLeft: getRealSize(10),
    width: getRealSize(9),
    height: getRealSize(6),
  },
  roleList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  roleButton: {
    position: 'relative',
    height: getRealSize(38),
    width: getRealSize(98),
    backgroundColor: '#f2f2f2',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: getRealSize(10),
    marginRight: getRealSize(15),
  },
  roleButtonActive: {
    backgroundColor: '#fff',
    borderWidth: 2,
    borderColor: '#333',
  },
  roleButtonActiveImage: {
    position: 'absolute',
    bottom: -1,
    right: 0,
    width: getRealSize(18),
    height: getRealSize(13),
  },
  roleButtonText: {
    fontSize: getRealSize(14),
    color: '#333',
    fontWeight: '400',
  },
  roleButtonTextActive: {
    color: '#333',
    fontWeight: '500',
  },
  dateRangeContainer: {
    paddingVertical: getRealSize(15),
  },
  roleButtonMarginRight: {
    marginRight: 0,
  },
});

const StickyHeader: React.FC = () => {
  const {
    pageData,
    ctxRole,
    setCurrentRole,
    ctxDateRange,
    setCurrentDateRange,
    activeTimeTab,
    timeTabs,
    setActiveTimeTab,
  } = useDataPage();
  const role_list = pageData?.role_list || [];
  const [localRole, setLocalRole] = useState(ctxRole);
  const [roleVisible, setRoleVisible] = useState(false);
  const [dateVisible, setDateVisible] = useState(false);
  const [localDateRange, setLocalDateRange] = useState<[string, string]>([
    '',
    '',
  ]);
  const lastActiveTimeTab = useRef<number>(activeTimeTab);
  const lastCtxDateRange = useRef<[string, string]>(['', '']);

  useEffect(() => {
    setLocalRole(ctxRole);
  }, [ctxRole]);

  // 时间筛选
  const handleDateChange = (value: number) => {
    switch (value) {
      case 0: {
        if (activeTimeTab === value) {
          return;
        }
        lastCtxDateRange.current = ctxDateRange;
        setCurrentDateRange([
          dayjs().startOf('day').format('YYYY-MM-DD'),
          dayjs().endOf('day').format('YYYY-MM-DD'),
        ]);
        break;
      }
      case 1: {
        if (activeTimeTab === value) {
          return;
        }
        lastCtxDateRange.current = ctxDateRange;
        setCurrentDateRange([
          dayjs().startOf('month').format('YYYY-MM-DD'),
          dayjs().endOf('month').format('YYYY-MM-DD'),
        ]);
        break;
      }
      case 2: {
        lastCtxDateRange.current = ctxDateRange;
        if (activeTimeTab !== value) {
          setCurrentDateRange(['', '']);
        }
        setDateVisible(true);
        break;
      }
      default:
        break;
    }
    lastActiveTimeTab.current = activeTimeTab;
    setActiveTimeTab(value);
  };

  // 自定义日期筛选格式化
  const formatDateRange = useMemo(() => {
    if (ctxDateRange[0] && ctxDateRange[1] && activeTimeTab === 2) {
      return `${dayjs(ctxDateRange[0]).format('MM-DD')}至${dayjs(ctxDateRange[1]).format('MM-DD')}`;
    }
    return '自定义';
  }, [ctxDateRange, activeTimeTab]);

  return (
    <>
      <Header title='数据' zIndex={102} bgColor='#FFFFFF' hideBack={true} />
      <View style={styles.stickyHeader}>
        {/* 左侧时间筛选 */}
        <View style={styles.timeFilterContainer}>
          {timeTabs.map(({ label, value }) => (
            <TouchableOpacity
              key={value}
              style={[
                styles.timeTab,
                activeTimeTab === value
                  ? styles.timeTabActive
                  : styles.timeTabInactive,
              ]}
              onPress={() => handleDateChange(value)}
              activeOpacity={0.9}
            >
              <Text
                style={[
                  styles.timeTabText,
                  activeTimeTab === value && styles.timeTabTextActive,
                ]}
              >
                {label === '自定义' ? formatDateRange : label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* 右侧角色下拉 */}
        <TouchableOpacity
          style={styles.roleDropdown}
          onPress={() => {
            setRoleVisible(true);
          }}
          activeOpacity={0.9}
        >
          <Text style={styles.roleText}>{ctxRole?.name || '选择角色'}</Text>
          <Image
            source={{
              uri: 'https://static.soyoung.com/sy-design/3u5f7j35qkn711753259920865.png',
            }}
            style={styles.arrow}
          />
        </TouchableOpacity>
      </View>
      {/* 角色选择器 */}
      <Modal
        isVisible={roleVisible}
        {...modalAnimation}
        style={modalStyle.modal}
        animationIn='slideInUp'
        animationOut='slideOutDown'
      >
        <View style={modalStyle.modalContainer}>
          <View style={modalStyle.modalHeader}>
            {/* 关闭按钮 */}
            <TouchableOpacity
              style={modalStyle.closeButton}
              onPress={() => setRoleVisible(false)}
            >
              <Image
                style={modalStyle.closeImage}
                source={{
                  uri: 'https://static.soyoung.com/sy-design/bzsokyai5osd1753416667877.png',
                }}
              />
            </TouchableOpacity>
            <Text style={modalStyle.modalTitle}>选择角色</Text>
          </View>
          <View style={modalStyle.modalContent}>
            <View style={styles.roleList}>
              {role_list.map((role, index) => (
                <TouchableOpacity
                  key={role.id}
                  style={[
                    styles.roleButton,
                    localRole?.id === role.id && styles.roleButtonActive,
                    index > 0 &&
                      index % 2 === 0 &&
                      styles.roleButtonMarginRight,
                  ]}
                  onPress={() => setLocalRole(role)}
                  activeOpacity={0.9}
                >
                  <Text
                    style={[
                      styles.roleButtonText,
                      localRole?.id === role.id && styles.roleButtonTextActive,
                    ]}
                  >
                    {role.name}
                  </Text>
                  {localRole?.id === role.id ? (
                    <Image
                      source={{
                        uri: 'https://static.soyoung.com/sy-design/3o6q6zpv0xqzv1753416667887.png',
                      }}
                      style={styles.roleButtonActiveImage}
                    />
                  ) : null}
                </TouchableOpacity>
              ))}
            </View>
          </View>
          {/* 底部按钮 */}
          <View style={modalStyle.modalFooter}>
            <TouchableOpacity
              style={modalStyle.cancelButton}
              onPress={() => setRoleVisible(false)}
              activeOpacity={0.9}
            >
              <Text style={modalStyle.cancelButtonText}>取消</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={modalStyle.confirmButton}
              onPress={() => {
                if (localRole) {
                  setCurrentRole(localRole);
                  setRoleVisible(false);
                }
              }}
              activeOpacity={0.9}
            >
              <Text style={modalStyle.confirmButtonText}>确定</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      {/* 自定义日期选择器 */}
      <Modal
        isVisible={dateVisible}
        {...modalAnimation}
        style={modalStyle.modal}
        animationIn='slideInUp'
        animationOut='slideOutDown'
        onModalShow={() => {
          if (ctxDateRange[0] && ctxDateRange[1]) {
            setLocalDateRange(ctxDateRange);
          }
        }}
        onModalHide={() => {
          setLocalDateRange(['', '']);
        }}
      >
        <View style={modalStyle.modalContainer}>
          <View style={modalStyle.modalHeader}>
            <Text style={modalStyle.modalTitle}>选择时间</Text>
            <TouchableOpacity
              style={modalStyle.closeButton}
              onPress={() => {
                setActiveTimeTab(lastActiveTimeTab.current);
                setCurrentDateRange(lastCtxDateRange.current);
                setDateVisible(false);
              }}
            >
              <Image
                style={modalStyle.closeImage}
                source={{
                  uri: 'https://static.soyoung.com/sy-design/bzsokyai5osd1753416667877.png',
                }}
              />
            </TouchableOpacity>
          </View>
          <View style={modalStyle.modalContent}>
            <View style={styles.dateRangeContainer}>
              <DateInput
                value={localDateRange}
                isRange={true}
                type='year-month-day'
                onChange={value => {
                  // if (value[0] && value[1]) {
                  //   // 验证是否选择未来日期
                  //   const startDate = dayjs(value[0]);
                  //   const endDate = dayjs(value[1]);
                  //   const today = dayjs();
                  // }
                  setLocalDateRange(value as [string, string]);
                }}
              />
            </View>
          </View>
          {/* 底部按钮 */}
          <View style={modalStyle.modalFooter}>
            <TouchableOpacity
              style={modalStyle.cancelButton}
              onPress={() => {
                // console.log('reset', lastCtxDateRange.current);
                setLocalDateRange(['', '']);
              }}
              activeOpacity={0.9}
            >
              <Text style={modalStyle.cancelButtonText}>重置</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={modalStyle.confirmButton}
              onPress={() => {
                if (!localDateRange[0] || !localDateRange[1]) {
                  Bridge.showToast('请选择日期');
                  return;
                }
                const startDate = dayjs(localDateRange[0]);
                const endDate = dayjs(localDateRange[1]);
                const diffInMonths = endDate.diff(startDate, 'month', true);
                if (diffInMonths > 3) {
                  Bridge.showToast('起止时间间隔不能超过3个月');
                  return;
                }
                const today = dayjs();
                if (
                  startDate.isAfter(today, 'day') &&
                  endDate.isAfter(today, 'day')
                ) {
                  Bridge.showToast('不能选择未来日期');
                  return;
                }
                setDateVisible(false);
                // console.log('reset', lastCtxDateRange.current);
                setCurrentDateRange(localDateRange);
              }}
              activeOpacity={0.9}
            >
              <Text style={modalStyle.confirmButtonText}>确定</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </>
  );
};

export default StickyHeader;
