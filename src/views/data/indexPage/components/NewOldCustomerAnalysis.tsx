import React, { memo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { getRealSize } from '@/common/utils';
import { CustomerAnalysisListItem } from '../index.interfaces';
import { deepCompareArray } from '../index.utils';

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    padding: getRealSize(15),
    marginBottom: getRealSize(10),
    overflow: 'hidden',
  },
  title: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(15),
    color: '#333333',
    fontWeight: '500',
  },
  tableHeader: {
    marginBottom: getRealSize(5),
    marginTop: getRealSize(10),
    flexDirection: 'row',
    height: getRealSize(28),
    alignItems: 'center',
    backgroundColor: 'rgba(247,247,247,0.48)',
    // borderRadius: getRealSize(4),
  },
  th: {
    flex: 1,
    paddingHorizontal: getRealSize(10),
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#333333',
    fontWeight: '400',
  },
  thOld: {
    flex: 1,
    color: '#61B43E',
    textAlign: 'right',
  },
  thNew: {
    flex: 1,
    textAlign: 'right',
    color: '#2E86F2',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: getRealSize(5),
    lineHeight: getRealSize(18),
  },
  td: {
    flex: 1,
    paddingHorizontal: getRealSize(10),
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    lineHeight: getRealSize(18),
    color: '#333333',
    fontWeight: '400',
  },
  tdOld: {
    flex: 1,
    color: '#61B43E',
    fontSize: getRealSize(15),
    fontWeight: '700',
    textAlign: 'right',
    paddingHorizontal: 0,
  },
  tdNew: {
    flex: 1,
    color: '#3A8BFF',
    fontSize: getRealSize(15),
    fontWeight: '700',
    textAlign: 'right',
  },
});

const NewOldCustomerAnalysis: React.FC<{
  customers_analysis: Array<CustomerAnalysisListItem>;
}> = memo(
  ({ customers_analysis }) => {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>新老客分析</Text>
        {/* 表头 */}
        <View style={styles.tableHeader}>
          <Text style={styles.th}>分析项</Text>
          <Text style={[styles.th, styles.thOld]}>老客</Text>
          <Text style={[styles.th, styles.thNew]}>新客</Text>
        </View>

        {/* 数据行 */}
        {customers_analysis.map(
          ({
            customers_analysis_key,
            customers_analysis_name,
            customers_analysis_old,
            customers_analysis_new,
          }) => (
            <View key={customers_analysis_key} style={styles.row}>
              <Text numberOfLines={1} style={styles.td}>
                {customers_analysis_name}
              </Text>
              <Text style={[styles.td, styles.tdOld]}>
                {customers_analysis_old}
              </Text>
              <Text style={[styles.td, styles.tdNew]}>
                {customers_analysis_new}
              </Text>
            </View>
          )
        )}
      </View>
    );
  },
  (prevProps, nextProps) => {
    return deepCompareArray(
      prevProps.customers_analysis,
      nextProps.customers_analysis
    );
  }
);

export default NewOldCustomerAnalysis;
