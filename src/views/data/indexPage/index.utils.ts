export const formattedNumber = (value: number | string) => {
  if (typeof value === 'string') {
    value = Number(value);
  }
  // 如果数字不超过1万，直接返回原始数字
  if (value > -10000 && value < 10000) {
    return value.toString(); // 直接输出数字字符串
  }
  // 超过1万时，截断小数点后两位
  let formatted =
    value < 0
      ? Math.trunc((value / 10000) * 100) / 100
      : Math.floor((value / 10000) * 100) / 100; // 截断到两位小数
  formatted = Number(formatted.toString()); // 去掉多余的小数位
  // 如果小数点后是.00，去掉.00
  if (formatted.toString().endsWith('.00')) {
    return Math.floor(formatted).toString() + 'w';
  }
  return formatted + 'w';
};
export const formatted = (value: number | string) => {
  if (typeof value === 'string') {
    value = Number(value);
  }
  const result = Math.floor(value / 1000) / 10; // 向下取整保留一位小数
  return result % 1 === 0 ? result.toString() : result.toFixed(1); // 如果是整数，移除小数点部分
};

export const formattedInt = (value: number | string) => {
  if (typeof value === 'string') {
    value = Number(value);
  }
  // 如果数字不超过1万，直接返回原始数字
  if (value > -10000 && value < 10000) {
    return value.toString(); // 直接输出数字字符串
  }
  const tmp = value / 10000;
  return `${Number(tmp.toFixed(2))}w`;
};

/**
 * 深度比较两个值是否相等
 * 支持原始类型、数组、对象的深度比较
 */
export const deepEqual = (a: any, b: any): boolean => {
  // 严格相等检查（包括引用相等）
  if (a === b) return true;

  // null/undefined检查
  if (a == null || b == null) return a === b;

  // 类型检查
  if (typeof a !== typeof b) return false;

  // 数组比较
  if (Array.isArray(a) && Array.isArray(b)) {
    return deepCompareArray(a, b);
  }

  // 对象比较
  if (typeof a === 'object' && typeof b === 'object') {
    return deepCompareObject(a, b);
  }

  // 其他类型直接比较
  return a === b;
};

/**
 * 深度比较两个数组是否相等
 */
export const deepCompareArray = <T>(arr1: T[], arr2: T[]): boolean => {
  // 长度比较
  if (arr1.length !== arr2.length) return false;

  // 逐个元素深度比较
  for (let i = 0; i < arr1.length; i++) {
    if (!deepEqual(arr1[i], arr2[i])) {
      return false;
    }
  }

  return true;
};

/**
 * 深度比较两个对象是否相等
 */
export const deepCompareObject = (obj1: any, obj2: any): boolean => {
  // 获取所有键
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  // 键数量比较
  if (keys1.length !== keys2.length) return false;

  // 逐个键值深度比较
  for (const key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!deepEqual(obj1[key], obj2[key])) return false;
  }

  return true;
};

/**
 * 浅比较数组（只比较第一层）
 * 性能更好，适用于简单数组
 */
export const shallowCompareArray = <T>(arr1: T[], arr2: T[]): boolean => {
  if (arr1.length !== arr2.length) return false;

  for (let i = 0; i < arr1.length; i++) {
    if (arr1[i] !== arr2[i]) return false;
  }

  return true;
};

/**
 * 比较两个日期范围数组是否相等
 * 专门用于 [startDate, endDate] 格式
 */
export const compareDateRange = (
  range1: [string, string],
  range2: [string, string]
): boolean => {
  if (!range1 || !range2) return range1 === range2;
  return range1[0] === range2[0] && range1[1] === range2[1];
};
