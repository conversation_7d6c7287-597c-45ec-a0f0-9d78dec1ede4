/**
 * 新建预约页面 - 类型定义
 */

// 客户信息类型
export interface CustomerInfo {
  name: string;
  gender: string;
  age: string;
  phone: string;
  id?: string;
  customer_id_str?: string; // 字符串类型的客户ID，避免大数值溢出
  customer_id?: string; // 从 customer_id_str 映射而来的客户ID，用于业务接口传参
}

// 门店信息类型
export interface StoreInfo {
  name: string;
  id: string;
}

// 项目信息类型
export interface ProjectInfo {
  title: string;
  product_id: string;
  order_id: string;
  top_order_id: string;
  display_order_id: string;
  reserve_id?: string; // 预约ID，跳转日历页面需要
}

// 时间段信息类型
export interface TimeSlot {
  from: string;
  to: string;
  reserved_num: number;
  remain_num: number;
  total_num: number;
  cashback?: string;
  available?: boolean;
}

// 治疗师/咨询师信息类型
export interface StaffInfo {
  name: string;
  id: string;
}

// 预约依据类型
export enum ReserveType {
  NO_ORDER = 3, // 无单预约
  WITH_ORDER = 4, // 有单预约
}

// 表单状态类型
export interface FormState {
  customer: CustomerInfo;
  store: StoreInfo;
  project: ProjectInfo;
  reserveType: ReserveType;
  selectedDateIndex: number;
  selectedTimeIndex: number;
  curDate: string;
  timeList: TimeSlot[];
  reason: string;
  therapist: StaffInfo;
  consultant: StaffInfo;
  notes: string;
  confirmWithReservation: boolean;
  doctor_designate_yn: boolean;
  consult_designate_yn: boolean;
  showReason: boolean;
}

// 日历项目类型
export interface CalendarItem {
  date: string;
  remain_num: number;
  total_num: number;
  cashback?: string;
}

// 返现信息类型
export interface CashbackInfo {
  date: string;
  cashback: string;
}

// 日历相关类型
export interface CalendarData {
  planList: CalendarItem[];
  cashbackList: CashbackInfo[];
  reserveCalendarNotAvailableDates: string[];
  startDate: string;
  endDate: string;
}

// 页面导航参数类型
export interface CreateReserveRouteParams {
  customerId?: string;
  storeId?: string;
  projectId?: string;
}

// API 响应类型 (根据实际API响应格式调整)
export interface ApiResponse<T = any> {
  errorCode: number;
  responseData: T;
  errorMsg?: string;
}

// 验证错误类型
export interface ValidationError {
  field: string;
  message: string;
}
