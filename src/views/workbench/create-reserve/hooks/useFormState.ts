import { useState, useCallback } from 'react';
import dayjs from 'dayjs';
import {
  FormState,
  CustomerInfo,
  StoreInfo,
  ProjectInfo,
  StaffInfo,
  ReserveType,
  TimeSlot,
} from '../types';

/**
 * 表单状态管理 Hook
 */
export const useFormState = () => {
  // 初始化表单状态
  const [formState, setFormState] = useState<FormState>({
    customer: { name: '', gender: '', age: '', phone: '' },
    store: { name: '', id: '' },
    project: {
      title: '',
      product_id: '',
      order_id: '',
      top_order_id: '',
      display_order_id: '',
    },
    reserveType: ReserveType.WITH_ORDER,
    selectedDateIndex: 0,
    selectedTimeIndex: -1,
    curDate: dayjs().format('YYYY-MM-DD'),
    timeList: [],
    reason: '',
    therapist: { name: '', id: '' },
    consultant: { name: '', id: '' },
    notes: '',
    confirmWithReservation: true,
    doctor_designate_yn: false,
    consult_designate_yn: false,
    showReason: false,
  });

  // 0. 重置客户变更相关数据（保留客户与门店，清空其余）
  const resetCustomerRelatedData = useCallback(() => {
    setFormState(prev => ({
      ...prev,
      project: {
        title: '',
        product_id: '',
        order_id: '',
        top_order_id: '',
        display_order_id: '',
      },
      reserveType: ReserveType.WITH_ORDER,
      selectedDateIndex: 0,
      selectedTimeIndex: -1,
      curDate: dayjs().format('YYYY-MM-DD'),
      timeList: [],
      reason: '',
      therapist: { name: '', id: '' },
      consultant: { name: '', id: '' },
      notes: '',
      confirmWithReservation: true,
      doctor_designate_yn: false,
      consult_designate_yn: false,
      showReason: false,
    }));
  }, []);

  // 1. 重置门店相关数据（保留客户）
  const resetStoreRelatedData = useCallback(() => {
    setFormState(prev => ({
      ...prev,
      project: {
        title: '',
        product_id: '',
        order_id: '',
        top_order_id: '',
        display_order_id: '',
      },
      selectedDateIndex: 0,
      selectedTimeIndex: -1,
      curDate: dayjs().format('YYYY-MM-DD'),
      timeList: [],
      reason: '',
      therapist: { name: '', id: '' },
      consultant: { name: '', id: '' },
      notes: '',
      reserveType: ReserveType.WITH_ORDER, // 恢复默认预约依据
      confirmWithReservation: true,
      doctor_designate_yn: false,
      consult_designate_yn: false,
      showReason: false,
    }));
  }, []);

  // 2. 重置预约依据相关数据（保留门店、客户）
  const resetReserveTypeRelatedData = useCallback(() => {
    setFormState(prev => ({
      ...prev,
      project: {
        title: '',
        product_id: '',
        order_id: '',
        top_order_id: '',
        display_order_id: '',
      },
      selectedDateIndex: 0,
      selectedTimeIndex: -1,
      curDate: dayjs().format('YYYY-MM-DD'),
      timeList: [],
      reason: '',
      therapist: { name: '', id: '' },
      consultant: { name: '', id: '' },
      notes: '',
      confirmWithReservation: true,
      doctor_designate_yn: false,
      consult_designate_yn: false,
      showReason: false,
    }));
  }, []);

  // 3. 重置预约项目相关数据（保留门店、客户、预约依据、日期选择）
  const resetProjectRelatedData = useCallback(() => {
    setFormState(prev => ({
      ...prev,
      selectedTimeIndex: -1,
      timeList: [],
      reason: '',
      therapist: { name: '', id: '' },
      consultant: { name: '', id: '' },
      notes: '',
      confirmWithReservation: true,
      doctor_designate_yn: false,
      consult_designate_yn: false,
      showReason: false,
    }));
  }, []);

  // 4. 重置预约日期相关数据（保留门店、客户、预约依据、预约项目）
  const resetDateRelatedData = useCallback(() => {
    setFormState(prev => ({
      ...prev,
      selectedTimeIndex: -1,
      timeList: [],
      reason: '',
      therapist: { name: '', id: '' },
      consultant: { name: '', id: '' },
      notes: '',
      doctor_designate_yn: false,
      consult_designate_yn: false,
      showReason: false,
    }));
  }, []);

  // 更新客户信息（触发重置：除客户外其他清空）
  const updateCustomer = useCallback(
    (customer: CustomerInfo) => {
      setFormState(prev => ({ ...prev, customer }));
      // 客户变更时重置除客户外的所有数据
      resetCustomerRelatedData();
    },
    [resetCustomerRelatedData]
  );

  // 更新门店信息（触发重置：除客户外其他清空）
  const updateStore = useCallback(
    (store: StoreInfo) => {
      setFormState(prev => ({ ...prev, store }));
      // 门店变更时重置相关数据
      resetStoreRelatedData();
    },
    [resetStoreRelatedData]
  );

  // 更新项目信息（触发重置：除门店、客户、预约依据外其他清空）
  const updateProject = useCallback(
    (project: ProjectInfo) => {
      setFormState(prev => ({ ...prev, project }));
      // 项目变更时重置相关数据
      resetProjectRelatedData();
    },
    [resetProjectRelatedData]
  );

  // 更新预约依据（触发重置：除门店、客户外其他清空）
  const updateReserveType = useCallback(
    (reserveType: ReserveType) => {
      setFormState(prev => ({ ...prev, reserveType }));
      // 预约依据变更时重置相关数据
      resetReserveTypeRelatedData();
    },
    [resetReserveTypeRelatedData]
  );

  // 更新当前日期（触发重置：除门店、客户、预约依据、预约项目外其他清空）
  const updateCurDate = useCallback(
    (curDate: string) => {
      setFormState(prev => {
        // 如果切换到今日，强制选中确认预约（参考sy-chain calendarClick逻辑）
        const today = dayjs().format('YYYY-MM-DD');
        const confirmWithReservation =
          curDate === today ? true : prev.confirmWithReservation;

        return {
          ...prev,
          curDate,
          confirmWithReservation,
        };
      });
      // 日期变更时重置相关数据
      resetDateRelatedData();
    },
    [resetDateRelatedData]
  );

  // 更新时间列表
  const updateTimeList = useCallback((timeList: TimeSlot[]) => {
    // 确保传入的是数组
    const safeTimeList = Array.isArray(timeList) ? timeList : [];
    console.log('updateTimeList 接收到数据:', timeList);
    console.log('updateTimeList 处理后数据:', safeTimeList);

    setFormState(prev => ({
      ...prev,
      timeList: safeTimeList,
      selectedTimeIndex: -1, // 重置选中的时间
      reason: '', // 重置超约原因
      showReason: false, // 重置显示超约原因
    }));
  }, []);

  // 选择时间
  const selectTime = useCallback(
    (index: number) => {
      const timeSlot = formState.timeList[index];
      if (!timeSlot) return;

      // 判断是否超约
      const remain_num = Number(timeSlot.remain_num);
      const total_num = Number(timeSlot.total_num);

      const isNoStock =
        isNaN(remain_num) ||
        isNaN(total_num) ||
        (remain_num === 0 && total_num === 0);

      const isOverBooked = remain_num === 0 && total_num > 0;

      if (isNoStock || isOverBooked) {
        // 超约情况，显示超约原因输入
        setFormState(prev => ({
          ...prev,
          selectedTimeIndex: index,
          showReason: true,
        }));
      } else {
        // 未超约
        setFormState(prev => ({
          ...prev,
          selectedTimeIndex: index,
          showReason: false,
          reason: '',
        }));
      }
    },
    [formState.timeList]
  );

  // 更新超约原因
  const updateReason = useCallback((reason: string) => {
    setFormState(prev => ({ ...prev, reason }));
  }, []);

  // 更新治疗师
  const updateTherapist = useCallback((therapist: StaffInfo) => {
    setFormState(prev => ({ ...prev, therapist }));
  }, []);

  // 更新咨询师
  const updateConsultant = useCallback((consultant: StaffInfo) => {
    setFormState(prev => ({ ...prev, consultant }));
  }, []);

  // 更新备注
  const updateNotes = useCallback((notes: string) => {
    setFormState(prev => ({ ...prev, notes }));
  }, []);

  // 切换确认预约状态（仅用于非今日的情况）
  const toggleConfirmWithReservation = useCallback(() => {
    setFormState(prev => ({
      ...prev,
      confirmWithReservation: !prev.confirmWithReservation,
    }));
  }, []);

  // 强制设置确认预约状态为true（用于当天预约）
  const forceConfirmWithReservation = useCallback(() => {
    setFormState(prev => ({
      ...prev,
      confirmWithReservation: true,
    }));
  }, []);

  // 切换治疗师指定状态
  const toggleDoctorDesignate = useCallback(() => {
    setFormState(prev => ({
      ...prev,
      doctor_designate_yn: !prev.doctor_designate_yn,
    }));
  }, []);

  // 切换咨询师指定状态
  const toggleConsultDesignate = useCallback(() => {
    setFormState(prev => ({
      ...prev,
      consult_designate_yn: !prev.consult_designate_yn,
    }));
  }, []);

  // 重置所有数据
  const resetAllData = useCallback(() => {
    setFormState({
      customer: { name: '', gender: '', age: '', phone: '' },
      store: { name: '', id: '' },
      project: {
        title: '',
        product_id: '',
        order_id: '',
        top_order_id: '',
        display_order_id: '',
      },
      reserveType: ReserveType.WITH_ORDER,
      selectedDateIndex: 0,
      selectedTimeIndex: -1,
      curDate: dayjs().format('YYYY-MM-DD'),
      timeList: [],
      reason: '',
      therapist: { name: '', id: '' },
      consultant: { name: '', id: '' },
      notes: '',
      confirmWithReservation: true,
      doctor_designate_yn: false,
      consult_designate_yn: false,
      showReason: false,
    });
  }, []);

  return {
    formState,
    updateCustomer,
    updateStore,
    updateProject,
    updateReserveType,
    updateCurDate,
    updateTimeList,
    selectTime,
    updateReason,
    updateTherapist,
    updateConsultant,
    updateNotes,
    toggleConfirmWithReservation,
    forceConfirmWithReservation,
    toggleDoctorDesignate,
    toggleConsultDesignate,
    resetAllData,
    resetCustomerRelatedData,
    resetStoreRelatedData,
    resetReserveTypeRelatedData,
    resetProjectRelatedData,
    resetDateRelatedData,
  };
};
