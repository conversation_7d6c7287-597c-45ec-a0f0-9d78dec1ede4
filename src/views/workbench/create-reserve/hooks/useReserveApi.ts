import { useState, useCallback } from 'react';
import api, { FetchModule } from '../../../../common/api';
import { getNativeLoginInfo } from '../../../../common/getNativeLoginInfo';
/**
 * 预约相关API调用 Hook
 *
 * @description 已根据 sy-chain 工程真实接口进行改造
 * @version 2.0.0 - 移除 mock 数据，使用真实接口
 */
export const useReserveApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取日历数据
  const fetchCalendarData = useCallback(
    async (params: {
      storeId: string;
      projectId: string;
      reserveType: number;
      startDate: string;
      endDate: string;
      orderId?: string;
      topOrderId?: string;
    }) => {
      setLoading(true);
      setError(null);

      try {
        // 使用真实接口：/chain-wxapp/v1/reserve/calendar
        const requestParams = {
          date_start: params.startDate,
          date_end: params.endDate,
          ...(params.storeId && { tenant_id: params.storeId }),
          order_type: params.reserveType,
          product_id: params.projectId,
          ...(params.orderId && { order_id: params.orderId }),
          ...(params.topOrderId && { top_order_id: params.topOrderId }),
        };

        const response = await api.reactNativeFetch(
          '/chain-wxapp/v1/reserve/calendar',
          requestParams
        );

        if (response.errorCode === 0) {
          return response.responseData;
        } else {
          throw new Error(response.errorMsg || '获取日历数据失败');
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '网络错误';
        setError(errorMessage);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // 获取时间段数据
  const fetchTimeSlots = useCallback(
    async (params: {
      storeId: string;
      projectId: string;
      reserveType: number;
      date: string;
      orderId?: string;
      topOrderId?: string;
    }) => {
      setLoading(true);
      setError(null);

      try {
        // 使用真实接口：/chain-wxapp/v1/reserve/calendarDetail
        const requestParams = {
          date: params.date,
          product_id: params.projectId,
          order_type: params.reserveType,
          ...(params.storeId && { tenant_id: params.storeId }),
          ...(params.orderId && { order_id: params.orderId }),
          ...(params.topOrderId && { top_order_id: params.topOrderId }),
        };

        const response = await api.reactNativeFetch(
          '/chain-wxapp/v1/reserve/calendarDetail',
          requestParams,
          FetchModule.Method.POST // 使用 GET 方法
        );

        if (response.errorCode === 0) {
          // 返回时间段列表
          return response.responseData?.items || [];
        } else {
          throw new Error(response.errorMsg || '获取时间段失败');
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '网络错误';
        setError(errorMessage);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // 创建预约
  const createReservation = useCallback(
    async (params: {
      customerId: string;
      storeId: string;
      projectId: string;
      reserveType: number;
      date: string;
      timeStart: string;
      timeEnd: string;
      orderId?: string;
      topOrderId?: string;
      therapistId?: string;
      consultantId?: string;
      reason?: string;
      notes?: string;
      confirmWithReservation: boolean;
      doctor_designate_yn: boolean;
      consult_designate_yn: boolean;
    }) => {
      setLoading(true);
      setError(null);

      try {
        // 使用新接口：/chain-wxapp/v1/reserve/submitNew
        // 参数类型与sy-chain原工程保持一致，除customer_id改为字符串外
        const trimmedReason = (params.reason ?? '').toString().trim();
        const requestParams = {
          customer_id: String(params.customerId), // 确保为字符串类型防止溢出
          tenant_id: params.storeId,
          order_type: params.reserveType, // 保持原类型，与sy-chain一致
          product_id: params.projectId, // 保持原类型，与sy-chain一致
          date: params.date,
          time_start: params.timeStart,
          time_end: params.timeEnd,
          confirm_intention: params.confirmWithReservation, // 保持布尔类型，与sy-chain一致
          doctor_designate_yn: params.doctor_designate_yn ? 1 : 0,
          consult_designate_yn: params.consult_designate_yn ? 1 : 0,
          // 与 sy-chain 对齐：始终传递 force_reserve_reason（未填写时传空字符串）
          force_reserve_reason: trimmedReason,
          ...(params.orderId && { order_id: params.orderId }), // 保持原类型，与sy-chain一致
          ...(params.topOrderId && { top_order_id: params.topOrderId }), // 保持原类型，与sy-chain一致
          ...(params.therapistId && { doctor_id: params.therapistId }), // 保持原类型，与sy-chain一致
          ...(params.consultantId && { consultant_id: params.consultantId }), // 保持原类型，与sy-chain一致
          ...(params.notes && { remark: params.notes }),
        };

        const response = await api.reactNativeFetch(
          '/chain-wxapp/v1/reserve/submitNew',
          requestParams,
          FetchModule.Method.POST
        );

        // 接口正确返回格式：{errorCode: 0, errorMsg: "ok"}
        if (response.errorCode === 0) {
          return response;
        } else {
          throw new Error(response.errorMsg || '创建预约失败');
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '网络错误';
        setError(errorMessage);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // 获取门店信息列表
  const fetchStoreList = useCallback(
    async (params?: {
      keyword?: string;
      customerId?: string;
      orderType?: number;
      orderId?: string;
      topOrderId?: string;
      productId?: string;
    }) => {
      setLoading(true);
      setError(null);

      try {
        // 使用真实接口：/chain-wxapp/v1/reserve/tenantList
        const requestParams = {
          page: 1,
          size: 100, // 获取足够多的门店
          ...(params?.keyword && { keyword: params.keyword }),
          ...(params?.orderType && { order_type: params.orderType }),
          ...(params?.orderId && { order_id: params.orderId }),
          ...(params?.topOrderId && { top_order_id: params.topOrderId }),
          ...(params?.productId && { product_id: params.productId }),
        };

        const response = await api.reactNativeFetch(
          '/chain-wxapp/v1/reserve/tenantList',
          requestParams,
          FetchModule.Method.POST
        );

        if (response.errorCode === 0) {
          return response.responseData || [];
        } else {
          throw new Error(response.errorMsg || '获取门店信息失败');
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '网络错误';
        setError(errorMessage);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // 获取当前门店信息（根据存储的tenant_id或cross_tenant_id）
  const fetchCurrentStoreInfo = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // 获取存储的门店ID（需要考虑普通和协同两种情况）
      const loginInfo = await getNativeLoginInfo();
      const extInfo =
        loginInfo?.ext_info && typeof loginInfo.ext_info === 'string'
          ? JSON.parse(loginInfo.ext_info || '{}')
          : null;

      // 优先使用协同门店ID，否则使用普通门店ID
      const tenantId = extInfo?.cross_tenant_id || loginInfo?.tenant_id;

      if (!tenantId) {
        console.log('未找到存储的门店ID');
        return null;
      }

      console.log(
        '获取到存储的门店ID:',
        tenantId,
        '类型:',
        extInfo?.cross_tenant_id ? '协同门店' : '普通门店'
      );

      // 使用门店列表接口查询具体门店详情
      const response = await api.pagefetch({
        path: 'chain-wxapp/v1/reserve/tenantList',
        method: FetchModule.Method.GET,
        params: {
          id: tenantId, // 指定门店ID查询
        },
      });

      if (response.errorCode === 0 && response.responseData?.length > 0) {
        // 找到匹配的门店信息
        const storeInfo = response.responseData.find(
          (item: { id?: string | number; name?: string }) =>
            item.id?.toString() === tenantId?.toString()
        );

        if (storeInfo) {
          console.log('门店信息获取成功:', storeInfo.name);
          return storeInfo;
        } else {
          console.log('未找到匹配的门店信息');
          return null;
        }
      } else {
        throw new Error(response.errorMsg || '获取当前门店信息失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '网络错误';
      setError(errorMessage);
      console.error('获取当前门店信息失败:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // 获取预约项目列表
  const fetchReserveProducts = useCallback(
    async (params: {
      customerId: string;
      storeId: string;
      orderType: number;
      keyword?: string;
    }) => {
      setLoading(true);
      setError(null);

      try {
        // 使用真实接口：/chain-wxapp/v1/reserve/getReserveProducts
        const requestParams = {
          customer_id: params.customerId,
          tenant_id: params.storeId,
          order_type: params.orderType,
          page: 1,
          size: 100,
          ...(params.keyword && { keyword: params.keyword }),
        };

        const response = await api.reactNativeFetch(
          '/chain-wxapp/v1/reserve/getReserveProducts',
          requestParams,
          FetchModule.Method.POST
        );

        if (response.errorCode === 0) {
          return response.responseData || [];
        } else {
          throw new Error(response.errorMsg || '获取项目列表失败');
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '网络错误';
        setError(errorMessage);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // 获取医生和咨询师列表
  const fetchDoctorAndConsult = useCallback(
    async (params: {
      storeId: string;
      customerId: string;
      date: string;
      orderType: number;
      startTime?: string;
      endTime?: string;
      orderId?: string;
      topOrderId?: string;
      productId?: string;
      keyword?: string;
    }) => {
      setLoading(true);
      setError(null);

      try {
        // 使用真实接口：/chain-wxapp/v1/reserve/getDoctorAndConsult
        const requestParams = {
          tenant_id: params.storeId,
          customer_id: params.customerId,
          date: params.date,
          order_type: params.orderType,
          page: 1,
          size: 100,
          ...(params.startTime && { start_time: params.startTime }),
          ...(params.endTime && { end_time: params.endTime }),
          ...(params.orderId && { order_id: params.orderId }),
          ...(params.topOrderId && { top_order_id: params.topOrderId }),
          ...(params.productId && { product_id: params.productId }),
          ...(params.keyword && { keyword: params.keyword }),
        };

        const response = await api.reactNativeFetch(
          '/chain-wxapp/v1/reserve/getDoctorAndConsult',
          requestParams,
          FetchModule.Method.POST
        );

        if (response.errorCode === 0) {
          return response.responseData || [];
        } else {
          throw new Error(response.errorMsg || '获取人员列表失败');
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '网络错误';
        setError(errorMessage);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // 验证表单数据
  const validateForm = useCallback((formData: any) => {
    const errors: Array<{ field: string; message: string }> = [];

    if (!formData.customer.name) {
      errors.push({ field: 'customer', message: '请选择客户' });
    }

    if (!formData.store.id) {
      errors.push({ field: 'store', message: '请选择门店' });
    }

    if (!formData.project.title) {
      errors.push({ field: 'project', message: '请选择项目' });
    }

    if (formData.selectedTimeIndex === -1) {
      errors.push({ field: 'time', message: '请选择预约时段' });
    }

    if (formData.showReason && !formData.reason.trim()) {
      errors.push({ field: 'reason', message: '请填写超约原因' });
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }, []);

  return {
    loading,
    error,
    fetchCalendarData,
    fetchTimeSlots,
    createReservation,
    fetchStoreList,
    fetchCurrentStoreInfo,
    fetchReserveProducts,
    fetchDoctorAndConsult,
    validateForm,
  };
};
