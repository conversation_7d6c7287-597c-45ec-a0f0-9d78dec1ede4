import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { BaseModal } from './BaseModal';
import { getRealSize } from '../../../../common/utils';

interface ToastModalProps {
  visible: boolean;
  message: string;
  type?: 'success' | 'error' | 'info';
  duration?: number;
  onClose: () => void;
}

/**
 * Toast 提示组件
 * 替代 uni.showToast()
 */
export const ToastModal: React.FC<ToastModalProps> = ({
  visible,
  message,
  type = 'info',
  duration = 2000,
  onClose,
}) => {
  useEffect(() => {
    if (visible && duration > 0) {
      const timer = setTimeout(() => {
        onClose();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [visible, duration, onClose]);

  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return {
          backgroundColor: '#52c41a',
          color: '#ffffff',
        };
      case 'error':
        return {
          backgroundColor: '#ff4d4f',
          color: '#ffffff',
        };
      case 'info':
      default:
        return {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          color: '#ffffff',
        };
    }
  };

  const typeStyles = getTypeStyles();

  return (
    <BaseModal
      visible={visible}
      onClose={onClose}
      position='center'
      backdropOpacity={0}
      animationInTiming={200}
      animationOutTiming={200}
    >
      <View
        style={[
          styles.container,
          { backgroundColor: typeStyles.backgroundColor },
        ]}
      >
        <Text style={[styles.message, { color: typeStyles.color }]}>
          {message}
        </Text>
      </View>
    </BaseModal>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: getRealSize(40),
    paddingVertical: getRealSize(24),
    borderRadius: getRealSize(8),
    maxWidth: getRealSize(600),
    minWidth: getRealSize(200),
  },
  message: {
    fontSize: getRealSize(28),
    textAlign: 'center',
    lineHeight: getRealSize(40),
  },
});
