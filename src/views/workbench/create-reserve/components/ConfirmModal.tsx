import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { BaseModal } from './BaseModal';
import { getRealSize } from '../../../../common/utils';

interface ConfirmModalProps {
  visible: boolean;
  title?: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
  confirmButtonColor?: string;
}

/**
 * 确认对话框组件
 * 替代 uni.showModal()
 */
export const ConfirmModal: React.FC<ConfirmModalProps> = ({
  visible,
  title,
  message,
  confirmText = '确定',
  cancelText = '取消',
  onConfirm,
  onCancel,
  confirmButtonColor = '#ff6b35',
}) => {
  return (
    <BaseModal visible={visible} onClose={onCancel} position='center'>
      <View style={styles.container}>
        {title && <Text style={styles.title}>{title}</Text>}

        <Text style={styles.message}>{message}</Text>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.cancelButton]}
            onPress={onCancel}
          >
            <Text style={styles.cancelButtonText}>{cancelText}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.button,
              styles.confirmButton,
              { backgroundColor: confirmButtonColor },
            ]}
            onPress={onConfirm}
          >
            <Text style={styles.confirmButtonText}>{confirmText}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </BaseModal>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: getRealSize(40),
    minWidth: getRealSize(280),
  },
  title: {
    fontSize: getRealSize(36),
    fontWeight: '500',
    color: '#333333',
    textAlign: 'center',
    marginBottom: getRealSize(20),
  },
  message: {
    fontSize: getRealSize(28),
    color: '#666666',
    textAlign: 'center',
    lineHeight: getRealSize(40),
    marginBottom: getRealSize(40),
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    flex: 1,
    height: getRealSize(80),
    borderRadius: getRealSize(8),
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
    marginRight: getRealSize(20),
  },
  confirmButton: {
    backgroundColor: '#ff6b35',
  },
  cancelButtonText: {
    fontSize: getRealSize(28),
    color: '#666666',
  },
  confirmButtonText: {
    fontSize: getRealSize(28),
    color: '#ffffff',
    fontWeight: '500',
  },
});
