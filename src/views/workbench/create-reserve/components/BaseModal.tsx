import React from 'react';
import { View, StyleSheet } from 'react-native';
import Modal from 'react-native-modal';
import { getRealSize } from '../../../../common/utils';
import { modalAnimation } from '@/constant/modal_animation';

interface BaseModalProps {
  visible: boolean;
  onClose: () => void;
  children: React.ReactNode;
  position?: 'bottom' | 'center' | 'top';
  backdropOpacity?: number;
  animationInTiming?: number;
  animationOutTiming?: number;
}

/**
 * 基础弹窗组件
 * 统一使用 react-native-modal 实现
 */
export const BaseModal: React.FC<BaseModalProps> = ({
  visible,
  onClose,
  children,
  position = 'bottom',
  backdropOpacity = 0.5,
  animationInTiming = 300,
  animationOutTiming = 300,
}) => {
  const getAnimationIn = () => {
    switch (position) {
      case 'bottom':
        return 'slideInUp';
      case 'top':
        return 'slideInDown';
      case 'center':
      default:
        return 'fadeIn';
    }
  };

  const getAnimationOut = () => {
    switch (position) {
      case 'bottom':
        return 'slideOutDown';
      case 'top':
        return 'slideOutUp';
      case 'center':
      default:
        return 'fadeOut';
    }
  };

  const getJustifyContent = () => {
    switch (position) {
      case 'bottom':
        return 'flex-end';
      case 'top':
        return 'flex-start';
      case 'center':
      default:
        return 'center';
    }
  };

  return (
    <Modal
      isVisible={visible}
      {...modalAnimation}
      onBackdropPress={onClose}
      animationIn={getAnimationIn()}
      animationOut={getAnimationOut()}
      backdropOpacity={backdropOpacity}
      style={[styles.modal, { justifyContent: getJustifyContent() }]}
    >
      <View
        style={[
          styles.container,
          position === 'bottom' && styles.bottomContainer,
          position === 'center' && styles.centerContainer,
          position === 'top' && styles.topContainer,
        ]}
      >
        {children}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    margin: 0,
  },
  container: {
    backgroundColor: '#ffffff',
    maxHeight: '80%',
  },
  bottomContainer: {
    borderTopLeftRadius: getRealSize(20),
    borderTopRightRadius: getRealSize(20),
  },
  centerContainer: {
    borderRadius: getRealSize(12),
    marginHorizontal: getRealSize(30),
  },
  topContainer: {
    borderBottomLeftRadius: getRealSize(20),
    borderBottomRightRadius: getRealSize(20),
  },
});
