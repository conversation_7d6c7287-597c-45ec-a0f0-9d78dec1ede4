import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableWithoutFeedback,
  TextInput,
  Image,
  DeviceEventEmitter,
  AppState,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import Header from '@/components/header';
import dayjs from 'dayjs';
import { go } from '@soyoung/react-native-base';
import jsApi from '@soyoung/react-native-jsapi';
import { useFormState } from './hooks/useFormState';
import { useReserveApi } from './hooks/useReserveApi';
import { switchTab } from '@/common/jumpPage';
import { styles } from './styles';
import { getRealSize } from '../../../common/utils';
import { TimeSlot, ReserveType, CalendarData } from './types';

interface CreateReserveTargetProps {
  pageShowFlag?: boolean;
  navigation?: any;
  route?: any;
  [key: string]: any;
}

/**
 * 新建预约页面组件
 *
 * @description 完整的预约创建流程页面，支持客户选择、门店选择、项目选择、
 *              时间选择、人员指定等完整业务流程
 *
 * @features
 * - 支持有单/无单两种预约模式
 * - 集成日历和时间段选择
 * - 支持治疗师/咨询师指定
 * - 完整的表单验证和错误处理
 * - 超约原因填写和处理
 * - 预约创建和确认功能
 *
 * <AUTHOR> sy-chain uni-app 项目
 * @version 1.0.0
 * @since 2024-01
 */
const CreateReserveTarget: React.FC<CreateReserveTargetProps> = ({
  pageShowFlag: _pageShowFlag,
  navigation: _navigation,
  route: _route,
  ..._props
}) => {
  const insets = useSafeAreaInsets();
  const keyboardVerticalOffset = insets.top + 44; // Header高度约44

  const {
    formState,
    updateCustomer,
    updateStore,
    updateProject,
    updateReserveType,
    updateCurDate,
    updateTimeList,
    selectTime,
    updateReason,
    updateTherapist,
    updateConsultant,
    updateNotes,
    toggleConfirmWithReservation,
    forceConfirmWithReservation,
    toggleDoctorDesignate,
    toggleConsultDesignate,
    resetAllData,
  } = useFormState();

  const {
    loading,
    // error: _error,
    fetchCalendarData,
    fetchTimeSlots,
    createReservation,
    fetchCurrentStoreInfo,
    // fetchStoreList: _fetchStoreList,
    // fetchReserveProducts: _fetchReserveProducts,
    // fetchDoctorAndConsult: _fetchDoctorAndConsult,
    validateForm,
  } = useReserveApi();

  // 日历相关状态
  const [calendarData, setCalendarData] = useState<CalendarData>({
    planList: [],
    cashbackList: [],
    reserveCalendarNotAvailableDates: [],
    startDate: dayjs().format('YYYY-MM-DD'),
    endDate: dayjs().add(6, 'day').format('YYYY-MM-DD'),
  });

  // 日历ScrollView的ref
  const dateScrollViewRef = useRef<ScrollView>(null);
  // 表单ScrollView的ref
  const formScrollRef = useRef<ScrollView>(null);
  // 备注输入框的ref
  const notesInputRef = useRef<TextInput>(null);
  // 超约原因输入框的ref
  const reasonInputRef = useRef<TextInput>(null);

  // 始终持有最新表单状态，避免事件回调中读取到过期值
  const latestFormRef = useRef(formState);
  useEffect(() => {
    latestFormRef.current = formState;
  }, [formState]);

  // 键盘状态监听（修复iOS输入时遮挡和空白问题）
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  useEffect(() => {
    const showEvent =
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow';
    const hideEvent =
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide';

    const showSub = Keyboard.addListener(showEvent as any, e => {
      const height = e?.endCoordinates?.height || 0;
      console.log('键盘高度:', height, 'Platform:', Platform.OS);
      console.log(
        '计算的padding:',
        Platform.OS === 'ios' ? height : Math.max(height * 0.4, 120)
      );
      setKeyboardVisible(true);
      setKeyboardHeight(height);
    });
    const hideSub = Keyboard.addListener(hideEvent as any, () => {
      setKeyboardVisible(false);
      setKeyboardHeight(0);
    });

    return () => {
      showSub?.remove?.();
      hideSub?.remove?.();
    };
  }, []);

  // 输入框聚焦时精确滚动定位，确保输入区域不被键盘遮挡
  const scrollToInputField = useCallback(
    (inputRef: React.RefObject<TextInput>) => {
      setTimeout(() => {
        // 先尝试让输入框获得焦点
        inputRef.current?.focus();

        // 使用更精确的滚动定位方案
        setTimeout(
          () => {
            if (inputRef.current && formScrollRef.current) {
              // 测量输入框相对于ScrollView的位置
              inputRef.current.measureLayout(
                formScrollRef.current.getScrollableNode(),
                (x, y, width, height) => {
                  // 计算合适的滚动位置
                  const inputBottom = y + height;
                  const viewportHeight =
                    Platform.OS === 'ios'
                      ? keyboardVisible
                        ? // iOS: 屏幕高度 - 键盘高度 - 安全区域 - header高度
                          Platform.select({ ios: 812, default: 667 }) -
                          keyboardHeight -
                          insets.top -
                          44
                        : 667
                      : 400;

                  // 确保输入框底部在可视区域内，留出缓冲空间（避免被footer遮挡）
                  const bufferSpace = getRealSize(150); // 增加缓冲空间，考虑footer高度和额外间距
                  const targetScrollY = Math.max(
                    0,
                    inputBottom - viewportHeight + bufferSpace
                  );

                  console.log('Smart scroll calculation:', {
                    inputY: y,
                    inputHeight: height,
                    inputBottom,
                    viewportHeight,
                    keyboardHeight,
                    targetScrollY,
                    deviceHeight: Platform.select({ ios: 812, default: 667 }),
                  });

                  formScrollRef.current?.scrollTo({
                    y: targetScrollY,
                    animated: true,
                  });
                },
                () => {
                  // 如果measureLayout失败，回退到原来的方案
                  console.log('measureLayout failed, fallback to scrollToEnd');
                  formScrollRef.current?.scrollToEnd({ animated: true });
                }
              );
            }
          },
          Platform.OS === 'ios' ? 150 : 200
        ); // iOS更快响应，Android稍慢
      }, 50);
    },
    [keyboardVisible, keyboardHeight, insets.top]
  );

  /**
   * 滚动到选中的日期
   */
  const scrollToSelectedDate = useCallback(
    (selectedDate: string) => {
      const startDate = dayjs(calendarData.startDate);
      const targetDate = dayjs(selectedDate);

      // 计算目标日期是第几个
      const daysDiff = targetDate.diff(startDate, 'day');

      if (daysDiff >= 0) {
        // 每个日期项的宽度：58px(dateItem) + 10px(marginRight) = 68px
        const itemWidth = getRealSize(68);
        const scrollX = daysDiff * itemWidth;

        // 滚动到目标位置，稍微向前一点以便看到选中的日期
        const targetScrollX = Math.max(0, scrollX - getRealSize(100));

        // 延迟执行，确保ScrollView已经渲染完成
        setTimeout(() => {
          dateScrollViewRef.current?.scrollTo({
            x: targetScrollX,
            animated: true,
          });
        }, 100);
      }
    },
    [calendarData.startDate]
  );

  /**
   * 标准化时间段数据
   */
  const normalizeTimeSlots = useCallback((timeSlots: any[]): TimeSlot[] => {
    console.log('normalizeTimeSlots 开始处理数据:', timeSlots);

    if (!timeSlots || !Array.isArray(timeSlots)) {
      console.warn('时间段数据不是数组:', timeSlots);
      return [];
    }

    const validSlots = timeSlots
      .filter(slot => {
        const isValid =
          slot &&
          typeof slot === 'object' &&
          typeof slot.from === 'string' &&
          typeof slot.to === 'string' &&
          typeof slot.total_num !== 'undefined' &&
          typeof slot.remain_num !== 'undefined' &&
          typeof slot.reserved_num !== 'undefined';

        if (!isValid) {
          console.warn('无效的时间段数据:', slot);
        }
        return isValid;
      })
      .map(slot => {
        const normalizedSlot = {
          ...slot,
          // 确保数字字段是数字类型
          total_num: Number(slot.total_num) || 0,
          remain_num: Number(slot.remain_num) || 0,
          reserved_num: Number(slot.reserved_num) || 0,
          // 确保cashback是字符串
          cashback: String(slot.cashback || ''),
          // 确保from和to是字符串
          from: String(slot.from || ''),
          to: String(slot.to || ''),
        };

        console.log('标准化后的时间段:', normalizedSlot);
        return normalizedSlot;
      });

    console.log('normalizeTimeSlots 处理完成，返回:', validSlots);
    return validSlots;
  }, []);

  // const dateSelectRef = useRef<any>(null);

  /**
   * 处理页面选择结果的函数
   */
  const handlePageSelectResult = useCallback(
    (eventData: any) => {
      try {
        const { data } = eventData;

        // 处理客户选择
        if (data.customer) {
          const customerInfo = {
            id: data.customer.id ? String(data.customer.id) : '',
            customer_id_str:
              data.customer.customer_id_str ||
              (data.customer.id ? String(data.customer.id) : ''),
            // 关键修复：将customer_id_str的值保存为customer_id字段
            customer_id:
              data.customer.customer_id_str ||
              (data.customer.id ? String(data.customer.id) : ''),
            name: data.customer.name || '',
            gender: data.customer.gender || '',
            age: data.customer.age || '',
            phone: data.customer.masked_mobile || data.customer.phone || '',
          };
          console.log('💾 客户数据保存:', customerInfo);
          updateCustomer(customerInfo);
          // 清空日期组件的优惠角标（保留日期范围）
          clearCalendarCashback();
        }

        // 处理门店选择
        if (data.store) {
          const storeInfo = {
            id: data.store.id ? String(data.store.id) : '',
            name: data.store.name || '',
          };
          updateStore(storeInfo);
          // 清空日期组件的优惠角标（保留日期范围）
          clearCalendarCashback();

          // 门店变更时，如果已选择项目，需要重新获取时间段数据
          if (formState.project.product_id) {
            const delay = Platform.OS === 'ios' ? 50 : 100;
            setTimeout(async () => {
              try {
                const timeSlots = await fetchTimeSlots({
                  storeId: storeInfo.id,
                  projectId: formState.project.product_id,
                  reserveType: formState.reserveType,
                  date: formState.curDate,
                  orderId: formState.project.order_id,
                  topOrderId: formState.project.top_order_id,
                });

                console.log('门店选择后API返回数据:', timeSlots);

                // 使用统一的时间段数据标准化函数
                const validTimeSlots = normalizeTimeSlots(timeSlots);
                updateTimeList(validTimeSlots);
                console.log('门店选择后标准化的时间段数据:', validTimeSlots);
              } catch (err) {
                console.error('门店变更后获取时间段失败:', err);
                updateTimeList([]);
              }
            }, delay);
          }
        }

        // 处理项目选择
        if (data.project) {
          const projectInfo = {
            title: data.project.name || '',
            product_id: data.project.id ? String(data.project.id) : '',
            order_id: data.project.order_id || '',
            top_order_id: data.project.top_order_id || '',
            display_order_id: data.project.display_order_id || '',
          };
          updateProject(projectInfo);
          // 清空日期组件的优惠角标（保留日期范围）
          clearCalendarCashback();
          console.log('iOS项目选择: 已清空cashback，准备获取新数据');

          // 项目选择后，立即获取日历数据和当前日期的时间段数据
          // iOS可能需要更短的延迟来确保状态更新完成
          const delay = Platform.OS === 'ios' ? 50 : 100;
          setTimeout(async () => {
            console.log('iOS项目选择: 开始异步获取日历数据, delay:', delay);
            // 基于当前选中日期计算正确的日期范围（与checkCalendarDate逻辑一致）
            const latestForm = latestFormRef.current;
            const selectedDate = latestForm.curDate;
            const today = dayjs().format('YYYY-MM-DD');

            let newStartDate;
            const newEndDate = dayjs(selectedDate)
              .add(6, 'day')
              .format('YYYY-MM-DD');

            if (dayjs(selectedDate).isBefore(dayjs(today))) {
              // 如果是过去的日期，使用今天
              newStartDate = today;
            } else {
              // 如果是未来的日期，用选中日期-6天
              const dateMinus6 = dayjs(selectedDate)
                .subtract(6, 'day')
                .format('YYYY-MM-DD');
              // 如果减去6天后早于今天，则使用今天
              newStartDate = dayjs(dateMinus6).isBefore(dayjs(today))
                ? today
                : dateMinus6;
            }

            // 1. 先获取日历数据，使用正确的日期范围
            const calendarParams = {
              storeId: latestForm.store.id,
              projectId: projectInfo.product_id,
              reserveType: latestForm.reserveType,
              startDate: newStartDate,
              endDate: newEndDate,
              orderId: projectInfo.order_id,
              topOrderId: projectInfo.top_order_id,
            };

            console.log('项目切换API参数:', {
              selectedDate: formState.curDate,
              apiDateRange: { newStartDate, newEndDate },
              uiDateRange: {
                startDate: calendarData.startDate,
                endDate: calendarData.endDate,
              },
              note: 'API使用计算的日期范围，UI保持用户选择的范围',
            });

            try {
              const calendarResult = await fetchCalendarData(calendarParams);

              if (calendarResult && Array.isArray(calendarResult)) {
                // 处理不可预约日期
                const notAvailableDates = calendarResult
                  .filter(item => {
                    const remain_num = Number(item.remain_num);
                    const total_num = Number(item.total_num);
                    if (
                      isNaN(remain_num) ||
                      isNaN(total_num) ||
                      (remain_num === 0 && total_num === 0)
                    ) {
                      return true;
                    }
                    if (
                      (remain_num === 0 && total_num > 0) ||
                      (remain_num > total_num && total_num !== 0)
                    ) {
                      return true;
                    }
                    return false;
                  })
                  .map(item => item.date);

                // 处理cashback信息
                const cashbackList = calendarResult
                  .filter(item => {
                    return item.cashback && item.date;
                  })
                  .map(item => ({
                    date: item.date,
                    cashback: item.cashback,
                  }));

                setCalendarData(prev => ({
                  ...prev,
                  // 不更新startDate和endDate，保持用户当前的日期范围
                  planList: calendarResult || [],
                  cashbackList: cashbackList,
                  reserveCalendarNotAvailableDates: notAvailableDates,
                }));

                console.log('iOS项目切换后更新日历数据:', {
                  planList: calendarResult.length,
                  cashbackList: cashbackList.length,
                  cashbackDetails: cashbackList,
                  currentDate: latestForm.curDate,
                  preservedUIRange: {
                    startDate: calendarData.startDate,
                    endDate: calendarData.endDate,
                  },
                });
              }
            } catch (err) {
              console.error('获取日历数据失败:', err);
            }

            // 2. 获取当前选中日期的时间段数据，保持用户的日期选择
            try {
              const timeSlots = await fetchTimeSlots({
                storeId: latestForm.store.id,
                projectId: projectInfo.product_id,
                reserveType: latestForm.reserveType,
                date: latestForm.curDate,
                orderId: projectInfo.order_id,
                topOrderId: projectInfo.top_order_id,
              });

              console.log('项目选择后API返回数据:', timeSlots);

              // 使用统一的时间段数据标准化函数
              const validTimeSlots = normalizeTimeSlots(timeSlots);
              updateTimeList(validTimeSlots);
              console.log('项目选择后标准化的时间段数据:', validTimeSlots);
            } catch (err) {
              console.error('获取时间段失败:', err);
              updateTimeList([]);
            }
          }, delay);
        }

        // 处理员工选择
        if (data.employee) {
          const staffInfo = {
            id: data.employee.id ? String(data.employee.id) : '',
            name: data.employee.name || '',
          };
          updateTherapist(staffInfo);
        }

        // 处理咨询师选择
        if (data.consultant) {
          const consultantInfo = {
            id: data.consultant.id ? String(data.consultant.id) : '',
            name: data.consultant.name || '',
          };
          updateConsultant(consultantInfo);
        }

        // 处理治疗师选择
        if (data.therapist) {
          const therapistInfo = {
            id: data.therapist.id ? String(data.therapist.id) : '',
            name: data.therapist.name || '',
          };
          updateTherapist(therapistInfo);
        }
      } catch (err) {
        console.error('处理选择结果事件失败:', err);
      }
    },
    [
      updateCustomer,
      updateStore,
      updateProject,
      updateTherapist,
      updateConsultant,
      normalizeTimeSlots,
      fetchTimeSlots,
      updateTimeList,
    ]
  );

  // 监听页面选择结果事件
  useEffect(() => {
    const subscription = DeviceEventEmitter.addListener(
      'page_select_result',
      handlePageSelectResult
    );

    return () => {
      subscription.remove();
    };
  }, [handlePageSelectResult]);

  // 检查日历返回的日期数据
  const checkCalendarDate = useCallback(async () => {
    try {
      const reserveCalendarDate = await AsyncStorage.getItem(
        'reserveCalendarDate'
      );
      if (reserveCalendarDate) {
        console.log('预约日历返回的日期', reserveCalendarDate);

        // 更新选中的日期
        updateCurDate(reserveCalendarDate);

        // 更新日历数据的日期范围
        const selectedDate = reserveCalendarDate;
        const today = dayjs().format('YYYY-MM-DD');

        // 检查是否为当天，如果是当天则必须同时确认预约（参考sy-chain calendarClick逻辑）
        if (selectedDate === today) {
          forceConfirmWithReservation();
        }

        let newStartDate;
        const newEndDate = dayjs(selectedDate)
          .add(6, 'day')
          .format('YYYY-MM-DD');

        if (dayjs(selectedDate).isBefore(dayjs(today))) {
          // 如果是过去的日期，使用今天
          newStartDate = today;
        } else {
          // 如果是未来的日期，用选中日期-6天
          const dateMinus6 = dayjs(selectedDate)
            .subtract(6, 'day')
            .format('YYYY-MM-DD');
          // 如果减去6天后早于今天，则使用今天
          newStartDate = dayjs(dateMinus6).isBefore(dayjs(today))
            ? today
            : dateMinus6;
        }

        // 重新获取日历数据和时间段数据（参考sy-chain onShow逻辑）
        if (formState.store.id && formState.project.product_id) {
          const params = {
            storeId: formState.store.id,
            projectId: formState.project.product_id,
            reserveType: formState.reserveType,
            startDate: newStartDate,
            endDate: newEndDate,
            orderId: formState.project.order_id,
            topOrderId: formState.project.top_order_id,
          };

          // 先获取并更新日历数据
          const calendarResult = await fetchCalendarData(params);

          if (calendarResult && Array.isArray(calendarResult)) {
            // 处理不可预约日期
            const notAvailableDates = calendarResult
              .filter(item => {
                const remain_num = Number(item.remain_num);
                const total_num = Number(item.total_num);
                if (
                  isNaN(remain_num) ||
                  isNaN(total_num) ||
                  (remain_num === 0 && total_num === 0)
                ) {
                  return true;
                }
                if (
                  (remain_num === 0 && total_num > 0) ||
                  (remain_num > total_num && total_num !== 0)
                ) {
                  return true;
                }
                return false;
              })
              .map(item => item.date);

            // 处理cashback信息
            const cashbackList = calendarResult
              .filter(item => {
                return item.cashback && item.date;
              })
              .map(item => ({
                date: item.date,
                cashback: item.cashback,
              }));

            setCalendarData(prev => ({
              ...prev,
              startDate: newStartDate,
              endDate: newEndDate,
              planList: calendarResult || [],
              cashbackList: cashbackList,
              reserveCalendarNotAvailableDates: notAvailableDates,
            }));

            console.log('日历返回cashback数据:', cashbackList);
          }

          // 然后获取选中日期的时间段数据
          setTimeout(async () => {
            try {
              const timeSlots = await fetchTimeSlots({
                storeId: formState.store.id,
                projectId: formState.project.product_id,
                reserveType: formState.reserveType,
                date: selectedDate,
                orderId: formState.project.order_id,
                topOrderId: formState.project.top_order_id,
              });

              console.log('日历选择后API返回数据:', timeSlots);

              // 使用统一的时间段数据标准化函数
              const validTimeSlots = normalizeTimeSlots(timeSlots);
              updateTimeList(validTimeSlots);
              console.log('日历选择后标准化的时间段数据:', validTimeSlots);
            } catch (err) {
              console.error('日历选择后获取时间段失败:', err);
              updateTimeList([]);
            }
          }, 100);

          // 等待一段时间后滚动到选中的日期，使用新的startDate
          setTimeout(() => {
            const startDate = dayjs(newStartDate);
            const targetDate = dayjs(selectedDate);
            const daysDiff = targetDate.diff(startDate, 'day');

            if (daysDiff >= 0) {
              const itemWidth = getRealSize(68);
              const scrollX = daysDiff * itemWidth;
              const targetScrollX = Math.max(0, scrollX - getRealSize(100));

              dateScrollViewRef.current?.scrollTo({
                x: targetScrollX,
                animated: true,
              });

              console.log('日历滚动:', {
                selectedDate,
                newStartDate,
                daysDiff,
                scrollX,
                targetScrollX,
              });
            }
          }, 500);
        }

        // 清理存储的数据
        await AsyncStorage.removeItem('reserveCalendarDate');
      }
    } catch (err) {
      console.error('处理日历返回数据失败:', err);
    }
  }, [
    updateCurDate,
    forceConfirmWithReservation,
    fetchCalendarData,
    fetchTimeSlots,
    normalizeTimeSlots,
    updateTimeList,
    scrollToSelectedDate,
    formState.store.id,
    formState.project.product_id,
    formState.reserveType,
    formState.project.order_id,
    formState.project.top_order_id,
  ]);

  // 监听应用状态变化，检查日历返回数据
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'active') {
        // 应用变为活跃状态时检查日历返回数据
        checkCalendarDate();
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange
    );

    // 组件挂载时也检查一次
    checkCalendarDate();

    return () => {
      subscription?.remove();
    };
  }, [checkCalendarDate]);

  // iOS 场景修复：页面重新聚焦时也检查日历返回数据（Android 依赖 AppState，iOS 返回 RN 页面不一定触发 active）
  useFocusEffect(
    useCallback(() => {
      console.log('useFocusEffect: 触发检查预约日历返回日期');
      checkCalendarDateWithRetry();
    }, [checkCalendarDate])
  );

  // 额外兜底：当壳组件上报页面显示(pageShowFlag)为true时，延迟检查一次（适配iOS返回未触发focus的情况）
  useEffect(() => {
    if (_pageShowFlag) {
      console.log('pageShowFlag为true，延迟检查预约日历返回日期');
      const timer = setTimeout(() => {
        checkCalendarDateWithRetry();
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [_pageShowFlag, checkCalendarDate]);

  /**
   * 解决iOS偶发未读取到 AsyncStorage 的竞态问题：
   * 在页面聚焦/显示后的短窗口内进行多次重试读取，直到读到为止或达到最大次数
   */
  const checkCalendarDateWithRetry = useCallback(
    (attempt: number = 0) => {
      const maxAttempts = 10; // 最多重试10次
      const intervalMs = 150; // 每次间隔150ms

      AsyncStorage.getItem('reserveCalendarDate')
        .then(value => {
          if (value) {
            if (__DEV__) {
              console.log(
                `checkCalendarDateWithRetry: 第${attempt + 1}次读取成功 ->`,
                value
              );
            }
            // 读到了就走一次完整处理流程
            checkCalendarDate();
          } else if (attempt < maxAttempts - 1) {
            if (__DEV__) {
              console.log(
                `checkCalendarDateWithRetry: 第${attempt + 1}次未读取到，准备重试`
              );
            }
            setTimeout(
              () => checkCalendarDateWithRetry(attempt + 1),
              intervalMs
            );
          } else {
            if (__DEV__) {
              console.log(
                `checkCalendarDateWithRetry: 重试${maxAttempts}次后仍无返回日期，结束重试`
              );
            }
          }
        })
        .catch(err => {
          console.warn('checkCalendarDateWithRetry: 读取失败', err);
        });
    },
    [checkCalendarDate]
  );

  /**
   * 页面初始化
   */
  useEffect(() => {
    // 初始化获取当前门店信息
    initStoreInfo();
  }, []);

  /**
   * 清空日期组件中的优惠信息（cashback），保留现有的日期范围与计划数据
   * 用于表单切换后的UI即时反馈：移除“最高返多少”等角标
   */
  const clearCalendarCashback = useCallback(() => {
    setCalendarData(prev => ({
      ...prev,
      cashbackList: [],
    }));
  }, []);

  // 清理函数 - 组件卸载时清理AsyncStorage
  useEffect(() => {
    return () => {
      AsyncStorage.removeItem('reserveCalendarDate');
    };
  }, []);

  /**
   * 初始化门店信息
   */
  const initStoreInfo = async () => {
    try {
      const storeInfo = await fetchCurrentStoreInfo();
      if (storeInfo && storeInfo.name && storeInfo.id) {
        updateStore({
          name: storeInfo.name,
          id: storeInfo.id,
        });
      }
    } catch (err) {
      console.error('获取门店信息失败:', err);
    }
  };

  /**
   * 获取日历数据
   */
  // 获取日历数据（供项目/日期等变化后调用）
  const handleFetchCalendarData = async () => {
    if (!formState.store.id || !formState.project.product_id) {
      return;
    }

    try {
      const calendarResult = await fetchCalendarData({
        storeId: formState.store.id,
        projectId: formState.project.product_id,
        reserveType: formState.reserveType,
        startDate: calendarData.startDate,
        endDate: calendarData.endDate,
        orderId: formState.project.order_id,
        topOrderId: formState.project.top_order_id,
      });

      if (calendarResult && Array.isArray(calendarResult)) {
        // 处理不可预约日期（参考 sy-chain 的逻辑）
        const notAvailableDates = calendarResult
          .filter(item => {
            const remain_num = Number(item.remain_num);
            const total_num = Number(item.total_num);
            // 尚未开始或无可预约则不显示
            if (
              isNaN(remain_num) ||
              isNaN(total_num) ||
              (remain_num === 0 && total_num === 0)
            ) {
              return true;
            }
            // 剩余为0且总数>0或剩余>总数均视为不可预约
            if (
              (remain_num === 0 && total_num > 0) ||
              (remain_num > total_num && total_num !== 0)
            ) {
              return true;
            }
            return false;
          })
          .map(item => item.date);

        // 处理cashback信息
        const cashbackList = calendarResult
          .filter(item => {
            return item.cashback && item.date;
          })
          .map(item => ({
            date: item.date,
            cashback: item.cashback,
          }));

        setCalendarData(prev => ({
          ...prev,
          planList: calendarResult || [],
          cashbackList: cashbackList,
          reserveCalendarNotAvailableDates: notAvailableDates,
        }));
      }
    } catch (err) {
      console.error('获取日历数据失败:', err);
    }
  };

  /**
   * 获取时间段数据
   */
  const handleFetchTimeSlots = async (date: string) => {
    if (!formState.store.id || !formState.project.product_id) {
      return;
    }

    try {
      const timeSlots = await fetchTimeSlots({
        storeId: formState.store.id,
        projectId: formState.project.product_id,
        reserveType: formState.reserveType,
        date,
        orderId: formState.project.order_id,
        topOrderId: formState.project.top_order_id,
      });

      console.log('handleFetchTimeSlots API返回数据:', timeSlots);

      // 使用统一的时间段数据标准化函数
      const validTimeSlots = normalizeTimeSlots(timeSlots);
      updateTimeList(validTimeSlots);
      console.log('handleFetchTimeSlots标准化的时间段数据:', validTimeSlots);
    } catch (err) {
      console.error('获取时间段失败:', err);
      updateTimeList([]);
    }
  };

  /**
   * 选择客户
   */
  const handleSelectCustomer = () => {
    // 跳转到客户选择页面
    const selectedId = formState.customer.id || '';

    go(
      `app.soyoung://rn/staff/page-select?type=customer&selectedId=${selectedId}`
    );
  };

  /**
   * 选择门店
   */
  const handleSelectStore = () => {
    // 跳转到门店选择页面
    const selectedId = formState.store.id || '';

    go(
      `app.soyoung://rn/staff/page-select?type=store&selectedId=${selectedId}`
    );
  };

  /**
   * 选择项目
   */
  const handleSelectProject = () => {
    if (!formState.customer.name) {
      jsApi.toNative('showToast', { toast: '请先选择客户' });
      return;
    }

    if (!formState.store.id) {
      jsApi.toNative('showToast', { toast: '请先选择门店' });
      return;
    }

    // 跳转到项目选择页面
    const selectedId = formState.project.display_order_id || '';
    const storeId = formState.store.id || '';
    const customerId =
      formState.customer.customer_id_str || formState.customer.id || '';
    const reserveType = formState.reserveType || '';

    go(
      `app.soyoung://rn/staff/page-select?type=project&selectedId=${selectedId}&storeId=${storeId}&customerId=${customerId}&reserveType=${reserveType}`
    );
  };

  /**
   * 处理预约依据变更
   */
  const handleReserveTypeChange = (type: ReserveType) => {
    if (type !== formState.reserveType) {
      updateReserveType(type);
      // UI即时反馈：清空日期组件的优惠角标，但保留当前UI的日期范围
      clearCalendarCashback();
      // 不在此时立即拉取日历数据，避免使用旧的 project/store 参数导致现金返还角标又被写回
      // 等用户重新选择项目后再拉取对应的日历与时段数据
    }
  };

  /**
   * 日历点击事件
   */
  const handleCalendarClick = (date: string) => {
    updateCurDate(date);
    handleFetchTimeSlots(date);

    // 滚动到选中的日期（延迟执行，确保状态更新完成）
    setTimeout(() => {
      scrollToSelectedDate(date);
    }, 50);
  };

  /**
   * 显示日历
   */
  const handleShowCalendar = () => {
    // 检查是否已选择客户和门店和项目
    if (!formState.customer.name) {
      jsApi.toNative('showToast', { toast: '请选择客户' });
      return;
    }
    if (!formState.store.name) {
      jsApi.toNative('showToast', { toast: '请选择门店' });
      return;
    }
    if (
      (formState.reserveType === 4 && !formState.project.order_id) ||
      (formState.reserveType === 3 && !formState.project.product_id)
    ) {
      jsApi.toNative('showToast', { toast: '请选择项目' });
      return;
    }

    // 构建跳转URL，参考原工程sy-chain的完整参数
    let url = `followup/calendar?calendar_type=reserve&curDate=${formState.curDate}`;

    // 添加预约需要的参数
    url += `&tenant_id=${formState.store.id}`;
    url += `&customer_id=${String(formState.customer.id)}`; // 确保为字符串类型
    url += `&order_type=${formState.reserveType}`;
    url += `&order_id=${formState.project.order_id || ''}`;
    url += `&top_order_id=${formState.project.top_order_id || ''}`;
    url += `&product_id=${formState.project.product_id || ''}`;
    url += `&reserve_id=${formState.project.reserve_id || ''}`; // 补充缺失的reserve_id参数
    url += `&date_start=${dayjs().format('YYYY-MM-DD')}`; // 补充缺失的起始日期
    url += `&date_end=${dayjs().add(100, 'day').format('YYYY-MM-DD')}`; // 补充缺失的结束日期

    // 使用go函数跳转到日历页面
    go(`app.soyoung://rn/${url}`);
  };

  /**
   * 处理同时确认预约点击（参考sy-chain handleClickConfirmWithReservation）
   */
  const handleToggleConfirmWithReservation = () => {
    const today = dayjs().format('YYYY-MM-DD');

    // 如果是当天，必选（直接参考sy-chain逻辑）
    if (formState.curDate === today) {
      forceConfirmWithReservation();
      jsApi.toNative('showToast', { toast: '预约当天必须同时确认' });
      return;
    }

    // 非当天可以正常切换
    toggleConfirmWithReservation();
  };

  /**
   * 选择治疗师/咨询师
   */
  const handleSelectConsultant = (type: 'therapist' | 'consultant') => {
    // 校验客户是否已选择（检查customer_id字段）
    if (!formState.customer.customer_id) {
      jsApi.toNative('showToast', { toast: '请选择客户' });
      return;
    }

    // 校验门店是否已选择
    if (!formState.store.id) {
      jsApi.toNative('showToast', { toast: '请选择门店' });
      return;
    }

    // 校验项目是否已选择，区分有单/无单
    if (
      (formState.reserveType === 4 && !formState.project.order_id) ||
      (formState.reserveType === 3 && !formState.project.product_id)
    ) {
      jsApi.toNative('showToast', { toast: '请选择项目' });
      return;
    }

    // 构建跳转参数，包含必要的查询条件
    const params = new URLSearchParams();
    params.append('type', type);
    params.append('storeId', formState.store.id);
    params.append(
      'customerId',
      String(formState.customer.customer_id || formState.customer.id)
    ); // 确保为字符串类型
    params.append('orderType', String(formState.reserveType));
    params.append('date', formState.curDate);

    // 传递当前选中的治疗师或咨询师ID（如果存在）
    if (type === 'consultant' && formState.consultant.id) {
      params.append('selectedId', formState.consultant.id);
    } else if (type === 'therapist' && formState.therapist.id) {
      params.append('selectedId', formState.therapist.id);
    }

    // 传递项目相关参数（字段必须存在，空则传空字符串）
    params.append('productId', formState.project.product_id || '');
    params.append('orderId', formState.project.order_id || '');
    params.append('topOrderId', formState.project.top_order_id || '');

    // 传递时间段信息（字段必须存在，若未选择则传空字符串）
    const selectedSlot =
      formState.selectedTimeIndex >= 0
        ? formState.timeList[formState.selectedTimeIndex]
        : undefined;
    params.append('startTime', selectedSlot?.from || '');
    params.append('endTime', selectedSlot?.to || '');

    go(`app.soyoung://rn/staff/page-select?${params.toString()}`);
  };

  /**
   * 判断时间段是否没有库存
   */
  const isTimeSlotNoStock = (item: TimeSlot): boolean => {
    const remain_num = Number(item.remain_num);
    const total_num = Number(item.total_num);

    return (
      isNaN(remain_num) ||
      isNaN(total_num) ||
      (remain_num === 0 && total_num === 0)
    );
  };

  /**
   * 判断时间段是否置灰
   */
  const isTimeSlotDisabled = (timeSlot: TimeSlot): boolean => {
    // 仅在"无库存（remain=0 且 total=0）或数据无效"时置灰
    // 剩余为 0 但 total > 0 表示允许超约，不应置灰
    return isTimeSlotNoStock(timeSlot);
  };

  /**
   * 判断时间段是否超约（样式置灰但可选择）
   */
  const isTimeSlotOverbooked = (timeSlot: TimeSlot): boolean => {
    const remain_num = Number(timeSlot.remain_num);
    const total_num = Number(timeSlot.total_num);

    // 超约：剩余为0但总数>0
    return remain_num === 0 && total_num > 0;
  };

  /**
   * 选择时间的处理函数
   */
  const handleSelectTime = (index: number) => {
    const timeSlot = formState.timeList[index];

    // 选择时间
    selectTime(index);

    // 判断是否超约
    if (isTimeSlotDisabled(timeSlot)) {
      // 超约情况，需要填写原因
      // showReason会在selectTime中自动设置
    } else {
      // 未超约
    }
  };

  /**
   * 提交预约
   */
  const handleSubmitReservation = async () => {
    // 表单验证
    const validation = validateForm(formState);
    if (!validation.isValid) {
      const firstError = validation.errors[0];
      jsApi.toNative('showToast', { toast: firstError.message });
      return;
    }

    try {
      const selectedTimeSlot = formState.timeList[formState.selectedTimeIndex];

      const result = await createReservation({
        customerId: String(formState.customer.id!), // 确保为字符串类型防止溢出
        storeId: formState.store.id,
        projectId: formState.project.product_id!,
        reserveType: formState.reserveType,
        date: formState.curDate,
        timeStart: selectedTimeSlot.from,
        timeEnd: selectedTimeSlot.to,
        orderId: formState.project.order_id || undefined,
        topOrderId: formState.project.top_order_id || undefined,
        therapistId: formState.therapist.id || undefined,
        consultantId: formState.consultant.id || undefined,
        reason: formState.reason || undefined,
        notes: formState.notes || undefined,
        confirmWithReservation: formState.confirmWithReservation,
        doctor_designate_yn: formState.doctor_designate_yn,
        consult_designate_yn: formState.consult_designate_yn,
      });

      // 检查新接口返回格式：{errorCode: 0, errorMsg: "ok"}
      if (result && result.errorCode === 0) {
        const successMessage = formState.confirmWithReservation
          ? '预约创建并确认成功'
          : '预约提交成功';
        jsApi.toNative('showToast', { toast: successMessage });

        // 关键修复：给toast显示留出时间，避免iOS下页面切换打断toast显示
        // 参考其他成功页面的处理方式，延迟执行页面切换操作
        setTimeout(() => {
          resetAllData();
          switchTab('RN:APPENTRY');
        }, 1000); // 1.5秒延迟，确保toast完整显示
      }
    } catch (err) {
      console.error('提交预约失败:', err);
      const errorMessage = err instanceof Error ? err.message : '提交预约失败';
      jsApi.toNative('showToast', { toast: errorMessage });
    }
  };

  /**
   * 渲染必填星号
   */
  const renderRequiredAsterisk = () => (
    <Text style={{ color: '#ff4d4f', marginLeft: getRealSize(2) }}>*</Text>
  );

  /**
   * 渲染时间网格
   */
  const renderTimeGrid = () => {
    // 添加安全检查
    if (
      !formState.timeList ||
      !Array.isArray(formState.timeList) ||
      formState.timeList.length === 0
    ) {
      return null;
    }

    const screenWidth = getRealSize(375);
    const containerPadding = getRealSize(15); // 容器左右各15px
    const timeSlotWidth = getRealSize(78); // 时间切片固定宽度78px
    const verticalGap = getRealSize(8); // 垂直间距8px

    // 动态计算水平间距，确保左右边距都是15px
    const availableWidth = screenWidth - containerPadding * 2; // 可用宽度 = 屏幕宽度 - 左右padding
    const totalTimeSlotWidth = timeSlotWidth * 4; // 4个时间切片的总宽度
    const totalGapWidth = availableWidth - totalTimeSlotWidth; // 剩余空间用作间距
    const horizontalGap = totalGapWidth / 3; // 3个间距平均分配

    // 按行分组，每行4个
    const rows = [];
    for (let i = 0; i < formState.timeList.length; i += 4) {
      rows.push(formState.timeList.slice(i, i + 4));
    }

    return (
      <View>
        {rows.map((rowItems, rowIndex) => (
          <View
            key={`row-${rowIndex}`}
            style={{
              flexDirection: 'row',
              marginBottom: rowIndex === rows.length - 1 ? 0 : verticalGap,
            }}
          >
            {rowItems.map((timeSlot, colIndex) => {
              // 额外的安全检查
              if (!timeSlot || typeof timeSlot !== 'object') {
                return (
                  <View
                    key={`invalid-${rowIndex}-${colIndex}`}
                    style={[
                      styles.timeSlot,
                      {
                        width: timeSlotWidth,
                        opacity: 0,
                        marginRight: colIndex === 3 ? 0 : horizontalGap,
                      },
                    ]}
                  />
                );
              }

              const index = rowIndex * 4 + colIndex;
              const isSelected = formState.selectedTimeIndex === index;
              const isDisabled = isTimeSlotDisabled(timeSlot);
              const isOverbooked = isTimeSlotOverbooked(timeSlot);

              return (
                <TouchableWithoutFeedback
                  key={`time-${index}`}
                  onPress={() => handleSelectTime(index)}
                  disabled={isDisabled}
                >
                  <View
                    style={[
                      styles.timeSlot,
                      {
                        width: timeSlotWidth,
                        marginRight: colIndex === 3 ? 0 : horizontalGap,
                      },
                      isSelected && styles.timeSlotActive,
                      isDisabled && styles.timeSlotDisabled,
                      isOverbooked && styles.timeSlotOverbooked,
                    ]}
                  >
                    <Text
                      style={[
                        styles.timeValue,
                        isSelected && { color: '#ffffff' },
                        isOverbooked &&
                          !isSelected &&
                          styles.timeValueOverbooked,
                      ]}
                    >
                      {String(timeSlot.from || '')}
                    </Text>
                    <Text
                      style={[
                        styles.timeAvailable,
                        isSelected && { color: '#ffffff' },
                        isOverbooked &&
                          !isSelected &&
                          styles.timeAvailableOverbooked,
                      ]}
                    >
                      约{String(timeSlot.reserved_num || 0)}｜剩
                      {String(timeSlot.remain_num || 0)}
                    </Text>
                    {timeSlot.cashback &&
                    typeof timeSlot.cashback === 'string' &&
                    timeSlot.cashback.trim() !== '' ? (
                      <View style={styles.timeCashback}>
                        <Text
                          style={styles.timeCashbackText}
                          numberOfLines={1}
                          ellipsizeMode='tail'
                        >
                          {timeSlot.cashback}
                        </Text>
                      </View>
                    ) : null}
                  </View>
                </TouchableWithoutFeedback>
              );
            })}

            {/* 如果这一行不足4个，填充透明占位符保持布局一致 */}
            {Array.from({ length: 4 - rowItems.length }, (_, emptyIndex) => {
              const currentPosition = rowItems.length + emptyIndex;
              return (
                <View
                  key={`empty-${rowIndex}-${emptyIndex}`}
                  style={[
                    styles.timeSlot,
                    {
                      width: timeSlotWidth,
                      opacity: 0,
                      marginRight: currentPosition === 3 ? 0 : horizontalGap,
                    },
                  ]}
                />
              );
            })}
          </View>
        ))}
      </View>
    );
  };

  /**
   * 渲染日期选择器
   */
  const renderDateSelector = () => {
    const today = dayjs();

    // 使用calendarData中的startDate和endDate，与sy-chain保持一致
    const displayStartDate = dayjs(calendarData.startDate);
    const displayEndDate = dayjs(calendarData.endDate);

    // 计算需要显示的日期数量
    const daysDiff = displayEndDate.diff(displayStartDate, 'day') + 1;
    const datesCount = Math.max(daysDiff, 7); // 至少显示7天

    const dates = Array.from({ length: datesCount }, (_, index) => {
      const date = displayStartDate.add(index, 'day');
      const dateStr = date.format('YYYY-MM-DD');
      const isSelected = dateStr === formState.curDate;
      const isToday = dateStr === today.format('YYYY-MM-DD');

      let label = '';
      if (isToday) {
        label = '今日';
      } else {
        const weekdays = [
          '周日',
          '周一',
          '周二',
          '周三',
          '周四',
          '周五',
          '周六',
        ];
        label = weekdays[date.day()];
      }

      // 从日历数据中获取返现信息
      const cashbackInfo = calendarData.cashbackList.find(
        item => item.date === dateStr
      );
      const cashback = cashbackInfo?.cashback || null;

      return {
        date: dateStr,
        display: date.format('MM/DD'),
        label,
        isSelected,
        cashback,
      };
    });

    return (
      <View style={styles.dateSelectorContainer}>
        <ScrollView
          ref={dateScrollViewRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.dateScrollView}
        >
          {dates.map(item => (
            <TouchableWithoutFeedback
              key={item.date}
              onPress={() => handleCalendarClick(item.date)}
            >
              <View
                style={[
                  styles.dateItem,
                  item.isSelected && styles.dateItemSelected,
                ]}
              >
                {item.cashback && (
                  <View style={styles.dateItemCashback}>
                    <Text style={styles.dateItemCashbackText}>
                      {item.cashback}
                    </Text>
                  </View>
                )}
                <Text
                  style={[
                    styles.dateItemDate,
                    item.isSelected && styles.dateItemDateSelected,
                  ]}
                >
                  {item.display}
                </Text>
                <Text
                  style={[
                    styles.dateItemLabel,
                    item.isSelected && styles.dateItemLabelSelected,
                  ]}
                >
                  {item.label}
                </Text>
              </View>
            </TouchableWithoutFeedback>
          ))}
        </ScrollView>

        <TouchableWithoutFeedback onPress={handleShowCalendar}>
          <View style={styles.calendarButton}>
            <Image
              source={{
                uri: 'https://static.soyoung.com/sy-design/1yubgvsyhbgj61753259744184.png',
              }}
              style={styles.calendarIcon}
            />
            <Text style={styles.calendarText}>日历</Text>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  };

  // 计算用于展示的订单ID（与 sy-chain 一致：优先 top_order_id 非 '0'，否则使用 display_order_id）
  const getProjectDisplayOrderId = () => {
    const topId = String(formState.project.top_order_id || '');
    if (topId && topId !== '0') return topId;
    return String(formState.project.display_order_id || '');
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <KeyboardAvoidingView
        style={styles.content}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={keyboardVerticalOffset}
      >
        {/* Header */}
        <Header
          title='新建预约'
          zIndex={102}
          bgColor='#FFFFFF'
          hideBack={false}
        />

        <View style={{ flex: 1 }}>
          <ScrollView
            ref={formScrollRef}
            style={styles.formContainer}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps='handled'
            contentInsetAdjustmentBehavior='always'
            contentContainerStyle={{
              paddingBottom: keyboardVisible
                ? Platform.OS === 'ios'
                  ? keyboardHeight
                  : Math.max(keyboardHeight * 0.4, 120) // Android: 使用键盘高度的40%，最小120
                : getRealSize(12) +
                  getRealSize(42) +
                  getRealSize(30) +
                  insets.bottom,
            }}
          >
            {/* 客户信息 */}
            <TouchableWithoutFeedback onPress={handleSelectCustomer}>
              <View style={styles.formItem}>
                <Text style={styles.label}>客户{renderRequiredAsterisk()}</Text>
                <View style={styles.valueContainer}>
                  <Text style={styles.value}>
                    {(formState.customer.name || '').toString() || '请选择客户'}
                  </Text>
                  {formState.customer.name &&
                  (formState.customer.gender || formState.customer.age) ? (
                    <Text style={styles.info}>
                      （
                      {[
                        formState.customer.gender || '',
                        formState.customer.age || '',
                      ]
                        .filter(Boolean)
                        .join(' | ')}
                      ）
                    </Text>
                  ) : null}
                  <Image
                    style={styles.arrowIcon}
                    source={{
                      uri: 'https://static.soyoung.com/sy-design/6eboz32amcrz1753259744838.png',
                    }}
                    resizeMode='contain'
                  />
                </View>
              </View>
            </TouchableWithoutFeedback>

            {/* 预约门店 */}
            <TouchableWithoutFeedback onPress={handleSelectStore}>
              <View style={styles.formItem}>
                <Text style={styles.label}>
                  预约门店{renderRequiredAsterisk()}
                </Text>
                <View style={styles.valueContainer}>
                  <Text style={styles.value}>
                    {(formState.store.name || '').toString() || '请选择门店'}
                  </Text>
                  <Image
                    style={styles.arrowIcon}
                    source={{
                      uri: 'https://static.soyoung.com/sy-design/6eboz32amcrz1753259744838.png',
                    }}
                    resizeMode='contain'
                  />
                </View>
              </View>
            </TouchableWithoutFeedback>

            {/* 预约依据 */}
            <View style={styles.formItem}>
              <Text style={styles.label}>
                预约依据{renderRequiredAsterisk()}
              </Text>
              <View style={styles.radioGroup}>
                <TouchableWithoutFeedback
                  onPress={() =>
                    handleReserveTypeChange(ReserveType.WITH_ORDER)
                  }
                >
                  <View
                    style={[styles.radioItem, { marginRight: getRealSize(10) }]}
                  >
                    <View
                      style={[
                        styles.radioCircle,
                        formState.reserveType === ReserveType.WITH_ORDER &&
                          styles.radioCircleActive,
                      ]}
                    >
                      {formState.reserveType === ReserveType.WITH_ORDER && (
                        <View style={styles.radioInner} />
                      )}
                    </View>
                    <Text
                      style={[
                        styles.radioLabel,
                        formState.reserveType !== ReserveType.WITH_ORDER &&
                          styles.radioLabelGray,
                      ]}
                    >
                      有单预约
                    </Text>
                  </View>
                </TouchableWithoutFeedback>

                <TouchableWithoutFeedback
                  onPress={() => handleReserveTypeChange(ReserveType.NO_ORDER)}
                >
                  <View style={styles.radioItem}>
                    <View
                      style={[
                        styles.radioCircle,
                        formState.reserveType === ReserveType.NO_ORDER &&
                          styles.radioCircleActive,
                      ]}
                    >
                      {formState.reserveType === ReserveType.NO_ORDER && (
                        <View style={styles.radioInner} />
                      )}
                    </View>
                    <Text
                      style={[
                        styles.radioLabel,
                        formState.reserveType !== ReserveType.NO_ORDER &&
                          styles.radioLabelGray,
                      ]}
                    >
                      无单预约
                    </Text>
                  </View>
                </TouchableWithoutFeedback>
              </View>
            </View>

            {/* 预约项目 */}
            <TouchableWithoutFeedback onPress={handleSelectProject}>
              <View style={styles.formItem}>
                <Text style={styles.label}>
                  预约项目{renderRequiredAsterisk()}
                </Text>
                <View style={styles.valueContainer}>
                  <View style={styles.valueContainerOrder}>
                    <Text style={styles.value}>
                      {(formState.project.title || '').toString() ||
                        '请选择项目'}
                    </Text>
                    {formState.reserveType === ReserveType.WITH_ORDER &&
                    (formState.project.top_order_id ||
                      formState.project.display_order_id) ? (
                      <Text style={styles.info}>
                        订单ID：{getProjectDisplayOrderId()}
                      </Text>
                    ) : null}
                  </View>
                  <Image
                    style={styles.arrowIcon}
                    source={{
                      uri: 'https://static.soyoung.com/sy-design/6eboz32amcrz1753259744838.png',
                    }}
                    resizeMode='contain'
                  />
                </View>
              </View>
            </TouchableWithoutFeedback>

            {/* 预约时段 */}
            <View style={[styles.formItem, styles.timeItem]}>
              <Text style={styles.label}>
                预约时段{renderRequiredAsterisk()}
              </Text>

              {/* 日期选择器 */}
              {renderDateSelector()}

              {/* 时间选择 */}
              {formState.timeList &&
                Array.isArray(formState.timeList) &&
                formState.timeList.length > 0 && (
                  <View style={styles.timeSelector}>{renderTimeGrid()}</View>
                )}
            </View>

            {/* 超约原因 */}
            {formState.timeList &&
              Array.isArray(formState.timeList) &&
              formState.timeList.length > 0 &&
              formState.showReason && (
                <>
                  <View style={[styles.formItem, styles.formItemReason]}>
                    <Text style={styles.label}>
                      超约原因{renderRequiredAsterisk()}
                    </Text>
                  </View>
                  <View style={[styles.formItem, styles.formItemReasonInput]}>
                    <View style={styles.inputContainer}>
                      <TextInput
                        ref={reasonInputRef}
                        style={styles.input}
                        value={formState.reason}
                        onChangeText={updateReason}
                        placeholder='超约仍需预约，请填写超约原因'
                        placeholderTextColor='#aaabb3'
                        onFocus={() => scrollToInputField(reasonInputRef)}
                      />
                    </View>
                  </View>
                </>
              )}

            {/* 治疗师 */}
            <View style={styles.formItem}>
              <Text style={styles.label}>治疗师</Text>
              <View style={styles.valueContainer}>
                <View style={[styles.radioGroup, styles.therapistSelector]}>
                  <TouchableWithoutFeedback onPress={toggleDoctorDesignate}>
                    <View style={styles.radioContainer}>
                      <View
                        style={[
                          styles.radioCircle,
                          formState.doctor_designate_yn &&
                            styles.radioCircleActive,
                        ]}
                      >
                        {formState.doctor_designate_yn && (
                          <View style={styles.radioInner} />
                        )}
                      </View>
                      <Text style={styles.radioLabel}>用户指定</Text>
                    </View>
                  </TouchableWithoutFeedback>
                </View>
                <TouchableWithoutFeedback
                  onPress={() => handleSelectConsultant('therapist')}
                >
                  <View style={styles.valueContainer}>
                    <Text style={styles.value}>
                      {(formState.therapist.name || '').toString()}
                    </Text>
                  </View>
                </TouchableWithoutFeedback>
                <TouchableWithoutFeedback
                  onPress={() => handleSelectConsultant('therapist')}
                >
                  <View style={styles.arrowBox}>
                    <Image
                      style={styles.arrowIcon}
                      source={{
                        uri: 'https://static.soyoung.com/sy-design/6eboz32amcrz1753259744838.png',
                      }}
                      resizeMode='contain'
                    />
                  </View>
                </TouchableWithoutFeedback>
              </View>
            </View>

            {/* 咨询师 */}
            <View style={styles.formItem}>
              <Text style={styles.label}>咨询师</Text>
              <View style={styles.valueContainer}>
                <View style={[styles.radioGroup, styles.consultantSelector]}>
                  <TouchableWithoutFeedback onPress={toggleConsultDesignate}>
                    <View style={styles.radioContainer}>
                      <View
                        style={[
                          styles.radioCircle,
                          formState.consult_designate_yn &&
                            styles.radioCircleActive,
                        ]}
                      >
                        {formState.consult_designate_yn && (
                          <View style={styles.radioInner} />
                        )}
                      </View>
                      <Text style={styles.radioLabel}>用户指定</Text>
                    </View>
                  </TouchableWithoutFeedback>
                </View>
                <TouchableWithoutFeedback
                  onPress={() => handleSelectConsultant('consultant')}
                >
                  <View style={styles.valueContainer}>
                    <Text style={styles.value}>
                      {(formState.consultant.name || '').toString()}
                    </Text>
                  </View>
                </TouchableWithoutFeedback>
                <TouchableWithoutFeedback
                  onPress={() => handleSelectConsultant('consultant')}
                >
                  <View style={styles.arrowBox}>
                    <Image
                      style={styles.arrowIcon}
                      source={{
                        uri: 'https://static.soyoung.com/sy-design/6eboz32amcrz1753259744838.png',
                      }}
                      resizeMode='contain'
                    />
                  </View>
                </TouchableWithoutFeedback>
              </View>
            </View>

            {/* 备注 */}
            <View style={[styles.formItem, styles.formItemReason]}>
              <Text style={styles.label}>备注</Text>
            </View>
            <View style={[styles.formItem, styles.formItemReasonInput]}>
              <View style={styles.inputContainer}>
                <TextInput
                  ref={notesInputRef}
                  style={styles.textarea}
                  value={formState.notes}
                  onChangeText={updateNotes}
                  placeholder='请输入内容'
                  placeholderTextColor='#aaabb3'
                  multiline
                  textAlignVertical='top'
                  onFocus={() => scrollToInputField(notesInputRef)}
                />
              </View>
            </View>
          </ScrollView>
        </View>

        {/* 底部按钮 */}
        <View
          style={[
            styles.footer,
            {
              // 覆盖原有的margin布局，改为绝对定位到页面底部；
              // iOS 键盘弹起时动态调整bottom位置
              position: 'absolute',
              left: 0,
              right: 0,
              bottom:
                keyboardVisible && Platform.OS === 'ios' ? keyboardHeight : 0,
              marginHorizontal: 0,
              paddingHorizontal: getRealSize(15),
              paddingBottom: keyboardVisible ? getRealSize(8) : getRealSize(15),
            },
          ]}
        >
          <View style={styles.footerContainer}>
            <TouchableWithoutFeedback
              onPress={handleToggleConfirmWithReservation}
            >
              <View style={styles.radioItem}>
                <View
                  style={[
                    styles.radioCircle,
                    formState.confirmWithReservation &&
                      styles.radioCircleActive,
                  ]}
                >
                  {formState.confirmWithReservation && (
                    <View style={styles.radioInner} />
                  )}
                </View>
              </View>
            </TouchableWithoutFeedback>
            <Text style={styles.checkboxLabel}>同时确认预约</Text>
          </View>

          <TouchableWithoutFeedback
            onPress={handleSubmitReservation}
            disabled={loading}
          >
            <View
              style={[styles.submitBtn, loading && styles.submitBtnDisabled]}
            >
              <Text style={styles.submitBtnText}>
                {formState.confirmWithReservation
                  ? '创建预约并确认'
                  : '提交预约'}
              </Text>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default CreateReserveTarget;
