import { StyleSheet } from 'react-native';
import { getRealSize } from '../../../common/utils';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff', // 改为白色背景，与原版一致
  },

  content: {
    flex: 1,
  },

  // 页面头部
  header: {
    height: getRealSize(44),
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomWidth: getRealSize(0.5),
    borderBottomColor: '#e0e0e0',
  },

  // 顶部标题栏
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#ffffff',
    height: getRealSize(44),
    paddingHorizontal: getRealSize(16),
  },

  backButton: {
    width: getRealSize(44),
    height: getRealSize(44),
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: getRealSize(-12),
  },

  backIcon: {
    width: getRealSize(18),
    height: getRealSize(34),
    fontSize: getRealSize(34),
    color: '#333333',
    fontWeight: '300',
    textAlign: 'center',
    lineHeight: getRealSize(34),
  },

  headerTitle: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(17),
    color: '#030303',
    letterSpacing: 0,
    textAlign: 'center',
    lineHeight: getRealSize(22),
    fontWeight: '500',
  },

  headerRight: {
    width: getRealSize(32),
  },

  formContainer: {
    flex: 1,
    padding: 0, // 移除padding，让form-item自己控制间距
  },

  // 表单项基础样式
  formItem: {
    flexDirection: 'row', // 水平布局：label + value
    paddingHorizontal: getRealSize(16), // 左右间距16px
    paddingVertical: getRealSize(16), // 上下间距16px
    alignItems: 'flex-start', // 顶部对齐，支持多行文本
    backgroundColor: '#ffffff',
  },

  // 时间选择特殊布局
  timeItem: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    paddingBottom: getRealSize(12), // 增加底部间距
    borderBottomWidth: 0, // 时间选择项无分隔线
  },

  // 标签样式
  label: {
    width: getRealSize(90), // 固定宽度90px
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(14),
    color: '#333333',
    letterSpacing: 0,
    fontWeight: '500',
    flexShrink: 0,
    marginTop: getRealSize(1), // 轻微的顶部间距，与文本基线对齐
    marginBottom: 0, // 水平布局时无需margin
  },

  required: {
    // 通过伪元素样式在组件中实现
  },

  // 值容器样式
  valueContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center', // 顶部对齐，支持多行文本
    justifyContent: 'flex-end', // 右对齐
    flexWrap: 'wrap', // 允许换行
  },

  valueContainerOrder: {
    flex: 1,
    alignItems: 'flex-end', // 右对齐
  },

  // 值文本样式
  value: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15), // 15px
    color: '#333333',
    fontWeight: '400',
    textAlign: 'right',
    flex: 1, // 自适应宽度，防止溢出
    flexShrink: 1, // 允许收缩
  },

  // 附加信息样式
  info: {
    color: '#666666',
    fontSize: getRealSize(15),
    flexShrink: 0, // 不允许收缩，保持完整显示
    marginLeft: getRealSize(4), // 与客户名称保持间距
  },

  // 订单ID样式
  orderId: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12), // 12px
    color: '#777777',
    fontWeight: '400',
    marginTop: getRealSize(2),
  },

  // 箭头图标
  arrowIcon: {
    width: getRealSize(6), // 6px
    height: getRealSize(9), // 9px
    marginLeft: getRealSize(8),
    flexShrink: 0, // 不允许收缩，保持图标尺寸
  },

  // 单选框组
  radioGroup: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end', // 右对齐
  },

  radioItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  radioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  radioCircle: {
    width: getRealSize(12), // 12px
    height: getRealSize(12),
    borderWidth: getRealSize(1),
    borderColor: '#333333', // 边框颜色#333333
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: getRealSize(8),
    backgroundColor: '#ffffff', // 白色背景
  },

  radioCircleActive: {
    borderColor: '#333333', // 保持边框颜色#333333
    backgroundColor: '#ffffff', // 选中时背景保持白色
  },

  radioInner: {
    width: getRealSize(8), // 8px
    height: getRealSize(8),
    backgroundColor: '#333333', // 选中填充#333333
  },

  radioLabel: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15),
    color: '#333333',
    fontWeight: '400',
  },

  radioLabelGray: {
    color: '#777777',
  },

  // 日期选择器容器
  dateSelector: {
    width: '100%',
  },

  dateSelectorContainer: {
    width: '100%',
    height: getRealSize(76),
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    marginTop: getRealSize(15),
  },

  dateScrollView: {
    flex: 1,
  },

  dateItem: {
    alignItems: 'center',
    justifyContent: 'center',
    width: getRealSize(60),
    height: getRealSize(76),
    marginRight: getRealSize(10),
    backgroundColor: '#f2f2f2',
    position: 'relative',
  },

  dateItemSelected: {
    backgroundColor: '#333333',
  },

  dateItemCashback: {
    position: 'absolute',
    top: getRealSize(0),
    left: getRealSize(0),
    // 标签宽度与父容器一致，支持多行显示
    width: '100%',
    minHeight: getRealSize(14),
    backgroundColor: '#A9EA6A',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
    paddingHorizontal: getRealSize(0),
  },

  dateItemCashbackText: {
    fontSize: getRealSize(10),
    color: '#030303',
    fontWeight: '400',
    // 允许换行并居中显示
    textAlign: 'center',
    flexWrap: 'wrap',
    maxWidth: '100%',
  },

  dateItemDate: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(11),
    color: '#222222',
    letterSpacing: 0,
    textAlign: 'center',
    fontWeight: '500',
    marginTop: getRealSize(4),
  },

  dateItemDateSelected: {
    color: '#ffffff',
  },

  dateItemLabel: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(11),
    color: '#222222',
    letterSpacing: 0,
    textAlign: 'center',
    fontWeight: '500',
  },

  dateItemLabelSelected: {
    color: '#ffffff',
  },

  memberBadge: {
    position: 'absolute',
    bottom: getRealSize(0),
    left: 0,
    right: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },

  memberIcon: {
    width: '100%',
    height: getRealSize(16), // 高度自适应，设置基础高度
    resizeMode: 'contain',
  },

  memberText: {
    fontSize: getRealSize(10),
    color: '#ffffff',
    fontWeight: '500',
  },

  calendarButton: {
    alignItems: 'flex-end',
    justifyContent: 'center',
    height: getRealSize(76),
    width: getRealSize(40),
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: '#ffffff',
  },

  calendarIcon: {
    width: getRealSize(15),
    height: getRealSize(14),
    marginBottom: getRealSize(2),
    marginRight: getRealSize(3),
  },

  calendarText: {
    fontSize: getRealSize(11),
    color: '#030303',
  },

  // 时间选择器
  timeSelector: {
    width: '100%',
    marginTop: getRealSize(20), // 距离日期选择器20px
    // paddingHorizontal由renderTimeGrid函数动态设置
  },

  timeSlot: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    height: getRealSize(56), // 固定高度56px
    backgroundColor: '#f2f2f2', // 参考日期选择器默认背景
    position: 'relative',
    // 宽度由组件动态设置确保布局精确
  },

  timeSlotActive: {
    backgroundColor: '#333333', // 参考日期选择器选中背景
  },

  timeSlotDisabled: {
    opacity: 0.6,
    backgroundColor: '#f0f0f0',
  },

  // 超约状态样式：样式置灰但可选择
  timeSlotOverbooked: {
    // 背景保持原样，不改变背景色
  },

  timeValue: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(11), // 参考日期选择器字体大小
    color: '#222222', // 参考日期选择器颜色
    letterSpacing: 0,
    textAlign: 'center',
    fontWeight: '500',
    marginBottom: getRealSize(2),
  },

  timeAvailable: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(11), // 参考日期选择器字体大小
    color: '#222222', // 参考日期选择器颜色
    letterSpacing: 0,
    textAlign: 'center',
    fontWeight: '500',
  },

  // 超约状态字体样式：置灰色 #BABABA
  timeValueOverbooked: {
    color: '#BABABA',
  },

  timeAvailableOverbooked: {
    color: '#BABABA',
  },

  timeCashback: {
    position: 'absolute',
    top: getRealSize(0),
    left: getRealSize(0),
    height: getRealSize(14),
    backgroundColor: '#A9EA6A',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
    paddingHorizontal: getRealSize(2),
    maxWidth: getRealSize(76), // 限制最大宽度不超过时间切片宽度（78px - 2px边距）
  },

  timeCashbackText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(10),
    color: '#030303',
    letterSpacing: 0,
    fontWeight: '400',
  },

  // 输入框容器
  inputContainer: {
    flex: 1,
    borderBottomWidth: getRealSize(0.5),
    borderBottomColor: '#dedede',
  },

  input: {
    width: '100%',
    fontSize: getRealSize(13),
    color: '#333333',
    textAlign: 'left',
    height: getRealSize(38),
    padding: 0,
  },

  textarea: {
    width: '100%',
    fontSize: getRealSize(13),
    color: '#333333',
    textAlign: 'left',
    paddingTop: getRealSize(10),
    minHeight: getRealSize(30),
    textAlignVertical: 'top',
    padding: 0,
  },

  // 治疗师/咨询师选择器
  therapistSelector: {
    marginRight: 'auto',
    justifyContent: 'flex-start',
  },

  consultantSelector: {
    marginRight: 'auto',
    justifyContent: 'flex-start',
  },

  clickArea: {
    minWidth: getRealSize(30),
    minHeight: getRealSize(21.5),
    textAlign: 'center',
    lineHeight: getRealSize(21.5),
  },

  arrowBox: {
    padding: getRealSize(5),
  },

  // 底部样式
  footer: {
    backgroundColor: '#ffffff',
    paddingTop: getRealSize(12),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  footerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  checkboxLabel: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15),
    color: '#555555',
    letterSpacing: 0,
    textAlign: 'center',
    fontWeight: '400',
    marginLeft: getRealSize(8),
  },

  submitBtn: {
    backgroundColor: '#333333',
    width: getRealSize(170),
    height: getRealSize(42),
    alignItems: 'center',
    justifyContent: 'center',
  },

  submitBtnText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(13),
    color: '#FFFFFF',
    letterSpacing: 0,
    textAlign: 'center',
    fontWeight: '500',
  },

  submitBtnDisabled: {
    backgroundColor: '#cccccc',
  },

  // 特殊表单项样式
  formItemReason: {
    paddingBottom: 0,
  },

  formItemReasonInput: {
    paddingTop: 0,
  },

  // 占位符样式
  placeholder: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#aaabb3',
    fontWeight: '400',
  },

  // 激活状态
  active: {
    // 用于radioItem的active状态，通过radioCircleActive实现
  },
});
