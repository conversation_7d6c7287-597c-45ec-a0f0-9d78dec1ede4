import React from 'react';
import CreateReserveTarget from './CreateReserveTarget';

/**
 * 新建预约页面埋点壳组件
 *
 * 功能说明：
 * - 统一页面埋点管理
 * - 生命周期标准化（didAppear/willDisappear）
 * - 页面显示状态管理（pageShowFlag）
 * - 业务逻辑委托给 CreateReserveTarget 组件
 *
 * <AUTHOR> sy-chain uni-app 项目
 * @version 1.0.0
 * @since 2024-01
 */

interface CreateReserveProps {
  navigation: any;
  route: any;
  [key: string]: any;
}

interface CreateReserveState {
  pageShow: boolean;
}

class CreateReserve extends React.Component<
  CreateReserveProps,
  CreateReserveState
> {
  constructor(props: CreateReserveProps) {
    super(props);
    this.state = {
      pageShow: true,
    };
  }

  /**
   * 页面名称（用于埋点）
   * @returns 页面标识符
   */
  soyoungPageName() {
    return 'CreateReserve';
  }

  /**
   * 页面埋点信息
   * @returns 埋点数据对象
   */
  soyoungPageInfo() {
    return {
      page_name: '新建预约',
      page_type: 'create_reservation',
    };
  }

  /**
   * 页面显示生命周期
   * 当页面进入可见状态时调用
   */
  didAppear() {
    this.setState({ pageShow: true });
    // 可在此处添加页面显示埋点逻辑
    console.log('CreateReserve页面显示', this.soyoungPageInfo());
  }

  /**
   * 页面隐藏生命周期
   * 当页面离开可见状态时调用
   */
  willDisappear() {
    this.setState({ pageShow: false });
    // 可在此处添加页面隐藏埋点逻辑
    console.log('CreateReserve页面隐藏', this.soyoungPageInfo());
  }

  /**
   * 组件挂载后调用页面显示生命周期
   */
  componentDidMount() {
    this.didAppear();
  }

  preferredStatusBarStyle() {
    return '2';
  }

  /**
   * 组件卸载前调用页面隐藏生命周期
   */
  componentWillUnmount() {
    this.willDisappear();
  }

  render() {
    return (
      <CreateReserveTarget {...this.props} pageShowFlag={this.state.pageShow} />
    );
  }
}

export default CreateReserve;
