import { StyleSheet } from 'react-native';
import { getRealSize } from '../../../common/utils';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },

  content: {
    flex: 1,
  },

  // 搜索框容器
  searchContainer: {
    backgroundColor: '#ffffff',
    paddingHorizontal: getRealSize(16),
    paddingVertical: getRealSize(12),
    borderBottomWidth: getRealSize(0.5),
    borderBottomColor: '#e0e0e0',
  },

  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 0, // 去掉圆角
    paddingHorizontal: getRealSize(12),
    height: getRealSize(36),
  },

  searchIcon: {
    width: getRealSize(16),
    height: getRealSize(16),
    marginRight: getRealSize(8),
  },

  searchInput: {
    flex: 1,
    fontSize: getRealSize(14),
    color: '#333333',
    padding: 0,
  },

  clearButton: {
    width: getRealSize(24),
    height: getRealSize(24),
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: getRealSize(8),
  },

  clearIcon: {
    width: getRealSize(14),
    height: getRealSize(14),
  },

  // 列表容器
  listContainer: {
    flex: 1,
    backgroundColor: '#f8f8f8', // 父组件背景色
    paddingHorizontal: getRealSize(16),
  },

  listContent: {
    paddingVertical: getRealSize(12),
  },

  // 空状态
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: getRealSize(120),
  },

  emptyText: {
    fontSize: getRealSize(14),
    color: '#999999',
    textAlign: 'center',
  },

  // 加载状态
  loadingContainer: {
    alignItems: 'center',
  },

  loadingText: {
    fontSize: getRealSize(12),
    color: '#999999',
  },

  // 加载更多底部样式（参考客户列表页面）
  loadingFooter: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: getRealSize(15),
  },
});

// 客户列表项样式
export const customerStyles = StyleSheet.create({
  resultItem: {
    backgroundColor: '#ffffff',
    borderRadius: 0, // 去掉圆角
    padding: getRealSize(15),
    marginBottom: getRealSize(10),
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: getRealSize(0.5),
    borderColor: 'transparent',
  },

  selectedItem: {
    backgroundColor: '#EBFBDC',
    borderColor: '#61B43E',
  },

  avatarContainer: {
    width: getRealSize(50),
    height: getRealSize(50),
    borderRadius: getRealSize(25), // 圆形头像
    backgroundColor: '#61B43E', // 客户头像背景色改为 #61B43E
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: getRealSize(12),
  },

  avatarText: {
    color: '#ffffff',
    fontSize: getRealSize(22),
    fontWeight: 'bold',
  },

  customerInfo: {
    flex: 1,
    flexDirection: 'column',
  },

  customerNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getRealSize(8),
  },

  customerName: {
    fontSize: getRealSize(16),
    fontWeight: 'bold',
    color: '#333333',
    marginRight: getRealSize(10),
  },

  customerGenderAge: {
    fontSize: getRealSize(12),
    color: '#666666',
  },

  customerPhone: {
    fontSize: getRealSize(12),
    color: '#999999',
  },
});

// 门店列表项样式
export const storeStyles = StyleSheet.create({
  // 默认门店列表项样式：白色背景 + #333333 文字
  resultItem: {
    backgroundColor: '#ffffff', // 默认白色背景
    borderRadius: 0, // 去掉圆角
    borderWidth: getRealSize(1),
    borderColor: '#e0e0e0', // 默认灰色边框
    width: getRealSize(343), // 门店列表宽度 343px
    height: getRealSize(54), // 门店列表高度 54px
    paddingHorizontal: getRealSize(15),
    marginBottom: getRealSize(10),
    position: 'relative',
    justifyContent: 'center', // 垂直居中
  },

  // 选中门店列表项样式：绿色背景 + 绿色文字
  selectedItem: {
    backgroundColor: '#EBFBDC', // 选中时绿色背景
    borderColor: '#61B43E', // 选中时绿色边框
  },

  storeInfo: {
    flexDirection: 'column',
  },

  // 默认门店名称样式：#333333 文字颜色
  storeName: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15),
    color: '#333333', // 默认文字颜色
    fontWeight: '400',
    letterSpacing: 0,
  },

  // 选中门店名称样式：绿色文字
  selectedStoreName: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15),
    color: '#61B43E', // 选中时绿色文字
    fontWeight: '400',
    letterSpacing: 0,
  },

  cashback: {
    position: 'absolute',
    right: getRealSize(-1),
    top: getRealSize(-1),
    backgroundColor: '#ff4040',
    borderRadius: 0, // 去掉圆角
    paddingHorizontal: getRealSize(8),
    paddingVertical: getRealSize(2),
    fontSize: getRealSize(12),
    color: '#ffffff',
    fontWeight: '400',
  },
});

// 项目列表项样式（参考门店列表设计）
export const projectStyles = StyleSheet.create({
  // 默认项目列表项样式：白色背景 + #333333 文字
  resultItem: {
    backgroundColor: '#ffffff', // 默认白色背景
    borderRadius: 0, // 去掉圆角
    borderWidth: getRealSize(1),
    borderColor: 'transparent', // 默认灰色边框
    width: getRealSize(343), // 项目列表宽度 343px（与门店一致）
    minHeight: getRealSize(70), // 最小高度70px，支持订单号显示
    paddingHorizontal: getRealSize(15),
    paddingVertical: getRealSize(15), // 上下内边距15px
    marginBottom: getRealSize(10),
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },

  // 选中项目列表项样式：绿色背景 + 绿色文字
  selectedItem: {
    backgroundColor: '#EBFBDC', // 选中时绿色背景（与门店一致）
    borderColor: '#61B43E', // 选中时绿色边框（与门店一致）
  },

  projectInfo: {
    flexDirection: 'column',
    flex: 1,
  },

  projectTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getRealSize(8),
  },

  orderIdRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  orderIdLabel: {
    fontSize: getRealSize(14),
    color: '#999999',
    fontFamily: 'PingFangSC-Regular',
    fontWeight: '400',
  },

  orderId: {
    fontSize: getRealSize(14),
    color: '#999999',
    fontFamily: 'PingFangSC-Regular',
    fontWeight: '400',
  },

  // 默认项目名称样式：#333333 文字颜色
  projectName: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15), // 与门店文字大小一致
    color: '#333333', // 默认文字颜色
    fontWeight: '400',
    letterSpacing: 0,
  },

  // 选中项目名称样式：绿色文字
  selectedProjectName: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15),
    color: '#61B43E', // 选中时绿色文字（与门店一致）
    fontWeight: '400',
    letterSpacing: 0,
  },

  projectDetails: {
    flexDirection: 'column',
    marginTop: getRealSize(8),
  },

  // 新增样式：项目次数信息
  projectTimes: {
    fontSize: getRealSize(13),
    color: '#007AFF',
    marginBottom: getRealSize(4),
    fontWeight: '500',
  },

  projectLeftTimes: {
    fontSize: getRealSize(13),
    color: '#FF9500',
    marginBottom: getRealSize(4),
    fontWeight: '500',
  },

  projectOrderId: {
    fontSize: getRealSize(12),
    color: '#666666',
    marginBottom: getRealSize(4),
  },

  projectCashback: {
    fontSize: getRealSize(12),
    color: '#FF3B30',
    marginBottom: getRealSize(4),
    fontWeight: '500',
  },

  // 兼容旧数据的额外详情容器
  projectExtraDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: getRealSize(8),
  },

  projectPrice: {
    fontSize: getRealSize(14),
    color: '#ff6b35',
    fontWeight: '600',
    marginRight: getRealSize(12),
  },

  projectDuration: {
    fontSize: getRealSize(12),
    color: '#666666',
  },

  // 返现标签样式
  cashback: {
    position: 'absolute',
    right: getRealSize(0),
    top: getRealSize(0),
    backgroundColor: '#ff4040',
    paddingHorizontal: getRealSize(4),
    paddingVertical: getRealSize(0),
    height: getRealSize(16),
    justifyContent: 'center',
    alignItems: 'center',
  },

  cashbackText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(10),
    color: '#ffffff',
    fontWeight: '400',
  },
});

// 员工/咨询师/治疗师列表项样式
export const employeeStyles = StyleSheet.create({
  resultItem: {
    backgroundColor: '#ffffff',
    borderRadius: 0, // 去掉圆角
    padding: getRealSize(15),
    marginBottom: getRealSize(10),
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: getRealSize(0.5),
    borderColor: 'transparent',
  },

  selectedItem: {
    backgroundColor: '#ebf9f6',
    borderColor: '#00ab84',
  },

  avatarContainer: {
    width: getRealSize(45),
    height: getRealSize(45),
    borderRadius: 0, // 去掉圆角
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: getRealSize(12),
  },

  avatarImage: {
    width: getRealSize(45),
    height: getRealSize(45),
    borderRadius: 0, // 去掉圆角
  },

  avatarText: {
    color: '#666666',
    fontSize: getRealSize(18),
    fontWeight: 'bold',
  },

  employeeInfo: {
    flex: 1,
    flexDirection: 'column',
  },

  employeeName: {
    fontSize: getRealSize(16),
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: getRealSize(4),
  },

  employeePosition: {
    fontSize: getRealSize(12),
    color: '#666666',
  },

  employeeDetails: {
    fontSize: getRealSize(12),
    color: '#999999',
    marginTop: getRealSize(2),
  },
});

// 治疗师样式
export const therapistStyles = StyleSheet.create({
  // 列表项基础样式（无圆角）
  resultItem: {
    backgroundColor: '#ffffff',
    borderRadius: 0, // 无圆角
    paddingHorizontal: getRealSize(16), // 原始尺寸
    paddingVertical: getRealSize(12), // 原始尺寸
    marginBottom: getRealSize(10), // 原始尺寸
    borderWidth: getRealSize(1), // 原始尺寸
    borderColor: '#ffffff',
    flexDirection: 'row',
    alignItems: 'center',
  },

  // 选中状态样式
  selectedItem: {
    backgroundColor: '#EBFBDC', // 选中背景色
    borderColor: '#61B43E', // 选中边框色
  },

  // 头像容器
  avatarContainer: {
    marginRight: getRealSize(12), // 原始尺寸
  },

  // 头像样式
  avatar: {
    width: getRealSize(40), // 原始尺寸
    height: getRealSize(40), // 原始尺寸
    borderRadius: getRealSize(20), // 无圆角，方形头像
    backgroundColor: '#61B43E',
    justifyContent: 'center',
    alignItems: 'center',
  },

  selectedAvatar: {
    backgroundColor: '#4a8f30', // 选中时头像颜色稍深
  },

  // 头像文字
  avatarText: {
    fontSize: getRealSize(12), // 原始尺寸
    fontWeight: 'bold',
    color: '#ffffff',
  },

  selectedAvatarText: {
    color: '#ffffff',
  },

  // 信息区域
  infoContainer: {
    flex: 1,
    justifyContent: 'center',
  },

  // 姓名样式
  name: {
    fontSize: getRealSize(14), // 原始尺寸
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: getRealSize(2), // 原始尺寸
  },

  selectedName: {
    color: '#2d5016', // 选中时姓名颜色
  },

  // 详情样式
  details: {
    fontSize: getRealSize(12), // 原始尺寸
    color: '#666666',
    lineHeight: getRealSize(18), // 原始尺寸
  },

  selectedDetails: {
    color: '#4a7028', // 选中时详情颜色
  },

  // 选中指示器
  checkIcon: {
    marginLeft: getRealSize(8), // 原始尺寸
    width: getRealSize(20), // 原始尺寸
    height: getRealSize(20), // 原始尺寸
    justifyContent: 'center',
    alignItems: 'center',
  },

  checkText: {
    fontSize: getRealSize(12), // 原始尺寸
    color: '#61B43E',
    fontWeight: 'bold',
  },
});

// 咨询师样式（与治疗师样式相同）
export const consultantStyles = StyleSheet.create({
  // 列表项基础样式（无圆角）
  resultItem: {
    backgroundColor: '#ffffff',
    borderRadius: 0, // 无圆角
    paddingHorizontal: getRealSize(16), // 原始尺寸
    paddingVertical: getRealSize(12), // 原始尺寸
    marginBottom: getRealSize(10), // 原始尺寸
    borderWidth: getRealSize(1), // 原始尺寸
    borderColor: '#ffffff',
    flexDirection: 'row',
    alignItems: 'center',
  },

  // 选中状态样式
  selectedItem: {
    backgroundColor: '#EBFBDC', // 选中背景色
    borderColor: '#61B43E', // 选中边框色
  },

  // 头像容器
  avatarContainer: {
    marginRight: getRealSize(12), // 原始尺寸
  },

  // 头像样式
  avatar: {
    width: getRealSize(40), // 原始尺寸
    height: getRealSize(40), // 原始尺寸
    borderRadius: getRealSize(20), // 无圆角，方形头像
    backgroundColor: '#61B43E',
    justifyContent: 'center',
    alignItems: 'center',
  },

  selectedAvatar: {
    backgroundColor: '#4a8f30', // 选中时头像颜色稍深
  },

  // 头像文字
  avatarText: {
    fontSize: getRealSize(12), // 原始尺寸
    fontWeight: 'bold',
    color: '#ffffff',
  },

  selectedAvatarText: {
    color: '#ffffff',
  },

  // 信息区域
  infoContainer: {
    flex: 1,
    justifyContent: 'center',
  },

  // 姓名样式
  name: {
    fontSize: getRealSize(14), // 原始尺寸
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: getRealSize(2), // 原始尺寸
  },

  selectedName: {
    color: '#2d5016', // 选中时姓名颜色
  },

  // 详情样式
  details: {
    fontSize: getRealSize(12), // 原始尺寸
    color: '#666666',
    lineHeight: getRealSize(18), // 原始尺寸
  },

  selectedDetails: {
    color: '#4a7028', // 选中时详情颜色
  },

  // 选中指示器
  checkIcon: {
    marginLeft: getRealSize(8), // 原始尺寸
    width: getRealSize(20), // 原始尺寸
    height: getRealSize(20), // 原始尺寸
    justifyContent: 'center',
    alignItems: 'center',
  },

  checkText: {
    fontSize: getRealSize(12), // 原始尺寸
    color: '#61B43E',
    fontWeight: 'bold',
  },
});
