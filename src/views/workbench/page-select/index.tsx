import React from 'react';
import { PageSelectProps } from './types';
import PageSelectTarget from './PageSelectTarget';

/**
 * 页面选择入口组件（埋点架构）
 *
 * @description 统一的页面选择入口，支持客户、门店、项目、员工等多种类型选择
 *
 * @features
 * - 遵循项目统一的页面埋点架构规范
 * - 支持页面生命周期管理
 * - 页面显示状态传递
 * - 统一的页面埋点信息
 *
 * <AUTHOR> sy-chain uni-app 项目
 * @version 1.0.0
 * @since 2024-01
 */
class PageSelect extends React.Component<PageSelectProps> {
  constructor(props: PageSelectProps) {
    super(props);
    this.state = {
      pageShow: true,
    };
  }

  /**
   * 页面名称（用于埋点）
   */
  soyoungPageName() {
    const pageType = this.props.route?.params?.type || 'customer';
    return `PageSelect_${pageType}`;
  }

  /**
   * 页面埋点信息
   */
  soyoungPageInfo() {
    const pageType = this.props.route?.params?.type || 'customer';
    const pageNames: { [key: string]: string } = {
      customer: '选择客户',
      store: '选择门店',
      project: '选择项目',
      employee: '选择员工',
      consultant: '选择咨询师',
      therapist: '选择治疗师',
    };

    return {
      page_name: pageNames[pageType] || '页面选择',
      page_type: `select_${pageType}`,
    };
  }

  /**
   * 页面显示生命周期
   */
  didAppear() {
    this.setState({ pageShow: true });
  }

  /**
   * 页面隐藏生命周期
   */
  willDisappear() {
    this.setState({ pageShow: false });
  }

  render() {
    return (
      <PageSelectTarget
        {...this.props}
        pageShowFlag={(this.state as any).pageShow}
      />
    );
  }
}

export default PageSelect;
