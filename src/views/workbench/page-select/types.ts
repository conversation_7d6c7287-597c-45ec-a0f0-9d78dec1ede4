export interface PageSelectProps {
  pageShowFlag?: boolean;
  navigation?: {
    navigate: (route: string, params?: Record<string, unknown>) => void;
    goBack: () => void;
    setOptions?: (options: Record<string, unknown>) => void;
  };
  route?: {
    params?: {
      type?: string;
      selectedId?: string;
      storeId?: string;
      customerId?: string;
      reserveType?: string;
      [key: string]: unknown;
    };
  };
  [key: string]: unknown;
}

// 基础选择项接口
export interface SelectItem {
  id: number;
  name: string;
  [key: string]: unknown;
}

// 客户接口，继承自SelectItem
export interface Customer extends SelectItem {
  phone: string;
  masked_mobile: string;
  gender: string;
  age: string;
}

// 门店接口，继承自SelectItem
export interface Store extends SelectItem {
  storeId: string;
  cashback?: string;
}

// 项目接口，基于新的接口数据结构
export interface Project {
  id?: number; // 使用 product_id 作为 id
  name?: string; // 使用 title 作为 name
  title?: string; // 项目标题
  product_id?: number; // 产品ID
  order_type?: number; // 订单类型
  order_id?: string; // 订单ID
  display_order_id?: string; // 显示订单ID
  total_times?: number; // 总次数
  left_times?: number; // 剩余次数
  top_order_id?: string; // 顶级订单ID
  is_tyg_order?: number; // 是否为体验官订单
  cashback?: string; // 返现信息
  // 兼容旧字段
  price?: number;
  duration?: number;
}

// 员工接口，继承自SelectItem
export interface Employee extends SelectItem {
  position: string;
  avatar?: string;
}

// 咨询师接口，基于接口数据结构
export interface Consultant extends SelectItem {
  reserved_cnt: number; // 该时段确认预约用户数
  label: string; // 标签信息，显示预约统计
}

// 治疗师接口，基于接口数据结构
export interface Therapist extends SelectItem {
  big_photoelectric_cnt: number; // 大光电确认数量
  big_injection_cnt: number; // 大注射确认数量
  other_cnt: number; // 其他确认数量
  label: string; // 标签信息，显示确认统计
}

export enum PageType {
  CUSTOMER = 'customer',
  EMPLOYEE = 'employee',
  STORE = 'store',
  PROJECT = 'project',
  CONSULTANT = 'consultant',
  THERAPIST = 'therapist',
}

export interface PageConfig {
  title: string;
  placeholder: string;
  searchKey: string;
  emptyText: string;
}
