import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableWithoutFeedback,
  Image,
  RefreshControl,
  DeviceEventEmitter,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { back } from '@soyoung/react-native-base';
import Header from '@/components/header';
import { usePageSelectApi } from './hooks/usePageSelectApi';
import ResultItemCustomer from './components/ResultItemCustomer';
import ResultItemStore from './components/ResultItemStore';
import ResultItemProject from './components/ResultItemProject';
import ResultItemEmployee from './components/ResultItemEmployee';
import ResultItemConsultant from './components/ResultItemConsultant';
import ResultItemTherapist from './components/ResultItemTherapist';
import {
  PageSelectSkeleton,
  cleanupPageSelectSkeletonAnimation,
} from './components/PageSelectSkeleton';
import {
  PageSelectProps,
  PageType,
  PageConfig,
  SelectItem,
  Customer,
  Store,
  Project,
  Employee,
  Consultant,
  Therapist,
} from './types';
import { styles } from './styles';

/**
 * 页面选择组件（Target）
 *
 * @description 统一的选择页面，支持客户、门店、项目、员工等多种类型选择
 *
 * @features
 * - 支持搜索和分页加载
 * - 统一的页面配置和组件渲染
 * - 去掉所有圆角，采用方形设计
 * - 门店列表特殊样式（345x54px，#EBFBDC背景，#61B43E边框）
 * - 客户头像背景色 #61B43E
 * - 支持下拉刷新和上拉加载更多
 *
 * <AUTHOR> sy-chain uni-app 项目
 * @version 1.0.0
 * @since 2024-01
 */
const PageSelectTarget: React.FC<PageSelectProps> = ({
  pageShowFlag,
  navigation,
  route,
  ..._props
}) => {
  // 正确解析嵌套的参数结构：route?.params?.params?.type
  const getRouteParam = (key: string): string | null => {
    const params = route?.params as Record<string, unknown> | undefined;
    const nestedParams = params?.params as Record<string, unknown> | undefined;
    const value = nestedParams?.[key] || params?.[key];
    return typeof value === 'string' ? value : null;
  };

  // 使用 useState 管理 pageType，确保参数变化能触发重新渲染
  const [pageType, setPageType] = useState<string>(
    getRouteParam('type') || PageType.CUSTOMER
  );
  const selectedId =
    getRouteParam('selectedId') || route?.params?.selectedId || null;
  const storeId = getRouteParam('storeId') || '';
  const customerId = getRouteParam('customerId') || '';
  const reserveType = getRouteParam('reserveType') || '';
  const productId = getRouteParam('productId') || '';
  const orderId = getRouteParam('orderId') || '';
  const topOrderId = getRouteParam('topOrderId') || '';
  const startTime = getRouteParam('startTime') || '';
  const endTime = getRouteParam('endTime') || '';

  // 监听路由参数变化，实时更新页面类型
  useEffect(() => {
    const newPageType = getRouteParam('type') || PageType.CUSTOMER;

    if (newPageType !== pageType) {
      setPageType(newPageType || PageType.CUSTOMER);
      if (__DEV__) {
        console.log('PageSelectTarget - 页面类型变化:', {
          oldPageType: pageType,
          newPageType,
        });
      }
    }
  }, [route?.params, pageType]);

  // 调试信息（仅在有selectedId时输出）
  if (__DEV__ && selectedId) {
    console.log('PageSelectTarget - 页面状态（有选中ID）:', {
      pageType,
      selectedId,
      selectedIdType: typeof selectedId,
    });
  }

  // 页面配置
  const PAGE_CONFIG: { [key: string]: PageConfig } = {
    [PageType.CUSTOMER]: {
      title: '选择客户',
      placeholder: '输入姓名、手机号或尾号搜索',
      searchKey: 'customerKeyword',
      emptyText: '暂无客户信息',
    },
    [PageType.EMPLOYEE]: {
      title: '选择员工',
      placeholder: '输入姓名搜索',
      searchKey: 'employeeKeyword',
      emptyText: '暂无员工信息',
    },
    [PageType.STORE]: {
      title: '选择门店',
      placeholder: '输入门店名称',
      searchKey: 'storeKeyword',
      emptyText: '暂无门店信息',
    },
    [PageType.PROJECT]: {
      title: '选择项目',
      placeholder: '输入项目名称搜索',
      searchKey: 'projectKeyword',
      emptyText: '暂无项目信息',
    },
    [PageType.CONSULTANT]: {
      title: '选择咨询师',
      placeholder: '输入咨询师姓名搜索',
      searchKey: 'consultantKeyword',
      emptyText: '暂无咨询师信息',
    },
    [PageType.THERAPIST]: {
      title: '选择治疗师',
      placeholder: '输入治疗师姓名搜索',
      searchKey: 'therapistKeyword',
      emptyText: '暂无治疗师信息',
    },
  };

  const currentConfig = PAGE_CONFIG[pageType] || PAGE_CONFIG[PageType.CUSTOMER];

  // 状态管理
  const [searchKeyword, setSearchKeyword] = useState('');
  // 防抖/竞态控制：仅应用最后一次请求的结果
  const requestCounterRef = useRef(0);
  const activeRequestRef = useRef(0);
  const [resultList, setResultList] = useState<SelectItem[]>([]);
  const [showEmpty, setShowEmpty] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [pageNum, setPageNum] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const searchInputRef = useRef<TextInput>(null);
  const {
    loading,
    // error,
    fetchCustomers,
    fetchStores,
    fetchProjects,
    fetchEmployees,
    fetchConsultants,
    fetchTherapists,
  } = usePageSelectApi();

  // 页面初始化
  useEffect(() => {
    navigation?.setOptions?.({
      title: currentConfig.title,
    });

    // 调试：查看AsyncStorage中的数据
    if (__DEV__) {
      AsyncStorage.getItem('page_select_result')
        .then(result => {
          console.log('当前AsyncStorage中的数据:', {
            exists: !!result,
            data: result ? JSON.parse(result) : null,
          });
        })
        .catch(err => {
          console.log('读取AsyncStorage失败:', err);
        });
    }

    // 初始化页面数据，包括处理回显
    initializePageData();
  }, [pageType]);

  /**
   * 初始化页面数据，处理已选择项的回显
   */
  const initializePageData = async () => {
    if (__DEV__) {
      console.log('初始化页面数据开始:', {
        pageType,
        selectedId,
        hasSelectedId: !!selectedId,
        selectedIdValue: selectedId,
      });
    }

    try {
      // 如果有 selectedId，尝试获取已选择的项目信息进行回显
      if (selectedId) {
        if (__DEV__) {
          console.log('有选中ID，开始回显流程');
        }
        await handleItemEcho();
      } else {
        if (__DEV__) {
          console.log('没有选中ID，直接获取数据');
        }
        // 没有选中项，直接获取数据
        fetchData(false);
      }
    } catch (err) {
      console.error('初始化页面数据失败:', err);
      // 失败时仍然获取数据
      fetchData(false);
    } finally {
      // 标记初始化完成
      setIsInitialized(true);
    }
  };

  /**
   * 处理选中项回显
   */
  const handleItemEcho = async () => {
    if (__DEV__) {
      console.log('开始处理回显:', {
        pageType,
        selectedId,
        selectedIdType: typeof selectedId,
      });
    }

    try {
      // 尝试从 AsyncStorage 读取已选择的项目信息
      const savedResult = await AsyncStorage.getItem('page_select_result');

      if (__DEV__) {
        console.log('AsyncStorage 读取结果:', {
          savedResult,
          exists: !!savedResult,
        });
      }

      if (savedResult) {
        const parsedResult = JSON.parse(savedResult);
        let savedItem: SelectItem | null = null;
        let echoKeyword = '';

        if (__DEV__) {
          console.log('解析的存储数据:', parsedResult);
        }

        // 根据页面类型获取对应的已选择项
        switch (pageType) {
          case PageType.CUSTOMER:
            savedItem = parsedResult?.customer;
            if (savedItem) {
              const customer = savedItem as Customer;
              echoKeyword =
                customer.name || customer.masked_mobile || customer.phone || '';
              if (__DEV__) {
                console.log('客户回显数据:', {
                  customer,
                  echoKeyword,
                  name: customer.name,
                  masked_mobile: customer.masked_mobile,
                  phone: customer.phone,
                });
              }
            }
            break;
          case PageType.STORE:
            savedItem = parsedResult?.store;
            if (savedItem) {
              echoKeyword = savedItem.name || '';
            }
            break;
          case PageType.PROJECT:
            savedItem = parsedResult?.project;
            if (savedItem) {
              const project = savedItem as unknown as Project;
              echoKeyword = project.title || project.name || '';
            }
            break;
          case PageType.EMPLOYEE:
            savedItem = parsedResult?.employee;
            if (savedItem) {
              echoKeyword = savedItem.name || '';
            }
            break;
          case PageType.CONSULTANT:
            savedItem = parsedResult?.consultant;
            if (savedItem) {
              echoKeyword = savedItem.name || '';
            }
            break;
          case PageType.THERAPIST:
            savedItem = parsedResult?.therapist;
            if (savedItem) {
              echoKeyword = savedItem.name || '';
            }
            break;
        }

        if (__DEV__) {
          console.log('回显匹配检查:', {
            savedItem,
            savedItemId: savedItem?.id,
            savedItemIdType: typeof savedItem?.id,
            selectedId,
            selectedIdType: typeof selectedId,
            stringMatch: String(savedItem?.id) === String(selectedId),
            echoKeyword,
            hasKeyword: !!echoKeyword,
          });
        }

        // 修复ID匹配问题：统一转换为字符串进行比较
        if (
          savedItem &&
          String(savedItem.id) === String(selectedId) &&
          echoKeyword
        ) {
          // 找到匹配的项目，设置搜索关键字
          setSearchKeyword(echoKeyword);
          // 延迟设置搜索框的值，确保组件已渲染
          setTimeout(() => {
            if (searchInputRef.current) {
              searchInputRef.current.setNativeProps({ text: echoKeyword });
              if (__DEV__) {
                console.log('搜索框设置完成:', { echoKeyword });
              }
            } else {
              if (__DEV__) {
                console.log('搜索框引用不存在');
              }
            }
          }, 100);

          if (__DEV__) {
            console.log('选中项回显成功:', {
              pageType,
              savedItem,
              echoKeyword,
              selectedId,
            });
          }

          // 直接传递echoKeyword进行搜索，避免状态更新延迟问题
          fetchData(false, echoKeyword);
          return; // 提前返回，避免执行下面的默认fetchData
        } else {
          if (__DEV__) {
            console.log('回显条件不满足，跳过回显:', {
              hasSavedItem: !!savedItem,
              savedItemId: savedItem?.id,
              selectedId: selectedId,
              stringMatch: String(savedItem?.id) === String(selectedId),
              hasEchoKeyword: !!echoKeyword,
              echoKeyword,
            });
          }
        }
      } else {
        if (__DEV__) {
          console.log('AsyncStorage 中没有保存的选择结果');
        }
      }

      // 获取数据（没有回显关键字时的默认调用）
      fetchData(false);
    } catch (err) {
      console.error('处理选中项回显失败:', err);
      fetchData(false);
    }
  };

  // 页面聚焦时重新获取数据 (跳过初始化时的调用)
  const [isInitialized, setIsInitialized] = useState(false);

  useFocusEffect(
    useCallback(() => {
      // 只有在没有搜索关键词的情况下才重新获取数据
      // 如果有搜索关键词，说明是回显或用户搜索的结果，不应该被覆盖
      if (pageShowFlag && isInitialized && !searchKeyword) {
        fetchData(false);
      }
    }, [pageShowFlag, isInitialized, searchKeyword])
  );

  // 清理骨架屏动画
  useEffect(() => {
    return () => {
      cleanupPageSelectSkeletonAnimation();
    };
  }, []);

  /**
   * 重置分页参数
   */
  const resetPagination = () => {
    setPageNum(1);
    setHasMore(true);
    setShowEmpty(false);
  };

  /**
   * 获取数据
   */
  const fetchData = async (loadMore: boolean = false, keyword?: string) => {
    try {
      if (loadMore) {
        setLoadingMore(true);
      }

      // 解析路由参数，支持嵌套结构
      const nestedParams = (route?.params as any)?.params || {};
      const currentPageNum = loadMore ? pageNum + 1 : 1;
      // 使用传入的keyword参数，如果没有则使用状态中的searchKeyword
      const currentKeyword = keyword !== undefined ? keyword : searchKeyword;

      // 记录并声明当前请求token，确保只应用最新一次请求结果
      const reqToken = ++requestCounterRef.current;
      activeRequestRef.current = reqToken;
      let response;

      switch (pageType) {
        case PageType.CUSTOMER:
          response = await fetchCustomers(currentKeyword, currentPageNum);
          break;
        case PageType.STORE:
          response = await fetchStores(currentKeyword, currentPageNum);
          break;
        case PageType.PROJECT:
          const projectParams = {
            customer_id: customerId, // 客户ID（从客户接口的customer_id_str字段获取并保存）
            tenant_id: storeId,
            order_type: reserveType || '3', // 直接使用路由传递的reserveType值，4=有单，3=无单
          };

          console.log('[PROJECT DEBUG] 调用fetchProjects参数:', {
            currentKeyword,
            currentPageNum,
            pageSize: 10,
            projectParams,
            allRouteParams: route?.params,
          });

          response = await fetchProjects(
            currentKeyword,
            currentPageNum,
            10, // pageSize
            projectParams
          );

          console.log('[PROJECT DEBUG] fetchProjects响应:', response);
          break;
        case PageType.EMPLOYEE:
          response = await fetchEmployees(
            currentKeyword,
            undefined,
            currentPageNum
          );
          break;
        case PageType.CONSULTANT:
          const consultantParams = {
            date: nestedParams?.date || route?.params?.date,
            tenant_id: storeId,
            order_type:
              nestedParams?.orderType ||
              route?.params?.orderType ||
              reserveType ||
              '3',
            customer_id:
              nestedParams?.customerId ||
              route?.params?.customerId ||
              customerId, // 使用customer_id_str
            product_id:
              nestedParams?.productId || route?.params?.productId || productId,
            order_id:
              nestedParams?.orderId || route?.params?.orderId || orderId,
            top_order_id:
              nestedParams?.topOrderId ||
              route?.params?.topOrderId ||
              topOrderId,
            start_time:
              nestedParams?.startTime || route?.params?.startTime || startTime,
            end_time:
              nestedParams?.endTime || route?.params?.endTime || endTime,
            keyword: '',
          };

          if (__DEV__) {
            console.log('[CONSULTANT DEBUG] 调用fetchConsultants参数:', {
              currentKeyword,
              currentPageNum,
              pageSize: 10,
              consultantParams,
              allRouteParams: route?.params,
            });
          }

          response = await fetchConsultants(
            currentKeyword,
            currentPageNum,
            10, // pageSize
            consultantParams
          );

          if (__DEV__) {
            console.log('[CONSULTANT DEBUG] fetchConsultants响应:', response);
          }
          break;
        case PageType.THERAPIST:
          const therapistParams = {
            date: nestedParams?.date || route?.params?.date,
            tenant_id: storeId,
            order_type:
              nestedParams?.orderType ||
              route?.params?.orderType ||
              reserveType ||
              '3',
            customer_id:
              nestedParams?.customerId ||
              route?.params?.customerId ||
              customerId, // 与接口保持一致
            product_id:
              nestedParams?.productId || route?.params?.productId || productId,
            order_id:
              nestedParams?.orderId || route?.params?.orderId || orderId,
            top_order_id:
              nestedParams?.topOrderId ||
              route?.params?.topOrderId ||
              topOrderId,
            start_time:
              nestedParams?.startTime || route?.params?.startTime || startTime,
            end_time:
              nestedParams?.endTime || route?.params?.endTime || endTime,
            keyword: '',
          };

          if (__DEV__) {
            console.log('[THERAPIST DEBUG] 调用fetchTherapists参数:', {
              currentKeyword,
              currentPageNum,
              pageSize: 10,
              therapistParams,
              allRouteParams: route?.params,
            });
          }

          response = await fetchTherapists(
            currentKeyword,
            currentPageNum,
            10, // pageSize
            therapistParams
          );

          if (__DEV__) {
            console.log('[THERAPIST DEBUG] fetchTherapists响应:', response);
          }
          break;
        default:
          response = { list: [], total: 0, hasMore: false };
      }

      if (__DEV__) {
        console.log('[DEBUG] 处理API响应数据:', {
          pageType,
          responseList: response.list,
          listLength: response.list?.length || 0,
          hasMore: response.hasMore,
          loadMore,
        });
      }

      // 如果在等待过程中有新的请求发起，则丢弃本次结果，避免旧结果覆盖新结果
      if (reqToken !== activeRequestRef.current) {
        if (__DEV__) {
          console.log('[DEBUG] 丢弃过期请求结果:', {
            pageType,
            currentKeyword,
            reqToken,
            active: activeRequestRef.current,
          });
        }
        return;
      }

      if (loadMore) {
        setResultList(prev => {
          const newList = [...prev, ...response.list];
          if (__DEV__) {
            console.log('[DEBUG] 加载更多后的列表:', newList.length);
          }
          return newList;
        });
        setPageNum(currentPageNum);
      } else {
        setResultList(response.list);
        if (__DEV__) {
          console.log('[DEBUG] 设置新列表:', {
            newListLength: response.list?.length || 0,
            firstItem: response.list?.[0],
          });
        }
        setPageNum(1);
        resetPagination();
      }

      setHasMore(response.hasMore);
      setShowEmpty(response.list.length === 0 && !loadMore);
    } catch (err) {
      console.error('获取数据失败:', err);
    } finally {
      setLoadingMore(false);
    }
  };

  /**
   * 加载更多数据
   */
  const loadMoreData = () => {
    if (!loadingMore && hasMore && !loading) {
      fetchData(true);
    }
  };

  /**
   * 搜索
   */
  const handleSearch = () => {
    resetPagination();
    fetchData(false);
  };

  /**
   * 输入框内容变化
   */
  const handleInput = (text: string) => {
    setSearchKeyword(text);
    // 当输入被清空时，立即刷新列表，避免保留旧的搜索结果
    if (text.trim() === '') {
      resetPagination();
      // 传入空关键词，触发各类型页面的默认刷新逻辑
      // 使用setTimeout切到下一帧，确保searchKeyword状态与UI同步后再发请求，避免沿用旧keyword
      setTimeout(() => {
        fetchData(false, '');
      }, 0);
    }
  };

  /**
   * 专门处理项目选择
   */
  const handleProjectSelect = async (project: Project) => {
    try {
      // 构建项目选择返回结果，确保字段映射正确
      const mappedProject = {
        // 新建预约页面期望的字段
        id: project.product_id, // 使用 product_id 作为 id
        name: project.title || project.name, // 使用 title 作为 name
        // 保留原有字段供其他地方使用
        title: project.title,
        product_id: project.product_id,
        order_id: project.order_id || '',
        display_order_id: project.display_order_id || '',
        top_order_id: project.top_order_id || '',
        order_type: project.order_type,
        total_times: project.total_times,
        left_times: project.left_times,
        is_tyg_order: project.is_tyg_order,
        cashback: project.cashback,
      };

      const result = { project: mappedProject };

      // 保存选择结果到 AsyncStorage（合并已有结果，避免覆盖其它类型选择）
      try {
        const prevStr = await AsyncStorage.getItem('page_select_result');
        const prevObj = prevStr ? JSON.parse(prevStr) : {};
        const merged = { ...prevObj, ...result };
        await AsyncStorage.setItem(
          'page_select_result',
          JSON.stringify(merged)
        );
        console.log('PageSelectTarget - 合并保存项目选择结果:', merged);
      } catch (mergeErr) {
        console.warn('PageSelectTarget - 合并保存失败，回退为直接保存项目结果');
        await AsyncStorage.setItem(
          'page_select_result',
          JSON.stringify(result)
        );
      }

      console.log('PageSelectTarget - 映射后的项目数据:', mappedProject);

      // 使用 DeviceEventEmitter 事件机制进行数据回传
      DeviceEventEmitter.emit('page_select_result', {
        type: pageType,
        data: result,
        timestamp: Date.now(),
      });

      // 返回上一页
      back();
    } catch (err) {
      console.error('保存项目选择结果失败:', err);
    }
  };

  /**
   * 选择项目（通用方法）
   */
  const handleSelect = async (item: SelectItem) => {
    try {
      // 根据页面类型构建返回结果，参考原工程逻辑
      let result: Record<string, SelectItem> = {};
      switch (pageType) {
        case PageType.CUSTOMER:
          result = { customer: item };
          break;
        case PageType.STORE:
          result = { store: item };
          break;
        case PageType.PROJECT:
          result = { project: item };
          break;
        case PageType.EMPLOYEE:
          result = { employee: item };
          break;
        case PageType.CONSULTANT:
          result = { consultant: item };
          break;
        case PageType.THERAPIST:
          result = { therapist: item };
          break;
        default:
          result = { item };
      }

      // 保存选择结果到 AsyncStorage（合并已有结果，避免覆盖其它类型选择），使用统一的key
      try {
        const prevStr = await AsyncStorage.getItem('page_select_result');
        const prevObj = prevStr ? JSON.parse(prevStr) : {};
        const merged = { ...prevObj, ...result };
        await AsyncStorage.setItem(
          'page_select_result',
          JSON.stringify(merged)
        );

        if (__DEV__) {
          console.log('PageSelectTarget - 合并保存选择结果:', {
            pageType,
            item,
            merged,
            mergedString: JSON.stringify(merged),
            selectedItemId: item.id,
          });
        }
      } catch (mergeErr) {
        // 合并失败则退回到直接覆盖保存，保证功能可用
        await AsyncStorage.setItem(
          'page_select_result',
          JSON.stringify(result)
        );

        if (__DEV__) {
          console.warn('PageSelectTarget - 合并保存失败，已回退为直接保存:', {
            pageType,
            item,
            result,
          });
        }
      }

      // 统一使用 DeviceEventEmitter 事件机制进行数据回传
      DeviceEventEmitter.emit('page_select_result', {
        type: pageType,
        data: result,
        timestamp: Date.now(),
      });

      // 返回上一页
      back();
    } catch (err) {
      console.error('保存选择结果失败:', err);
    }
  };

  /**
   * 清空输入框
   */
  const handleClearInput = () => {
    setSearchKeyword('');
    searchInputRef.current?.clear();
    resetPagination();
    // 传入空关键词，避免异步状态导致沿用旧关键词
    fetchData(false, '');
  };

  /**
   * 下拉刷新
   */
  const handleRefresh = async () => {
    setRefreshing(true);
    resetPagination();
    await fetchData(false);
    setRefreshing(false);
  };

  /**
   * 渲染列表项
   */
  const renderItem = (item: SelectItem, _index: number) => {
    // 针对不同页面类型使用不同的ID比较逻辑
    let isSelected = false;

    if (selectedId) {
      switch (pageType) {
        case PageType.PROJECT:
          // 项目类型：使用 display_order_id 进行比较
          const projectItem = item as unknown as Project;
          isSelected =
            String(projectItem.display_order_id) === String(selectedId);
          break;
        default:
          // 其他类型：使用 item.id 进行比较
          isSelected = String(item.id) === String(selectedId);
          break;
      }
    }

    // 调试信息
    if (__DEV__ && selectedId) {
      console.log('PageSelectTarget - 渲染列表项选中状态:', {
        pageType,
        itemId: item.id,
        itemIdType: typeof item.id,
        projectDisplayOrderId:
          pageType === PageType.PROJECT
            ? (item as unknown as Project).display_order_id
            : 'N/A',
        selectedId,
        selectedIdType: typeof selectedId,
        isSelected,
        comparisonType:
          pageType === PageType.PROJECT ? 'display_order_id' : 'item.id',
      });
    }

    switch (pageType) {
      case PageType.CUSTOMER:
        return (
          <ResultItemCustomer
            key={item.id}
            customer={item as Customer}
            selected={isSelected}
            onSelect={handleSelect}
          />
        );
      case PageType.STORE:
        return (
          <ResultItemStore
            key={item.id}
            store={item as Store}
            selected={isSelected}
            onSelect={handleSelect}
          />
        );
      case PageType.PROJECT:
        return (
          <ResultItemProject
            key={item.id}
            project={item as unknown as Project}
            selected={isSelected}
            onSelect={handleProjectSelect}
          />
        );
      case PageType.EMPLOYEE:
        return (
          <ResultItemEmployee
            key={item.id}
            employee={item as Employee}
            selected={isSelected}
            onSelect={handleSelect}
            type='employee'
          />
        );
      case PageType.CONSULTANT:
        return (
          <ResultItemConsultant
            key={item.id}
            consultant={item as Consultant}
            selected={isSelected}
            onSelect={handleSelect}
          />
        );
      case PageType.THERAPIST:
        return (
          <ResultItemTherapist
            key={item.id}
            therapist={item as Therapist}
            selected={isSelected}
            onSelect={handleSelect}
          />
        );
      default:
        return null;
    }
  };

  /**
   * 渲染空状态
   */
  const renderEmpty = () => {
    if (loading && resultList.length === 0) {
      return <PageSelectSkeleton pageType={pageType} count={8} />;
    }

    if (showEmpty) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>{currentConfig.emptyText}</Text>
        </View>
      );
    }

    return null;
  };

  /**
   * 渲染加载更多
   */
  const renderLoadMore = () => {
    // 如果没有更多数据且列表有内容，显示"没有更多啦"
    if (!hasMore && resultList.length > 0) {
      return (
        <View style={styles.loadingFooter}>
          <Text style={styles.loadingText}>没有更多啦</Text>
        </View>
      );
    }

    // 如果没有更多数据且列表为空，不显示任何内容
    if (!hasMore) return null;

    // 如果正在加载更多，显示加载指示器和文案
    if (loadingMore) {
      return (
        <View style={styles.loadingFooter}>
          <ActivityIndicator size='small' color='#61B43E' />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      );
    }

    return null;
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <View style={styles.content}>
        {/* Header */}
        <Header
          title={currentConfig.title}
          zIndex={102}
          bgColor='#FFFFFF'
          hideBack={false}
        />

        {/* 搜索框 */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Image
              source={{
                uri: 'https://static.soyoung.com/sy-design/jsc2u0zt10171745301997998.png',
              }}
              style={styles.searchIcon}
              resizeMode='contain'
            />
            <TextInput
              ref={searchInputRef}
              style={styles.searchInput}
              placeholder={currentConfig.placeholder}
              placeholderTextColor='#999999'
              value={searchKeyword}
              onChangeText={handleInput}
              onSubmitEditing={handleSearch}
              returnKeyType='search'
            />
            {searchKeyword ? (
              <TouchableWithoutFeedback onPress={handleClearInput}>
                <View style={styles.clearButton}>
                  <Image
                    source={{
                      uri: 'https://static.soyoung.com/sy-design/13dwj7cauhbgx1746523095118.png',
                    }}
                    style={styles.clearIcon}
                    resizeMode='contain'
                  />
                </View>
              </TouchableWithoutFeedback>
            ) : null}
          </View>
        </View>

        {/* 结果列表 */}
        <View style={styles.listContainer}>
          <ScrollView
            style={{ flex: 1 }}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={['#00ab84']}
                tintColor='#00ab84'
              />
            }
            onScrollEndDrag={e => {
              const { nativeEvent } = e;
              const isCloseToBottom =
                nativeEvent.layoutMeasurement.height +
                  nativeEvent.contentOffset.y >=
                nativeEvent.contentSize.height - 20;
              if (isCloseToBottom) {
                loadMoreData();
              }
            }}
            onMomentumScrollEnd={e => {
              const { nativeEvent } = e;
              const isCloseToBottom =
                nativeEvent.layoutMeasurement.height +
                  nativeEvent.contentOffset.y >=
                nativeEvent.contentSize.height - 20;
              if (isCloseToBottom) {
                loadMoreData();
              }
            }}
          >
            {resultList.map(renderItem)}
            {renderLoadMore()}
            {renderEmpty()}
          </ScrollView>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default PageSelectTarget;
