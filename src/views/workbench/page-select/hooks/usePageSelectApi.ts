import { useState, useCallback } from 'react';
import api, { FetchModule } from '../../../../common/api';
import { PageType } from '../types';

/**
 * 页面选择API相关的自定义Hook
 * 迁移自 sy-chain 原工程，使用 pagefetch 方法
 * 支持客户、门店、项目、员工、咨询师、治疗师列表获取
 */
export const usePageSelectApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * 统一的数据获取方法，迁移自 sy-chain fetchData 逻辑
   */
  const fetchData = useCallback(
    async (
      pageType: PageType,
      keyword: string = '',
      pageNum: number = 1,
      pageSize: number = 10,
      pageOptions: Record<string, unknown> = {}
    ) => {
      try {
        setLoading(true);
        setError(null);

        let response;

        switch (pageType) {
          case PageType.CUSTOMER:
            // 客户搜索接口，没有关键词时不请求
            if (!keyword) {
              return { list: [], hasMore: false };
            }
            response = await api.pagefetch({
              path: 'chain-wxapp/v1/reserve/searchCustomer',
              method: FetchModule.Method.POST,
              params: {
                query: keyword,
                page: pageNum,
                size: pageSize,
              },
            });
            break;

          case PageType.EMPLOYEE:
            // 员工搜索接口
            response = await api.pagefetch({
              path: 'chain-wxapp/v1/reserve/searchEmployee',
              method: FetchModule.Method.POST,
              params: {
                keyword,
                page: pageNum,
                size: pageSize,
              },
            });
            break;

          case PageType.STORE:
            // 门店搜索接口
            const storeParams: Record<string, unknown> = {
              keyword,
              page: pageNum,
              size: pageSize,
            };
            // 添加可选参数
            if (pageOptions.order_type) {
              storeParams.order_type = pageOptions.order_type;
            }
            if (pageOptions.order_id) {
              storeParams.order_id = pageOptions.order_id;
            }
            if (pageOptions.top_order_id) {
              storeParams.top_order_id = pageOptions.top_order_id;
            }
            if (pageOptions.product_id) {
              storeParams.product_id = pageOptions.product_id;
            }
            if (pageOptions.reserve_id) {
              storeParams.reserve_id = pageOptions.reserve_id;
            }

            response = await api.pagefetch({
              path: 'chain-wxapp/v1/reserve/tenantList',
              method: FetchModule.Method.POST,
              params: storeParams,
            });
            break;

          case PageType.PROJECT:
            // 项目搜索接口
            response = await api.pagefetch({
              path: 'chain-wxapp/v1/reserve/getReserveProducts',
              method: FetchModule.Method.POST,
              params: {
                keyword,
                order_type: pageOptions.order_type, // 预约类型：4=有单，3=无单
                customer_id: pageOptions.customer_id, // 客户ID（前端将customer_id_str保存为customer_id）
                tenant_id: pageOptions.tenant_id, // 门店ID
                page: pageNum,
                size: pageSize,
              },
            });
            break;

          case PageType.CONSULTANT:
            // 咨询师搜索接口
            response = await api.pagefetch({
              path: 'chain-wxapp/v1/reserve/getDoctorAndConsult',
              method: FetchModule.Method.POST,
              params: {
                keyword,
                date: pageOptions.date,
                tenant_id: pageOptions.tenant_id,
                order_type: pageOptions.order_type,
                customer_id: pageOptions.customer_id,
                start_time: pageOptions.start_time,
                end_time: pageOptions.end_time,
                order_id: pageOptions.order_id,
                top_order_id: pageOptions.top_order_id,
                product_id: pageOptions.product_id,
                page: pageNum,
                size: pageSize,
              },
            });
            break;

          case PageType.THERAPIST:
            // 治疗师搜索接口
            response = await api.pagefetch({
              path: 'chain-wxapp/v1/reserve/getDoctorAndConsult',
              method: FetchModule.Method.POST,
              params: {
                keyword,
                date: pageOptions.date,
                tenant_id: pageOptions.tenant_id,
                order_type: pageOptions.order_type,
                customer_id: pageOptions.customer_id,
                start_time: pageOptions.start_time,
                end_time: pageOptions.end_time,
                order_id: pageOptions.order_id,
                top_order_id: pageOptions.top_order_id,
                product_id: pageOptions.product_id,
                page: pageNum,
                size: pageSize,
              },
            });
            break;

          default:
            throw new Error(`不支持的页面类型: ${pageType}`);
        }

        if (!response || response.errorCode !== 0) {
          throw new Error(response?.errorMsg || '获取信息失败');
        }

        // 根据不同类型处理响应数据，保持与原工程一致
        let newList = [];
        let hasMore = false;

        if (pageType === PageType.CUSTOMER) {
          // 客户搜索接口特殊处理
          if (
            response.errorCode === 0 &&
            response.responseData &&
            Array.isArray(response.responseData.list)
          ) {
            // 将接口返回的customer_id_str字段映射为前端使用的customer_id字段
            newList = response.responseData.list.map((customer: any) => ({
              ...customer,
              customer_id: customer.customer_id_str || customer.customer_id, // 优先使用customer_id_str，兼容customer_id
              id:
                customer.customer_id_str || customer.customer_id || customer.id, // 确保id字段也正确
            }));
            hasMore = newList.length === pageSize;
          }
        } else if (pageType === PageType.STORE) {
          // 门店搜索接口特殊处理
          if (
            response.errorCode === 0 &&
            response.responseData &&
            Array.isArray(response.responseData)
          ) {
            newList = response.responseData;
            hasMore = newList.length === pageSize;
          }
        } else if (pageType === PageType.PROJECT) {
          // 项目搜索接口特殊处理
          if (
            response.errorCode === 0 &&
            response.responseData &&
            Array.isArray(response.responseData)
          ) {
            newList = response.responseData.map(
              (item: Record<string, unknown>) => ({
                ...item,
                tempId: `${item.id}_${Date.now()}_${Math.random()
                  .toString(36)
                  .substr(2, 9)}`,
              })
            );
            hasMore = newList.length === pageSize;
          }
        } else if (
          pageType === PageType.CONSULTANT ||
          pageType === PageType.THERAPIST
        ) {
          // 咨询师/治疗师搜索接口特殊处理
          if (response.errorCode === 0 && response.responseData) {
            // 根据页面类型选择正确的数据字段
            if (
              pageType === PageType.THERAPIST &&
              Array.isArray(response.responseData.doctor_list)
            ) {
              newList = response.responseData.doctor_list;
            } else if (
              pageType === PageType.CONSULTANT &&
              Array.isArray(response.responseData.consultant_list)
            ) {
              newList = response.responseData.consultant_list;
            }
            hasMore = newList.length === pageSize;
          }
        } else if (pageType === PageType.EMPLOYEE) {
          // 员工搜索接口特殊处理
          if (
            response.errorCode === 0 &&
            response.responseData &&
            Array.isArray(response.responseData.list)
          ) {
            newList = response.responseData.list;
            hasMore = newList.length === pageSize;
          }
        }

        return {
          list: newList,
          hasMore,
          total: response.responseData?.total || newList.length,
        };
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : '网络错误';
        setError(errorMsg);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  /**
   * 获取客户列表
   */
  const fetchCustomers = useCallback(
    async (
      keyword: string = '',
      pageNum: number = 1,
      pageSize: number = 10
    ) => {
      const result = await fetchData(
        PageType.CUSTOMER,
        keyword,
        pageNum,
        pageSize
      );
      return result;
    },
    [fetchData]
  );

  /**
   * 获取门店列表
   */
  const fetchStores = useCallback(
    async (
      keyword: string = '',
      pageNum: number = 1,
      pageSize: number = 10,
      pageOptions: Record<string, unknown> = {}
    ) => {
      const result = await fetchData(
        PageType.STORE,
        keyword,
        pageNum,
        pageSize,
        pageOptions
      );
      return result;
    },
    [fetchData]
  );

  /**
   * 获取项目列表
   */
  const fetchProjects = useCallback(
    async (
      keyword: string = '',
      pageNum: number = 1,
      pageSize: number = 10,
      pageOptions: Record<string, unknown> = {}
    ) => {
      const result = await fetchData(
        PageType.PROJECT,
        keyword,
        pageNum,
        pageSize,
        pageOptions
      );
      return result;
    },
    [fetchData]
  );

  /**
   * 获取员工列表
   */
  const fetchEmployees = useCallback(
    async (
      keyword: string = '',
      pageNum: number = 1,
      pageSize: number = 10
    ) => {
      const result = await fetchData(
        PageType.EMPLOYEE,
        keyword,
        pageNum,
        pageSize
      );
      return result;
    },
    [fetchData]
  );

  /**
   * 获取咨询师列表
   */
  const fetchConsultants = useCallback(
    async (
      keyword: string = '',
      pageNum: number = 1,
      pageSize: number = 10,
      pageOptions: Record<string, unknown> = {}
    ) => {
      const result = await fetchData(
        PageType.CONSULTANT,
        keyword,
        pageNum,
        pageSize,
        pageOptions
      );
      return result;
    },
    [fetchData]
  );

  /**
   * 获取治疗师列表
   */
  const fetchTherapists = useCallback(
    async (
      keyword: string = '',
      pageNum: number = 1,
      pageSize: number = 10,
      pageOptions: Record<string, unknown> = {}
    ) => {
      const result = await fetchData(
        PageType.THERAPIST,
        keyword,
        pageNum,
        pageSize,
        pageOptions
      );
      return result;
    },
    [fetchData]
  );

  return {
    loading,
    error,
    fetchData,
    fetchCustomers,
    fetchStores,
    fetchProjects,
    fetchEmployees,
    fetchConsultants,
    fetchTherapists,
  };
};
