import { useState, useCallback } from 'react';
import api from '../../../../common/api';

/**
 * 页面选择API相关的自定义Hook
 * 用于获取客户、门店、项目、员工、咨询师、治疗师列表
 * 使用标准的API响应结构，与useReserveApi.ts保持一致
 */
export const usePageSelectApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * 获取客户列表
   */
  const fetchCustomers = useCallback(
    async (
      keyword: string = '',
      pageNum: number = 1,
      pageSize: number = 10
    ) => {
      try {
        setLoading(true);
        setError(null);

        const response = await api.reactNativeFetch('/staff/customer/search', {
          keyword,
          pageNum,
          pageSize,
        });

        if (response.errorCode === 0) {
          return response.responseData;
        } else {
          throw new Error(response.errorMsg || '获取客户列表失败');
        }
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : '网络错误';
        setError(errorMsg);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  /**
   * 获取门店列表
   */
  const fetchStores = useCallback(
    async (
      keyword: string = '',
      pageNum: number = 1,
      pageSize: number = 10
    ) => {
      try {
        setLoading(true);
        setError(null);

        const response = await api.reactNativeFetch('/staff/store/search', {
          keyword,
          pageNum,
          pageSize,
        });

        if (response.errorCode === 0) {
          return response.responseData;
        } else {
          throw new Error(response.errorMsg || '获取门店列表失败');
        }
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : '网络错误';
        setError(errorMsg);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  /**
   * 获取项目列表
   */
  const fetchProjects = useCallback(
    async (
      keyword: string = '',
      pageNum: number = 1,
      pageSize: number = 10
    ) => {
      try {
        setLoading(true);
        setError(null);

        const response = await api.reactNativeFetch('/staff/project/search', {
          keyword,
          pageNum,
          pageSize,
        });

        if (response.errorCode === 0) {
          return response.responseData;
        } else {
          throw new Error(response.errorMsg || '获取项目列表失败');
        }
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : '网络错误';
        setError(errorMsg);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  /**
   * 获取员工列表
   */
  const fetchEmployees = useCallback(
    async (
      keyword: string = '',
      pageNum: number = 1,
      pageSize: number = 10
    ) => {
      try {
        setLoading(true);
        setError(null);

        const response = await api.reactNativeFetch('/staff/employee/search', {
          keyword,
          pageNum,
          pageSize,
        });

        if (response.errorCode === 0) {
          return response.responseData;
        } else {
          throw new Error(response.errorMsg || '获取员工列表失败');
        }
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : '网络错误';
        setError(errorMsg);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  /**
   * 获取咨询师列表
   */
  const fetchConsultants = useCallback(
    async (
      keyword: string = '',
      pageNum: number = 1,
      pageSize: number = 10
    ) => {
      try {
        setLoading(true);
        setError(null);

        const response = await api.reactNativeFetch(
          '/staff/consultant/search',
          {
            keyword,
            pageNum,
            pageSize,
          }
        );

        if (response.errorCode === 0) {
          return response.responseData;
        } else {
          throw new Error(response.errorMsg || '获取咨询师列表失败');
        }
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : '网络错误';
        setError(errorMsg);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  /**
   * 获取治疗师列表
   */
  const fetchTherapists = useCallback(
    async (
      keyword: string = '',
      pageNum: number = 1,
      pageSize: number = 10
    ) => {
      try {
        setLoading(true);
        setError(null);

        const response = await api.reactNativeFetch('/staff/therapist/search', {
          keyword,
          pageNum,
          pageSize,
        });

        if (response.errorCode === 0) {
          return response.responseData;
        } else {
          throw new Error(response.errorMsg || '获取治疗师列表失败');
        }
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : '网络错误';
        setError(errorMsg);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  return {
    loading,
    error,
    fetchCustomers,
    fetchStores,
    fetchProjects,
    fetchEmployees,
    fetchConsultants,
    fetchTherapists,
  };
};
