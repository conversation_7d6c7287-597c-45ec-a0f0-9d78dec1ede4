import React from 'react';
import { View, Text, TouchableWithoutFeedback } from 'react-native';
import { Therapist } from '../types';
import { therapistStyles } from '../styles';

interface ResultItemTherapistProps {
  therapist: Therapist;
  selected: boolean;
  onSelect: (therapist: Therapist) => void;
}

/**
 * 治疗师选择结果项组件
 *
 * @description 显示治疗师信息，包含确认预约数量统计
 *
 * @features
 * - 去掉所有边框圆角，采用方形设计
 * - 选中状态：边框颜色#61B43E，背景色#EBFBDC
 * - 显示治疗师姓名和确认预约统计信息
 * - 尺寸相比原工程rpx减半处理
 *
 * <AUTHOR>
 * @version 1.0.0
 */
const ResultItemTherapist: React.FC<ResultItemTherapistProps> = ({
  therapist,
  selected,
  onSelect,
}) => {
  const handleSelect = () => {
    onSelect(therapist);
  };

  // 获取治疗师姓名首字母作为头像显示
  const getAvatarText = () => {
    return therapist.name ? therapist.name.substring(0, 1) : '医';
  };

  return (
    <TouchableWithoutFeedback onPress={handleSelect}>
      <View
        style={[
          therapistStyles.resultItem,
          selected && therapistStyles.selectedItem,
        ]}
      >
        {/* 头像区域 */}
        <View style={therapistStyles.avatarContainer}>
          <View
            style={[
              therapistStyles.avatar,
              selected && therapistStyles.selectedAvatar,
            ]}
          >
            <Text
              style={[
                therapistStyles.avatarText,
                selected && therapistStyles.selectedAvatarText,
              ]}
            >
              {getAvatarText()}
            </Text>
          </View>
        </View>

        {/* 信息区域 */}
        <View style={therapistStyles.infoContainer}>
          <Text
            style={[
              therapistStyles.name,
              selected && therapistStyles.selectedName,
            ]}
            numberOfLines={1}
          >
            {String(therapist.name || '未知治疗师')}
          </Text>

          <Text
            style={[
              therapistStyles.details,
              selected && therapistStyles.selectedDetails,
            ]}
            numberOfLines={2}
          >
            {String(therapist.label || '')}
          </Text>
        </View>

        {/* 选中状态指示器 */}
        {selected && (
          <View style={therapistStyles.checkIcon}>
            <Text style={therapistStyles.checkText}>✓</Text>
          </View>
        )}
      </View>
    </TouchableWithoutFeedback>
  );
};

export default ResultItemTherapist;
