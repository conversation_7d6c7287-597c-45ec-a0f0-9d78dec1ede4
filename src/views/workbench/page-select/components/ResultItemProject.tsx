import React from 'react';
import { View, Text, TouchableWithoutFeedback } from 'react-native';
import { Project } from '../types';
import { projectStyles } from '../styles';

interface ResultItemProjectProps {
  project: Project;
  selected: boolean;
  onSelect: (project: Project) => void;
}

/**
 * 项目选择结果项组件
 *
 * @description 显示项目信息，包括项目名称、价格、时长等
 *
 * @features
 * - 去掉所有边框圆角，采用方形设计
 * - 支持选中状态显示
 * - 显示项目价格和时长信息
 * - 保持与原版功能一致
 *
 * <AUTHOR> sy-chain uni-app 项目
 * @version 1.0.0
 * @since 2024-01
 */
const ResultItemProject: React.FC<ResultItemProjectProps> = ({
  project,
  selected,
  onSelect,
}) => {
  const handleSelect = () => {
    onSelect(project);
  };

  // 安全地获取项目名称
  const projectTitle = String(project.title || project.name || '未知项目');

  // 安全地获取剩余次数
  const leftTimesText =
    project.left_times && Number(project.left_times) > 0
      ? ` (${project.left_times}次)`
      : '';

  // 安全地获取订单号
  const hasOrderId = Boolean(project.display_order_id || project.top_order_id);
  const orderId = hasOrderId
    ? String(
        project.top_order_id && String(project.top_order_id) !== '0'
          ? project.top_order_id
          : project.display_order_id || ''
      )
    : '';

  // 安全地获取返现信息
  const hasCashback = Boolean(
    project.cashback &&
      typeof project.cashback === 'string' &&
      project.cashback.trim() !== ''
  );

  return (
    <TouchableWithoutFeedback onPress={handleSelect}>
      <View
        style={[
          projectStyles.resultItem,
          selected && projectStyles.selectedItem,
        ]}
      >
        {/* 项目信息：项目名称+订单号 */}
        <View style={projectStyles.projectInfo}>
          <View style={projectStyles.projectTitleRow}>
            <Text
              style={[
                projectStyles.projectName,
                selected && projectStyles.selectedProjectName,
              ]}
            >
              {projectTitle}
              {leftTimesText}
            </Text>
          </View>

          {hasOrderId && (
            <View style={projectStyles.orderIdRow}>
              <Text
                style={[
                  projectStyles.orderIdLabel,
                  selected && { color: '#61B43E' },
                ]}
              >
                订单ID:
              </Text>
              <Text
                style={[
                  projectStyles.orderId,
                  selected && { color: '#61B43E' },
                ]}
              >
                {orderId}
              </Text>
            </View>
          )}
        </View>

        {hasCashback && (
          <View style={projectStyles.cashback}>
            <Text style={projectStyles.cashbackText}>
              {String(project.cashback)}
            </Text>
          </View>
        )}
      </View>
    </TouchableWithoutFeedback>
  );
};

export default ResultItemProject;
