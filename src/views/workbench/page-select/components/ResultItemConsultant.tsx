import React from 'react';
import { View, Text, TouchableWithoutFeedback } from 'react-native';
import { Consultant } from '../types';
import { consultantStyles } from '../styles';

interface ResultItemConsultantProps {
  consultant: Consultant;
  selected: boolean;
  onSelect: (consultant: Consultant) => void;
}

/**
 * 咨询师选择结果项组件
 *
 * @description 显示咨询师信息，包含该时段确认预约用户数统计
 *
 * @features
 * - 去掉所有边框圆角，采用方形设计
 * - 选中状态：边框颜色#61B43E，背景色#EBFBDC
 * - 显示咨询师姓名和该时段确认预约用户数统计
 * - 尺寸相比原工程rpx减半处理
 *
 * <AUTHOR>
 * @version 1.0.0
 */
const ResultItemConsultant: React.FC<ResultItemConsultantProps> = ({
  consultant,
  selected,
  onSelect,
}) => {
  const handleSelect = () => {
    onSelect(consultant);
  };

  // 获取咨询师姓名首字母作为头像显示
  const getAvatarText = () => {
    return consultant.name ? consultant.name.substring(0, 1) : '咨';
  };

  return (
    <TouchableWithoutFeedback onPress={handleSelect}>
      <View
        style={[
          consultantStyles.resultItem,
          selected && consultantStyles.selectedItem,
        ]}
      >
        {/* 头像区域 */}
        <View style={consultantStyles.avatarContainer}>
          <View
            style={[
              consultantStyles.avatar,
              selected && consultantStyles.selectedAvatar,
            ]}
          >
            <Text
              style={[
                consultantStyles.avatarText,
                selected && consultantStyles.selectedAvatarText,
              ]}
            >
              {getAvatarText()}
            </Text>
          </View>
        </View>

        {/* 信息区域 */}
        <View style={consultantStyles.infoContainer}>
          <Text
            style={[
              consultantStyles.name,
              selected && consultantStyles.selectedName,
            ]}
            numberOfLines={1}
          >
            {String(consultant.name || '未知咨询师')}
          </Text>

          <Text
            style={[
              consultantStyles.details,
              selected && consultantStyles.selectedDetails,
            ]}
            numberOfLines={2}
          >
            {String(consultant.label || '')}
          </Text>
        </View>

        {/* 选中状态指示器 */}
        {selected && (
          <View style={consultantStyles.checkIcon}>
            <Text style={consultantStyles.checkText}>✓</Text>
          </View>
        )}
      </View>
    </TouchableWithoutFeedback>
  );
};

export default ResultItemConsultant;
