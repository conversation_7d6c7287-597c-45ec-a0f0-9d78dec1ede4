import React from 'react';
import { View, Text, TouchableWithoutFeedback, Image } from 'react-native';
import { Employee, Consultant, Therapist } from '../types';
import { employeeStyles } from '../styles';

interface ResultItemEmployeeProps {
  employee: Employee | Consultant | Therapist;
  selected: boolean;
  onSelect: (employee: Employee | Consultant | Therapist) => void;
  type?: 'employee' | 'consultant' | 'therapist';
}

/**
 * 员工选择结果项组件（通用）
 *
 * @description 显示员工信息，支持员工、咨询师、治疗师等类型
 *
 * @features
 * - 去掉所有边框圆角，采用方形设计
 * - 支持头像显示和默认头像
 * - 支持选中状态显示
 * - 显示职位和其他详细信息
 *
 * <AUTHOR> sy-chain uni-app 项目
 * @version 1.0.0
 * @since 2024-01
 */
const ResultItemEmployee: React.FC<ResultItemEmployeeProps> = ({
  employee,
  selected,
  onSelect,
  type = 'employee',
}) => {
  const handleSelect = () => {
    onSelect(employee);
  };

  // 获取员工姓名首字母作为头像显示
  const getAvatarText = () => {
    const name = String(employee.name || '');
    return name.trim() ? name.substring(0, 1) : '员';
  };

  // 获取员工详细信息
  const getEmployeeDetails = () => {
    const details = [];

    if (type === 'consultant') {
      const consultant = employee as Consultant;
      if (consultant.specialties && consultant.specialties.length > 0) {
        details.push(`专长: ${consultant.specialties.join('、')}`);
      }
    } else if (type === 'therapist') {
      const therapist = employee as Therapist;
      if (therapist.level && String(therapist.level).trim()) {
        details.push(`等级: ${String(therapist.level).trim()}`);
      }
      if (therapist.experience && String(therapist.experience).trim()) {
        details.push(`经验: ${String(therapist.experience).trim()}年`);
      }
    }

    return details.join(' | ');
  };

  return (
    <TouchableWithoutFeedback onPress={handleSelect}>
      <View
        style={[
          employeeStyles.resultItem,
          selected && employeeStyles.selectedItem,
        ]}
      >
        {/* 员工头像 */}
        <View style={employeeStyles.avatarContainer}>
          {employee.avatar ? (
            <Image
              source={{ uri: employee.avatar }}
              style={employeeStyles.avatarImage}
              resizeMode='cover'
            />
          ) : (
            <Text style={employeeStyles.avatarText}>{getAvatarText()}</Text>
          )}
        </View>

        {/* 员工信息 */}
        <View style={employeeStyles.employeeInfo}>
          <Text style={employeeStyles.employeeName}>
            {String(employee.name || '未知员工')}
          </Text>
          <Text style={employeeStyles.employeePosition}>
            {String(employee.position || '员工')}
          </Text>
          {getEmployeeDetails() && getEmployeeDetails().length > 0 && (
            <Text style={employeeStyles.employeeDetails}>
              {getEmployeeDetails()}
            </Text>
          )}
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default ResultItemEmployee;
