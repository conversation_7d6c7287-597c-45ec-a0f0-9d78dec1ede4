/**
 * PageSelect页面骨架屏组件
 *
 * @description 为page-select页面提供现代化的骨架屏loading效果，
 *              根据不同页面类型展示相应的骨架结构
 *
 * @features
 * - 支持客户、门店、项目、治疗师、咨询师等多种类型的骨架屏
 * - 共享动画实例，性能优化
 * - 闪烁动画效果
 * - 响应式设计，适配不同列表项高度
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01
 */

import React, { useEffect, useMemo, useCallback } from 'react';
import { View, StyleSheet, Animated, AppState } from 'react-native';
import { getRealSize } from '../../../../common/utils';
import { PageType } from '../types';

// 创建共享的动画实例
const sharedSkeletonAnimation = new Animated.Value(0);
let animationLoop: Animated.CompositeAnimation | null = null;
let isAnimating = false;
let isPageVisible = true;

// 启动共享动画
const startSharedAnimation = () => {
  if (isAnimating || !isPageVisible) return;

  isAnimating = true;
  sharedSkeletonAnimation.setValue(0);

  animationLoop = Animated.loop(
    Animated.timing(sharedSkeletonAnimation, {
      toValue: 1,
      duration: 1500,
      useNativeDriver: true,
    })
  );

  animationLoop.start();
};

// 停止共享动画
const stopSharedAnimation = () => {
  if (animationLoop) {
    animationLoop.stop();
    animationLoop = null;
  }
  isAnimating = false;
};

// 暂停动画（当页面不可见时）
const pauseAnimation = () => {
  if (animationLoop) {
    animationLoop.stop();
    animationLoop = null;
  }
  isAnimating = false;
};

// 恢复动画（当页面重新可见时）
const resumeAnimation = () => {
  if (!isAnimating && isPageVisible) {
    startSharedAnimation();
  }
};

// 监听应用状态变化
const handleAppStateChange = (nextAppState: string) => {
  if (nextAppState === 'active') {
    isPageVisible = true;
    resumeAnimation();
  } else {
    isPageVisible = false;
    pauseAnimation();
  }
};

// 初始化应用状态监听
let appStateListener: { remove: () => void } | null = null;
const initAppStateListener = () => {
  if (!appStateListener) {
    appStateListener = AppState.addEventListener(
      'change',
      handleAppStateChange
    );
  }
};

// 清理应用状态监听
const cleanupAppStateListener = () => {
  if (appStateListener) {
    appStateListener.remove();
    appStateListener = null;
  }
};

interface SkeletonItemProps {
  width?: number | string;
  height?: number;
  borderRadius?: number;
  marginBottom?: number;
  marginTop?: number;
  marginLeft?: number;
  marginRight?: number;
}

const SkeletonItem: React.FC<SkeletonItemProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 4,
  marginBottom = 0,
  marginTop = 0,
  marginLeft = 0,
  marginRight = 0,
}) => {
  // 使用共享动画实例
  const opacity = useMemo(
    () =>
      sharedSkeletonAnimation.interpolate({
        inputRange: [0, 0.5, 1],
        outputRange: [0.3, 0.7, 0.3],
      }),
    []
  );

  const translateX = useMemo(
    () =>
      sharedSkeletonAnimation.interpolate({
        inputRange: [0, 1],
        outputRange: [-100, 100],
      }),
    []
  );

  const itemStyle = useMemo(
    () => ({
      width,
      height: getRealSize(height),
      borderRadius: getRealSize(borderRadius),
      marginBottom: getRealSize(marginBottom),
      marginTop: getRealSize(marginTop),
      marginLeft: getRealSize(marginLeft),
      marginRight: getRealSize(marginRight),
      backgroundColor: '#F0F0F0',
      overflow: 'hidden' as const,
    }),
    [
      width,
      height,
      borderRadius,
      marginBottom,
      marginTop,
      marginLeft,
      marginRight,
    ]
  );

  return (
    <View style={itemStyle}>
      {/* 基础背景 */}
      <View style={[StyleSheet.absoluteFill, styles.baseBackground]} />

      {/* 动画闪烁层 */}
      <Animated.View
        style={[
          StyleSheet.absoluteFill,
          styles.shimmerLayer,
          {
            opacity,
            transform: [{ translateX }],
          },
        ]}
      />
    </View>
  );
};

// 客户列表项骨架屏
const CustomerItemSkeleton: React.FC = () => {
  return (
    <View style={styles.customerItem}>
      {/* 头像 - 与customerStyles.avatarContainer一致 */}
      <SkeletonItem width={50} height={50} borderRadius={25} marginRight={12} />

      {/* 信息区域 - 与customerStyles.customerInfo一致 */}
      <View style={styles.customerInfo}>
        {/* 姓名和性别年龄行 - 与customerStyles.customerNameContainer一致 */}
        <View style={styles.customerNameContainer}>
          <SkeletonItem width={60} height={16} marginRight={10} />
          <SkeletonItem width={50} height={12} />
        </View>

        {/* 手机号 - 与customerStyles.customerPhone一致 */}
        <SkeletonItem width={120} height={12} />
      </View>
    </View>
  );
};

// 门店/项目列表项骨架屏
const StoreItemSkeleton: React.FC = () => {
  return (
    <View style={styles.storeItem}>
      <SkeletonItem width={'80%'} height={16} />
    </View>
  );
};

// 治疗师/咨询师列表项骨架屏
const StaffItemSkeleton: React.FC = () => {
  return (
    <View style={styles.staffItem}>
      {/* 头像 - 与therapistStyles.avatar一致 */}
      <SkeletonItem width={40} height={40} borderRadius={20} marginRight={12} />

      {/* 信息区域 - 与therapistStyles.infoContainer一致 */}
      <View style={styles.staffInfo}>
        {/* 姓名 - 与therapistStyles.name一致 */}
        <SkeletonItem width={80} height={14} marginBottom={2} />

        {/* 详情信息 - 与therapistStyles.details一致 */}
        <SkeletonItem width={150} height={12} />
      </View>
    </View>
  );
};

interface PageSelectSkeletonProps {
  pageType: string;
  count?: number;
}

/**
 * PageSelect页面骨架屏主组件
 */
const PageSelectSkeleton: React.FC<PageSelectSkeletonProps> = ({
  pageType,
  count = 8,
}) => {
  // 启动共享动画和初始化应用状态监听
  useEffect(() => {
    initAppStateListener();
    startSharedAnimation();

    return () => {
      // 注意：这里不停止动画，因为可能有其他骨架屏在使用
      // 动画会在所有骨架屏都卸载时自动停止
    };
  }, []);

  // 根据页面类型渲染不同的骨架屏
  const renderSkeletonItem = useCallback(() => {
    switch (pageType) {
      case PageType.CUSTOMER:
        return <CustomerItemSkeleton />;
      case PageType.STORE:
      case PageType.PROJECT:
        return <StoreItemSkeleton />;
      case PageType.CONSULTANT:
      case PageType.THERAPIST:
      case PageType.EMPLOYEE:
        return <StaffItemSkeleton />;
      default:
        return <CustomerItemSkeleton />;
    }
  }, [pageType]);

  const skeletonItems = useMemo(
    () =>
      Array.from({ length: count }, (_, index) => (
        <View key={`skeleton-${index}`} style={styles.skeletonItemContainer}>
          {renderSkeletonItem()}
        </View>
      )),
    [count, renderSkeletonItem]
  );

  return <View style={styles.container}>{skeletonItems}</View>;
};

// 小的加载更多骨架屏
const LoadMoreSkeleton: React.FC = () => {
  useEffect(() => {
    initAppStateListener();
    startSharedAnimation();
  }, []);

  return (
    <View style={styles.loadMoreContainer}>
      <SkeletonItem width={60} height={12} marginRight={8} />
      <SkeletonItem width={40} height={12} />
    </View>
  );
};

// 添加一个清理函数，用于在页面完全卸载时停止动画
export const cleanupPageSelectSkeletonAnimation = () => {
  stopSharedAnimation();
  cleanupAppStateListener();
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  skeletonItemContainer: {
    // 不设置marginBottom，让每个列表项自己控制间距
  },

  // 客户列表项样式 - 与customerStyles.resultItem一致
  customerItem: {
    backgroundColor: '#ffffff',
    borderRadius: 0, // 去掉圆角
    padding: getRealSize(15),
    marginBottom: getRealSize(10),
    flexDirection: 'row',
    alignItems: 'center',
  },

  // 客户信息区域 - 与customerStyles.customerInfo一致
  customerInfo: {
    flex: 1,
    flexDirection: 'column',
  },

  // 客户姓名容器 - 与customerStyles.customerNameContainer一致
  customerNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getRealSize(8),
  },

  // 门店/项目列表项样式 - 与storeStyles.resultItem一致
  storeItem: {
    backgroundColor: '#ffffff',
    borderRadius: 0, // 去掉圆角
    borderWidth: getRealSize(1),
    borderColor: '#ffffff', // 默认灰色边框
    width: getRealSize(343), // 门店列表宽度 343px
    height: getRealSize(54), // 门店列表高度 54px
    paddingHorizontal: getRealSize(15),
    marginBottom: getRealSize(10),
    position: 'relative',
    justifyContent: 'center', // 垂直居中
  },

  // 治疗师/咨询师列表项样式 - 与therapistStyles.resultItem一致
  staffItem: {
    backgroundColor: '#ffffff',
    borderRadius: 0, // 无圆角
    paddingHorizontal: getRealSize(16), // 原始尺寸
    paddingVertical: getRealSize(12), // 原始尺寸
    marginBottom: getRealSize(10), // 原始尺寸
    borderWidth: getRealSize(1), // 原始尺寸
    borderColor: '#ffffff',
    flexDirection: 'row',
    alignItems: 'center',
  },

  // 治疗师/咨询师信息区域 - 与therapistStyles.infoContainer一致
  staffInfo: {
    flex: 1,
    justifyContent: 'center',
  },

  // 加载更多样式
  loadMoreContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: getRealSize(20),
  },

  // 基础背景样式
  baseBackground: {
    backgroundColor: '#F0F0F0',
  },

  // 闪烁层样式
  shimmerLayer: {
    backgroundColor: '#FFFFFF',
  },
});

export { PageSelectSkeleton, LoadMoreSkeleton };
export default PageSelectSkeleton;
