import React from 'react';
import { View, Text, TouchableWithoutFeedback } from 'react-native';
import { Customer } from '../types';
import { customerStyles } from '../styles';

interface ResultItemCustomerProps {
  customer: Customer;
  selected: boolean;
  onSelect: (customer: Customer) => void;
}

/**
 * 客户选择结果项组件
 *
 * @description 显示客户信息，包括头像、姓名、性别年龄、手机号等
 *
 * @features
 * - 客户头像背景色为 #61B43E
 * - 去掉所有边框圆角，采用方形设计
 * - 支持选中状态显示
 * - 显示脱敏手机号
 *
 * <AUTHOR> sy-chain uni-app 项目
 * @version 1.0.0
 * @since 2024-01
 */
const ResultItemCustomer: React.FC<ResultItemCustomerProps> = ({
  customer,
  selected,
  onSelect,
}) => {
  const handleSelect = () => {
    onSelect(customer);
  };

  // 安全检查：确保customer对象存在
  if (!customer || typeof customer !== 'object') {
    console.warn('ResultItemCustomer - 无效的客户数据:', customer);
    return null;
  }

  // 安全的数据提取和转换
  const safeCustomer = {
    name: customer.name != null ? String(customer.name) : '',
    gender: customer.gender != null ? String(customer.gender) : '',
    age: customer.age != null ? String(customer.age) : '',
    masked_mobile:
      customer.masked_mobile != null ? String(customer.masked_mobile) : '',
    phone: customer.phone != null ? String(customer.phone) : '',
  };

  // 获取客户姓名首字母作为头像显示
  const getAvatarText = () => {
    return safeCustomer.name.trim() ? safeCustomer.name.substring(0, 1) : '客';
  };

  // 格式化性别和年龄显示
  const getGenderAge = () => {
    const parts = [];
    if (safeCustomer.gender.trim()) {
      parts.push(safeCustomer.gender.trim());
    }
    if (safeCustomer.age.trim()) {
      parts.push(safeCustomer.age.trim());
    }
    return parts.join(' | ');
  };

  // 计算一次性别年龄信息
  const genderAgeText = getGenderAge();

  // 调试信息
  if (__DEV__) {
    console.log('ResultItemCustomer - 渲染数据:', {
      originalCustomer: customer,
      safeCustomer,
      genderAgeText,
      selected,
    });
  }

  return (
    <TouchableWithoutFeedback onPress={handleSelect}>
      <View
        style={[
          customerStyles.resultItem,
          selected && customerStyles.selectedItem,
        ]}
      >
        {/* 客户头像 - 背景色 #61B43E，方形无圆角 */}
        <View style={customerStyles.avatarContainer}>
          <Text style={customerStyles.avatarText}>{getAvatarText()}</Text>
        </View>

        {/* 客户信息 */}
        <View style={customerStyles.customerInfo}>
          <View style={customerStyles.customerNameContainer}>
            <Text style={customerStyles.customerName}>
              {safeCustomer.name || '未知客户'}
            </Text>
            {genderAgeText &&
            typeof genderAgeText === 'string' &&
            genderAgeText.length > 0 ? (
              <Text style={customerStyles.customerGenderAge}>
                {genderAgeText}
              </Text>
            ) : null}
          </View>
          <Text style={customerStyles.customerPhone}>
            手机号：{safeCustomer.masked_mobile || safeCustomer.phone || '--'}
          </Text>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default ResultItemCustomer;
