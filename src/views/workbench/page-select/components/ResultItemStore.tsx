import React from 'react';
import { View, Text, TouchableWithoutFeedback } from 'react-native';
import { Store } from '../types';
import { storeStyles } from '../styles';

interface ResultItemStoreProps {
  store: Store;
  selected: boolean;
  onSelect: (store: Store) => void;
}

/**
 * 门店选择结果项组件
 *
 * @description 显示门店信息，包括门店名称和返现信息
 *
 * @features
 * - 门店列表尺寸：长345px，宽54px
 * - 背景色：#EBFBDC，边框：1px solid #61B43E
 * - 门店名称样式：PingFangSC-Regular, 15px, #61B43E, 400
 * - 去掉所有边框圆角，采用方形设计
 * - 支持返现标签显示
 *
 * <AUTHOR> sy-chain uni-app 项目
 * @version 1.0.0
 * @since 2024-01
 */
const ResultItemStore: React.FC<ResultItemStoreProps> = ({
  store,
  selected,
  onSelect,
}) => {
  const handleSelect = () => {
    onSelect(store);
  };

  // 数据验证和安全处理
  if (!store || typeof store !== 'object') {
    console.warn('ResultItemStore - 无效的门店数据:', store);
    return null;
  }

  // 安全获取门店名称
  const storeName = store.name ? String(store.name).trim() : '未知门店';

  // 安全获取返现信息
  const cashbackText =
    store.cashback && typeof store.cashback === 'string'
      ? store.cashback.trim()
      : '';

  // 调试信息
  if (__DEV__) {
    console.log('ResultItemStore - 渲染数据:', {
      store,
      storeName,
      cashbackText,
      selected,
    });
  }

  return (
    <TouchableWithoutFeedback onPress={handleSelect}>
      <View
        style={[storeStyles.resultItem, selected && storeStyles.selectedItem]}
      >
        {/* 门店信息 */}
        <View style={storeStyles.storeInfo}>
          <Text
            style={
              selected ? storeStyles.selectedStoreName : storeStyles.storeName
            }
          >
            {storeName}
          </Text>
        </View>

        {/* 返现标签 */}
        {store.cashback !== '' && (
          <Text style={storeStyles.cashback}>{cashbackText}</Text>
        )}
      </View>
    </TouchableWithoutFeedback>
  );
};

export default ResultItemStore;
