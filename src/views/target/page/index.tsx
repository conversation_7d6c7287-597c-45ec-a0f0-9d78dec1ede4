import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  Platform,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import BulletinBoard from '../components/BulletinBoard';
import Table from '../components/Table';
import api from '../../../common/api';
import { ATrack } from '@soyoung/react-native-container';
import { getRealSize } from '../../../common/utils';
import jsApi from '@soyoung/react-native-jsapi';
import DatePicker from '@/components/DatePicker';
import { checkPermission } from '@/common/checkPermission';
import Empty from '@/components/Empty';
import { TargetPageSkeleton } from '../components/TargetSkeleton';
import PermissionWrapper from '@/components/PermissionWrapper';

interface QuotaItem {
  id: number;
  name: string;
}

interface Quota {
  id: number;
  name: string;
  target_value: number;
  complete_value: number;
  complete_rate: number;
}

interface ChildTarget {
  object_name: string;
  quotas: Quota[];
}

interface Target {
  quotas: Quota[];
  child_target: ChildTarget[];
}

interface StoreTargetData {
  quota_items: QuotaItem[];
  target: Target;
}

interface PersonalTargetData {
  quota_items: QuotaItem[];
  targets: Array<{
    month: string;
    quotas: Quota[];
  }>;
}

const TargetPage: React.FC = () => {
  const [headerTabId, setHeaderTabId] = useState<number>(0);
  const [yearMonthList, setYearMonthList] = useState<string[]>([]);
  const [storeDate, setStoreDate] = useState<string>('');
  const [storeTargetData, setStoreTargetData] =
    useState<StoreTargetData | null>(null);
  const [storeQuotaSelectId, setStoreQuotaSelectId] = useState<number>(0);
  const [personalDate, setPersonalDate] = useState<string>('');
  const [personalTargetDate, setPersonalTargetDate] =
    useState<PersonalTargetData | null>(null);
  const [isManager, setIsManager] = useState<boolean>(false);
  // 添加DatePicker相关状态
  const [showPicker, setShowPicker] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [hasPermission, setHasPermission] = useState<boolean>(true);
  const insets = useSafeAreaInsets();

  // 状态栏高度
  const statusBarHeight = insets.top;
  const headerHeight = Platform.OS === 'ios' ? 44 : 56;

  // 获取当前门店数据面板 - 使用 useMemo 优化
  const storeDataPanel = useMemo(() => {
    return (
      storeTargetData?.target?.quotas.find(
        find => find.id === storeQuotaSelectId
      ) || null
    );
  }, [storeTargetData, storeQuotaSelectId]);

  // 获取表格数据 - 使用 useMemo 优化
  const tableData = useMemo(() => {
    switch (headerTabId) {
      case 0:
        return {
          header: [null],
          table:
            (storeTargetData?.target?.child_target
              ?.map(item => {
                const data = item.quotas?.find(
                  find => find?.id === storeQuotaSelectId
                );
                if (data) {
                  return {
                    header: item.object_name,
                    content: [
                      {
                        target_value: data?.target_value,
                        complete_value: data?.complete_value,
                        complete_rate: `${data?.complete_rate}%`,
                      },
                    ],
                  };
                }
                return null;
              })
              ?.filter(filter => filter !== null) as any[]) || [],
        };
      case 1:
        return {
          header: personalTargetDate?.quota_items || [],
          table:
            personalTargetDate?.targets?.map(item => {
              return {
                header: item.month,
                content:
                  personalTargetDate?.quota_items?.map(map => {
                    const data = item?.quotas?.find(
                      find => find?.id === map?.id
                    );
                    return {
                      target_value:
                        data?.target_value !== undefined
                          ? data.target_value
                          : '-',
                      complete_value:
                        data?.complete_value !== undefined
                          ? data.complete_value
                          : '-',
                      complete_rate:
                        data?.complete_rate !== undefined
                          ? `${data.complete_rate}%`
                          : '-',
                    };
                  }) || [],
              };
            }) || [],
        };
      default:
        return {
          header: [],
          table: [],
        };
    }
  }, [headerTabId, storeTargetData, storeQuotaSelectId, personalTargetDate]);

  // 返回上一页 - 使用 useCallback 优化
  const handleBack = useCallback(() => {
    jsApi.toNative('backAnimated', {
      transitionType: '0',
      disableAnimation: '0',
    });
  }, []);

  // 切换日期
  const switchDates = async (type: 'left' | 'right') => {
    const dateIndex = yearMonthList.findIndex(item => item === personalDate);

    // 如果日期超出范围，则不进行切换
    if (
      (dateIndex === 0 && type === 'left') ||
      (dateIndex === yearMonthList.length - 1 && type === 'right')
    ) {
      jsApi.toNative('showToast', {
        toast: '已经超过日期可选范围',
      });
      return;
    }

    let newDate = '';

    switch (type) {
      case 'left':
        if (dateIndex > 0) {
          newDate = yearMonthList[dateIndex - 1];
        }
        break;
      case 'right':
        if (dateIndex < yearMonthList.length - 1) {
          newDate = yearMonthList[dateIndex + 1];
        }
        break;
    }
    setPersonalDate(newDate);
    const data = await getEmployeeYearTarget(newDate);
    setPersonalTargetDate(data);
  };

  // 切换门店指标类型 - 使用 useCallback 优化
  const switchStoreQuotaTypes = useCallback((item: QuotaItem) => {
    setStoreQuotaSelectId(item.id);
  }, []);

  // 切换 Tab
  const switchTab = async (tabId: number) => {
    setHeaderTabId(tabId);
    const list = await getYearMonthList(tabId === 0 ? 2 : 1);
    setYearMonthList(list);
    if (tabId === 1) {
      // 进入“我的目标”：如果有今年则选今年，否则选第一个
      if (!personalDate) {
        const currentYear = String(new Date().getFullYear());
        const defaultYear = list?.includes(currentYear)
          ? currentYear
          : list?.[0] || '';
        if (defaultYear) {
          setPersonalDate(defaultYear);
          const data = await getEmployeeYearTarget(defaultYear);
          setPersonalTargetDate(data);
        }
      }
    }
  };

  // 格式化日期 - 使用 useCallback 优化
  const dateFormatting = useCallback((date: string) => {
    return Number(date.replace(/-/g, ''));
  }, []);

  const getYearMonthList = async (type: number) => {
    // API 调用逻辑
    try {
      const res = await api.pagefetch({
        path: '/chain-wxapp/v1/target/yearMonthList',
        params: {
          period_type: type,
        },
        isLoading: false,
      });

      if (res.errorCode === 0 && res.responseData) {
        return res.responseData;
      } else {
        if (res.errorCode === 10002) {
          setHasPermission(false);
          return [];
        }
        if (res.errorCode !== 0) {
          jsApi.toNative('showToast', {
            toast: res.errorMsg || '获取年月列表失败',
          });
        }
        return [];
      }
    } catch (e) {
      jsApi.toNative('showToast', {
        toast: '获取年月列表异常',
      });
      console.log('getYearMonthList error', e);
      return [];
    }
  };

  const getTenantMonthTarget = async (date?: string) => {
    try {
      const reqDate = date || storeDate;
      const res = await api.pagefetch({
        path: '/chain-wxapp/v1/target/getTenantMonthTarget',
        params: {
          year_month: dateFormatting(reqDate),
        },
        isLoading: false,
      });
      if (res.errorCode === 0 && res.responseData) {
        return res.responseData;
      } else {
        if (res.errorCode === 10002) {
          setHasPermission(false);
          return null;
        }
        if (res.errorCode !== 0) {
          jsApi.toNative('showToast', {
            toast: res.errorMsg || '获取门店月度目标失败',
          });
        }
        return null;
      }
    } catch (e) {
      jsApi.toNative('showToast', {
        toast: '获取门店月度目标异常',
      });
      console.log('getTenantMonthTarget error', e);
      return null;
    }
  };

  const getEmployeeYearTarget = async (date?: string) => {
    // API 调用逻辑
    try {
      const year = date || personalDate;
      const res = await api.pagefetch({
        path: '/chain-wxapp/v1/target/getEmployeeYearTarget',
        params: {
          year,
        },
        isLoading: false,
      });
      if (res.errorCode === 0 && res.responseData) {
        return res.responseData;
      } else {
        if (res.errorCode === 10002) {
          setHasPermission(false);
          return null;
        }
        if (res.errorCode !== 0) {
          jsApi.toNative('showToast', {
            toast: res.errorMsg || '获取个人年度目标失败',
          });
        }
        return null;
      }
    } catch (e) {
      jsApi.toNative('showToast', {
        toast: '获取个人年度目标异常',
      });
      console.log('getEmployeeYearTarget error', e);
      return null;
    }
  };

  // 打开日期选择器 - 使用 useCallback 优化
  const handleClickDatePicker = useCallback(() => {
    setShowPicker(true);
  }, []);

  // 关闭日期选择器 - 使用 useCallback 优化
  const closePicker = useCallback(() => {
    setShowPicker(false);
  }, []);

  // 获取当前选择器的值 - 使用 useMemo 优化
  const currentPickerValue = useMemo((): string => {
    if (headerTabId === 0) {
      // 门店目标：返回年月格式的storeDate
      return storeDate;
    } else {
      // 个人目标：返回年份格式的personalDate
      return personalDate;
    }
  }, [headerTabId, storeDate, personalDate]);

  // 获取当前选择器的标题 - 使用 useMemo 优化
  const currentPickerTitle = useMemo((): string => {
    if (headerTabId === 0) {
      return '选择年月';
    } else {
      return '选择年份';
    }
  }, [headerTabId]);

  // 确认日期选择
  const handleDateConfirm = async (selectedDate: string) => {
    if (headerTabId === 0) {
      // 门店目标：更新storeDate并获取新数据
      setStoreDate(selectedDate);
      try {
        const data = await getTenantMonthTarget(selectedDate);
        setStoreTargetData(data as StoreTargetData);
        if (data) {
          setStoreQuotaSelectId(data.quota_items[0].id);
        }
      } catch (error) {
        console.error('获取门店目标数据失败:', error);
      }
    } else {
      // 个人目标：更新personalDate并获取新数据
      setPersonalDate(selectedDate);
      // 获取个人年度目标数据
      const data = await getEmployeeYearTarget(selectedDate);
      setPersonalTargetDate(data);
    }
    closePicker();
  };

  // 获取DatePicker的类型
  const getPickerType = () => {
    if (headerTabId === 0) {
      return 'year-month'; // 门店目标选择年月
    } else {
      return 'year'; // 个人目标选择年份
    }
  };

  useEffect(() => {
    // 初始化逻辑
    const initPage = async () => {
      try {
        setLoading(true);
        const _manager = await checkPermission('wx:tenant:target');

        setIsManager(_manager);

        const date = new Date();
        setHeaderTabId(_manager ? 0 : 1);

        const list = await getYearMonthList(_manager ? 2 : 1);
        setYearMonthList(list as string[]);

        if (_manager) {
          const month = date.getMonth() + 1;
          setStoreDate(
            `${date.getFullYear()}-${month < 10 ? '0' + month : month}`
          );
          const data = await getTenantMonthTarget();

          setStoreTargetData(data as StoreTargetData);
          if (data) {
            setStoreQuotaSelectId(data.quota_items[0].id);
          }
        } else {
          // 进入“我的目标”：如果有今年则选今年，否则选第一个
          const currentYear = String(date.getFullYear());
          const defaultYear = (list as string[])?.includes(currentYear)
            ? currentYear
            : (list as string[])?.[0] || '';
          if (defaultYear) {
            setPersonalDate(defaultYear);
            const data = await getEmployeeYearTarget(defaultYear);
            setPersonalTargetDate(data as PersonalTargetData);
          }
        }
      } catch (error) {
        console.error('初始化页面失败:', error);
      } finally {
        setLoading(false);
      }
    };

    initPage();
  }, []);

  const renderDateText = () => {
    if (headerTabId === 0 && storeDate) {
      const dateList = storeDate.split('-');
      if (dateList.length >= 2) {
        return `${dateList[0]}年${dateList[1]}月`;
      }
      // 如果日期格式不正确，返回原始日期或默认值
      return storeDate || '日期未设置';
    }
    return '日期未设置';
  };

  return (
    <View style={{ ...styles.container }}>
      <View
        style={[
          styles.target,
          {
            paddingTop: statusBarHeight + headerHeight,
          },
        ]}
      >
        {/* Header */}
        <View
          style={[
            styles.targetHeaderBox,
            { height: statusBarHeight + headerHeight + 14 },
          ]}
        >
          <View
            style={[
              styles.targetHeader,
              { top: statusBarHeight, height: headerHeight },
            ]}
          >
            <ATrack onPress={handleBack} style={styles.backButton}>
              <Image
                source={{
                  uri: 'https://static.soyoung.com/sy-design/**************************.png',
                }}
                style={styles.backIcon}
              />
            </ATrack>
            <View style={styles.headerTabs}>
              {isManager && (
                <ATrack
                  onPress={() => switchTab(0)}
                  style={styles.headerTabItem}
                >
                  <Text
                    style={[
                      styles.headerTitle,
                      headerTabId === 0 && styles.headerTitleSelected,
                    ]}
                  >
                    门店目标
                  </Text>
                  {headerTabId === 0 && <View style={styles.headerUnderline} />}
                </ATrack>
              )}
              <ATrack onPress={() => switchTab(1)} style={styles.headerTabItem}>
                <Text
                  style={[
                    styles.headerTitle,
                    headerTabId === 1 && styles.headerTitleSelected,
                  ]}
                >
                  我的目标
                </Text>
                {headerTabId === 1 && <View style={styles.headerUnderline} />}
              </ATrack>
            </View>
          </View>
        </View>
        {!hasPermission ? (
          <PermissionWrapper hasPermission={false} />
        ) : loading ? (
          <TargetPageSkeleton />
        ) : (
          <>
            {/* Date Picker */}
            {headerTabId === 0 && storeDate ? (
              <View style={styles.datePickerContainer}>
                <ATrack
                  style={styles.datePicker}
                  onPress={handleClickDatePicker}
                >
                  <Text style={styles.dateText}>{renderDateText()}</Text>
                  <Image
                    source={{
                      uri: 'https://static.soyoung.com/sy-design/6eboz32amcrz1753258539331.png',
                    }}
                    style={styles.dateIcon}
                    resizeMode='contain'
                  />
                </ATrack>
              </View>
            ) : null}

            {/* Body */}
            <View style={styles.body}>
              {/* Store Target Tabs */}
              {headerTabId === 0 && storeTargetData && (
                <View style={styles.storeTabsContainer}>
                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    style={styles.storeTargetTabs}
                  >
                    {storeTargetData.quota_items.map((item, index) => (
                      <ATrack
                        key={index}
                        style={styles.storeTargetTabButton}
                        onPress={() => switchStoreQuotaTypes(item)}
                      >
                        <Text
                          style={[
                            styles.storeTargetTab,
                            storeQuotaSelectId === item.id &&
                              styles.storeTargetTabSelected,
                          ]}
                        >
                          {item.name}
                        </Text>
                        {storeQuotaSelectId === item.id && (
                          <View style={styles.storeTabUnderline} />
                        )}
                      </ATrack>
                    ))}
                  </ScrollView>
                </View>
              )}

              {/* Personal Date Selector */}
              {headerTabId === 1 && personalDate ? (
                <View style={styles.personalDateSelector}>
                  <ATrack
                    onPress={() => switchDates('left')}
                    hitSlop={{
                      top: 30,
                      bottom: 30,
                      left: 30,
                      right: 30,
                    }}
                  >
                    <Image
                      source={{
                        uri: 'https://static.soyoung.com/sy-design/2stqqz9og4hfc1753258539178.png',
                      }}
                      style={[styles.arrowIcon, styles.leftArrow]}
                      resizeMode='contain'
                    />
                  </ATrack>
                  <ATrack
                    style={styles.personalDateCenter}
                    onPress={handleClickDatePicker}
                  >
                    <Text style={styles.personalDateText}>{personalDate}</Text>
                    <Image
                      source={{
                        uri: 'https://static.soyoung.com/sy-design/6eboz32amcrz1753258539179.png',
                      }}
                      style={styles.dropDownIcon}
                      resizeMode='contain'
                    />
                  </ATrack>
                  <ATrack
                    onPress={() => switchDates('right')}
                    hitSlop={{
                      top: 30,
                      bottom: 30,
                      left: 30,
                      right: 30,
                    }}
                  >
                    <Image
                      source={{
                        uri: 'https://static.soyoung.com/sy-design/2stqqz9og4hfc1753258539178.png',
                      }}
                      style={styles.arrowIcon}
                      resizeMode='contain'
                    />
                  </ATrack>
                </View>
              ) : null}

              {/* Content */}
              {(headerTabId === 0 && !storeTargetData) ||
              (headerTabId === 1 && !personalTargetDate) ? (
                <Empty text='暂无数据' />
              ) : (
                <View style={styles.contentContainer}>
                  {headerTabId === 0 && (
                    <>
                      {storeDataPanel && (
                        <View style={styles.bulletinBoardContainer}>
                          <BulletinBoard store={storeDataPanel} />
                        </View>
                      )}
                      <View style={[styles.tableContainer]}>
                        <Table
                          key={`store-table-${storeQuotaSelectId}`}
                          type={headerTabId}
                          tableData={tableData}
                        />
                      </View>
                    </>
                  )}
                  {headerTabId === 1 && (
                    <View style={styles.tableContainer2}>
                      <Table
                        key={`personal-table-${personalDate}`}
                        type={headerTabId}
                        tableData={tableData}
                      />
                    </View>
                  )}
                </View>
              )}
            </View>
          </>
        )}
      </View>
      <DatePicker
        visible={showPicker}
        value={currentPickerValue}
        title={currentPickerTitle}
        type={getPickerType()}
        onConfirm={handleDateConfirm}
        onCancel={closePicker}
        yearMonthList={yearMonthList}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'red',
  },
  target: {
    flex: 1,
    backgroundColor: '#fff',
  },
  targetHeaderBox: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 99999,
  },
  targetHeader: {
    position: 'absolute',
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  backButton: {
    position: 'absolute',
    left: 0,
    top: getRealSize(10),
    //transform: [{ translateY: -getRealSize(10) }],
  },
  backIcon: {
    width: getRealSize(44),
    height: getRealSize(44),
  },
  headerTitle: {
    fontSize: getRealSize(18),
    color: '#777777',
    fontWeight: '500',
  },
  headerTitleSelected: {
    color: '#333333',
  },
  headerTabs: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTabItem: {
    paddingHorizontal: getRealSize(15),
    paddingVertical: getRealSize(8),
    position: 'relative',
    alignItems: 'center',
  },
  headerUnderline: {
    position: 'absolute',
    bottom: 0,
    left: '50%',
    transform: [{ translateX: getRealSize(10) }],
    width: getRealSize(20),
    height: getRealSize(2),
    backgroundColor: '#333333',
  },
  datePickerContainer: {
    paddingHorizontal: getRealSize(15),
    paddingVertical: getRealSize(8),
    marginTop: getRealSize(10),
  },
  datePicker: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  dateText: {
    fontSize: getRealSize(16),
    color: '#333333',
  },
  dateIcon: {
    width: getRealSize(9),
    height: getRealSize(9),
    marginLeft: getRealSize(5),
    marginTop: getRealSize(2),
  },
  body: {
    flex: 1,
    backgroundColor: '#ffffff',
    marginTop: getRealSize(5),
    paddingBottom: getRealSize(15),
  },
  storeTabsContainer: {
    // paddingTop: getRealSize(15),
    paddingBottom: getRealSize(10),
  },
  storeTargetTabs: {
    paddingHorizontal: getRealSize(15),
  },
  storeTargetTabButton: {
    marginRight: getRealSize(25),
    paddingVertical: getRealSize(5),
    paddingBottom: getRealSize(10),
    position: 'relative',
  },
  storeTargetTab: {
    fontSize: getRealSize(15),
    color: '#777777',
    fontWeight: '500',
  },
  storeTargetTabSelected: {
    color: '#333333',
    fontWeight: 'bold',
  },
  storeTabUnderline: {
    position: 'absolute',
    bottom: 0,
    left: '50%',
    transform: [{ translateX: -getRealSize(12) }],
    width: getRealSize(24),
    height: getRealSize(2),
    backgroundColor: '#333333',
  },
  personalDateSelector: {
    height: getRealSize(57),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  arrowIcon: {
    width: getRealSize(10),
    height: getRealSize(15),
  },
  leftArrow: {
    transform: [{ rotate: '180deg' }],
  },
  personalDateCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: getRealSize(40),
    marginRight: getRealSize(30),
  },
  personalDateText: {
    fontSize: getRealSize(16),
    color: '#333333',
    fontWeight: '500',
  },
  dropDownIcon: {
    width: getRealSize(12),
    height: getRealSize(20),
    marginLeft: getRealSize(6),
  },
  contentContainer: {
    flex: 1,
    paddingLeft: getRealSize(15),
  },
  bulletinBoardContainer: {
    paddingRight: getRealSize(15),
    marginBottom: getRealSize(15),
  },
  tableContainer: {
    flex: 1,
    marginRight: getRealSize(15),
  },
  tableContainer2: {
    flex: 1,
    marginRight: getRealSize(15),
  },
});

TargetPage.displayName = 'TargetPage';

export default TargetPage;
