import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import ProcessCircle from './ProcessCircle';
import { getRealSize } from '@/common/utils';

interface Store {
  name: string;
  target_value: number;
  complete_value: number;
  complete_rate: number;
}

interface BulletinBoardProps {
  store: Store;
}

const BulletinBoard: React.FC<BulletinBoardProps> = ({ store }) => {
  const completeValueFormat = () => {
    if (Number.isInteger(store.complete_value)) {
      return store.complete_value;
    } else {
      return Number(store.complete_value);
    }
  };

  return (
    <View style={styles.bulletinBoard}>
      <Text style={styles.title}>{store.name || '未知指标'}</Text>
      <View style={styles.body}>
        <View style={styles.completionRate}>
          <ProcessCircle
            value={store.complete_rate || 0}
            size={getRealSize(75)}
            strokeWidth={getRealSize(6)}
            showLabelTitle={true}
          />
        </View>
        <View
          style={[styles.textContainer, { marginRight: getRealSize(56 / 2) }]}
        >
          <Text style={styles.moneyText}>{store.target_value || 0}</Text>
          <Text style={styles.titleText}>目标值</Text>
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.moneyText}>{completeValueFormat()}</Text>
          <Text style={styles.titleText}>实际达成</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  bulletinBoard: {
    backgroundColor: '#f8f8f8',
    padding: getRealSize(15),
    height: getRealSize(135),
  },
  title: {
    // fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(14),
    color: '#161616',
    fontWeight: 'bold',
    marginBottom: getRealSize(10),
  },
  body: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  completionRate: {
    marginRight: getRealSize(30),
  },
  textContainer: {
    flexDirection: 'column',
  },
  moneyText: {
    fontFamily: 'DINAlternate-Bold',
    fontSize: getRealSize(18),
    color: '#303233',
    fontWeight: '700',
    paddingBottom: getRealSize(10),
  },
  titleText: {
    // fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#aaabb3',
    fontWeight: '400',
  },
});

export default BulletinBoard;
