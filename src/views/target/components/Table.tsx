import React, { useState, useMemo, useCallback } from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { cloneDeep } from 'lodash';
import { getRealSize } from '@/common/utils';
import SmartTable from '@/components/SmartTable';

interface TableHeader {
  id?: number;
  name?: string;
}

interface TableContent {
  target_value: number | string;
  complete_value: number | string;
  complete_rate: string;
}

interface TableItem {
  header: string;
  content: TableContent[];
}

interface TableData {
  header: (TableHeader | null)[];
  table: TableItem[];
}

interface TableProps {
  type: number; // 0: 门店目标, 1: 个人目标
  tableData: TableData;
  styleAssembly?: any;
}

interface SortState {
  columnIndex: number | null;
  direction: 'asc' | 'desc' | 'none';
}

const Table: React.FC<TableProps> = ({ type, tableData, styleAssembly }) => {
  // 排序状态管理
  const [sortState, setSortState] = useState<SortState>({
    columnIndex: 2, // 初始时按实际完成列排序（columnIndex: 2 = 实际完成）
    direction: 'desc', // 倒序排列
  });

  // 计算排序后的表格数据
  const tableDataTable = useMemo(() => {
    try {
      // 只有门店目标模式且有排序状态时才排序
      if (
        type === 0 &&
        sortState.columnIndex !== null &&
        sortState.direction !== 'none'
      ) {
        const temp = cloneDeep(tableData.table);

        temp.sort((obj1, obj2) => {
          const content1 = obj1.content[0];
          const content2 = obj2.content[0];

          if (!content1 || !content2) return 0;

          const target_value_1 = Number(content1.target_value) || 0;
          const complete_value_1 = Number(content1.complete_value) || 0;
          const complete_rate_1 =
            Number(content1.complete_rate.toString().replace('%', '')) || 0;

          const target_value_2 = Number(content2.target_value) || 0;
          const complete_value_2 = Number(content2.complete_value) || 0;
          const complete_rate_2 =
            Number(content2.complete_rate.toString().replace('%', '')) || 0;

          let compareResult = 0;

          // 根据列索引决定比较哪个值
          // columnIndex: 1=目标值, 2=实际完成, 3=完成率
          switch (sortState.columnIndex) {
            case 1: // 目标值
              compareResult = target_value_1 - target_value_2;
              break;
            case 2: // 实际完成
              compareResult = complete_value_1 - complete_value_2;
              break;
            case 3: // 完成率
              compareResult = complete_rate_1 - complete_rate_2;
              break;
            default:
              compareResult = 0;
          }

          // 升序还是降序
          const result =
            sortState.direction === 'asc' ? compareResult : -compareResult;

          return result;
        });

        return temp;
      } else {
        return tableData.table;
      }
    } catch (error) {
      console.error('排序错误:', error);
      return tableData.table;
    }
  }, [type, tableData.table, sortState]);

  // 处理排序点击
  const handleSort = useCallback(
    (columnIndex: number, _direction: 'asc' | 'desc' | 'none') => {
      // 只在门店目标模式下允许排序
      if (type !== 0) {
        return;
      }

      // SmartTable 的列索引需要调整
      // SmartTable传过来的 columnIndex 从第二列开始是1，我们需要映射到正确的索引
      const mappedColumnIndex = columnIndex; // 1=目标值, 2=实际完成, 3=完成率

      let nextDirection: 'asc' | 'desc' | 'none';

      if (
        sortState.columnIndex === mappedColumnIndex &&
        sortState.columnIndex !== null
      ) {
        // 同一列：循环切换排序方向
        switch (sortState.direction) {
          case 'asc':
            nextDirection = 'desc';
            break;
          case 'desc':
            nextDirection = 'none';
            break;
          case 'none':
          default:
            nextDirection = 'asc';
            break;
        }
      } else {
        // 不同列或首次点击：从升序开始（修复逻辑）
        nextDirection = 'asc';
      }

      const newSortState = {
        columnIndex: nextDirection === 'none' ? null : mappedColumnIndex,
        direction: nextDirection,
      };

      setSortState(newSortState);
    },
    [type, sortState]
  );

  // 转换数据格式以适配 SmartTable
  const convertedData = useMemo(() => {
    // 构建表头数据
    const headerData: {
      groups?: { name: string; columns: number }[];
      columns: string[];
    } = {
      columns: [type === 0 ? '员工' : '月份'],
    };

    if (type === 1 && tableData.header.length > 0) {
      // 个人目标：有分组表头
      headerData.groups = tableData.header.map(headerItem => ({
        name: headerItem?.name || '执行业绩',
        columns: 3, // 每组3列：目标值、实际完成、完成率
      }));

      // 构建列标题
      tableData.header.forEach(() => {
        headerData.columns.push(`目标值`);
        headerData.columns.push(`实际完成`);
        headerData.columns.push(`完成率`);
      });
    } else {
      // 门店目标：无分组，直接列标题
      headerData.columns.push('目标值', '实际完成', '完成率');
    }

    // 构建表格数据
    const rows: (string | number)[][] = tableDataTable.map(item => {
      const row: (string | number)[] = [item.header];

      item.content.forEach(contentItem => {
        row.push(
          contentItem?.target_value !== undefined
            ? contentItem.target_value
            : '-',
          contentItem?.complete_value !== undefined
            ? contentItem.complete_value
            : '-',
          contentItem?.complete_rate || '-'
        );
      });

      return row;
    });

    // 构建列宽数组
    const columnWidths: number[] = [getRealSize(85)]; // 第一列固定宽度
    const contentColumnWidth = getRealSize(86);

    for (let i = 1; i < headerData.columns.length; i++) {
      columnWidths.push(contentColumnWidth);
    }

    return {
      headerData,
      rows,
      columnWidths,
    };
  }, [type, tableData, tableDataTable]);

  if (tableDataTable.length === 0) {
    return (
      <View style={styles.tableNodata}>
        <Image
          source={{
            uri: 'https://static.soyoung.com/sy-pre/3p3ankt14acdd-1713013800670.png',
          }}
          style={styles.nodataImage}
        />
        <Text style={styles.nodataText}>门店目标未下放员工</Text>
      </View>
    );
  }

  return (
    <SmartTable
      headerData={convertedData.headerData}
      tableData={convertedData.rows}
      widthArr={convertedData.columnWidths}
      style={styleAssembly}
      stickyFirstColumn={true}
      sortable={type === 0}
      onSort={handleSort}
      currentSortColumn={sortState.columnIndex}
      currentSortDirection={sortState.direction}
      borderStyle={{ borderWidth: 1, borderColor: '#e9e9ed' }}
      headerStyle={{ backgroundColor: '#f1f3f3' }}
      textStyle={{
        fontSize: getRealSize(14),
        color: '#303233',
        fontWeight: '700',
        fontFamily: 'DINAlternate-Bold',
      }}
      headerTextStyle={{
        fontSize: getRealSize(13),
        color: '#303233',
        fontWeight: '500',
      }}
      columnHeaderTextStyle={{
        fontSize: getRealSize(13),
        color: '#303233',
        fontWeight: '400',
      }}
      firstColumnTextStyle={{
        fontSize: getRealSize(13),
        color: '#303233',
        fontWeight: '400',
        fontFamily: 'PingFangSC-Regular',
      }}
      removeClippedSubviews={true}
      maxToRenderPerBatch={8}
      windowSize={8}
      scrollEventThrottle={16}
    />
  );
};

const styles = StyleSheet.create({
  tableNodata: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    paddingTop: getRealSize(80),
  },
  nodataImage: {
    width: getRealSize(150),
    height: getRealSize(120),
  },
  nodataText: {
    fontSize: getRealSize(14),
    color: '#777777',
    marginTop: getRealSize(10),
  },
});

export default Table;
