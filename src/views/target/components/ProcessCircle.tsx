import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Svg, { Circle, Defs, LinearGradient, Stop } from 'react-native-svg';
import { getRealSize } from '@/common/utils';

interface ProcessCircleProps {
  size?: number;
  value?: number;
  strokeWidth?: number;
  padding?: number;
  showLabelTitle?: boolean;
}

const ProcessCircle: React.FC<ProcessCircleProps> = ({
  size = 75,
  value = 0,
  strokeWidth = 6,
  padding = 6,
  showLabelTitle = false,
}) => {
  const displayValue = Number(value) || 0;
  const radius = size / 2 - padding - strokeWidth / 2;
  const circumference = 2 * Math.PI * radius;
  // 修改填充方向：从顺时针改为逆时针
  const strokeDashoffset = circumference * (1 - displayValue / 100);

  const getColors = (num: number): [string, string] => {
    if (num < 50) {
      return ['#FF6481', '#F33155'];
    } else if (num < 85) {
      return ['#FFA675', '#FF8708'];
    } else {
      return ['#61B43E', '#A9EA6A'];
    }
  };

  const [startColor, endColor] = getColors(displayValue);

  return (
    <View style={[styles.wrap, { width: size, height: size }]}>
      <Svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
        <Defs>
          <LinearGradient id='gradient' x1='0%' y1='0%' x2='100%' y2='100%'>
            <Stop offset='0%' stopColor={startColor} />
            <Stop offset='100%' stopColor={endColor} />
          </LinearGradient>
        </Defs>

        {/* 背景圆环 */}
        <Circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke='#FBFBFB'
          strokeWidth={strokeWidth}
          fill='none'
        />

        {/* 进度圆环 */}
        {displayValue > 0 && (
          <Circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke='url(#gradient)'
            strokeWidth={strokeWidth}
            fill='none'
            strokeDasharray={`${(displayValue / 100) * circumference} ${circumference}`}
            strokeDashoffset={0}
            transform={`rotate(-90 ${size / 2} ${size / 2}) scale(1, -1) translate(0, -${size})`}
            strokeLinecap='round'
          />
        )}
      </Svg>

      <View style={[styles.labelWrap, size >= 75 && styles.big]}>
        <View style={styles.labelTop}>
          <Text style={styles.labelValue} numberOfLines={1}>
            {(() => {
              // 如果是小数，保留最多2位小数并去掉末尾的0
              const processedValue = Number.isInteger(displayValue)
                ? displayValue
                : parseFloat(displayValue.toFixed(2));

              const valueStr = String(processedValue);

              // 对于常见的百分比值，优先考虑显示完整性
              // 100.xx 这种情况应该显示完整的两位小数
              if (processedValue >= 100 && processedValue < 1000) {
                return processedValue.toFixed(2).replace(/\.00$/, '');
              }

              return valueStr.length > 5
                ? valueStr.slice(0, 4) + '...'
                : valueStr;
            })()}
          </Text>
          <Text style={styles.percent}>%</Text>
        </View>
      </View>
      {showLabelTitle && <Text style={styles.labelTitle}>完成率</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  wrap: {
    overflow: 'visible',
    borderRadius: 999,
    position: 'relative',
    shadowColor: 'rgba(65, 162, 140, 0.2)',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 1,
    shadowRadius: 7,
    backgroundColor: '#ffffff',
    elevation: 3,
  },
  labelWrap: {
    position: 'absolute',
    left: '50%',
    top: '46%',
    transform: [
      { translateX: getRealSize(-28) },
      { translateY: getRealSize(-10) },
    ],
    width: getRealSize(60),
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
  },
  big: {
    top: '47%',
  },
  labelTop: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  labelValue: {
    fontFamily: 'DINAlternate-Bold',
    fontSize: getRealSize(15),
    color: '#333333',
    fontWeight: '700',
  },
  percent: {
    fontFamily: 'DINAlternate-Bold',
    fontSize: getRealSize(12),
    color: '#333333',
    transform: [{ scale: 0.83 }],
  },
  labelTitle: {
    color: '#aaabb3',
    fontSize: getRealSize(12),
    position: 'absolute',
    top: '56%',
    left: '50%',
    transform: [{ translateX: getRealSize(-17) }, { scale: 0.58 }],
  },
});

export default ProcessCircle;
