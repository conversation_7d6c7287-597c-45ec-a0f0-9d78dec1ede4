import React from 'react';
import TargetPage from './page';

interface PageProps {}

class DetailPage extends React.Component<PageProps> {
  constructor(props: PageProps) {
    super(props);
    this.state = {
      pageShow: true,
    };
  }

  soyoungPageName() {
    return '';
  }

  /** 页面埋点 */
  soyoungPageInfo() {
    return {};
  }

  didAppear() {
    this.setState({
      pageShow: true,
    });
  }

  willDisappear() {
    this.setState({
      pageShow: false,
    });
  }

  preferredStatusBarStyle() {
    return '2';
  }

  render() {
    return <TargetPage />;
  }
}

export default DetailPage;
