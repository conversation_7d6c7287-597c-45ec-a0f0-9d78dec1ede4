import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, ScrollView, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { ATrack } from '@soyoung/react-native-container';
import { getRealSize } from '../../../common/utils';
import jsApi from '@soyoung/react-native-jsapi';
import { jumpReactNativePage } from '@/common/jumpPage';
import Header from '@/components/header';
import api from '@/common/api';

interface AppItem {
  id: string;
  name: string;
  icon: string;
  hasRedDot?: boolean;
  url?: string;
}

interface AppCategory {
  cat_name: string;
  service_list: AppItem[];
}

interface EntryPageProps {
  crossChange?: boolean;
  pageShowFlag?: boolean;
}

// 单个应用项组件
interface AppItemComponentProps {
  item: AppItem;
  onPress: (item: AppItem) => void;
}

const AppItemComponent: React.FC<AppItemComponentProps> = React.memo(
  ({ item, onPress }) => {
    const handlePress = useCallback(() => {
      onPress(item);
    }, [item, onPress]);

    return (
      <ATrack style={styles.appItem} onPress={handlePress}>
        <View style={styles.appIconContainer}>
          <Image source={{ uri: item.icon }} style={styles.appIcon} />
          {item.hasRedDot && <View style={styles.redDot} />}
        </View>
        <Text style={styles.appName}>{item.name}</Text>
      </ATrack>
    );
  }
);

// 应用分类组件
interface AppCategoryComponentProps {
  category: AppCategory;
  onAppPress: (item: AppItem) => void;
}

const AppCategoryComponent: React.FC<AppCategoryComponentProps> = React.memo(
  ({ category, onAppPress }) => {
    return (
      <View style={styles.categoryContainer}>
        <Text style={styles.categoryTitle}>{category.cat_name}</Text>
        <View style={styles.whiteBackground}>
          <View style={styles.appsContainer}>
            {category.service_list.map((item, index) => (
              <AppItemComponent
                key={`${item.id}-${index}`}
                item={item}
                onPress={onAppPress}
              />
            ))}
          </View>
        </View>
      </View>
    );
  }
);

const EntryPageLayout: React.FC<EntryPageProps> = props => {
  // 应用分类数据
  const [appCategories, setAppCategories] = useState<AppCategory[]>([]);
  const [loading, setLoading] = useState(false);

  const handleAppPress = useCallback((item: AppItem) => {
    if (item.url) {
      // 如果有路由配置，进行页面跳转
      jumpReactNativePage(item.url);
    } else {
      // 否则显示提示
      jsApi.toNative('showToast', {
        toast: `${item.name}功能开发中`,
      });
    }
  }, []);

  // 获取应用列表数据
  const fetchAppData = useCallback(async () => {
    setLoading(true);
    try {
      const res = await api.pagefetch({
        path: '/chain-wxapp/v1/service/all',
        params: {},
      });
      if (res.errorCode === 0 && res.responseData) {
        setAppCategories(res.responseData);
      }
    } catch (error) {
      console.error('EntryPage fetchAppData exception:', error);
    } finally {
      setLoading(false);
    }
  }, [setAppCategories]);

  // 页面聚焦时刷新数据
  useFocusEffect(
    useCallback(() => {
      console.log(
        'EntryPage useFocusEffect, pageShowFlag:',
        props.pageShowFlag
      );
      fetchAppData();
    }, [props.pageShowFlag, props.crossChange, fetchAppData])
  );

  useFocusEffect(
    useCallback(() => {
      console.log(
        'EntryPage useFocusEffect, pageShowFlag:',
        props.pageShowFlag
      );
    }, [])
  );

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <View style={styles.page}>
        {/* Header */}
        <Header title='应用中心' hideBack={true} bgColor='#FFFFFF' />
        {/* Body */}
        <ScrollView
          style={styles.bodyContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
          bounces={false}
        >
          {appCategories.map((category, categoryIndex) => (
            <AppCategoryComponent
              key={`category-${categoryIndex}-${category.cat_name}`}
              category={category}
              onAppPress={handleAppPress}
            />
          ))}
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  page: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  bodyContainer: {
    flex: 1,
    paddingTop: getRealSize(15),
  },
  scrollContent: {
    paddingHorizontal: getRealSize(15),
    paddingBottom: getRealSize(30),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: getRealSize(100),
  },
  loadingText: {
    fontSize: getRealSize(14),
    color: '#666666',
  },
  categoryContainer: {
    marginBottom: getRealSize(24),
  },
  categoryTitle: {
    fontSize: getRealSize(16),
    color: '#333333',
    fontWeight: '500',
    marginBottom: getRealSize(15),
    marginLeft: getRealSize(4),
  },
  whiteBackground: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: getRealSize(20),
    paddingTop: getRealSize(15),
    paddingBottom: getRealSize(10),
  },
  appsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    marginHorizontal: getRealSize(-12),
  },
  appItem: {
    width: '25%',
    alignItems: 'center',
    marginBottom: getRealSize(10),
    paddingHorizontal: getRealSize(12),
  },
  appIconContainer: {
    position: 'relative',
  },
  appIcon: {
    width: getRealSize(40),
    height: getRealSize(40),
  },
  redDot: {
    position: 'absolute',
    top: getRealSize(-2),
    right: getRealSize(-2),
    width: getRealSize(8),
    height: getRealSize(8),
    backgroundColor: '#FF4D4F',
    borderRadius: getRealSize(4),
    borderWidth: 1,
    borderColor: '#FFFFFF',
  },
  appName: {
    fontSize: getRealSize(12),
    color: '#666666',
    textAlign: 'center',
    fontWeight: '400',
  },
});

export default EntryPageLayout;
