import React from 'react';
import {
  addCrossStatusListener,
  removeCrossStatusListener,
} from '@/constant/cross_event';
import EntryPageLayout from './page';

interface PageProps {}

interface PageState {
  crossChange: boolean;
  pageShow: boolean;
}

class Entry extends React.Component<PageProps, PageState> {
  private crossStatusSubscription: any = null;
  constructor(props: PageProps) {
    super(props);
    this.state = {
      crossChange: false,
      pageShow: true,
    };
  }

  soyoungPageName() {
    return 'Entry';
  }

  /** 页面埋点 */
  soyoungPageInfo() {
    return {
      page_name: '应用',
      page_type: 'entry',
      tabName: 'RN:APPENTRY',
    };
  }

  componentDidMount() {
    // 使用新的事件监听方式
    this.crossStatusSubscription = addCrossStatusListener(
      (info: { status: boolean }) => {
        console.log('entry page,cross_status_change', info);
        this.setState({
          crossChange: !this.state.crossChange,
        });
      }
    );
  }
  componentWillUnmount() {
    // 清理事件监听器，防止内存泄漏
    if (this.crossStatusSubscription) {
      removeCrossStatusListener(this.crossStatusSubscription);
      this.crossStatusSubscription = null;
    }
  }

  didAppear() {
    this.setState({
      pageShow: true,
    });
  }

  willDisappear() {
    this.setState({
      pageShow: false,
    });
  }

  preferredStatusBarStyle() {
    // 0默认 1 白色 2 黑色
    return '2';
  }

  render() {
    return (
      <EntryPageLayout
        crossChange={this.state.crossChange}
        pageShowFlag={this.state.pageShow}
      />
    );
  }
}

export default Entry;
