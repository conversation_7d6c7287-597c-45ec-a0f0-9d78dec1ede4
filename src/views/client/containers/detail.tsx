import React, {
  useState,
  useEffect,
  useMemo,
  useCallback,
  useRef,
} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Animated,
  ScrollView,
} from 'react-native';
import { getRealSize, hexToRgb } from '@/common/utils';
import Header from '@/components/header';
import api, { FetchModule } from '@/common/api';
import { jumpReactNativePage } from '@/common/jumpPage';
import { ATrack } from '@soyoung/react-native-container';
import { flatListProps } from '@/constant/flatlist_props';
import { getNativeLoginInfo } from '@/common/getNativeLoginInfo';
// 导入子组件
import OverView from '../components/OverView';
import Order from '../components/Order';
import Implement from '../components/Implement';
import Visit from '../components/Visit';
import ReturnVisit from '../components/ReturnVisit';
import Consult from '../components/Consult';
import ClientDetailSkeleton from '../components/ClientDetailSkeleton';

import { UserInfo, PageInfo } from '../types/detail';

interface PageProps {
  route: {
    params: {
      params: {
        id: string;
        goVisit?: number;
      };
    };
  };
  pageShow: boolean;
  insets: {
    bottom: number;
    left: number;
    right: number;
    top: number;
  };
}

const ClientDetailContainer: React.FC<PageProps> = ({
  route,
  pageShow: _pageShow,
}) => {
  // 状态管理
  const [pageInfo, setPageInfo] = useState<PageInfo>({});
  const [tabActive, setTabActive] = useState<number>(0);
  const [orderTabActive, setOrderTabActive] = useState<number>(0);
  const [customerId, setCustomerId] = useState<string>('');
  const [visitUserInfo, setVisitUserInfo] = useState<UserInfo | null>(null);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null); // 添加用户信息状态
  const [currentLoginUserInfo, setCurrentLoginUserInfo] =
    useState<UserInfo | null>(null); // 当前登录用户信息
  const [loading, setLoading] = useState<boolean>(true); // 初始为true，显示骨架屏

  const orderRef = useRef<any>(null);

  // FlatList 相关状态
  const scrollY = useRef(new Animated.Value(0)).current;
  const flatListRef = useRef<FlatList>(null);

  const selectTab = useCallback((i: number) => {
    // 直接使用当前状态，避免依赖pageInfo导致循环
    setTabActive(i);
    setCurrentLoginUserInfo(null);
    // 切换tab时滚动到顶部，无动画
    flatListRef.current?.scrollToOffset({ offset: 0, animated: false });
  }, []);

  // 获取页面数据
  const getData = useCallback(
    async (id: string, goVisit: boolean = false, isInit: boolean = false) => {
      if (isInit) {
        setLoading(true);
      }
      try {
        const params = { customer_id: id };

        const { errorCode, responseData } = await api.pagefetch({
          path: '/chain-wxapp/v1/customer/getCustomerWxInfoPageHeadData',
          params,
          method: FetchModule.Method.POST,
        });

        if (errorCode === 0 && responseData) {
          const data = responseData;
          setPageInfo(data);
          // setCustomerId(data.customer.customer_id);

          // 处理动态数据的日期
          if (data.get_dynamic_res) {
            data.get_dynamic_res.forEach((item: any) => {
              if (item.date) {
                item.isGreen =
                  new Date() < new Date(item.date.replace(/-/g, '/'));
              }
            });
          }

          // 设置用户信息状态
          if (data.customer) {
            setUserInfo({
              id: +data.customer.customer_id_str || +id,
              name: data.customer.realname || '未知用户',
            });
            console.log('data.customer', data.customer, goVisit);
            // 如果需要设置visitUserInfo，在数据获取完成后直接处理
            if (goVisit && isInit) {
              setVisitUserInfo({
                id: +data.customer.customer_id_str,
                name: data.customer.realname || '未知用户',
              });

              // 获取当前登录用户信息
              try {
                const loginInfo = await getNativeLoginInfo();
                if (loginInfo.user_id) {
                  // 获取用户详细信息（包括真实姓名）
                  try {
                    const userDetailResponse: FetchModule.ResponseData<{
                      name?: string;
                    }> = await api.reactNativeFetch(
                      'chain-wxapp/v1/user/info',
                      {},
                      FetchModule.Method.POST,
                      false
                    );

                    if (
                      userDetailResponse.errorCode === 0 &&
                      userDetailResponse.responseData
                    ) {
                      setCurrentLoginUserInfo({
                        id: userDetailResponse.responseData.user_id,
                        name:
                          userDetailResponse.responseData.name || '当前用户',
                      });
                    } else {
                      // 如果获取详细信息失败，使用默认名称
                      setCurrentLoginUserInfo({
                        id: +loginInfo.user_id,
                        name: '当前用户',
                      });
                    }
                  } catch (userInfoError) {
                    console.error('获取用户详细信息失败:', userInfoError);
                    // 如果获取详细信息失败，使用默认名称
                    setCurrentLoginUserInfo({
                      id: +loginInfo.user_id,
                      name: '当前用户',
                    });
                  }
                }
              } catch (error) {
                console.error('获取登录用户信息失败:', error);
              }

              setTabActive(3); // 直接设置tab而不调用selectTab
            }
          }
        } else if (errorCode === 101) {
        } else {
          console.error('获取页面数据失败:', responseData.errorMsg);
        }
      } catch (error) {
        console.error('获取页面数据失败:', error);
      } finally {
        setLoading(false);
      }
    },
    [] // 移除selectTab依赖，避免循环
  );

  // 刷新数据
  const refresh = useCallback(() => {
    getData(customerId, false, false);
  }, [customerId, getData]);

  // 轻量级刷新：只更新数据，不显示骨架图
  const refreshData = useCallback(async (id: string) => {
    try {
      const params = { customer_id: id };

      const { errorCode, responseData } = await api.pagefetch({
        path: '/chain-wxapp/v1/customer/getCustomerWxInfoPageHeadData',
        params,
        method: FetchModule.Method.POST,
        isLoading: false, // 关键：不显示loading
      });

      if (errorCode === 0 && responseData) {
        const data = responseData;

        // 处理动态数据的日期
        if (data.get_dynamic_res) {
          data.get_dynamic_res.forEach((item: any) => {
            if (item.date) {
              item.isGreen =
                new Date() < new Date(item.date.replace(/-/g, '/'));
            }
          });
        }

        setPageInfo(data);
      }
    } catch (error) {
      console.error('刷新数据失败:', error);
    }
  }, []);

  // 查看客户资料
  const goEdit = useCallback((id: number) => {
    // 跳转到客户信息页面，传递客户ID
    jumpReactNativePage(`client/info?id=${id}`);
  }, []);

  // 构建 FlatList 数据源
  const flatListData = useMemo(() => {
    // 包含三个项目：用户信息卡片、Tab导航（吸顶）和Tab内容
    return [
      { type: 'user_card', key: 'user_card' },
      { type: 'tab_navigation', key: 'tab_nav' },
      { type: 'tab_content', key: `tab_${tabActive}` },
    ];
  }, [tabActive]);

  // 渲染用户信息卡片
  const renderUserCard = useCallback(() => {
    if (!pageInfo.customer) return null;

    return (
      <View style={styles.userCardBox}>
        <View style={styles.userCard}>
          <View style={styles.userCardLeft}>
            <View style={styles.userImg}>
              <Text style={styles.userImgText}>
                {pageInfo.customer.realname
                  ? pageInfo.customer.realname[0]
                  : ''}
              </Text>
            </View>
          </View>

          <View style={styles.userCardCenter}>
            <View style={styles.line1}>
              <Text style={styles.userName} numberOfLines={1}>
                {pageInfo.customer.realname}
              </Text>
              {pageInfo.customer.gender ? (
                <Text style={styles.userGender}>
                  {pageInfo.customer.gender === 1 ? '男' : '女'}
                </Text>
              ) : null}
              {pageInfo.customer.gender && pageInfo.customer.age ? (
                <Text style={styles.columneLine}>|</Text>
              ) : null}
              {pageInfo.customer.age ? (
                <Text style={styles.userAge}>{pageInfo.customer.age}岁</Text>
              ) : null}
            </View>

            <View style={styles.line2}>
              <Text style={styles.phoneText}>
                手机号： {pageInfo.customer.mobile}
                {(pageInfo.add_c_status === 1 ||
                  pageInfo.add_c_status === 2) && (
                  <Text style={styles.wxLabel}> @微信</Text>
                )}
              </Text>
            </View>

            <View style={styles.line3}>
              {pageInfo.tags?.dynamic_tag_list?.map(item => (
                <View
                  key={item.tag_id}
                  style={[
                    styles.tag,
                    item.tag_color
                      ? {
                          backgroundColor: hexToRgb(item.tag_color, 0.1),
                        }
                      : {},
                  ]}
                >
                  <Text
                    style={[
                      styles.tagText,
                      item.tag_color ? { color: item.tag_color } : {},
                    ]}
                  >
                    {item.name}
                  </Text>
                </View>
              ))}
            </View>
          </View>

          <View style={styles.userCardRight}>
            <ATrack style={styles.editBtn} onPress={() => goEdit(+customerId)}>
              <View style={styles.editBtn}>
                <Text style={styles.editBtnText}>查看资料</Text>
              </View>
            </ATrack>
          </View>
        </View>
      </View>
    );
  }, [pageInfo, goEdit, customerId]);

  // 渲染标签页导航
  const renderTabNavigation = useCallback(() => {
    const tabs = ['概览', '到访', '咨询', '回访', '订单', '执行'];
    const isScrollable = tabs.length > 6;

    const tabContent = tabs.map((title, index) => (
      <ATrack
        key={index}
        style={[
          isScrollable ? styles.tabItemScrollable : styles.tabItem,
          tabActive === index && styles.tabItemActive,
        ]}
        onPress={() => selectTab(index)}
      >
        <Text
          style={[
            styles.tabItemText,
            tabActive === index && styles.tabItemTextActive,
          ]}
        >
          {title}
        </Text>
        {tabActive === index && <View style={styles.tabActiveIndicator} />}
      </ATrack>
    ));

    return (
      <View style={styles.tabNavigationContainer}>
        {isScrollable ? (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.tabScrollContainer}
            contentContainerStyle={styles.tabScrollContent}
          >
            {tabContent}
          </ScrollView>
        ) : (
          <View style={styles.tabContainer}>{tabContent}</View>
        )}
      </View>
    );
  }, [selectTab, tabActive]);

  // 使用useMemo稳定customerIdStr的值，避免不必要的重新渲染
  const customerIdStr = useMemo(() => {
    return pageInfo.customer?.customer_id_str || customerId || '';
  }, [pageInfo.customer?.customer_id_str, customerId]);

  // 优化goOrder函数，使用useCallback避免重复创建
  const goOrder = useCallback((tabIndex: number) => {
    setTabActive(4);
    setOrderTabActive(tabIndex);
  }, []);

  // 渲染 FlatList 项目
  const renderFlatListItem = useCallback(
    ({ item }) => {
      // 渲染用户信息卡片
      if (item.type === 'user_card') {
        return renderUserCard();
      }

      // 渲染Tab导航栏（吸顶）
      if (item.type === 'tab_navigation') {
        return renderTabNavigation();
      }

      // 渲染Tab内容
      if (item.type === 'tab_content') {
        return (
          <View style={styles.tabContentContainer}>
            {(() => {
              switch (tabActive) {
                case 0:
                  return (
                    <OverView
                      pageInfo={pageInfo}
                      customerId={customerIdStr}
                      refresh={refresh}
                      refreshData={() => refreshData(customerIdStr)}
                      goOrder={goOrder}
                    />
                  );
                case 1:
                  return (
                    <Visit
                      customerId={customerIdStr}
                      userInfo={visitUserInfo}
                    />
                  );
                case 2:
                  return <Consult customerId={customerIdStr} />;
                case 3:
                  return (
                    <ReturnVisit
                      customerId={customerIdStr}
                      userInfo={userInfo}
                      currentLoginUserInfo={currentLoginUserInfo}
                      pageShow={_pageShow}
                    />
                  );
                case 4:
                  return (
                    <Order
                      customerId={customerIdStr}
                      tabActive={orderTabActive}
                      ref={orderRef}
                    />
                  );
                case 5:
                  return <Implement customerId={customerIdStr} />;
                default:
                  return (
                    <OverView
                      pageInfo={pageInfo}
                      customerId={customerIdStr}
                      refresh={refresh}
                      refreshData={() => refreshData(customerIdStr)}
                      goOrder={goOrder}
                    />
                  );
              }
            })()}
          </View>
        );
      }

      return null;
    },
    [
      renderUserCard,
      renderTabNavigation,
      tabActive,
      pageInfo,
      customerIdStr,
      refresh,
      goOrder,
      visitUserInfo,
      userInfo,
      currentLoginUserInfo,
      orderTabActive,
      refreshData,
      _pageShow,
    ]
  );

  // 键提取器
  const keyExtractor = useCallback(item => item.key, []);

  // 滚动事件处理
  const handleScroll = Animated.event(
    [{ nativeEvent: { contentOffset: { y: scrollY } } }],
    { useNativeDriver: true }
  );

  // 页面初始化
  useEffect(() => {
    const { id, goVisit } = route.params.params || {};

    if (id) {
      setCustomerId(id);
      // 直接调用getData，避免依赖getData函数引起的循环
      getData(id, !!goVisit, true);
    }
  }, [route.params.params]); // 只依赖route参数，避免循环

  // 加载状态
  if (loading) {
    return (
      <View style={styles.container}>
        <Header title='客户详情' bgColor='#fff' />
        <ClientDetailSkeleton />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header title='客户详情' bgColor='#fff' />
      <View style={[{ flex: 1 }]}>
        <Animated.FlatList
          ref={flatListRef}
          data={flatListData}
          renderItem={renderFlatListItem}
          keyExtractor={keyExtractor}
          stickyHeaderIndices={[1]} // 让第二个项目（tab导航）吸顶
          showsVerticalScrollIndicator={false}
          onScroll={handleScroll}
          scrollEventThrottle={16}
          style={styles.flatList}
          contentContainerStyle={styles.flatListContent}
          {...flatListProps}
        />
      </View>
    </View>
  );
};

// 样式定义
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },

  flatList: {
    flex: 1,
  },

  flatListContent: {
    backgroundColor: '#f8f8f8',
  },

  // listHeaderContainer 样式已移除，因为不再使用 ListHeaderComponent

  tabNavigationContainer: {
    backgroundColor: '#ffffff',
    zIndex: 1, // 确保吸顶时在其他内容之上
  },

  tabContentContainer: {
    backgroundColor: '#ffffff',
    flex: 1,
    minHeight: 600, // 确保有足够的高度显示内容
  },

  // 用户卡片样式
  userCardBox: {
    marginTop: getRealSize(15),
  },

  userCard: {
    height: getRealSize(94),
    backgroundColor: '#ffffff',
    marginHorizontal: getRealSize(15),
    marginBottom: getRealSize(10),
    padding: getRealSize(15),
    flexDirection: 'row',
    alignSelf: 'center',
  },

  userCardLeft: {
    width: getRealSize(40),
  },

  userImg: {
    width: getRealSize(40),
    height: getRealSize(40),
    backgroundColor: '#61B43E',
    borderRadius: getRealSize(20),
    alignItems: 'center',
    justifyContent: 'center',
  },

  userImgText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(18),
    color: '#ffffff',
    fontWeight: '500',
  },

  userCardCenter: {
    flex: 1,
    marginLeft: getRealSize(10),
  },

  line1: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  userName: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '500',
  },

  userGender: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(11),
    color: '#777777',
    marginLeft: getRealSize(5),
  },

  columneLine: {
    marginHorizontal: getRealSize(4.5),
    color: '#dedede',
    fontSize: getRealSize(9),
  },

  userAge: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(11),
    color: '#777777',
  },

  line2: {
    marginTop: getRealSize(5),
  },

  phoneText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(11),
    color: '#777777',
    fontWeight: '400',
  },

  wxLabel: {
    color: '#00c535',
    marginLeft: getRealSize(4),
  },

  line3: {
    marginTop: getRealSize(5),
    flexDirection: 'row',
    flexWrap: 'wrap',
    height: getRealSize(20),
    overflow: 'hidden',
  },

  tag: {
    alignItems: 'center',
    justifyContent: 'center',
    height: getRealSize(17.5),
    paddingHorizontal: getRealSize(5),
    backgroundColor: '#F5F5F5',
    marginRight: getRealSize(5),
    marginBottom: getRealSize(5),
  },

  tagText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(10),
    color: '#555555',
    fontWeight: '400',
  },

  userCardRight: {
    width: getRealSize(60),
    marginLeft: getRealSize(10),
    justifyContent: 'center',
  },

  editBtn: {
    width: getRealSize(62),
    height: getRealSize(26),
    backgroundColor: '#ffffff',
    borderWidth: getRealSize(1),
    borderColor: '#f0f0f0',
    alignItems: 'center',
    justifyContent: 'center',
  },

  editBtnText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(11),
    color: '#555555',
  },

  // 标签页样式
  tabContainer: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  tabScrollContainer: {
    backgroundColor: '#ffffff',
  },

  tabScrollContent: {
    paddingHorizontal: getRealSize(15),
    alignItems: 'center',
  },

  tabItem: {
    paddingHorizontal: getRealSize(11),
    height: getRealSize(42),
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },

  tabItemScrollable: {
    paddingHorizontal: getRealSize(11),
    height: getRealSize(42),
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    minWidth: getRealSize(60), // 确保最小宽度
  },

  tabItemActive: {
    position: 'relative',
  },

  tabItemText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(15),
    color: '#646464',
    fontWeight: '500',
    lineHeight: getRealSize(42),
  },

  tabItemTextActive: {
    color: '#030303',
  },

  tabActiveIndicator: {
    position: 'absolute',
    bottom: 0,
    left: '50%',
    width: getRealSize(20),
    height: getRealSize(2),
    backgroundColor: '#030303',
    borderRadius: getRealSize(1.5),
  },
});

export default ClientDetailContainer;
