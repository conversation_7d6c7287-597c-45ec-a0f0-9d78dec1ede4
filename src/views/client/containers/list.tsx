import React, { useEffect, use<PERSON><PERSON>back, useRef, useReducer } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { flatListProps } from '@/constant/flatlist_props';
import { getRealSize } from '@/common/utils';
import { ATrack } from '@soyoung/react-native-container';
import LinearGradient from 'react-native-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import api, { FetchModule } from '@/common/api';
import { jumpReactNativePage } from '@/common/jumpPage';
import { back } from '@soyoung/react-native-base';
import Empty from '@/components/Empty';
import { checkPermission } from '@/common/checkPermission';
// 导入组件
import {
  TabBar,
  SearchInput,
  QuickFilter,
  UserCard,
  ClientListSkeleton,
  FullPageSkeleton,
} from '../components';
import { cleanupSkeletonAnimation } from '../components/ClientListSkeleton';

// 类型定义
interface CustomerItem {
  base: {
    customer_id: string;
    customer_id_str: string;
    realname: string;
    gender?: number;
    age?: number;
    mobile?: string;
  };
  tag: {
    dynamic_tag_list: Array<{
      tag_id: string;
      name: string;
      tag_color?: string;
    }>;
  };
  belong: {
    join_c?: boolean;
    staff_info?: {
      external_user_id: string;
    };
  };
  action: {
    pre_visit_date_format?: string;
  };
}

interface FilterItem {
  data_type: number;
  filter?: {
    title: string;
    filter_condition: string;
  };
  filter_group?: {
    title: string;
    filter_list: Array<{
      title: string;
      filter_condition: string;
    }>;
  };
  listCurrent?: number;
}

interface ClientListContainerProps {
  insets: {
    bottom: number;
    left: number;
    right: number;
    top: number;
  };
  pageShow: boolean;
}

// 状态类型定义
interface ClientListState {
  dataList: CustomerItem[];
  page: number;
  hasMore: boolean;
  total: number;
  selectTabValue: string;
  keyWords: string;
  clientFilterNumber: number;
  topCurrent: number;
  publicList: FilterItem[];
  publicSonList: any[];
  publicSonListShow: boolean;
  refreshing: boolean;
  loading: boolean;
  initialLoading: boolean;
  tabSwitchLoading: boolean;
  initialized: boolean;
}

// 动作类型
type ClientListAction =
  | { type: 'SET_DATA_LIST'; payload: CustomerItem[] }
  | { type: 'APPEND_DATA_LIST'; payload: CustomerItem[] }
  | { type: 'REFRESH_DATA_LIST'; payload: CustomerItem[] }
  | { type: 'SET_PAGE'; payload: number }
  | { type: 'SET_HAS_MORE'; payload: boolean }
  | { type: 'SET_TOTAL'; payload: number }
  | { type: 'SET_SELECT_TAB'; payload: string }
  | { type: 'SET_KEYWORDS'; payload: string }
  | { type: 'SET_FILTER_NUMBER'; payload: number }
  | { type: 'SET_TOP_CURRENT'; payload: number }
  | { type: 'SET_PUBLIC_LIST'; payload: FilterItem[] }
  | {
      type: 'UPDATE_PUBLIC_LIST';
      payload: { index: number; listCurrent?: number };
    }
  | { type: 'SET_PUBLIC_SON_LIST'; payload: any[] }
  | { type: 'SET_PUBLIC_SON_LIST_SHOW'; payload: boolean }
  | {
      type: 'SET_LOADING_STATE';
      payload: Partial<
        Pick<
          ClientListState,
          'refreshing' | 'loading' | 'initialLoading' | 'tabSwitchLoading'
        >
      >;
    }
  | { type: 'SET_REFRESHING'; payload: boolean }
  | { type: 'SET_TAB_SWITCH_LOADING'; payload: boolean }
  | { type: 'RESET_DATA' }
  | { type: 'RESET_FILTER' }
  | { type: 'SET_INITIALIZED'; payload: boolean };

// 初始状态
const initialState: ClientListState = {
  dataList: [],
  page: 0,
  hasMore: true,
  total: 0,
  selectTabValue: '',
  keyWords: '',
  clientFilterNumber: 0,
  topCurrent: -1,
  publicList: [],
  publicSonList: [],
  publicSonListShow: false,
  refreshing: false,
  loading: false,
  initialLoading: true,
  tabSwitchLoading: false,
  initialized: false,
};

// Reducer 函数
const clientListReducer = (
  state: ClientListState,
  action: ClientListAction
): ClientListState => {
  switch (action.type) {
    case 'SET_DATA_LIST':
      return { ...state, dataList: action.payload };
    case 'APPEND_DATA_LIST':
      return { ...state, dataList: [...state.dataList, ...action.payload] };
    case 'REFRESH_DATA_LIST':
      return { ...state, dataList: action.payload };
    case 'SET_PAGE':
      return { ...state, page: action.payload };
    case 'SET_HAS_MORE':
      return { ...state, hasMore: action.payload };
    case 'SET_TOTAL':
      return { ...state, total: action.payload };
    case 'SET_SELECT_TAB':
      return { ...state, selectTabValue: action.payload };
    case 'SET_KEYWORDS':
      return { ...state, keyWords: action.payload };
    case 'SET_FILTER_NUMBER':
      return { ...state, clientFilterNumber: action.payload };
    case 'SET_TOP_CURRENT':
      return { ...state, topCurrent: action.payload };
    case 'SET_PUBLIC_LIST':
      return { ...state, publicList: action.payload };
    case 'UPDATE_PUBLIC_LIST':
      return {
        ...state,
        publicList: state.publicList.map((item, i) =>
          i === action.payload.index
            ? { ...item, listCurrent: action.payload.listCurrent }
            : item
        ),
      };
    case 'SET_PUBLIC_SON_LIST':
      return { ...state, publicSonList: action.payload };
    case 'SET_PUBLIC_SON_LIST_SHOW':
      return { ...state, publicSonListShow: action.payload };
    case 'SET_LOADING_STATE':
      return { ...state, ...action.payload };
    case 'SET_REFRESHING':
      return { ...state, refreshing: action.payload };
    case 'SET_TAB_SWITCH_LOADING':
      return { ...state, tabSwitchLoading: action.payload };
    case 'RESET_DATA':
      return {
        ...state,
        dataList: [],
        page: 0,
        hasMore: true,
        total: 0,
      };
    case 'RESET_FILTER':
      return {
        ...state,
        clientFilterNumber: 0,
        topCurrent: -1,
        publicList: state.publicList.map(item => ({
          ...item,
          listCurrent: undefined,
        })),
        publicSonListShow: false,
      };
    case 'SET_INITIALIZED':
      return { ...state, initialized: action.payload };
    default:
      return state;
  }
};

const ClientListContainer: React.FC<ClientListContainerProps> = ({
  insets,
  pageShow,
}) => {
  // 使用 useReducer 管理复杂状态
  const [state, dispatch] = useReducer(clientListReducer, initialState);

  // 组件挂载状态追踪，避免内存泄漏
  const mountedRef = useRef(true);
  const beforeIndexRef = useRef<number>(-1);
  const prevClientFilterNumberRef = useRef<number>(-1);
  const isQuickFilterOperationRef = useRef<boolean>(false);

  // 清理函数
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      // 清理骨架屏动画
      cleanupSkeletonAnimation();
    };
  }, []);

  // 获取快捷筛选列表
  const getQuickFilterList = useCallback(async () => {
    try {
      const { errorCode, responseData } = await api.pagefetch({
        path: '/chain-wxapp/v1/customer/getQuickFilterList',
        method: FetchModule.Method.POST,
        isLoading: false,
        params: {},
      });

      if (errorCode === 0 && responseData) {
        dispatch({
          type: 'SET_PUBLIC_LIST',
          payload: [
            ...(responseData.public_list || []),
            ...(responseData.private_list || []),
          ],
        });
      }
    } catch (error) {
      console.error('获取快捷筛选列表失败:', error);
    }
  }, []);

  // 获取客户数据
  const getData = useCallback(
    async (
      resetData = false,
      customPage?: number,
      customKeyWords?: string,
      customSelectTab?: string,
      isRefreshing = false
    ) => {
      // 只设置loading状态，不影响initialLoading和tabSwitchLoading
      if (!resetData) {
        dispatch({ type: 'SET_LOADING_STATE', payload: { loading: true } });
      }

      try {
        const currentPage = resetData
          ? 0
          : customPage !== undefined
            ? customPage
            : state.page;
        const currentKeyWords =
          customKeyWords !== undefined ? customKeyWords : state.keyWords;
        const currentSelectTab =
          customSelectTab !== undefined
            ? customSelectTab
            : state.selectTabValue;

        // 从AsyncStorage获取筛选数据
        const clientFilterDataStr =
          await AsyncStorage.getItem('clientFilterData');
        const clientFilterNumberStr =
          await AsyncStorage.getItem('clientFilterNumber');
        const filterData = clientFilterDataStr
          ? JSON.parse(clientFilterDataStr)
          : {};
        const filterNumber = clientFilterNumberStr
          ? parseInt(clientFilterNumberStr, 10)
          : 0;

        // 处理筛选数据格式
        const processedFilterData: any = {};
        Object.keys(filterData).forEach((key: string) => {
          const value = (filterData as any)[key];
          if (Array.isArray(value)) {
            processedFilterData[key] = {
              value: value.length
                ? value
                    .map((i: any) => (i.id || i.id === 0 ? i.id : i))
                    .join(',')
                : '',
            };
          } else if (typeof value === 'object' && value !== null) {
            if (
              ['Tag4UserTag', 'Tag4AllergyTag', 'Tag4DynamicTag'].includes(key)
            ) {
              processedFilterData[key] = {
                value: value.value?.length
                  ? value.value.map((i: any) => i.id).join(',')
                  : '',
                condition: value.condition,
              };
            } else {
              processedFilterData[key] = {
                value: `${value.id}`,
              };
            }
          } else {
            processedFilterData[key] = {
              value: `${value}`,
            };
          }
        });

        const { errorCode, responseData, errorMsg } = await api.pagefetch({
          path: '/chain-wxapp/v1/customer/searchCustomerList',
          method: FetchModule.Method.POST,
          isLoading: false,
          params: {
            index: currentPage,
            limit: 10,
            key_word: currentKeyWords,
            select_tab: currentSelectTab,
            filter: filterNumber ? JSON.stringify(processedFilterData) : '',
          },
        });

        dispatch({ type: 'SET_PUBLIC_SON_LIST_SHOW', payload: false });

        if (errorCode === 0 && responseData) {
          dispatch({ type: 'SET_TOTAL', payload: responseData.total || 0 });
          dispatch({
            type: 'SET_HAS_MORE',
            payload: responseData.has_more || false,
          });

          if (responseData.list) {
            if (resetData) {
              // 如果是刷新操作，使用 REFRESH_DATA_LIST 来平滑更新数据
              dispatch({
                type: isRefreshing ? 'REFRESH_DATA_LIST' : 'SET_DATA_LIST',
                payload: responseData.list,
              });
              dispatch({ type: 'SET_PAGE', payload: 1 });
            } else {
              dispatch({
                type: 'APPEND_DATA_LIST',
                payload: responseData.list,
              });
              dispatch({ type: 'SET_PAGE', payload: state.page + 1 });
            }
          }
        } else {
          console.log('获取数据失败', errorMsg);
        }
      } catch (error) {
        console.log('获取数据失败', error);
      } finally {
        dispatch({
          type: 'SET_LOADING_STATE',
          payload: {
            loading: false,
            refreshing: false,
            initialLoading: false,
            tabSwitchLoading: false,
          },
        });
        dispatch({ type: 'SET_INITIALIZED', payload: true });
      }
    },
    [state.page, state.keyWords, state.selectTabValue]
  );

  // 重置存储并获取数据
  const resetStorageAndFetchData = useCallback(async () => {
    await AsyncStorage.removeItem('clientFilterData');
    await AsyncStorage.removeItem('clientFilterNumber');
    dispatch({ type: 'SET_FILTER_NUMBER', payload: 0 });
    dispatch({ type: 'SET_TOP_CURRENT', payload: -1 });
    dispatch({
      type: 'SET_PUBLIC_LIST',
      payload: state.publicList.map(item => ({
        ...item,
        listCurrent: undefined,
      })),
    });
    dispatch({ type: 'RESET_DATA' });
    dispatch({ type: 'SET_TAB_SWITCH_LOADING', payload: true });
    getData(true);
  }, [getData, state.publicList]);

  // 顶部标签点击
  const topClick = useCallback(
    async (index: number, item: FilterItem | null) => {
      if (index === -1) {
        resetStorageAndFetchData();
        return;
      }

      if (!item) return;

      switch (item.data_type) {
        case 2:
          // 标记为快捷筛选操作
          isQuickFilterOperationRef.current = true;

          dispatch({
            type: 'SET_PUBLIC_LIST',
            payload: state.publicList.map(listItem => ({
              ...listItem,
              listCurrent: undefined,
            })),
          });
          dispatch({ type: 'SET_PUBLIC_SON_LIST_SHOW', payload: false });
          if (item.filter) {
            const filterData =
              JSON.parse(item.filter.filter_condition).wx || {};
            const filterNumber = Object.keys(filterData).filter(key =>
              isValidData(filterData[key])
            ).length;
            await AsyncStorage.setItem(
              'clientFilterData',
              JSON.stringify(filterData)
            );
            await AsyncStorage.setItem(
              'clientFilterNumber',
              filterNumber.toString()
            );
            dispatch({ type: 'SET_FILTER_NUMBER', payload: filterNumber });
          }
          dispatch({ type: 'RESET_DATA' });
          dispatch({ type: 'SET_PAGE', payload: 0 });
          dispatch({ type: 'SET_TAB_SWITCH_LOADING', payload: true });
          getData(true, 0, state.keyWords, state.selectTabValue);
          beforeIndexRef.current = index;

          // 在下一个事件循环中重置标志位
          setTimeout(() => {
            isQuickFilterOperationRef.current = false;
          }, 0);
          break;
        case 1:
          if (item.filter_group) {
            dispatch({
              type: 'SET_PUBLIC_SON_LIST',
              payload: item.filter_group.filter_list,
            });
            if (state.topCurrent === index) {
              dispatch({
                type: 'SET_PUBLIC_SON_LIST_SHOW',
                payload: !state.publicSonListShow,
              });
              return;
            } else {
              dispatch({ type: 'SET_PUBLIC_SON_LIST_SHOW', payload: true });
            }
          }
          break;
        default:
          break;
      }
      dispatch({ type: 'SET_TOP_CURRENT', payload: index });
    },
    [
      resetStorageAndFetchData,
      state.publicList,
      state.keyWords,
      state.selectTabValue,
      state.topCurrent,
      state.publicSonListShow,
      getData,
    ]
  );

  // 点击下拉遮罩
  const clickPublic = useCallback(() => {
    dispatch({ type: 'SET_TOP_CURRENT', payload: beforeIndexRef.current });
    dispatch({ type: 'SET_PUBLIC_SON_LIST_SHOW', payload: false });
  }, []);

  // 下拉列表点击
  const listClick = useCallback(
    async (index: number, item: any) => {
      // 标记为快捷筛选操作
      isQuickFilterOperationRef.current = true;

      // 合并状态更新，避免中间状态导致的激活态问题
      dispatch({
        type: 'SET_PUBLIC_LIST',
        payload: state.publicList.map((filterItem, i) => ({
          ...filterItem,
          listCurrent: i === state.topCurrent ? index : undefined,
        })),
      });
      beforeIndexRef.current = state.topCurrent;
      dispatch({ type: 'SET_PUBLIC_SON_LIST_SHOW', payload: false });

      const filterData = JSON.parse(item.filter_condition).wx || {};
      const filterNumber = Object.keys(filterData).filter(key =>
        isValidData(filterData[key])
      ).length;
      await AsyncStorage.setItem(
        'clientFilterData',
        JSON.stringify(filterData)
      );
      await AsyncStorage.setItem('clientFilterNumber', filterNumber.toString());
      dispatch({ type: 'SET_FILTER_NUMBER', payload: filterNumber });
      dispatch({ type: 'RESET_DATA' });
      dispatch({ type: 'SET_PAGE', payload: 0 });
      dispatch({ type: 'SET_TAB_SWITCH_LOADING', payload: true });
      getData(true, 0, state.keyWords, state.selectTabValue);

      // 在下一个事件循环中重置标志位，确保 useEffect 能正确判断
      setTimeout(() => {
        isQuickFilterOperationRef.current = false;
      }, 0);
    },
    [
      state.publicList,
      state.topCurrent,
      state.keyWords,
      state.selectTabValue,
      getData,
    ]
  );

  // 验证数据有效性
  const isValidData = (data: any): boolean => {
    if (Array.isArray(data)) {
      return data.length > 0;
    }
    if (typeof data === 'object' && data !== null) {
      return !!(
        data.name ||
        (!data.length && data.condition === 'empty') ||
        data.length
      );
    }
    return !!(data || data === 0);
  };

  // 重置快捷筛选组件
  const resetQuickFilter = useCallback(() => {
    dispatch({ type: 'SET_TOP_CURRENT', payload: -1 });
    dispatch({ type: 'SET_PUBLIC_SON_LIST_SHOW', payload: false });
    dispatch({
      type: 'SET_PUBLIC_LIST',
      payload: state.publicList.map(item => ({
        ...item,
        listCurrent: undefined,
      })),
    });
    beforeIndexRef.current = -1;
  }, [state.publicList]);

  // 打开筛选页面
  const handleOpenFilter = useCallback(() => {
    jumpReactNativePage(`client/filter?selectTab=${state.selectTabValue}`);
  }, [state.selectTabValue]);

  // 标签切换
  const selectTab = useCallback(
    async (type: string) => {
      dispatch({ type: 'SET_PAGE', payload: 0 });
      dispatch({ type: 'RESET_DATA' });
      dispatch({ type: 'SET_KEYWORDS', payload: '' });
      dispatch({ type: 'SET_SELECT_TAB', payload: type });
      dispatch({ type: 'SET_TAB_SWITCH_LOADING', payload: true });
      await AsyncStorage.removeItem('clientFilterData');
      await AsyncStorage.removeItem('clientFilterNumber');
      dispatch({ type: 'SET_PUBLIC_SON_LIST_SHOW', payload: false });
      dispatch({
        type: 'SET_PUBLIC_LIST',
        payload: state.publicList.map(item => ({
          ...item,
          listCurrent: undefined,
        })),
      });
      dispatch({ type: 'SET_TOP_CURRENT', payload: -1 });
      beforeIndexRef.current = -1;
      dispatch({ type: 'SET_FILTER_NUMBER', payload: 0 });
      getData(true, 0, '', type);
    },
    [getData, state.publicList]
  );

  // 清除关键字
  const delKeyWords = useCallback(() => {
    dispatch({ type: 'SET_KEYWORDS', payload: '' });
    dispatch({ type: 'SET_PAGE', payload: 0 });
    dispatch({ type: 'RESET_DATA' });
    dispatch({ type: 'SET_TAB_SWITCH_LOADING', payload: true });
    getData(true, 0, '', state.selectTabValue);
  }, [getData, state.selectTabValue]);

  // 搜索提交
  const handleClickToSearch = useCallback(() => {
    dispatch({ type: 'SET_PAGE', payload: 0 });
    dispatch({ type: 'RESET_DATA' });
    dispatch({ type: 'SET_TAB_SWITCH_LOADING', payload: true });
    getData(true, 0, state.keyWords, state.selectTabValue);
  }, [getData, state.keyWords, state.selectTabValue]);

  // 跳转到客户详情
  const goDetail = useCallback((item: CustomerItem) => {
    jumpReactNativePage(`client/detail?id=${item.base.customer_id_str}`);
  }, []);

  // 跳转到微信聊天
  const goToChat = useCallback(async () => {
    // Alert.alert('微信聊天', `打开与 ${staffInfo.external_user_id} 的聊天`);
  }, []);

  // 下拉刷新
  const onRefresh = useCallback(async () => {
    dispatch({ type: 'SET_REFRESHING', payload: true });
    dispatch({ type: 'SET_PAGE', payload: 0 });
    // 重置 quickFilter 相关状态，但保留 keyWords
    dispatch({ type: 'SET_TOP_CURRENT', payload: -1 });
    await AsyncStorage.removeItem('clientFilterData');
    await AsyncStorage.removeItem('clientFilterNumber');
    dispatch({ type: 'SET_FILTER_NUMBER', payload: 0 });
    dispatch({
      type: 'SET_PUBLIC_LIST',
      payload: state.publicList.map(item => ({
        ...item,
        listCurrent: undefined,
      })),
    });
    dispatch({ type: 'SET_PUBLIC_SON_LIST_SHOW', payload: false });
    beforeIndexRef.current = -1;
    // 刷新时直接请求新数据，不清空现有数据
    getData(true, 0, state.keyWords, state.selectTabValue, true);
  }, [getData, state.keyWords, state.selectTabValue, state.publicList]);

  // 加载更多
  const loadMore = useCallback(() => {
    if (state.hasMore && !state.loading) {
      getData(false, state.page, state.keyWords, state.selectTabValue);
    }
  }, [
    state.hasMore,
    state.loading,
    getData,
    state.page,
    state.keyWords,
    state.selectTabValue,
  ]);

  // 初始化
  useEffect(() => {
    if (!pageShow) {
      return;
    }

    const initializeData = async () => {
      if (!state.initialized) {
        // 首次初始化时正常加载数据
        dispatch({
          type: 'SET_LOADING_STATE',
          payload: { initialLoading: true },
        });
        try {
          const clientFilterNumberStr =
            await AsyncStorage.getItem('clientFilterNumber');

          const storedClientFilterNumber = clientFilterNumberStr
            ? parseInt(clientFilterNumberStr, 10)
            : 0;

          dispatch({
            type: 'SET_FILTER_NUMBER',
            payload: storedClientFilterNumber,
          });

          const initialTab = (await checkPermission('customer:mine'))
            ? 'user'
            : 'all';
          dispatch({ type: 'SET_SELECT_TAB', payload: initialTab });

          await getQuickFilterList();
          await getData(true, 0, '', initialTab);
        } catch (error) {
          dispatch({
            type: 'SET_LOADING_STATE',
            payload: {
              initialLoading: false,
              loading: false,
            },
          });
          dispatch({ type: 'SET_INITIALIZED', payload: true });
        }
      } else {
        // 已初始化后，检查是否需要刷新
        try {
          const shouldRefresh = await AsyncStorage.getItem(
            'shouldRefreshFromFilter'
          );

          if (shouldRefresh === 'true') {
            // 从filter页面返回且数据发生变化，需要刷新
            const clientFilterNumberStr =
              await AsyncStorage.getItem('clientFilterNumber');
            const storedClientFilterNumber = clientFilterNumberStr
              ? parseInt(clientFilterNumberStr, 10)
              : 0;
            dispatch({
              type: 'SET_FILTER_NUMBER',
              payload: storedClientFilterNumber,
            });

            dispatch({ type: 'SET_TAB_SWITCH_LOADING', payload: true });
            getData(true, 0, state.keyWords, state.selectTabValue);

            // 清除刷新标识
            await AsyncStorage.removeItem('shouldRefreshFromFilter');
          } else {
            // 从其他页面返回，只更新filter数量显示，不重新请求数据
            const clientFilterNumberStr =
              await AsyncStorage.getItem('clientFilterNumber');
            const storedClientFilterNumber = clientFilterNumberStr
              ? parseInt(clientFilterNumberStr, 10)
              : 0;
            dispatch({
              type: 'SET_FILTER_NUMBER',
              payload: storedClientFilterNumber,
            });
          }
        } catch (error) {
          console.error('检查刷新标识失败:', error);
        }
      }
    };

    initializeData();
  }, [
    getData,
    getQuickFilterList,
    state.initialized,
    pageShow,
    state.selectTabValue,
  ]);

  // 监听筛选条件变化，重置快捷筛选
  useEffect(() => {
    if (state.initialized && prevClientFilterNumberRef.current !== -1) {
      // 只在已初始化且不是首次设置时才重置
      // 如果是快捷筛选操作导致的变化，则不重置
      if (
        prevClientFilterNumberRef.current !== state.clientFilterNumber &&
        !isQuickFilterOperationRef.current
      ) {
        resetQuickFilter();
      }
    }
    prevClientFilterNumberRef.current = state.clientFilterNumber;
  }, [state.clientFilterNumber, state.initialized, resetQuickFilter]);

  // 渲染客户卡片
  const renderItem = useCallback(
    ({ item }: { item: CustomerItem }) => (
      <UserCard
        item={item}
        onPress={() => goDetail(item)}
        onChatPress={goToChat}
      />
    ),
    [goDetail, goToChat]
  );

  const renderEmpty = useCallback(() => {
    return (
      <View style={styles.emptyContainer}>
        <Empty />
      </View>
    );
  }, []);

  // 渲染加载更多
  const renderFooter = useCallback(() => {
    if (state.loading && state.dataList.length > 0) {
      return (
        <View style={styles.loadingFooter}>
          <ActivityIndicator size='small' color='#61B43E' />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      );
    }
    if (!state.hasMore && state.dataList.length > 0) {
      return (
        <View style={styles.loadingFooter}>
          <Text style={styles.loadingText}>没有更多啦</Text>
        </View>
      );
    }
    return null;
  }, [state.loading, state.dataList.length, state.hasMore]);

  // 注意：移除 getItemLayout，因为 UserCard 的高度是动态的
  // 动态高度包括：手机号行的条件渲染、标签的 flexWrap 等

  // 优化的 keyExtractor
  const keyExtractor = useCallback(
    (item: CustomerItem) => item.base.customer_id,
    []
  );

  const handleBack = useCallback(() => {
    resetStorageAndFetchData();
    back();
  }, [resetStorageAndFetchData]);

  return (
    <>
      {state.initialLoading ? (
        // 首次加载时显示完整页面骨架屏
        <FullPageSkeleton insets={insets} />
      ) : (
        <View style={[styles.container, { paddingTop: insets.top }]}>
          {/* 顶部标签栏 */}
          <TabBar
            selectTabValue={state.selectTabValue}
            onSelectTab={selectTab}
            onBack={handleBack}
          />

          {/* 搜索和筛选区域 */}
          <View style={styles.filterSection}>
            <SearchInput
              value={state.keyWords}
              onChangeText={(text: string) =>
                dispatch({ type: 'SET_KEYWORDS', payload: text })
              }
              onSubmit={handleClickToSearch}
              onClear={delKeyWords}
            />

            <ATrack style={styles.filterIconBox} onPress={handleOpenFilter}>
              <Image
                source={{
                  uri: 'https://static.soyoung.com/sy-pre/2hw71fsbwugfk-1712751000687.png',
                }}
                style={styles.filterIcon}
              />
              {state.clientFilterNumber > 0 && (
                <View style={styles.clientFilterNumber}>
                  <Text style={styles.filterNumberText}>
                    {state.clientFilterNumber > 99
                      ? '99+'
                      : state.clientFilterNumber}
                  </Text>
                </View>
              )}
            </ATrack>
          </View>

          {/* 主要内容区域 */}
          <View style={styles.main}>
            {/* 快捷筛选 */}
            <QuickFilter
              publicList={state.publicList}
              topCurrent={state.topCurrent}
              publicSonList={state.publicSonList}
              publicSonListShow={state.publicSonListShow}
              onTopClick={topClick}
              onListClick={listClick}
              onClickPublic={clickPublic}
            />

            {/* 统计信息 */}
            {state.total > 0 && (
              <LinearGradient
                colors={['#ffffff', '#f8f8f8']}
                start={{ x: 0, y: 0 }}
                end={{ x: 0, y: 1 }}
                locations={[0.75, 1]}
              >
                <View style={styles.tips}>
                  <Text style={styles.tipsText}>筛选出{state.total}位客户</Text>
                </View>
              </LinearGradient>
            )}

            {/* 客户列表 */}
            {state.tabSwitchLoading ? (
              // 标签切换时只显示列表区域骨架屏
              <ClientListSkeleton count={6} />
            ) : (
              <FlatList
                data={state.dataList}
                renderItem={renderItem}
                keyExtractor={keyExtractor}
                contentContainerStyle={styles.listContainer}
                contentOffset={{ x: 0, y: 0 }}
                refreshControl={
                  <RefreshControl
                    refreshing={state.refreshing}
                    onRefresh={onRefresh}
                  />
                }
                onEndReached={loadMore}
                ListFooterComponent={renderFooter}
                ListEmptyComponent={!state.loading ? renderEmpty() : null}
                showsVerticalScrollIndicator={false}
                bounces={true}
                extraData={state.dataList.length}
                {...flatListProps}
              />
            )}
          </View>
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  filterSection: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getRealSize(10),
    paddingTop: getRealSize(11),
  },
  filterIconBox: {
    marginLeft: getRealSize(11),
    position: 'relative',
  },
  filterIcon: {
    width: getRealSize(44),
    height: getRealSize(44),
  },
  clientFilterNumber: {
    position: 'absolute',
    right: 0,
    top: 0,
    minWidth: getRealSize(14),
    height: getRealSize(14),
    backgroundColor: '#ff4040',
    borderRadius: getRealSize(7),
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: getRealSize(5),
  },
  filterNumberText: {
    fontSize: getRealSize(10),
    color: '#ffffff',
    fontWeight: '500',
  },
  main: {
    flex: 1,
    backgroundColor: '#f8f8f8',
    marginTop: getRealSize(11),
    overflow: 'hidden',
  },
  tips: {
    paddingLeft: getRealSize(15),
    paddingBottom: getRealSize(15),
  },
  tipsText: {
    fontSize: getRealSize(12),
    color: '#aaabb3',
    lineHeight: getRealSize(20),
  },
  listContainer: {
    paddingTop: getRealSize(5),
    paddingHorizontal: getRealSize(15),
    paddingBottom: getRealSize(20),
  },
  loadingFooter: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: getRealSize(10),
  },
  loadingText: {
    fontSize: getRealSize(12),
    color: '#777777',
    marginLeft: getRealSize(5),
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: getRealSize(500),
  },
});

export default ClientListContainer;
