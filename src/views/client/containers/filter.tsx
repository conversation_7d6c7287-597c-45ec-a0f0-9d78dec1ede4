import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, ScrollView, StyleSheet, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import jsApi from '@soyoung/react-native-jsapi';
import { ATrack } from '@soyoung/react-native-container';
import { getRealSize } from '@/common/utils';
import api, { FetchModule } from '@/common/api';
import Header from '@/components/header';
// 导入基础组件
import ButtonSelect from '@/components/ButtonSelect';
import InputText from '@/components/InputText';
import InputNumber from '@/components/InputNumber';
import CommonSelect from '@/components/CommonSelect';
import DateInput from '@/components/DateInput';
import BirthDayDateInput from '../components/BirthDayDateInput';
import ProjectSelect from '@/components/ProjectSelect';
import SourceSelect from '@/components/SourceSelect';
import StaffSelect from '@/components/StaffSelect';
import EmployeeSelect from '@/components/EmployeeSelect';
import PerformanceTagSelect from '@/components/PerformanceTagSelect';
import TagSelect from '../components/TagSelect';

// 条件映射
const conditionMap: Record<string, string> = {
  default: '',
  or: '包含任意范围',
  and: '包含全部范围',
  not: '不包含以下范围',
  empty: '无标签',
};

interface PageProps {
  route: {
    params: {
      params: {
        selectTab: string;
      };
    };
  };
  insets: {
    bottom: number;
    left: number;
    right: number;
    top: number;
  };
}

interface FilterItem {
  key: string;
  title: string;
  type: 'button' | 'text' | 'number' | 'date' | 'select' | 'custom';
  attribute?: {
    placeholder?: string[];
    range_separator?: string;
    range?: number;
    multiple?: number;
    fixed?: number[];
    max_length?: number;
    default_value?: string;
  };
  options?: Array<{
    label: string;
    value: number | string;
  }>;
}

interface FilterList {
  title: string;
  items: FilterItem[];
}

const FilterPage: React.FC<PageProps> = ({ route, insets }) => {
  const selectTab = route.params.params.selectTab;
  // 状态管理
  const [currentTab, setCurrentTab] = useState<number>(0);
  const [filterList, setFilterList] = useState<FilterList[]>([]);
  const [filterData, setFilterData] = useState<Record<string, any>>({});
  const [hasReset, setHasReset] = useState<boolean>(false);

  // 获取筛选配置数据
  const getData = useCallback(async () => {
    try {
      const { errorCode, responseData, errorMsg } = await api.pagefetch({
        path: '/chain-wxapp/v1/customer/getCustomerFilter',
        method: FetchModule.Method.POST,
        params: {
          select_tab: selectTab,
        },
      });

      if (errorCode === 0 && responseData) {
        // 将后端返回的数据转换为前端需要的格式
        const filterList: FilterList[] = responseData.filter_list || [];
        setFilterList(filterList);
      } else {
        console.error('获取筛选配置失败:', errorMsg);
      }
    } catch (error) {
      console.error('获取筛选配置异常:', error);
    }
  }, []);

  // 从存储中恢复筛选数据
  const restoreFilterData = useCallback(async () => {
    try {
      const clientFilterDataStr =
        await AsyncStorage.getItem('clientFilterData');
      const storedData = clientFilterDataStr
        ? JSON.parse(clientFilterDataStr)
        : {};
      setFilterData(storedData);
      setHasReset(false); // 恢复数据时重置重置标识
    } catch (error) {
      console.error('恢复筛选数据失败:', error);
      setFilterData({});
      setHasReset(false);
    }
  }, []);

  // 验证数据是否有效
  const isValidData = useCallback((data: any): boolean => {
    if (Array.isArray(data)) {
      return data.length > 0;
    }
    if (typeof data === 'object' && data !== null) {
      return !!(
        data.name ||
        (!data.value?.length && data.condition === 'empty') ||
        data.value?.length
      );
    }
    return !!(data || data === 0);
  }, []);

  // 计算筛选条件数量
  const getFilterNumber = useCallback((): number => {
    return Object.keys(filterData).filter(key => isValidData(filterData[key]))
      .length;
  }, [filterData, isValidData]);

  // 点击左侧标签
  const handleClickTab = useCallback((index: number) => {
    setCurrentTab(index);
  }, []);

  // 重置筛选条件
  const handleReset = useCallback(async () => {
    try {
      // 清空状态数据
      setFilterData({});
      setHasReset(true); // 设置重置标识

      // 不再清空存储数据，只清空页面状态
      // await AsyncStorage.removeItem('clientFilterData');
      // await AsyncStorage.removeItem('clientFilterNumber');
    } catch (error) {
      console.error('重置筛选数据失败:', error);
    }
  }, []);

  // 确认筛选
  const handleConfirm = useCallback(async () => {
    const filterNumber = getFilterNumber();

    try {
      // 获取当前存储的筛选数据用于比较
      const currentFilterDataStr =
        await AsyncStorage.getItem('clientFilterData');
      const currentFilterData = currentFilterDataStr
        ? JSON.parse(currentFilterDataStr)
        : {};

      // 检查筛选数据是否发生变化或者执行了重置操作
      const hasDataChanged =
        JSON.stringify(currentFilterData) !== JSON.stringify(filterData);

      // 保存筛选数据到存储
      await AsyncStorage.setItem(
        'clientFilterData',
        JSON.stringify(filterData)
      );
      await AsyncStorage.setItem('clientFilterNumber', filterNumber.toString());

      // 如果数据发生变化或执行了重置操作，设置刷新标识
      if (hasDataChanged || hasReset) {
        await AsyncStorage.setItem('shouldRefreshFromFilter', 'true');
      }

      // 返回到上一页，并传递结果
      // navigation.goBack();
      jsApi.toNative('backAnimated', {
        transitionType: '0',
        disableAnimation: '0',
      });
    } catch (error) {
      console.error('保存筛选数据失败:', error);
      Alert.alert('错误', '保存筛选条件失败');
    }
  }, [filterData, getFilterNumber, hasReset]);

  // 更新筛选数据
  const updateFilterData = useCallback(
    (key: string, value: any, _type: string) => {
      console.log('updateFilterData', key, value, _type);
      setFilterData(prevState => ({
        ...prevState,
        [key]: value,
      }));
      // 当用户修改筛选条件时，重置重置标识
      setHasReset(false);
    },
    []
  );

  // 渲染条件标签
  const renderConditionLabel = useCallback(
    (item: FilterItem) => {
      // 只为特定的标签类型显示条件标签
      if (
        ['Tag4UserTag', 'Tag4AllergyTag', 'Tag4DynamicTag'].includes(item.key)
      ) {
        const value = filterData[item.key];
        if (
          value &&
          ((value.value && value.value.length > 0) ||
            value.condition === 'empty')
        ) {
          const conditionText = conditionMap[value.condition || 'default'];
          if (conditionText) {
            return <Text style={styles.conditionLabel}>{conditionText}</Text>;
          }
        }
      }
      return null;
    },
    [filterData]
  );

  // 渲染筛选项内容
  const renderFilterItemContent = useCallback(
    (item: FilterItem) => {
      const value = filterData[item.key];

      switch (item.type) {
        case 'button':
          return (
            <ButtonSelect
              options={item.options || []}
              value={value}
              multiple={item.attribute?.multiple === 1}
              onChange={newValue =>
                updateFilterData(item.key, newValue, item.type)
              }
            />
          );
        case 'text':
          return (
            <InputText
              value={value || ''}
              placeholder={item.attribute?.placeholder?.[0]}
              maxLength={item.attribute?.max_length}
              onChange={newValue =>
                updateFilterData(item.key, newValue, item.type)
              }
            />
          );
        case 'number':
          return (
            <InputNumber
              value={value || ''}
              attribute={item.attribute || {}}
              onChange={newValue =>
                updateFilterData(item.key, newValue, item.type)
              }
            />
          );
        case 'date':
          return (
            <DateInput
              value={value}
              placeholder={item.attribute?.placeholder}
              isRange={item.attribute?.range === 1}
              rangeSeparator={item.attribute?.range_separator || '至'}
              convertToTimestamp={item.attribute?.range === 1}
              onChange={newValue =>
                updateFilterData(item.key, newValue, item.type)
              }
            />
          );
        case 'select':
          return (
            <CommonSelect
              active={value || []}
              title={item.title}
              multiple={item?.attribute?.multiple === 1}
              options={item.options || []}
              onChange={newValue =>
                updateFilterData(item.key, newValue, item.type)
              }
            />
          );
        case 'custom':
          switch (true) {
            case [
              'Appointment4Project',
              'Consultation4Project',
              'Consumption4RecentTreatmentProject',
              'Consumption4PayProject',
              'Consumption4TreatmentProject',
            ].includes(item.key):
              return (
                <ProjectSelect
                  active={value || []}
                  title={item.title}
                  multiple={true}
                  onChange={(newValue: any) => {
                    updateFilterData(item.key, newValue, item.type);
                  }}
                />
              );
            case ['PrivateDomain4Staff'].includes(item.key):
              return (
                <StaffSelect
                  active={value || []}
                  title={item.title}
                  multiple={true}
                  onChange={(newValue: any) => {
                    updateFilterData(item.key, newValue, item.type);
                  }}
                />
              );
            case ['Tag4UserTag', 'Tag4AllergyTag', 'Tag4DynamicTag'].includes(
              item.key
            ):
              return (
                <TagSelect
                  active={value || { value: [], condition: 'or' }}
                  title={item.title}
                  type={
                    ['Tag4UserTag', 'Tag4AllergyTag', 'Tag4DynamicTag'].indexOf(
                      item.key
                    ) + 1
                  }
                  onChange={(newValue: any) => {
                    updateFilterData(item.key, newValue, item.type);
                  }}
                />
              );
            case [
              'Consultation4CreateUser',
              'ReturnVisit4User',
              'CustomerAttribution4Consultant',
              'CustomerAttribution4Doctor',
              'CustomerAttribution4NetworkConsultant',
              'CustomerAttribution4Beautician',
              'CustomerAttribution4CustomerService',
            ].includes(item.key):
              return (
                <EmployeeSelect
                  active={value || []}
                  title={item.title}
                  multiple={true}
                  onChange={(newValue: any) => {
                    updateFilterData(item.key, newValue, item.type);
                  }}
                />
              );
            case [
              'Consumption4PayProjectTag',
              'Consumption4RecentTreatmentProjectTag',
              'Consumption4TreatmentProjectTag',
            ].includes(item.key):
              return (
                <PerformanceTagSelect
                  active={value || []}
                  title={item.title}
                  pid={Number(item.attribute?.default_value)}
                  multiple={true}
                  onChange={(newValue: any) => {
                    updateFilterData(item.key, newValue, item.type);
                  }}
                />
              );
            case ['CustomerAttribution4Source'].includes(item.key):
              return (
                <SourceSelect
                  active={value || []}
                  title={item.title}
                  multiple={true}
                  onChange={(newValue: any) => {
                    updateFilterData(item.key, newValue, item.type);
                  }}
                />
              );
            case ['UserData4Birthday'].includes(item.key):
              return (
                <BirthDayDateInput
                  value={value}
                  placeholder={['开始月份', '结束月份']}
                  isRange={true}
                  rangeSeparator={item.attribute?.range_separator || '至'}
                  convertToTimestamp={true}
                  onChange={newValue =>
                    updateFilterData(item.key, newValue, item.type)
                  }
                />
              );
          }
      }
    },
    [filterData, updateFilterData]
  );

  // 组件挂载时初始化数据
  useEffect(() => {
    const initializeData = async () => {
      await getData();
      await restoreFilterData();
    };

    initializeData();
  }, [getData, restoreFilterData]);

  const filterNumber = getFilterNumber();
  const currentItems = filterList[currentTab]?.items || [];

  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      <Header hideBack={false} title='筛选客户' bgColor='#fff' />
      <View style={styles.body}>
        {/* 左侧标签栏 */}
        <ScrollView
          style={styles.tabContainer}
          contentContainerStyle={styles.tabScrollContent}
          showsVerticalScrollIndicator={false}
        >
          {filterList.map((item, _index) => {
            const hasActiveFilter = item.items
              .map(i => i.key)
              .some(key => isValidData(filterData[key]));

            return (
              <ATrack
                key={_index}
                style={[
                  styles.tabItem,
                  currentTab === _index && styles.activeTabItem,
                ]}
                onPress={() => handleClickTab(_index)}
              >
                {hasActiveFilter && <View style={styles.activePoint} />}
                <Text
                  style={[
                    styles.tabText,
                    currentTab === _index && styles.activeTabText,
                  ]}
                >
                  {item.title}
                </Text>
              </ATrack>
            );
          })}
        </ScrollView>

        {/* 右侧筛选内容 */}
        <ScrollView
          style={styles.contentContainer}
          showsVerticalScrollIndicator={false}
        >
          {currentItems.map(item => (
            <View key={item.key} style={styles.filterItemContainer}>
              <View style={styles.filterItemTitleContainer}>
                <Text style={styles.filterItemTitle}>{item.title}</Text>
                {renderConditionLabel(item)}
              </View>
              <View style={styles.filterItemContent}>
                {renderFilterItemContent(item)}
              </View>
            </View>
          ))}
        </ScrollView>
      </View>

      {/* 底部操作栏 */}
      <View style={styles.footer}>
        <View style={styles.footerButtonRow}>
          <View style={styles.resetButtonBorder}>
            <ATrack style={styles.resetButton} onPress={handleReset}>
              <Text style={styles.resetButtonText}>重置</Text>
            </ATrack>
          </View>
          <ATrack style={styles.confirmButton} onPress={handleConfirm}>
            <Text style={styles.confirmButtonText}>
              确认{filterNumber > 0 && ` (${filterNumber})`}
            </Text>
          </ATrack>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  body: {
    flex: 1,
    flexDirection: 'row',
  },
  tabContainer: {
    width: getRealSize(90),
    maxWidth: getRealSize(90),
    minWidth: getRealSize(90),
    backgroundColor: '#f8f8f8',
  },
  tabScrollContent: {
    width: getRealSize(90),
    flexGrow: 1,
  },
  tabItem: {
    height: getRealSize(58),
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: getRealSize(10),
    position: 'relative',
  },
  activeTabItem: {
    backgroundColor: '#ffffff',
  },
  activePoint: {
    position: 'absolute',
    left: getRealSize(5),
    top: getRealSize(26),
    width: getRealSize(4),
    height: getRealSize(4),
    borderRadius: getRealSize(2),
    backgroundColor: '#61B43E',
  },
  tabText: {
    fontSize: getRealSize(13),
    color: '#646464',
    textAlign: 'center',
    lineHeight: getRealSize(20),
  },
  activeTabText: {
    color: '#030303',
    fontWeight: '500',
  },
  contentContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
    paddingHorizontal: getRealSize(10),
    paddingVertical: getRealSize(20),
  },
  filterItemContainer: {
    marginBottom: getRealSize(30),
  },
  filterItemTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getRealSize(10),
  },
  filterItemTitle: {
    fontSize: getRealSize(13),
    color: '#161616',
    fontWeight: '500',
  },
  conditionLabel: {
    fontSize: getRealSize(11),
    color: '#61B43E',
    fontWeight: '400',
    marginLeft: getRealSize(5),
  },
  filterItemContent: {
    // 筛选项内容的样式
  },
  footer: {
    backgroundColor: '#ffffff',
    paddingHorizontal: getRealSize(15),
    paddingVertical: getRealSize(10),
  },
  footerButtonRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resetButtonBorder: {
    width: getRealSize(75),
    height: getRealSize(40),
    borderWidth: 1,
    borderColor: '#333333',
    borderStyle: 'solid',
    marginRight: getRealSize(15),
  },
  resetButton: {
    flex: 1,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  resetButtonText: {
    fontSize: getRealSize(13),
    color: '#161616',
    fontWeight: '500',
    lineHeight: getRealSize(18), // 设置合适的 lineHeight 避免文字被裁切
    textAlign: 'center',
    includeFontPadding: false, // Android 特有属性，去除字体默认 padding
  },
  confirmButton: {
    flex: 1,
    height: getRealSize(40),
    backgroundColor: '#333333',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 0, // 确保没有默认 padding
  },
  confirmButtonText: {
    fontSize: getRealSize(13),
    color: '#ffffff',
    fontWeight: '500',
    lineHeight: getRealSize(18), // 设置合适的 lineHeight 避免文字被裁切
    textAlign: 'center',
    includeFontPadding: false, // Android 特有属性，去除字体默认 padding
  },
});

export default FilterPage;
