import React, { useState, use<PERSON><PERSON>back, memo } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import Header from '@/components/header';
import api, { FetchModule } from '@/common/api';
import { getRealSize } from '@/common/utils';
import Empty from '@/components/Empty';

// 类型定义
interface ContainerProps {
  insets: {
    bottom: number;
    left: number;
    right: number;
    top: number;
  };
  route?: {
    params?: {
      params?: {
        id?: string;
        [key: string]: any;
      };
    };
  };
}

interface UserInfo {
  base: {
    realname: string;
    gender: number;
    birthday: string;
    age: number;
    mobile: string;
    mobile_area_code?: string;
  };
  detail: {
    country_from_name: string;
    native_place: string;
    card_number: string;
    nation_name: string;
    job_name: string;
    marriage_name: string;
    birth_name: string;
    wechat: string;
    email: string;
    contact_address: string;
    remark: string;
  };
  belong: {
    tenant_name: string;
    referer_name: string;
    consultant_name: string;
    network_consultant_name: string;
    doctor_name: string;
    beautician_name: string;
    customer_service_name: string;
    record_number: string;
  };
}

// 信息条目组件
interface InfoItemProps {
  label: string;
  value: string;
}

const InfoItem: React.FC<InfoItemProps> = ({ label, value }) => (
  <View style={styles.item}>
    <View style={styles.left}>
      <Text style={styles.leftText}>{label}</Text>
    </View>
    <View style={styles.right}>
      <Text style={styles.rightText}>{value}</Text>
    </View>
  </View>
);

// 信息卡片组件
interface InfoCardProps {
  title: string;
  children: React.ReactNode;
}

const InfoCard: React.FC<InfoCardProps> = ({ title, children }) => (
  <View style={styles.card}>
    <Text style={styles.title}>{title}</Text>
    {children}
  </View>
);

const ClientInfoContainer: React.FC<ContainerProps> = ({ insets, route }) => {
  // 状态管理
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);

  // 获取客户ID
  const customerId = route?.params?.params?.id || '';

  // 获取数据方法
  const getData = useCallback(async () => {
    if (!customerId) {
      Alert.alert('错误', '缺少客户ID参数');
      return;
    }

    try {
      const response = await api.pagefetch({
        path: '/chain-wxapp/v1/customer/getCustomerForDisplay',
        params: { customer_id: customerId },
        method: FetchModule.Method.POST,
        isLoading: false,
      });

      if (response.errorCode === 0) {
        if (response.responseData) {
          setUserInfo(response.responseData as UserInfo);
        }
      } else {
        Alert.alert('获取数据失败', response.errorMsg || '未知错误');
      }
    } catch (error) {
      console.error('获取客户信息失败:', error);
      Alert.alert('错误', '网络请求失败，请稍后重试');
    }
  }, [customerId]);

  // 页面焦点效果 - 相当于Vue的onShow
  useFocusEffect(
    useCallback(() => {
      getData();
      return () => {
        // 页面失焦时的清理逻辑
      };
    }, [getData])
  );

  // 性别转换函数
  const getGenderText = (gender: number) => {
    switch (gender) {
      case 1:
        return '男';
      case 2:
        return '女';
      default:
        return '未知';
    }
  };

  // 格式化手机号
  const formatMobile = (mobile: string, areaCode?: string) => {
    const prefix = areaCode ? `+${areaCode}` : '';
    return `${prefix} ${mobile}`;
  };

  if (!userInfo) {
    return (
      <View style={[styles.container, { paddingBottom: insets.bottom }]}>
        <Empty />
      </View>
    );
  }

  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      <Header title='客户资料' bgColor='#ffffff' />
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* 基本信息卡片 */}
        <InfoCard title='基本信息'>
          <InfoItem label='姓名' value={userInfo.base.realname} />
          <InfoItem label='性别' value={getGenderText(userInfo.base.gender)} />
          <InfoItem label='生日' value={userInfo.base.birthday} />
          <InfoItem label='年龄' value={userInfo.base.age?.toString() || ''} />
          <InfoItem
            label='手机号'
            value={formatMobile(
              userInfo.base.mobile,
              userInfo.base.mobile_area_code
            )}
          />
          <InfoItem
            label='国家地区'
            value={userInfo.detail.country_from_name}
          />
          <InfoItem label='籍贯' value={userInfo.detail.native_place} />
          <InfoItem label='身份证号' value={userInfo.detail.card_number} />
          <InfoItem label='民族' value={userInfo.detail.nation_name} />
          <InfoItem label='职业' value={userInfo.detail.job_name} />
          <InfoItem label='婚姻状况' value={userInfo.detail.marriage_name} />
          <InfoItem label='生育情况' value={userInfo.detail.birth_name} />
          <InfoItem label='微信' value={userInfo.detail.wechat} />
          <InfoItem label='邮箱' value={userInfo.detail.email} />
          <InfoItem label='联系地址' value={userInfo.detail.contact_address} />
        </InfoCard>

        {/* 服务信息卡片 */}
        <InfoCard title='服务信息'>
          <InfoItem label='所属门店' value={userInfo.belong.tenant_name} />
          <InfoItem label='客户来源' value={userInfo.belong.referer_name} />
          <InfoItem label='咨询师' value={userInfo.belong.consultant_name} />
          <InfoItem
            label='电网咨询师'
            value={userInfo.belong.network_consultant_name}
          />
          <InfoItem label='主诊医生' value={userInfo.belong.doctor_name} />
          <InfoItem
            label='所属美容师'
            value={userInfo.belong.beautician_name}
          />
          <InfoItem
            label='客服专员'
            value={userInfo.belong.customer_service_name}
          />
          <InfoItem label='病历号' value={userInfo.belong.record_number} />
          <InfoItem label='备注' value={userInfo.detail.remark} />
        </InfoCard>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  scrollView: {
    flex: 1,
  },
  card: {
    marginBottom: getRealSize(5),
    backgroundColor: '#fff',
    padding: getRealSize(15),
    flexDirection: 'column',
  },
  title: {
    fontSize: getRealSize(15),
    lineHeight: getRealSize(21),
    fontFamily: 'PingFangSC-Medium',
    color: '#161616',
    fontWeight: '500',
    marginBottom: getRealSize(15),
  },
  item: {
    flexDirection: 'row',
    marginBottom: getRealSize(12),
  },
  left: {
    width: getRealSize(85),
  },
  leftText: {
    fontSize: getRealSize(15),
    fontFamily: 'PingFangSC-Regular',
    color: '#333333',
    lineHeight: getRealSize(20),
    fontWeight: '400',
  },
  right: {
    flex: 1,
  },
  rightText: {
    fontSize: getRealSize(15),
    fontFamily: 'PingFangSC-Regular',
    color: '#333333',
    lineHeight: getRealSize(20),
    fontWeight: '400',
    textAlign: 'right',
  },
});

export default memo(ClientInfoContainer);
