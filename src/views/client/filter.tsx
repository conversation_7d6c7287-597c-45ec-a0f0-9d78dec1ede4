import React, { Component } from 'react';
import { ClientFilterContainer } from './containers';
interface PageProps {
  route: {
    params: {
      params: {
        selectTab: string;
      };
    };
  };
  insets: {
    bottom: number;
    left: number;
    right: number;
    top: number;
  };
}

export default class Page extends Component<PageProps> {
  constructor(props: PageProps) {
    super(props);
    this.state = {
      pageShow: true,
    };
  }

  soyoungPageName() {
    return 'sy_clinic_other_service_record_list_page';
  }

  /** 页面埋点 */
  soyoungPageInfo() {
    return {};
  }

  didAppear() {
    // this.setState({
    //   pageShow: true,
    // });
  }

  willDisappear() {
    // this.setState({
    //   pageShow: false,
    // });
  }

  preferredStatusBarStyle() {
    // 0默认 1 白色 2 黑色
    return '2';
  }

  render() {
    return <ClientFilterContainer {...this.props} />;
  }
}
