import React, { useState, useCallback, memo, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import type { NativeSyntheticEvent, TextLayoutEventData } from 'react-native';
import { ATrack } from '@soyoung/react-native-container';
import { getRealSize } from '@/common/utils';
import type { ConsultItem } from './Consult';

interface ConsultCardProps {
  item: ConsultItem;
}

const ConsultCard: React.FC<ConsultCardProps> = memo(
  ({ item }) => {
    // 每个卡片独立管理自己的展开状态
    const [isExpanded, setIsExpanded] = useState<boolean>(false);
    // 新增：管理展开按钮显示状态
    const [shouldShowExpand, setShouldShowExpand] = useState<boolean>(false);
    // 新增：标记是否已经测量过文本行数
    const [hasMetricalized, setHasMetricalized] = useState<boolean>(false);

    // 切换展开状态
    const toggleExpanded = useCallback(() => {
      setIsExpanded(prev => !prev);
    }, []);

    // 文本布局变化回调，获取实际行数
    const handleTextLayout = useCallback(
      (event: NativeSyntheticEvent<TextLayoutEventData>) => {
        // 只在第一次测量时执行，获取完整文本的行数
        if (!hasMetricalized) {
          const { lines } = event.nativeEvent;
          const lineCount = lines.length;
          // 只有当文本超过两行时才显示展开按钮
          setShouldShowExpand(lineCount > 2);
          setHasMetricalized(true);
        }
      },
      [hasMetricalized]
    );

    // 构建完整的显示内容，参考 textItem.vue 的格式
    const treatmentContent = item.content
      ? `今日治疗：${item.content}`
      : '今日治疗：';
    const productContent =
      item.product_list && item.product_list.length > 0
        ? `咨询项目：${item.product_list.map(p => p.product_name).join('、')}`
        : '';

    // 组合完整内容
    const fullContent = [treatmentContent, productContent]
      .filter(Boolean)
      .join('\n');

    // 当内容发生变化时重置测量状态
    useEffect(() => {
      setHasMetricalized(false);
      setShouldShowExpand(false);
      setIsExpanded(false);
    }, [item.content, item.product_list]);

    return (
      <View style={styles.card}>
        <View style={styles.cardLine}>
          <View style={styles.timeBox}>
            <Text style={styles.timeText}>{item.consultation_time_str}</Text>
            <Text style={styles.descriptionText}>{item.consultation_time}</Text>
          </View>
          <View style={styles.nameBox}>
            <Text style={styles.nameText} numberOfLines={1}>
              {item.consult_name ? <Text>{item.consult_name}</Text> : null}
              {item.consult_name && item.tenant_name ? (
                <Text>&nbsp;|&nbsp;</Text>
              ) : null}
              {item.tenant_name ? <Text>{item.tenant_name}</Text> : null}
            </Text>
          </View>
        </View>

        {fullContent ? (
          <View style={styles.textContent}>
            {/* 参考 textItem.vue 的文本展示逻辑 */}
            <View style={styles.textContainer}>
              <Text
                style={styles.contentText}
                numberOfLines={hasMetricalized ? (isExpanded ? 0 : 2) : 0}
                onTextLayout={handleTextLayout}
              >
                {fullContent}
              </Text>
              {/* 基于实际行数判断是否显示展开按钮 */}
              {shouldShowExpand ? (
                <ATrack onPress={toggleExpanded} style={styles.expandButton}>
                  <Text style={styles.expandButtonText}>
                    {isExpanded ? '收起' : '更多'}
                  </Text>
                </ATrack>
              ) : null}
            </View>
          </View>
        ) : null}
      </View>
    );
  },
  (prevProps, nextProps) => {
    // 自定义比较函数，只有当item的关键属性发生变化时才重新渲染
    // 使用更高效的比较方式
    const prevItem = prevProps.item;
    const nextItem = nextProps.item;

    // 快速检查引用是否相同
    if (prevItem === nextItem) return true;

    // 检查关键属性（修复：使用 consultation_id 而不是 id）
    if (
      prevItem.consultation_id !== nextItem.consultation_id ||
      prevItem.content !== nextItem.content ||
      prevItem.consultation_time_str !== nextItem.consultation_time_str ||
      prevItem.consultation_time !== nextItem.consultation_time ||
      prevItem.consult_name !== nextItem.consult_name ||
      prevItem.tenant_name !== nextItem.tenant_name
    ) {
      return false;
    }

    // 检查product_list - 使用更高效的比较
    const prevProducts = prevItem.product_list || [];
    const nextProducts = nextItem.product_list || [];

    if (prevProducts.length !== nextProducts.length) return false;

    for (let i = 0; i < prevProducts.length; i++) {
      if (prevProducts[i].product_name !== nextProducts[i].product_name) {
        return false;
      }
    }

    return true;
  }
);

// 样式定义
const styles = StyleSheet.create({
  card: {
    position: 'relative',
    marginTop: getRealSize(10),
    width: getRealSize(345),
    alignSelf: 'center',
    padding: getRealSize(10),
    backgroundColor: '#ffffff',
    minHeight: getRealSize(60), // 设置最小高度减少布局跳跃
  },

  cardLine: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getRealSize(8),
  },

  timeBox: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  timeText: {
    fontFamily: 'PingFangSC-Medium',
    color: '#333333',
    fontSize: getRealSize(12),
    lineHeight: getRealSize(18),
    fontWeight: '600',
  },

  descriptionText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    lineHeight: getRealSize(18),
    marginLeft: getRealSize(5),
    color: '#999999',
  },

  nameBox: {
    flexShrink: 1,
    alignItems: 'flex-end',
  },

  nameText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#999999',
    textAlign: 'right',
  },

  textContent: {
    marginTop: getRealSize(10),
    padding: getRealSize(10),
    backgroundColor: '#F8F8F8',
  },

  contentText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#333333',
    lineHeight: getRealSize(18),
  },

  expandButton: {
    alignSelf: 'flex-end',
  },

  expandButtonText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#61B43E',
    fontWeight: '400',
  },

  textContainer: {
    position: 'relative',
  },
});

export default ConsultCard;
