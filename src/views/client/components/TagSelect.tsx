import React, { useState, useRef, useCallback, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import MultiSelect from '@/components/MultiSelect';
import TagPopup, { FilterActive, ActiveItem, TagPopupRef } from './TagPopup';

/**
 * 组件属性接口
 */
interface TagSelectProps {
  /** 当前活动状态 */
  active: FilterActive;
  /** 标签类型 1客户标签 2敏感标签 3动态标签 */
  type?: number;
  /** 按钮标题 */
  title?: string;
  /** 状态变化回调 */
  onChange?: (data: FilterActive) => void;
}

/**
 * 标签选择整合组件
 * 整合 MultiSelect 显示组件和 TagPopup 弹窗选择组件
 */
const TagSelect: React.FC<TagSelectProps> = ({
  active,
  type = 1,
  title = '选择标签',
  onChange,
}) => {
  // 内部状态管理
  const [compActive, setCompActive] = useState<FilterActive>(
    active || {
      value: [],
      condition: 'or',
    }
  );

  // 弹窗引用
  const tagPopupRef = useRef<TagPopupRef>(null);
  // 弹窗打开状态（预留用于未来扩展）
  // const [openPanel, setOpenPanel] = useState(false);

  /**
   * 监听外部 active 变化并同步到内部状态
   */
  useEffect(() => {
    if (active) {
      console.log('active', active);
      setCompActive(active);
    }
  }, [active]);

  /**
   * 处理打开弹窗
   */
  const handleOpen = useCallback(() => {
    setTimeout(() => {
      tagPopupRef.current?.open(type);
    }, 0);
  }, [type]);

  /**
   * 处理关闭弹窗
   */
  const handleClose = useCallback(() => {}, []);

  /**
   * 处理移除标签
   */
  const handleRemove = useCallback(
    (id: number) => {
      const newActive = {
        ...compActive,
        value: compActive.value.filter((item: ActiveItem) => item.id !== id),
      };
      setCompActive(newActive);

      // 触发回调
      onChange?.(newActive);
    },
    [compActive, onChange]
  );

  /**
   * 处理弹窗选择变化
   */
  const handleChange = useCallback(
    (data: FilterActive) => {
      setCompActive(data);

      // 触发回调
      onChange?.(data);
    },
    [onChange]
  );

  return (
    <View style={styles.container}>
      <MultiSelect
        active={compActive.value}
        buttonText={title}
        onPress={handleOpen}
        onRemove={handleRemove}
      />
      <TagPopup
        ref={tagPopupRef}
        title={title}
        type={type}
        active={compActive}
        onChange={handleChange}
        onClose={handleClose}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // 容器样式可根据需要调整
  },
});

export default TagSelect;
export type { FilterActive, ActiveItem };
