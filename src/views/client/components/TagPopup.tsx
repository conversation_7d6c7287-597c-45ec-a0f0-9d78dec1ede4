import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useCallback,
  useEffect,
} from 'react';
import {
  View,
  Text,
  TextInput,
  ScrollView,
  Image,
  StyleSheet,
  Platform,
} from 'react-native';
import Modal from 'react-native-modal';
import { ATrack } from '@soyoung/react-native-container';
import { getRealSize } from '@/common/utils';
import api, { FetchModule } from '@/common/api';
import Empty from '@/components/Empty';
import { modalAnimation } from '@/constant/modal_animation';

/**
 * 条件映射表
 */
const conditionMap = [
  {
    key: 'or',
    value: '包含任意范围',
  },
  {
    key: 'and',
    value: '包含全部范围',
  },
  {
    key: 'not',
    value: '不包含以下范围',
  },
  {
    key: 'empty',
    value: '无标签',
  },
];

/**
 * 标签项接口
 */
export interface ActiveItem {
  id: number;
  name: string;
}

/**
 * 筛选活动状态接口
 */
export interface FilterActive {
  value: ActiveItem[];
  condition: string;
}

/**
 * 标签项数据接口
 */
interface TagItem {
  tag_id: number;
  tag_name: string;
  children?: TagItem[];
}

/**
 * 组件属性接口
 */
interface TagPopupProps {
  /** 当前活动状态 */
  active: FilterActive;
  /** 标签类型 1客户标签 2敏感标签 3动态标签 */
  type?: number;
  /** 弹窗标题 */
  title?: string;
  /** 状态变化回调 */
  onChange?: (data: FilterActive) => void;
  /** 弹窗关闭回调 */
  onClose?: () => void;
}

/**
 * 组件暴露的方法接口
 */
export interface TagPopupRef {
  open: (type?: number) => void;
  close: () => void;
}

/**
 * 筛选标签选择弹窗组件
 */
const TagPopup = forwardRef<TagPopupRef, TagPopupProps>(
  ({ active, type = 1, title = '选择标签', onChange, onClose }, ref) => {
    // 状态管理
    const [visible, setVisible] = useState<boolean>(false);
    const [searchValue, setSearchValue] = useState<string>('');
    const [tagList, setTagList] = useState<TagItem[]>([]);
    const [originTagList, setOriginTagList] = useState<TagItem[]>([]);
    const [currentCondition, setCurrentCondition] = useState(
      active.condition || 'or'
    );
    const [listActive, setListActive] = useState<FilterActive>(
      active || {
        value: [],
        condition: 'or',
      }
    );

    /**
     * 同步外部 active 到内部状态
     */
    useEffect(() => {
      const newActive = active || {
        value: [],
        condition: 'or',
      };
      setListActive(newActive);
      setCurrentCondition(newActive.condition || 'or');
    }, [active]);

    /**
     * 获取标签数据
     */
    const getData = useCallback(
      async (tagType: number) => {
        try {
          const response = await api.pagefetch({
            path: '/chain-wxapp/v1/customer/searchGetGroupTagList',
            params: {
              tag_type: tagType,
              key_word: searchValue,
            },
            method: FetchModule.Method.POST,
            isLoading: false,
          });

          if (response.errorCode === 0) {
            const data = response.responseData?.tag_list || [];
            setTagList(data);
            setOriginTagList(data);
          }
        } catch (error) {
          // 获取标签数据失败
          setTagList([]);
          setOriginTagList([]);
        }
      },
      [searchValue]
    );

    /**
     * 更新选中状态
     */
    const updateActive = useCallback((data: ActiveItem) => {
      setListActive(prev => {
        const newValue = [...prev.value];
        const existingIndex = newValue.findIndex(item => item.id === data.id);

        if (existingIndex > -1) {
          // 如果已存在，则移除
          newValue.splice(existingIndex, 1);
        } else {
          // 如果不存在，则添加
          newValue.push(data);
        }

        return {
          ...prev,
          value: newValue,
        };
      });
    }, []);

    /**
     * 处理条件点击
     */
    const handleConditionClick = useCallback((key: string) => {
      setCurrentCondition(key);
      setListActive(prev => ({
        ...prev,
        condition: key,
      }));
    }, []);

    /**
     * 处理搜索
     */
    const handleSearch = useCallback(() => {
      if (!searchValue.trim()) {
        setTagList(originTagList);
        return;
      }

      const filteredList = originTagList
        .filter(item => {
          if (item.tag_name.includes(searchValue)) return true;
          if (item.children) {
            const hasMatchingChildren = item.children.some(child =>
              child.tag_name.includes(searchValue)
            );
            if (hasMatchingChildren) {
              return true;
            }
          }
          return false;
        })
        .map(item => {
          if (item.children) {
            const filteredChildren = item.children.filter(
              child =>
                child.tag_name.includes(searchValue) ||
                item.tag_name.includes(searchValue)
            );
            return {
              ...item,
              children: filteredChildren,
            };
          }
          return item;
        });

      setTagList(filteredList);
    }, [searchValue, originTagList]);

    /**
     * 清除搜索
     */
    const handleClearSearch = useCallback(() => {
      setSearchValue('');
      setTagList(originTagList);
    }, [originTagList]);

    /**
     * 打开弹窗
     */
    const open = useCallback(
      (tagType?: number) => {
        const actualType = tagType || type;
        setVisible(true);
        getData(actualType);
      },
      [type, getData]
    );

    /**
     * 关闭弹窗
     */
    const close = useCallback(() => {
      setVisible(false);
      setSearchValue(''); // 清空搜索框
      setTagList(originTagList); // 重置数据为未搜索状态
      // 重置选中状态到初始值
      const resetActive = active || {
        value: [],
        condition: 'or',
      };
      setListActive(resetActive);
      setCurrentCondition(resetActive.condition || 'or');
      onClose?.();
    }, [onClose, originTagList, active]);

    /**
     * 处理取消
     */
    const handleCancel = useCallback(() => {
      // 重置选中状态到初始值
      const resetActive = active || {
        value: [],
        condition: 'or',
      };
      setListActive(resetActive);
      setCurrentCondition(resetActive.condition || 'or');
      close();
    }, [close, active]);

    /**
     * 处理确认
     */
    const handleConfirm = useCallback(() => {
      onChange?.(listActive);
      close();
    }, [listActive, onChange, close]);

    // 监听搜索值变化，自动搜索
    useEffect(() => {
      const timer = setTimeout(() => {
        handleSearch();
      }, 300);

      return () => clearTimeout(timer);
    }, [searchValue, handleSearch]);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      open,
      close,
    }));

    const isSelected = (item: TagItem) => {
      return listActive.value.some(activeItem => activeItem.id === item.tag_id);
    };

    // 渲染主体内容
    const renderContent = () => {
      const hasValidTags = tagList.filter(item => item.children).length > 0;
      const shouldShowEmpty = !hasValidTags || listActive.condition === 'empty';

      if (shouldShowEmpty) {
        return <Empty />;
      }

      return (
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          {tagList
            .filter(item => item.children)
            .map(item => (
              <View key={item.tag_id} style={styles.groupItem}>
                <Text style={styles.groupTitle}>{item.tag_name}</Text>
                <View style={styles.tagButtonList}>
                  {(item.children || []).map(child => {
                    return (
                      <ATrack
                        key={child.tag_id}
                        style={styles.tagButton}
                        onPress={() =>
                          updateActive({
                            id: child.tag_id,
                            name: child.tag_name,
                          })
                        }
                      >
                        <View
                          style={[
                            styles.tagButtonContent,
                            isSelected(child) && styles.tagButtonActiveContent,
                          ]}
                        >
                          <Text
                            style={[
                              styles.tagButtonText,
                              isSelected(child) && styles.tagButtonActiveText,
                            ]}
                          >
                            {child.tag_name}
                          </Text>
                          {isSelected(child) && (
                            <Image
                              source={{
                                uri: 'https://static.soyoung.com/sy-design/3o6q6zpv0xqzv1753259861585.png',
                              }}
                              style={styles.checkIcon}
                            />
                          )}
                        </View>
                      </ATrack>
                    );
                  })}
                </View>
              </View>
            ))}
        </ScrollView>
      );
    };

    return (
      <Modal
        isVisible={visible}
        {...modalAnimation}
        onBackdropPress={handleCancel}
        animationIn='slideInUp'
        animationOut='slideOutDown'
        style={styles.modal}
        backdropOpacity={0.5}
        useNativeDriver={true}
        avoidKeyboard={false}
        statusBarTranslucent={Platform.OS === 'android'}
      >
        <View style={styles.popupContent}>
          {/* 顶部栏 */}
          <View style={styles.topBar}>
            <ATrack onPress={handleCancel}>
              <Text style={styles.cancelText}>取消</Text>
            </ATrack>
            <Text style={styles.titleText}>{title}</Text>
            <ATrack onPress={handleConfirm}>
              <Text style={styles.confirmText}>确认</Text>
            </ATrack>
          </View>

          {/* 条件栏 */}
          <View style={styles.conditionBar}>
            {conditionMap.map(item => (
              <ATrack
                key={item.key}
                style={[
                  styles.conditionItem,
                  currentCondition === item.key && styles.conditionItemActive,
                ]}
                onPress={() => handleConditionClick(item.key)}
              >
                <Text
                  style={[
                    styles.conditionText,
                    currentCondition === item.key && styles.conditionTextActive,
                  ]}
                >
                  {item.value}
                </Text>
              </ATrack>
            ))}
          </View>

          {/* 搜索栏 */}
          <View style={styles.searchContainer}>
            <Image
              style={styles.searchIcon}
              source={{
                uri: 'https://static.soyoung.com/sy-pre/1y4a2d4fr2l31-1711955400686.png',
              }}
              resizeMode='contain'
            />
            <TextInput
              style={styles.searchInput}
              value={searchValue}
              onChangeText={setSearchValue}
              placeholder='请输入标签或标签组'
              placeholderTextColor='#DEDEDE'
              returnKeyType='done'
              onSubmitEditing={handleSearch}
            />
            {searchValue.length > 0 && (
              <ATrack onPress={handleClearSearch} style={styles.clearButton}>
                <Image
                  style={styles.clearIcon}
                  source={{
                    uri: 'https://static.soyoung.com/sy-pre/219l297us0uts-1711617000689.png',
                  }}
                  resizeMode='contain'
                />
              </ATrack>
            )}
          </View>

          {/* 主体内容 */}
          <View style={styles.body}>{renderContent()}</View>
        </View>
      </Modal>
    );
  }
);

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  popupContent: {
    width: '100%',
    height: getRealSize(547), // 1094rpx / 2
    backgroundColor: '#ffffff',
  },
  topBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: getRealSize(15), // 30rpx / 2
    paddingTop: getRealSize(15),
  },
  cancelText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15), // 30rpx / 2
    color: '#777777',
    fontWeight: '400',
  },
  titleText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(16), // 32rpx / 2
    color: '#333333',
    fontWeight: '500',
  },
  confirmText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(15), // 30rpx / 2
    color: '#61B43E',
    fontWeight: '500',
  },
  conditionBar: {
    marginTop: getRealSize(20),
    flexDirection: 'row',
    paddingHorizontal: getRealSize(15), // 30rpx / 2
    justifyContent: 'space-between',
  },
  conditionItem: {
    marginRight: getRealSize(8), // 16rpx / 2
  },
  conditionItemActive: {},
  conditionText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13), // 26rpx / 2
    color: '#333333',
    fontWeight: '400',
  },
  conditionTextActive: {
    color: '#61B43E',
  },
  searchContainer: {
    marginTop: getRealSize(16), // 32rpx / 2
    marginHorizontal: getRealSize(15), // 30rpx / 2
    height: getRealSize(36), // 72rpx / 2
    backgroundColor: '#f8f8f8',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getRealSize(15), // 30rpx / 2
    position: 'relative',
  },
  searchIcon: {
    width: getRealSize(16), // 32rpx / 2
    height: getRealSize(16),
    marginRight: getRealSize(8), // 16rpx / 2
  },
  searchInput: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15), // 30rpx / 2
    color: '#161616',
    fontWeight: '400',
    flex: 1,
  },
  clearButton: {
    width: getRealSize(38), // 76rpx / 2
    height: getRealSize(38),
    position: 'absolute',
    right: 0,
    top: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  clearIcon: {
    width: getRealSize(16), // 32rpx / 2
    height: getRealSize(16),
  },
  body: {
    flex: 1,
    backgroundColor: '#f6f9f9',
    marginTop: getRealSize(10), // 20rpx / 2
  },
  scrollView: {
    flex: 1,
    backgroundColor: '#ffffff',
    paddingHorizontal: getRealSize(15), // 30rpx / 2
    paddingVertical: getRealSize(20), // 40rpx / 2
  },
  groupItem: {
    marginBottom: getRealSize(20), // 40rpx / 2
  },
  groupTitle: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(13), // 32rpx / 2
    lineHeight: getRealSize(22),
    color: '#333333',
    fontWeight: '500',
    marginBottom: getRealSize(10), // 20rpx / 2
  },
  tagButtonList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tagButton: {
    marginRight: getRealSize(10), // 20rpx / 2
    marginBottom: getRealSize(10), // 20rpx / 2
  },
  tagButtonText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13), // 26rpx / 2
    color: '#333333',
    fontWeight: '400',
    textAlign: 'center',
  },
  tagButtonActiveText: {
    color: '#333333',
  },
  checkIcon: {
    position: 'absolute',
    bottom: getRealSize(-1),
    right: getRealSize(-1),
    width: getRealSize(18),
    height: getRealSize(13),
  },
  tagButtonContent: {
    paddingHorizontal: getRealSize(15), // 30rpx / 2
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    flexDirection: 'row',
    height: getRealSize(38), // 76rpx / 2
    backgroundColor: '#f8f8f8',
    borderWidth: 2,
    borderColor: 'transparent',
    borderStyle: 'solid',
  },
  tagButtonActiveContent: {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#333333',
    borderStyle: 'solid',
  },
});

TagPopup.displayName = 'TagPopup';

export default TagPopup;
