import React, { memo, useMemo, useRef, useEffect } from 'react';
import { View, Text, ScrollView, Image, StyleSheet } from 'react-native';
import { getRealSize } from '../../../common/utils';
import { ATrack } from '@soyoung/react-native-container';
import CommonPopup, {
  CommonPopupRef,
  Options,
  ActiveItem,
} from '@/components/CommonPopup';

interface FilterItem {
  data_type: number;
  filter?: {
    title: string;
    filter_condition: string;
  };
  filter_group?: {
    title: string;
    filter_list: Array<{
      title: string;
      filter_condition: string;
    }>;
  };
  listCurrent?: number;
}

interface QuickFilterProps {
  publicList: FilterItem[];
  topCurrent: number;
  publicSonList: Array<{
    title: string;
    filter_condition: string;
  }>;
  publicSonListShow: boolean;
  onTopClick: (index: number, item: FilterItem | null) => void;
  onListClick: (
    index: number,
    item: { title: string; filter_condition: string }
  ) => void;
  onClickPublic: () => void;
  onClearOthers?: (currentIndex: number) => void; // 新增：清空其他筛选项的回调
}

// 静态数据提取到组件外部
const IMAGES = {
  leftGradient:
    'https://static.soyoung.com/sy-pre/3744cc5ixzull-1711433400751.png',
  arrowDown:
    'https://static.soyoung.com/sy-pre/25bxfmf38q0e5-1730808600645.png',
  arrowUp: 'https://static.soyoung.com/sy-pre/3aqojcg7a39m4-1730808600645.png',
  dropdownActive:
    'https://static.soyoung.com/sy-pre/25bxfmf38q0e5-1754557800629.png',
  dropdownDefault:
    'https://static.soyoung.com/sy-design/3u5f7j35qkn711753259920865.png',
} as const;

// 提取筛选项渲染组件
const FilterItemComponent = memo<{
  item: FilterItem;
  index: number;
  isActive: boolean;
  onPress: (index: number, item: FilterItem) => void;
  onClearOthers?: (currentIndex: number) => void;
}>(({ item, index, isActive, onPress, onClearOthers }) => {
  // 计算显示的文字：如果有选中的子选项，显示子选项文字，否则显示默认标题
  const title = useMemo(() => {
    if (item.data_type === 2) {
      return item.filter?.title;
    } else {
      // 检查是否有选中的子选项
      if (
        typeof item.listCurrent === 'number' &&
        item.listCurrent >= 0 &&
        item.filter_group?.filter_list &&
        item.filter_group.filter_list[item.listCurrent]
      ) {
        return item.filter_group.filter_list[item.listCurrent].title;
      }
      return item.filter_group?.title;
    }
  }, [item]);

  // 计算是否应该显示为激活态：要么是当前选中的顶级项，要么有选中的子选项
  const shouldShowActive = useMemo(() => {
    if (isActive) return true;
    // 如果有选中的子选项，也应该显示为激活态
    if (
      item.data_type !== 2 &&
      typeof item.listCurrent === 'number' &&
      item.listCurrent >= 0
    ) {
      return true;
    }
    return false;
  }, [isActive, item]);

  const handlePress = () => {
    onPress(index, item);
    // 如果不是"全部"选项，清空其他筛选项
    if (index !== -1) {
      onClearOthers?.(index);
    }
  };

  // 判断是否有下拉功能（data_type !== 2 的项目有下拉）
  const hasDropdown = item.data_type !== 2;

  return (
    <ATrack
      style={[styles.filterItem, shouldShowActive && styles.activeItem]}
      onPress={handlePress}
    >
      <Text
        style={[styles.filterText, shouldShowActive && styles.activeText]}
        numberOfLines={1}
      >
        {title}
      </Text>
      {hasDropdown && (
        <Image
          source={{
            uri: shouldShowActive
              ? IMAGES.dropdownActive
              : IMAGES.dropdownDefault,
          }}
          style={styles.dropdownArrow}
        />
      )}
    </ATrack>
  );
});

FilterItemComponent.displayName = 'FilterItemComponent';

const QuickFilter: React.FC<QuickFilterProps> = memo(
  ({
    publicList,
    topCurrent,
    publicSonList,
    publicSonListShow,
    onTopClick,
    onListClick,
    onClickPublic,
    onClearOthers,
  }) => {
    const popupRef = useRef<CommonPopupRef>(null);

    // 缓存当前选中项的listCurrent值
    const currentListCurrent = useMemo(() => {
      return publicList[topCurrent]?.listCurrent;
    }, [publicList, topCurrent]);

    // 缓存渲染的筛选项列表
    const renderFilterItems = useMemo(() => {
      return publicList.map((item, index) => (
        <FilterItemComponent
          key={index}
          item={item}
          index={index}
          isActive={topCurrent === index}
          onPress={onTopClick}
          onClearOthers={onClearOthers}
        />
      ));
    }, [publicList, topCurrent, onTopClick, onClearOthers]);

    // 将 publicSonList 转换为 CommonPopup 需要的 Options 格式
    const popupOptions = useMemo((): Options[] => {
      return publicSonList.map((item, index) => ({
        label: item.title,
        value: index,
      }));
    }, [publicSonList]);

    // 当前选中的选项（单选模式）
    const currentActive = useMemo(() => {
      if (
        currentListCurrent !== undefined &&
        publicSonList[currentListCurrent]
      ) {
        return {
          id: currentListCurrent,
          name: publicSonList[currentListCurrent].title,
        };
      }
      return { id: -1, name: '' };
    }, [currentListCurrent, publicSonList]);

    // 监听弹窗显示状态，控制弹窗开关
    useEffect(() => {
      if (publicSonListShow) {
        popupRef.current?.open();
      } else {
        popupRef.current?.close();
      }
    }, [publicSonListShow]);

    // 处理弹窗选择
    const handlePopupChange = (active: ActiveItem | ActiveItem[]) => {
      // 单选模式下，active 应该是单个 ActiveItem
      if (!Array.isArray(active) && active.id !== -1) {
        const selectedItem = publicSonList[active.id as number];
        if (selectedItem) {
          onListClick(active.id as number, selectedItem);
          // 注意：清空其他筛选项的逻辑应该在父组件的 onListClick 中处理
          // 移除这里的 onClearOthers 调用，避免状态更新冲突
        }
      }
    };

    // 处理"全部"按钮点击
    const handleAllClick = () => {
      onTopClick(-1, null);
      // 点击"全部"时清空所有筛选项
      onClearOthers?.(-1);
    };

    // 处理弹窗关闭
    const handlePopupClose = () => {
      onClickPublic();
    };

    return (
      <View style={styles.container}>
        <View style={styles.filterRow}>
          <ATrack
            style={[
              styles.filterItem,
              styles.allItem,
              topCurrent === -1 && styles.activeItem,
            ]}
            onPress={handleAllClick}
          >
            <Text
              style={[
                styles.filterText,
                topCurrent === -1 && styles.activeText,
              ]}
            >
              全部
            </Text>
          </ATrack>

          <View style={styles.scrollContainer}>
            <Image
              source={{ uri: IMAGES.leftGradient }}
              style={styles.leftImg}
            />
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.scrollView}
              contentContainerStyle={styles.scrollContent}
            >
              {renderFilterItems}
            </ScrollView>
          </View>
        </View>

        <CommonPopup
          ref={popupRef}
          active={currentActive}
          title={'请选择'}
          searchable={false}
          multiple={false}
          options={popupOptions}
          onClose={handlePopupClose}
          onChange={handlePopupChange}
        />
      </View>
    );
  }
);

QuickFilter.displayName = 'QuickFilter';

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    paddingBottom: getRealSize(14),
  },
  filterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: getRealSize(15),
  },
  filterItem: {
    maxWidth: getRealSize(200),
    height: getRealSize(28),
    backgroundColor: '#f5f5f5',
    marginRight: getRealSize(10),
    paddingHorizontal: getRealSize(15),
    flexDirection: 'row',
    alignItems: 'center',
  },
  allItem: {
    // 特殊样式用于"全部"按钮
  },
  activeItem: {
    backgroundColor: '#333333',
  },
  filterText: {
    fontSize: getRealSize(13),
    color: '#333333',
  },
  activeText: {
    color: '#FFFFFF',
  },
  scrollContainer: {
    flex: 1,
    position: 'relative',
  },
  leftImg: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: getRealSize(15),
    zIndex: 2,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingLeft: getRealSize(2.5),
  },
  dropdownArrow: {
    width: getRealSize(9),
    height: getRealSize(6),
    marginLeft: getRealSize(4),
  },
});

export default QuickFilter;
