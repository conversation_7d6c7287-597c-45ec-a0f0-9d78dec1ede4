import React, { memo, useMemo } from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import { getRealSize, hexToRgb } from '../../../common/utils';
import { ATrack } from '@soyoung/react-native-container';

interface UserCardProps {
  item: {
    base: {
      customer_id: string;
      realname: string;
      gender?: number;
      age?: number;
      mobile?: string;
    };
    tag: {
      dynamic_tag_list: Array<{
        tag_id: string;
        name: string;
        tag_color?: string;
      }>;
    };
    belong: {
      join_c?: boolean;
      staff_info?: {
        external_user_id: string;
      };
    };
    action: {
      pre_visit_date_format?: string;
    };
  };
  onPress: () => void;
  onChatPress?: (staffInfo: any) => void;
}

// 静态数据提取到组件外部
const WX_ICON_URI =
  'https://static.soyoung.com/sy-pre/3syqwho4slbkg-1753096200633.png';

// 提取标签渲染组件
const TagComponent = memo<{
  tag: {
    tag_id: string;
    name: string;
    tag_color?: string;
  };
}>(({ tag }) => {
  const tagStyle = useMemo(() => {
    return tag.tag_color
      ? {
          backgroundColor: hexToRgb(tag.tag_color, 0.1),
          borderColor: 'transparent',
        }
      : {
          backgroundColor: '#f5f5f5',
        };
  }, [tag.tag_color]);

  const textStyle = useMemo(() => {
    return tag.tag_color
      ? {
          color: tag.tag_color,
        }
      : {
          color: '#555555',
        };
  }, [tag.tag_color]);

  return (
    <View style={[styles.tag, tagStyle]}>
      <Text style={[styles.tagText, textStyle]}>{tag.name}</Text>
    </View>
  );
});

TagComponent.displayName = 'TagComponent';

const UserCard: React.FC<UserCardProps> = memo(
  ({ item, onPress, onChatPress }) => {
    const { base, tag, belong, action } = item;

    // 缓存用户头像文字
    const avatarText = useMemo(() => {
      return base.realname ? base.realname[0] : '';
    }, [base.realname]);

    // 缓存性别文本
    const genderText = useMemo(() => {
      if (!base.gender) return null;
      return base.gender === 1 ? '男' : '女';
    }, [base.gender]);

    // 缓存是否显示分隔符
    const showSeparator = useMemo(() => {
      return Boolean(base.gender && base.age);
    }, [base.gender, base.age]);

    // 缓存手机号显示文本
    const mobileDisplayText = useMemo(() => {
      if (!base.mobile) return null;
      const baseText = `手机号： ${base.mobile}`;
      return belong.join_c ? `${baseText} @微信` : baseText;
    }, [base.mobile, belong.join_c]);

    // 缓存渲染的标签列表
    const renderTags = useMemo(() => {
      return (tag?.dynamic_tag_list || []).map(tagItem => (
        <TagComponent key={tagItem.tag_id} tag={tagItem} />
      ));
    }, [tag?.dynamic_tag_list]);

    // 缓存是否显示微信图标
    const showWxIcon = useMemo(() => {
      return Boolean(belong.staff_info);
    }, [belong.staff_info]);

    const handleChatPress = () => {
      if (onChatPress && belong.staff_info) {
        onChatPress(belong.staff_info);
      }
    };

    return (
      <ATrack style={styles.container} onPress={onPress}>
        <View style={styles.leftSection}>
          <View style={styles.userImg}>
            <Text style={styles.userImgText}>{avatarText}</Text>
          </View>
        </View>

        <View style={styles.centerSection}>
          <View style={styles.line1}>
            <Text style={styles.name} numberOfLines={1}>
              {base.realname}
            </Text>
            {genderText ? (
              <Text style={styles.gender}>{genderText}</Text>
            ) : null}
            {showSeparator ? <Text style={styles.columeLine}>|</Text> : null}
            {base.age ? <Text style={styles.age}>{base.age}岁</Text> : null}
          </View>

          {mobileDisplayText ? (
            <View style={styles.line2}>
              <Text style={styles.mobileText}>
                {base.mobile && `手机号： ${base.mobile}`}
                {belong.join_c && <Text style={styles.wx}> @微信</Text>}
              </Text>
            </View>
          ) : null}

          <View style={styles.line3}>{renderTags}</View>
        </View>

        <View style={styles.rightSection}>
          <Text style={styles.description}>{action.pre_visit_date_format}</Text>
          {showWxIcon && (
            <ATrack onPress={handleChatPress}>
              <Image source={{ uri: WX_ICON_URI }} style={styles.wxIcon} />
            </ATrack>
          )}
        </View>
      </ATrack>
    );
  }
);

UserCard.displayName = 'UserCard';

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: '#ffffff',
    marginBottom: getRealSize(10),
    padding: getRealSize(15),
    flexDirection: 'row',
  },
  leftSection: {
    width: getRealSize(40),
  },
  userImg: {
    width: getRealSize(40),
    height: getRealSize(40),
    backgroundColor: '#61B43E',
    borderRadius: getRealSize(20),
    justifyContent: 'center',
    alignItems: 'center',
  },
  userImgText: {
    fontSize: getRealSize(18),
    textAlignVertical: 'center',
    color: '#ffffff',
    fontWeight: '500',
  },
  centerSection: {
    flex: 1,
    marginLeft: getRealSize(10),
  },
  line1: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  name: {
    maxWidth: getRealSize(150),
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '500',
  },
  gender: {
    fontSize: getRealSize(11),
    color: '#777777',
    marginLeft: getRealSize(5),
  },
  columeLine: {
    fontSize: getRealSize(9),
    color: '#dedede',
    marginHorizontal: getRealSize(4),
  },
  age: {
    fontSize: getRealSize(11),
    color: '#777777',
  },
  line2: {
    marginTop: getRealSize(5),
  },
  mobileText: {
    fontSize: getRealSize(11),
    color: '#777777',
  },
  wx: {
    color: '#00c535',
    marginLeft: getRealSize(2),
  },
  line3: {
    marginTop: getRealSize(5),
    flexDirection: 'row',
    flexWrap: 'wrap',
    height: getRealSize(18),
    overflow: 'hidden',
  },
  tag: {
    alignItems: 'center',
    justifyContent: 'center',
    height: getRealSize(18),
    paddingHorizontal: getRealSize(5),
    marginRight: getRealSize(2),
  },
  tagText: {
    fontSize: getRealSize(10),
    color: '#555555',
  },
  rightSection: {
    width: getRealSize(70),
    marginLeft: getRealSize(10),
    alignItems: 'flex-end',
  },
  description: {
    fontSize: getRealSize(11),
    color: '#aaabb3',
    textAlign: 'right',
  },
  wxIcon: {
    width: getRealSize(35),
    height: getRealSize(35),
    marginTop: getRealSize(10),
  },
});

export default UserCard;
