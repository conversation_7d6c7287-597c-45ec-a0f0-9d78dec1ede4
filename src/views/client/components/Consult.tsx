import React, { useState, useEffect, useCallback, memo } from 'react';
import { View, Text, StyleSheet, FlatList, Alert } from 'react-native';
import { flatListProps } from '@/constant/flatlist_props';
import Empty from '@/components/Empty';
import { ConsultSkeleton } from './ClientDetailSkeleton';
import ConsultCard from './ConsultCard';
import { getRealSize } from '@/common/utils';
import api, { FetchModule } from '@/common/api';
import LinearGradient from 'react-native-linear-gradient';

// 类型定义
export interface ConsultItem {
  consultation_id: number;
  consultation_time_str: string;
  consultation_time: string;
  consult_name: string;
  tenant_name: string;
  content?: string;
  product_list?: Array<{
    product_name: string;
    [key: string]: any;
  }>; // 咨询项目列表
}

interface ConsultProps {
  customerId: string;
}

const Consult: React.FC<ConsultProps> = memo(({ customerId }) => {
  // 状态管理
  const [dataList, setDataList] = useState<ConsultItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [page, setPage] = useState<number>(1);
  const [componentShow, setComponentShow] = useState<boolean>(true);
  const [initialLoading, setInitialLoading] = useState<boolean>(true);

  // 获取咨询数据 - 修复版本
  const getData = useCallback(
    async (pageNum: number, reset: boolean = false) => {
      if (loading) return;
      setLoading(true);

      try {
        const response = await api.pagefetch({
          path: '/chain-wxapp/v1/customer/getConsultationList',
          params: {
            customer_id: customerId,
            page: pageNum,
            size: 10,
          },
          isLoading: false,
          method: FetchModule.Method.POST,
        });

        if (response.errorCode === 0 && response.responseData) {
          const newData = response.responseData.list || [];
          console.log('获取到新数据:', {
            pageNum,
            reset,
            newDataLength: newData.length,
            total: response.responseData.total,
          });
          console.log(
            '新数据ID列表:',
            newData.map((item: ConsultItem) => item.consultation_id)
          );

          // 优化数据合并逻辑 - 减少不必要的数组重建
          let finalDataLength = 0;
          setDataList(prevDataList => {
            if (reset) {
              finalDataLength = newData.length;
              return newData;
            } else {
              // 使用concat而不是扩展运算符，性能更好
              const updatedData = prevDataList.concat(newData);
              finalDataLength = updatedData.length;
              console.log('数据合并结果:', {
                prevLength: prevDataList.length,
                newLength: newData.length,
                finalLength: updatedData.length,
              });
              return updatedData;
            }
          });

          // 使用准确的数据长度计算 hasMore
          const hasMoreData = response.responseData.total > finalDataLength;
          console.log('hasMore 更新:', {
            total: response.responseData.total,
            finalDataLength: finalDataLength,
            hasMore: hasMoreData,
          });
          setHasMore(hasMoreData);

          if (!reset) {
            setPage(pageNum + 1);
          } else {
            // 重置时确保 page 也重置为 2（因为已经加载了第1页）
            setPage(2);
          }
        } else {
          Alert.alert('提示', response.errorMsg || '获取数据失败');
        }
      } catch (error) {
        console.error('获取咨询数据失败:', error);
        Alert.alert('提示', '网络请求失败，请稍后重试');
      } finally {
        setLoading(false);
      }
    },
    [customerId, loading] // 只保留必要的依赖
  );

  // 加载更多数据 - 修复版本
  const loadMore = useCallback(() => {
    if (hasMore && !loading && componentShow) {
      // 直接使用当前的 page 值
      getData(page);
    }
  }, [hasMore, loading, componentShow, page, getData]);

  // 渲染列表项 - 使用独立的 ConsultCard 组件
  const renderItem = useCallback(({ item }: { item: ConsultItem }) => {
    return <ConsultCard item={item} />;
  }, []);

  // 优化的 keyExtractor
  const keyExtractor = useCallback(
    (item: ConsultItem) => `consultation_id-${item.consultation_id}`,
    []
  );

  // 滚动开始时的优化处理
  const onScrollBeginDrag = useCallback(() => {
    // 滚动开始时可以做一些优化处理
    // 例如暂停不必要的动画或状态更新
  }, []);

  // 滚动结束时的处理
  const onScrollEndDrag = useCallback(() => {
    // 滚动结束后的清理工作
  }, []);

  // 渲染列表底部
  const renderFooter = () => (
    <View style={styles.footer}>
      <Text style={styles.footerText}>
        {loading ? '加载中' : hasMore ? '上拉加载更多' : '没有更多啦'}
      </Text>
    </View>
  );

  // 渲染空状态
  const renderEmpty = () => (
    <View style={styles.empty}>
      <Empty />
    </View>
  );

  // 组件挂载时初始化
  useEffect(() => {
    setComponentShow(true);
    setInitialLoading(true);
    // 重置状态
    setPage(1);
    setHasMore(true);
    setDataList([]);
    // 获取第一页数据
    getData(1, true).finally(() => {
      setInitialLoading(false);
    });

    return () => {
      setComponentShow(false);
    };
  }, [customerId]);

  // 初始加载状态显示骨架屏
  if (initialLoading) {
    return <ConsultSkeleton />;
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#ffffff', '#f8f8f8']}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        locations={[0.1, 1]}
      >
        <View style={{ height: getRealSize(10) }} />
      </LinearGradient>
      {!dataList ? (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      ) : dataList.length > 0 ? (
        <FlatList
          data={dataList}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          onEndReached={loadMore}
          ListFooterComponent={renderFooter}
          ListEmptyComponent={renderEmpty}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContent}
          // 核心防抖动配置
          scrollEventThrottle={16}
          bounces={true}
          // 移除可能导致抖动的配置
          // decelerationRate='normal'
          // overScrollMode='auto'
          // nestedScrollEnabled={false}
          // legacyImplementation={true}
          // 简化的滚动事件处理
          onScrollBeginDrag={onScrollBeginDrag}
          onScrollEndDrag={onScrollEndDrag}
          // 关键优化：使用稳定的extraData
          extraData={`${dataList.length}-${loading}`}
          // 应用优化配置
          {...flatListProps}
        />
      ) : (
        renderEmpty()
      )}
    </View>
  );
});

// 样式定义
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },

  listContent: {
    paddingHorizontal: getRealSize(15),
    paddingTop: getRealSize(10),
  },

  footer: {
    paddingVertical: getRealSize(15),
    alignItems: 'center',
  },

  footerText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#777777',
  },

  empty: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: getRealSize(50),
  },

  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: getRealSize(50),
  },

  loadingText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(14),
    color: '#999999',
  },
});

export default Consult;
