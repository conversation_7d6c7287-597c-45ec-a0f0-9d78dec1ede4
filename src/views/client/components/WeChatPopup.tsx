import React, {
  useState,
  useCallback,
  forwardRef,
  useImperativeHandle,
} from 'react';
import {
  View,
  Text,
  TextInput,
  FlatList,
  Image,
  StyleSheet,
  Platform,
} from 'react-native';
import Modal from 'react-native-modal';
import { ATrack } from '@soyoung/react-native-container';
import { getRealSize } from '@/common/utils';
import api, { FetchModule } from '@/common/api';
import Empty from '@/components/Empty';
import { modalAnimation } from '@/constant/modal_animation';
import { flatListProps } from '@/constant/flatlist_props';

/**
 * 员工信息接口
 */
interface StaffInfo {
  id: number;
  is_my_acc: number;
  avatar: string;
  staff_name: string;
  departments: string;
  last_chat_time: string;
}

/**
 * 群聊信息接口
 */
interface GroupInfo {
  id: number;
  curr_staff_in_group: number;
  group_name: string;
  group_member_count: number;
  group_owner_name: string;
  group_id: string;
}

/**
 * 组件Props接口
 */
interface WeChatPopupProps {
  customer_id: string;
}

/**
 * 组件暴露的方法接口
 */
export interface WeChatPopupRef {
  open: (type?: 'staff' | 'group') => void;
  close: () => void;
}

/**
 * 企微&群聊弹窗组件
 * 支持员工列表和群聊列表的展示，包含搜索、分页加载等功能
 */
const WeChatPopup = forwardRef<WeChatPopupRef, WeChatPopupProps>(
  ({ customer_id }, ref) => {
    // 弹窗显示状态
    const [visible, setVisible] = useState(false);

    // 当前标签页类型
    const [tabType, setTabType] = useState<'staff' | 'group'>('staff');

    // 搜索关键词
    const [searchValue, setSearchValue] = useState('');

    // 员工列表相关状态
    const [staffList, setStaffList] = useState<StaffInfo[] | null>(null);
    const [staffPage, setStaffPage] = useState(0);

    // 群聊列表相关状态
    const [groupList, setGroupList] = useState<GroupInfo[] | null>(null);
    const [groupPage, setGroupPage] = useState(0);

    // 是否还有更多数据
    const [hasMore, setHasMore] = useState(false);

    // 加载状态
    const [loading, setLoading] = useState(false);

    /**
     * 获取员工列表数据
     */
    const getStaffData = useCallback(async () => {
      if (loading) return;

      try {
        setLoading(true);
        const { errorCode, responseData } = await api.pagefetch({
          path: '/chain-wxapp/v1/customer/getCustomerAddStaffList',
          params: {
            customer_id,
            page: staffPage,
          },
          method: FetchModule.Method.POST,
        });

        if (errorCode === 0 && responseData?.list) {
          const newList = responseData.list;
          setStaffList(prevList =>
            prevList ? prevList.concat(newList) : newList
          );
          setHasMore(responseData.has_more === 1);
        }
      } catch (error) {
        console.error('获取员工列表失败:', error);
      } finally {
        setLoading(false);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [customer_id, staffPage]);

    /**
     * 获取群聊列表数据
     */
    const getGroupData = useCallback(
      async (keyword: string = '', page: number = 0) => {
        if (loading) return;

        try {
          setLoading(true);

          const { errorCode, responseData } = await api.pagefetch({
            path: '/chain-wxapp/v1/customer/getCustomerJoinedGroupList',
            params: {
              customer_id,
              page,
              group_name: keyword,
            },
            method: FetchModule.Method.POST,
          });

          if (errorCode === 0 && responseData?.list) {
            const newList = responseData.list;
            setGroupList(prevList =>
              // 如果是第一页（搜索或重新加载），直接设置新数据；否则追加数据
              page === 0
                ? newList
                : prevList
                  ? prevList.concat(newList)
                  : newList
            );
            setHasMore(responseData.has_more === 1);
          }
        } catch (error) {
          console.error('获取群聊列表失败:', error);
        } finally {
          setLoading(false);
        }
      },
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [customer_id]
    );

    /**
     * 处理搜索
     */
    const handleClickToSearch = useCallback(
      (keyword: string) => {
        setGroupPage(0);
        // 搜索时重置群聊列表数据
        setGroupList(null);
        // 重新获取数据
        setTimeout(() => {
          getGroupData(keyword, 0);
        }, 100);
      },
      [getGroupData]
    );

    /**
     * 处理加载更多数据
     */
    const handleLoadMore = useCallback(() => {
      if (!hasMore || loading) return;

      if (tabType === 'staff') {
        const nextPage = staffPage + 1;
        setStaffPage(nextPage);
        // 员工列表使用 getStaffData，它内部会使用最新的 staffPage
        setTimeout(() => getStaffData(), 0);
      } else {
        const nextPage = groupPage + 1;
        setGroupPage(nextPage);
        // 群聊列表需要传入当前搜索关键词和新页码
        setTimeout(() => getGroupData(searchValue, nextPage), 0);
      }
    }, [
      hasMore,
      loading,
      tabType,
      staffPage,
      groupPage,
      getStaffData,
      getGroupData,
      searchValue,
    ]);

    /**
     * 切换标签页
     */
    const changeTab = useCallback(
      (type: 'staff' | 'group') => {
        if (type === tabType) return;

        setTabType(type);
        setHasMore(true);
        setLoading(false);

        // 重置数据
        setStaffList(null);
        setStaffPage(0);
        setGroupList(null);
        setGroupPage(0);

        // 获取对应数据
        if (type === 'staff') {
          setTimeout(() => {
            getStaffData();
          }, 100);
        } else {
          setTimeout(() => {
            getGroupData('', 0); // 明确传递空字符串和页码0
          }, 100);
        }
      },
      [tabType, getStaffData, getGroupData]
    );

    /**
     * 打开弹窗
     */
    const open = useCallback(
      (type: 'staff' | 'group' = 'staff') => {
        setVisible(true);
        setTabType(type);
        setLoading(false);

        // 初始化数据
        if (type === 'staff') {
          setStaffPage(0);
          setStaffList(null);
          setTimeout(() => {
            getStaffData();
          }, 100);
        } else {
          setGroupPage(0);
          setGroupList(null);
          setSearchValue(''); // 重置搜索框
          setTimeout(() => {
            getGroupData('', 0); // 明确传递空字符串和页码0
          }, 100);
        }
      },
      [getStaffData, getGroupData]
    );

    /**
     * 关闭弹窗
     */
    const close = useCallback(() => {
      setVisible(false);
      setSearchValue(''); // 清空搜索框
      // 重置数据为未搜索状态
      setStaffList(null);
      setGroupList(null);
    }, []);

    /**
     * 处理取消
     */
    const handleCancel = useCallback(() => {
      close();
    }, [close]);

    // 暴露方法给父组件
    useImperativeHandle(
      ref,
      () => ({
        open,
        close,
      }),
      [open, close]
    );

    /**
     * 渲染员工卡片
     */
    const renderStaffCard = useCallback(({ item }: { item: StaffInfo }) => {
      return (
        <View style={styles.listItem}>
          <View style={styles.staffCard}>
            {item.is_my_acc === 1 && (
              <View style={styles.tag}>
                <Text style={styles.tagText}>我的账号</Text>
              </View>
            )}
            <Image source={{ uri: item.avatar }} style={styles.avatar} />
            <View style={styles.staffCardMain}>
              <Text style={[styles.line, styles.line1]}>{item.staff_name}</Text>
              <Text style={styles.line} numberOfLines={1}>
                {item.departments}
              </Text>
              <Text style={styles.line}>
                最近沟通时间：{item.last_chat_time}
              </Text>
            </View>
          </View>
        </View>
      );
    }, []);

    /**
     * 渲染群聊卡片
     */
    const renderGroupCard = useCallback(({ item }: { item: GroupInfo }) => {
      return (
        <View style={styles.listItem}>
          <View style={styles.staffCard}>
            {item.curr_staff_in_group === 1 && (
              <View style={styles.tag}>
                <Text style={styles.tagText}>我参与的</Text>
              </View>
            )}
            <Image
              source={{
                uri: 'https://static.soyoung.com/sy-pre/t3z1tyfhrqqs-1754554200637.png',
              }}
              style={styles.avatar}
            />
            <View style={styles.staffCardMain}>
              <View style={styles.lineContainer}>
                <View style={styles.nameBox}>
                  <Text style={[styles.line, styles.line1]} numberOfLines={1}>
                    {item.group_name}
                  </Text>
                </View>
                <Text style={[styles.line, styles.line1]}>
                  （{item.group_member_count}）
                </Text>
              </View>
              <Text style={styles.line}>群主：{item.group_owner_name}</Text>
            </View>
          </View>
        </View>
      );
    }, []);

    /**
     * 渲染员工列表底部
     */
    const renderStaffFooter = useCallback(() => {
      if (loading) {
        return <Text style={styles.loadMore}>加载中...</Text>;
      }
      if (hasMore) {
        return null;
      }
      if (staffList && staffList.length > 0) {
        return <Text style={styles.loadMore}>没有更多啦</Text>;
      }
      return null;
    }, [loading, hasMore, staffList]);

    /**
     * 渲染群聊列表底部
     */
    const renderGroupFooter = useCallback(() => {
      if (loading) {
        return <Text style={styles.loadMore}>加载中...</Text>;
      }
      if (hasMore) {
        return null;
      }
      if (groupList && groupList.length > 0) {
        return <Text style={styles.loadMore}>没有更多啦</Text>;
      }
      return null;
    }, [loading, hasMore, groupList]);

    /**
     * 渲染员工空状态
     */
    const renderStaffEmptyComponent = useCallback(() => {
      return <Empty text='暂无员工数据' style={styles.emptyContainer} />;
    }, []);

    /**
     * 渲染群聊空状态
     */
    const renderGroupEmptyComponent = useCallback(() => {
      return <Empty text='暂无群聊数据' style={styles.emptyContainer} />;
    }, []);

    const staffKeyExtractor = useCallback((item: StaffInfo, index: number) => {
      return `staff-${item.id}-${index}`;
    }, []);

    /**
     * 获取群聊列表项key
     */
    const groupKeyExtractor = useCallback((item: GroupInfo, index: number) => {
      return `group-${item.group_id}-${index}`;
    }, []);

    /**
     * 处理搜索输入框确定按钮
     */
    const handleSearchSubmit = useCallback(() => {
      handleClickToSearch(searchValue);
    }, [searchValue, handleClickToSearch]);

    const handleClearIcon = useCallback(() => {
      setSearchValue('');
      // 清空搜索框时重置群聊列表，加载全部数据
      setGroupList(null);
      setGroupPage(0);
      setTimeout(() => {
        getGroupData('', 0);
      }, 100);
    }, [getGroupData]);

    return (
      <Modal
        isVisible={visible}
        {...modalAnimation}
        onBackdropPress={handleCancel}
        animationIn='slideInUp'
        animationOut='slideOutDown'
        style={styles.modal}
        useNativeDriver={true}
        avoidKeyboard={false}
        statusBarTranslucent={Platform.OS === 'android'}
      >
        <View style={styles.popupContent}>
          {/* 顶部区域 */}
          <View style={styles.popupTop}>
            {/* 顶部栏 */}
            <View style={styles.topBar}>
              <View style={styles.cancelButton} />
              <Text style={styles.title}>企微&群聊</Text>
              <ATrack onPress={handleCancel}>
                <Image
                  source={{
                    uri: 'https://static.soyoung.com/sy-pre/1t4hktog3apho-1712556600714.png',
                  }}
                  style={styles.confirmButton}
                />
              </ATrack>
            </View>

            {/* 标签页 */}
            <View style={styles.tabBox}>
              <View style={styles.tabLeft}>
                <ATrack
                  style={[
                    styles.tabItem,
                    tabType === 'staff' && styles.activeTab,
                  ]}
                  onPress={() => changeTab('staff')}
                >
                  <Text
                    style={[
                      styles.tabText,
                      tabType === 'staff' && styles.activeTabText,
                    ]}
                  >
                    员工
                  </Text>
                </ATrack>
                <ATrack
                  style={[
                    styles.tabItem,
                    tabType === 'group' && styles.activeTab,
                  ]}
                  onPress={() => changeTab('group')}
                >
                  <Text
                    style={[
                      styles.tabText,
                      tabType === 'group' && styles.activeTabText,
                    ]}
                  >
                    群聊
                  </Text>
                </ATrack>
              </View>
            </View>

            {/* 搜索框 - 仅群聊时显示 */}
            {tabType === 'group' && (
              <View style={styles.searchContainer}>
                <Image
                  source={{
                    uri: 'https://static.soyoung.com/sy-pre/1y4a2d4fr2l31-1711955400686.png',
                  }}
                  style={styles.searchIcon}
                />
                <TextInput
                  style={styles.searchInput}
                  placeholder='请输入群聊名称'
                  value={searchValue}
                  onChangeText={setSearchValue}
                  returnKeyType='search'
                  onSubmitEditing={handleSearchSubmit}
                />
                {searchValue.length > 0 && (
                  <ATrack onPress={handleClearIcon}>
                    <View style={styles.clearIconWrapper}>
                      <Image
                        source={{
                          uri: 'https://static.soyoung.com/sy-pre/219l297us0uts-1711617000689.png',
                        }}
                        style={styles.clearIcon}
                      />
                    </View>
                  </ATrack>
                )}
              </View>
            )}
          </View>

          {/* 内容区域 */}
          {tabType === 'staff' ? (
            <FlatList
              data={staffList || []}
              renderItem={renderStaffCard}
              keyExtractor={staffKeyExtractor}
              style={[styles.popupBody, styles.staffBody]}
              showsVerticalScrollIndicator={false}
              onEndReached={handleLoadMore}
              ListEmptyComponent={renderStaffEmptyComponent}
              ListFooterComponent={renderStaffFooter}
              getItemLayout={(data, index) => ({
                length: getRealSize(80),
                offset: getRealSize(80) * index,
                index,
              })}
              {...flatListProps}
            />
          ) : (
            <FlatList
              data={groupList || []}
              renderItem={renderGroupCard}
              keyExtractor={groupKeyExtractor}
              style={styles.popupBody}
              showsVerticalScrollIndicator={false}
              onEndReached={handleLoadMore}
              ListEmptyComponent={renderGroupEmptyComponent}
              ListFooterComponent={renderGroupFooter}
              getItemLayout={(data, index) => ({
                length: getRealSize(80),
                offset: getRealSize(80) * index,
                index,
              })}
              {...flatListProps}
            />
          )}
        </View>
      </Modal>
    );
  }
);

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  popupContent: {
    width: '100%',
    height: getRealSize(547),
    backgroundColor: '#FFFFFF',
  },
  popupTop: {
    paddingHorizontal: getRealSize(15),
    paddingTop: getRealSize(15),
    paddingBottom: getRealSize(10),
  },
  topBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  cancelButton: {
    width: getRealSize(20),
    height: getRealSize(20),
  },
  title: {
    fontSize: getRealSize(16),
    color: '#333333',
    fontWeight: '500',
  },
  confirmButton: {
    width: getRealSize(20),
    height: getRealSize(20),
  },
  tabBox: {
    height: getRealSize(35),
    marginTop: getRealSize(13),
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
  },
  tabLeft: {
    flexDirection: 'row',
    borderWidth: getRealSize(1),
    borderColor: '#F0F0F0',
    padding: getRealSize(4),
  },
  tabItem: {
    width: getRealSize(56),
    height: getRealSize(27),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: getRealSize(6),
  },
  activeTab: {
    backgroundColor: '#EBFBDC',
  },
  tabText: {
    fontSize: getRealSize(13),
    color: '#333333',
  },
  activeTabText: {
    color: '#61B8A3',
  },
  searchContainer: {
    marginTop: getRealSize(16),
    height: getRealSize(36),
    backgroundColor: '#f8f8f8',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getRealSize(15),
  },
  searchIcon: {
    width: getRealSize(16),
    height: getRealSize(16),
    marginRight: getRealSize(8),
  },
  searchInput: {
    flex: 1,
    fontSize: getRealSize(15),
    color: '#161616',
    paddingVertical: 0,
  },
  clearIconWrapper: {
    padding: getRealSize(5), // 10rpx
  },
  clearIcon: {
    width: getRealSize(12), // 24rpx
    height: getRealSize(12), // 24rpx
  },
  popupBody: {
    backgroundColor: '#f8f8f8',
    paddingHorizontal: getRealSize(15),
    paddingBottom: getRealSize(10),
    height: getRealSize(342),
  },
  staffBody: {
    height: getRealSize(390),
  },
  listItem: {
    marginTop: getRealSize(10),
  },
  loadMore: {
    textAlign: 'center',
    fontSize: getRealSize(12),
    color: '#777777',
    marginTop: getRealSize(20),
    marginBottom: getRealSize(10),
  },
  emptyContainer: {
    paddingTop: getRealSize(100),
  },
  staffCard: {
    backgroundColor: '#FFFFFF',
    padding: getRealSize(20),
    flexDirection: 'row',
    alignItems: 'flex-start',
    position: 'relative',
  },
  tag: {
    position: 'absolute',
    top: 0,
    left: getRealSize(10),
    backgroundColor: '#00AB84',
    borderRadius: getRealSize(8),
    paddingHorizontal: getRealSize(8),
    paddingVertical: getRealSize(2),
    zIndex: 1,
  },
  tagText: {
    fontSize: getRealSize(10),
    color: '#FFFFFF',
    fontWeight: '500',
  },
  avatar: {
    width: getRealSize(40),
    height: getRealSize(40),
    borderRadius: getRealSize(20),
    marginRight: getRealSize(12),
  },
  staffCardMain: {
    flex: 1,
  },
  lineContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getRealSize(4),
  },
  nameBox: {
    maxWidth: '70%',
  },
  line: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(11),
    color: '#777777',
    fontWeight: '400',
    marginBottom: getRealSize(4),
  },
  line1: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(14),
    color: '#333333',
    fontWeight: '500',
  },
  right: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  wxIcon: {
    width: getRealSize(24),
    height: getRealSize(24),
  },
});

export default WeChatPopup;
