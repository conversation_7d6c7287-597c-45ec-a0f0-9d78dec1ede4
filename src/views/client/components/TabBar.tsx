import React, { memo, useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, ImageBackground } from 'react-native';
import { ATrack } from '@soyoung/react-native-container';
import { back } from '@soyoung/react-native-base';
import { getRealSize } from '@/common/utils';
import { checkPermission } from '@/common/checkPermission';

interface TabBarProps {
  selectTabValue: string;
  onSelectTab: (type: string) => void;
  onBack?: () => void; // 添加返回按钮回调
}

const TabBar: React.FC<TabBarProps> = memo(
  ({ selectTabValue, onSelectTab, onBack }) => {
    const [hasMyCustomerPermission, setHasMyCustomerPermission] =
      useState(false);
    const [hasAllCustomerPermission, setHasAllCustomerPermission] =
      useState(false);

    useEffect(() => {
      const initPermissions = async () => {
        const myCustomerPerm = await checkPermission('customer:mine');
        const allCustomerPerm = await checkPermission('customer:all');
        setHasMyCustomerPermission(myCustomerPerm);
        setHasAllCustomerPermission(allCustomerPerm);
      };

      initPermissions();
    }, []);

    const handleBack = useCallback(() => {
      // 如果提供了onBack回调，则调用它；否则使用默认的back()函数
      if (onBack) {
        onBack();
      } else {
        back();
      }
    }, [onBack]);

    return (
      <View style={styles.tabBox}>
        <ATrack style={styles.image} onPress={handleBack}>
          <ImageBackground
            source={{
              uri: 'https://static.soyoung.com/sy-design/**************************.png',
            }}
            style={styles.image}
          />
        </ATrack>
        {hasMyCustomerPermission && (
          <ATrack style={styles.tabItem} onPress={() => onSelectTab('user')}>
            <Text
              style={[
                styles.tabText,
                selectTabValue === 'user' && styles.activeTabText,
              ]}
            >
              我的客户
            </Text>
            {selectTabValue === 'user' && (
              <View style={styles.activeIndicator} />
            )}
          </ATrack>
        )}
        {hasAllCustomerPermission && (
          <ATrack style={styles.tabItem} onPress={() => onSelectTab('all')}>
            <Text
              style={[
                styles.tabText,
                selectTabValue === 'all' && styles.activeTabText,
              ]}
            >
              全部客户
            </Text>
            {selectTabValue === 'all' && (
              <View style={styles.activeIndicator} />
            )}
          </ATrack>
        )}
      </View>
    );
  }
);

TabBar.displayName = 'TabBar';

const styles = StyleSheet.create({
  tabBox: {
    height: getRealSize(42),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ffffff',
    paddingBottom: getRealSize(10),
    paddingTop: getRealSize(10),
  },
  image: {
    width: getRealSize(44),
    height: getRealSize(44),
    justifyContent: 'flex-start',
    alignItems: 'center',
    position: 'absolute',
    left: 0,
    top: 0,
  },
  tabItem: {
    marginHorizontal: getRealSize(30),
    position: 'relative',
  },
  tabText: {
    fontSize: getRealSize(14),
    color: '#646464',
    fontFamily: 'PingFangSC-Regular',
    lineHeight: getRealSize(20),
  },
  activeTabText: {
    fontSize: getRealSize(14),
    color: '#646464',
    fontFamily: 'PingFangSC-Semibold',
    lineHeight: getRealSize(20),
  },
  activeIndicator: {
    position: 'absolute',
    bottom: getRealSize(-8),
    left: '50%',
    marginLeft: getRealSize(-9),
    width: getRealSize(20),
    height: getRealSize(2),
    backgroundColor: '#030303',
  },
});

export default TabBar;
