import React, { useState, useCallback, memo, useRef } from 'react';
import { View, Text, StyleSheet, Image, ScrollView, Modal } from 'react-native';
import { ATrack } from '@soyoung/react-native-container';
import { getRealSize } from '@/common/utils';
import api, { FetchModule } from '@/common/api';
import { PageInfo } from '../types/detail';
// 导入子组件
import TagPopup, { TagPopupRef } from '@/components/TagPopup';
import TagList from './TagList';
import WeChatPopup, { WeChatPopupRef } from './WeChatPopup';
import Empty from '@/components/Empty';
import DashedLine from '@/components/DashedLine';

// 类型定义
interface ActiveItem {
  tag_id: number;
  name: string;
  tag_color: string;
  select_type: number;
  tag_pid: number;
}

interface OverViewProps {
  pageInfo: PageInfo;
  customerId?: string;
  refresh?: () => void;
  refreshData?: () => void;
  goOrder?: (tabIndex: number) => void;
}

const OverView: React.FC<OverViewProps> = memo(
  ({ pageInfo, customerId = '', refresh, refreshData, goOrder }) => {
    // 状态管理
    const [tabType, setTabType] = useState<number>(0);
    const [compActive, setCompActive] = useState<number[]>([]);
    const [showUserAndGroup, setShowUserAndGroup] = useState<boolean>(false);
    const tagPopupRef = useRef<TagPopupRef>(null);
    const wechatPopupRef = useRef<WeChatPopupRef>(null);
    // // 打开用户和群组弹窗
    const openPop = useCallback((type: string) => {
      wechatPopupRef.current?.open(type as 'staff' | 'group');
    }, []);

    // 处理标签变更
    const handleTagChange = useCallback(
      async (data: ActiveItem | ActiveItem[]) => {
        try {
          const dataArray = Array.isArray(data) ? data : [data];
          const tagIds = dataArray.map(item => item.tag_id);

          const response = await api.pagefetch({
            path: '/chain-wxapp/v1/customer/saveCustomerTag',
            params: {
              customer_id: customerId,
              tag_type: tabType,
              tag_ids: tagIds.join(','),
            },
            method: FetchModule.Method.POST,
            isLoading: false,
          });

          if (response.errorCode === 0) {
            // 优先使用轻量级刷新，避免重新渲染骨架图
            if (refreshData) {
              refreshData();
            } else {
              // 降级到全量刷新
              refresh?.();
            }
          }
        } catch (error) {
          console.error('标签更新失败:', error);
        }
      },
      [customerId, tabType, refreshData, refresh]
    );

    // 处理标签弹窗关闭
    const handleTagPopupClose = useCallback(() => {
      // 弹窗关闭时的逻辑
    }, []);

    // 打开标签弹窗
    const openTagPop = useCallback(
      (type: number) => {
        setTabType(type);
        if (type === 1) {
          setCompActive(
            pageInfo.tags?.user_tag_list?.map(item => +item.tag_id) || []
          );
        } else {
          setCompActive(
            pageInfo.tags?.allergy_tag_list?.map(item => +item.tag_id) || []
          );
        }
        // 使用 setTimeout 确保状态更新完成后再打开弹窗
        setTimeout(() => {
          tagPopupRef.current?.open(type);
        }, 0);
      },
      [pageInfo.tags]
    );

    // 渲染企微信息卡片 - useCallback 优化
    const renderWechatInfo = useCallback(
      () => (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>企微信息</Text>

          {/* 微信昵称 */}
          <View style={styles.textLine}>
            <Text style={styles.label}>微信昵称：</Text>
            <View style={styles.rightContent}>
              {pageInfo.customer_private &&
              pageInfo.customer_private.external_user_info ? (
                <View style={styles.userInfo}>
                  <Image
                    style={styles.userImg}
                    source={{
                      uri: pageInfo.customer_private.external_user_info.avatar,
                    }}
                  />
                  <Text style={styles.userName} numberOfLines={1}>
                    {pageInfo.customer_private.external_user_info.wx_name}
                  </Text>
                </View>
              ) : null}
            </View>
          </View>
          <View style={styles.textLine}>
            <Text style={styles.label}>添加员工：</Text>
            <View style={styles.rightContent}>
              {pageInfo.customer_private?.last_add_staff ? (
                <>
                  <View style={styles.userInfo}>
                    <Image
                      style={styles.userImg}
                      source={{
                        uri: pageInfo.customer_private.last_add_staff.avatar,
                      }}
                    />
                    <Text style={styles.userName} numberOfLines={1}>
                      {pageInfo.customer_private.last_add_staff.staff_name}
                    </Text>
                    {(pageInfo.customer_private.add_staff_num || 0) > 1 && (
                      <Text style={styles.userName}>
                        等{pageInfo.customer_private.add_staff_num}个
                      </Text>
                    )}
                  </View>
                  <ATrack onPress={() => openPop('staff')}>
                    <View style={styles.viewBtnContent}>
                      <Text style={styles.viewBtnText}>查看</Text>
                    </View>
                  </ATrack>
                </>
              ) : (
                <Text style={styles.userNameGray}>暂未添加</Text>
              )}
            </View>
          </View>
          <View style={styles.textLine}>
            <Text style={styles.label}>加入群聊：</Text>
            <View style={styles.rightContent}>
              {pageInfo.customer_private?.last_joined_group ? (
                <>
                  <View style={styles.userInfo}>
                    <Image
                      style={styles.userImg}
                      source={{
                        uri: pageInfo.customer_private.last_joined_group.avatar,
                      }}
                    />
                    <Text style={styles.userName} numberOfLines={1}>
                      {pageInfo.customer_private.last_joined_group.group_name}
                    </Text>
                    {(pageInfo.customer_private.joined_group_num || 0) > 1 && (
                      <Text style={styles.userName}>
                        等{pageInfo.customer_private.joined_group_num}个
                      </Text>
                    )}
                  </View>
                  <ATrack onPress={() => openPop('group')}>
                    <View style={styles.viewBtnContent}>
                      <Text style={styles.viewBtnText}>查看</Text>
                    </View>
                  </ATrack>
                </>
              ) : (
                <Text style={styles.userNameGray}>暂未添加</Text>
              )}
            </View>
          </View>
        </View>
      ),
      [pageInfo.customer_private, openPop]
    );

    const handleGoToOrder = useCallback(
      (tabIndex: number) => {
        // 切换到订单Tab
        goOrder?.(tabIndex);
      },
      [goOrder]
    );

    // 渲染订单信息卡片 - useCallback 优化
    const renderOrderInfo = useCallback(
      () => (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>客户订单</Text>
          <View style={styles.orderItemBox}>
            <ATrack
              style={styles.orderItem}
              onPress={() => {
                handleGoToOrder(0);
              }}
            >
              <Text style={styles.orderNum}>
                {pageInfo.order_num?.product_num || 0}
              </Text>
              <Text style={styles.orderName}>院内项目</Text>
            </ATrack>

            <ATrack
              style={styles.orderItem}
              onPress={() => {
                handleGoToOrder(0);
              }}
            >
              <Text style={styles.orderNum}>
                {pageInfo.order_num?.enable_exec_num || 0}
              </Text>
              <Text style={styles.orderName}>可执行次数</Text>
            </ATrack>

            <ATrack
              style={styles.orderItem}
              onPress={() => {
                handleGoToOrder(1);
              }}
            >
              <Text style={styles.orderNum}>
                {pageInfo.order_num?.yx_order_num || 0}
              </Text>
              <Text style={styles.orderName}>优享订单</Text>
            </ATrack>
          </View>
        </View>
      ),
      [pageInfo.order_num]
    );
    // 渲染动态标签卡片
    const renderDynamicTags = useCallback(
      () => (
        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>动态标签</Text>
          </View>
          <View style={styles.tagContainer}>
            {pageInfo.tags?.dynamic_tag_list?.length ? (
              <TagList list={pageInfo.tags.dynamic_tag_list} />
            ) : (
              <Text style={styles.emptyText}>暂无标签</Text>
            )}
          </View>
        </View>
      ),
      [pageInfo.tags]
    );
    // 渲染敏感标签卡片
    const renderSensitiveTags = useCallback(
      () => (
        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>敏感标签</Text>
            <ATrack style={styles.editBtn} onPress={() => openTagPop(2)}>
              <View style={styles.editBtnBox}>
                <Text style={styles.editBtnText}>编辑</Text>
              </View>
            </ATrack>
          </View>
          <View style={styles.tagContainer}>
            {pageInfo.tags?.allergy_tag_list?.length ? (
              <TagList list={pageInfo.tags.allergy_tag_list} />
            ) : (
              <Text style={styles.emptyText}>暂无标签</Text>
            )}
          </View>
        </View>
      ),
      [pageInfo.tags, openTagPop]
    );

    // 渲染客户标签卡片
    const renderUserTags = useCallback(
      () => (
        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>客户标签</Text>
            <ATrack style={styles.editBtn} onPress={() => openTagPop(1)}>
              <View style={styles.editBtnBox}>
                <Text style={styles.editBtnText}>编辑</Text>
              </View>
            </ATrack>
          </View>
          <View style={styles.tagContainer}>
            {pageInfo.tags?.user_tag_list?.length ? (
              <TagList list={pageInfo.tags.user_tag_list} />
            ) : (
              <Text style={styles.emptyText}>暂无标签</Text>
            )}
          </View>
        </View>
      ),
      [pageInfo.tags, openTagPop]
    );

    const isGreen = useCallback((date: string) => {
      return new Date().getTime() < new Date(date).getTime();
    }, []);

    // 渲染客户动态卡片
    const renderCustomerDynamics = useCallback(
      () => (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>客户动态</Text>

          {pageInfo.get_dynamic_res && pageInfo.get_dynamic_res.length ? (
            <View style={styles.dateLineBox}>
              {pageInfo.get_dynamic_res.map((item: any, index: number) => (
                <View key={index} style={styles.dateLineItem}>
                  <View style={styles.timeBox}>
                    <View
                      style={[
                        styles.point,
                        isGreen(item.date) ? styles.pointGreen : {},
                      ]}
                    />
                    <Text style={styles.timeText}>{item.date}</Text>
                    <Text
                      style={[
                        styles.timeDesc,
                        isGreen(item.date) ? styles.timeDescGreen : {},
                      ]}
                    >
                      {item.date_desc}
                    </Text>
                  </View>
                  <View style={styles.dateLineContentBox}>
                    {item.list.map((inItem: any, inIndex: number) => (
                      <View key={inIndex} style={styles.dateLineContentItem}>
                        <DashedLine
                          style={{
                            height: getRealSize(57),
                            position: 'absolute',
                            top: getRealSize(-10),
                            left: getRealSize(-15),
                          }}
                          axis='vertical'
                          dashLength={2}
                          dashGap={2}
                          dashThickness={1}
                          dashColor='#f0f0f0'
                        />
                        <Text style={styles.contentLeft}>
                          {inItem.create_at_desc}
                        </Text>
                        <View style={styles.contentRight}>
                          <View style={styles.contentLine1}>
                            <View
                              style={[
                                styles.contentIconBox,
                                isGreen(item.date) &&
                                  styles.contentIconBoxGreen,
                              ]}
                            >
                              <Text
                                style={[
                                  styles.contentIcon,
                                  isGreen(item.date) && styles.contentIconGreen,
                                ]}
                              >
                                {inItem.label}
                              </Text>
                            </View>
                            <Text
                              style={styles.contentLine1Text}
                              numberOfLines={1}
                            >
                              {inItem.content}
                            </Text>
                          </View>
                          <Text style={styles.contentLine2} numberOfLines={1}>
                            门店：
                            {(inItem.tenant_obj &&
                              inItem.tenant_obj.tenant_name) ||
                              ''}
                          </Text>
                        </View>
                      </View>
                    ))}
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <View style={styles.emptyContainer}>
              <Empty />
            </View>
          )}
        </View>
      ),
      [pageInfo.get_dynamic_res]
    );

    return (
      <ScrollView style={styles.container}>
        {renderWechatInfo()}
        {renderOrderInfo()}
        {renderDynamicTags()}
        {renderUserTags()}
        {renderSensitiveTags()}

        {renderCustomerDynamics()}

        {/* 占位内容 - 后续添加更多功能 */}
        {/* <View style={styles.placeholderCard}>
          <Text style={styles.placeholderText}>其他功能区块</Text>
          <Text style={styles.placeholderSubText}>待完善...</Text>
        </View> */}

        {/* 弹窗组件 */}
        <Modal
          visible={showUserAndGroup}
          transparent={true}
          onRequestClose={() => setShowUserAndGroup(false)}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>用户信息</Text>
              <Text style={styles.modalContentText}>
                这里会显示用户的详细信息，包括联系方式、标签等
              </Text>
              <ATrack
                style={styles.modalBtn}
                onPress={() => setShowUserAndGroup(false)}
              >
                <Text style={styles.modalBtnText}>关闭</Text>
              </ATrack>
            </View>
          </View>
        </Modal>

        {/* TagPopup 组件 */}
        <TagPopup
          ref={tagPopupRef}
          active={compActive}
          type={tabType}
          title={tabType === 1 ? '客户标签' : '敏感标签'}
          multiple={true}
          onChange={handleTagChange}
          onClose={handleTagPopupClose}
        />
        {/* WeChatPopup 组件 */}
        <WeChatPopup customer_id={customerId} ref={wechatPopupRef} />
      </ScrollView>
    );
  }
);

// 样式定义
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f6f9f9',
  },

  card: {
    backgroundColor: '#ffffff',
    borderRadius: getRealSize(4),
    marginBottom: getRealSize(10),
    padding: getRealSize(15),
  },

  cardTitle: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(15),
    color: '#161616',
    fontWeight: '500',
    marginBottom: getRealSize(12),
  },

  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  textLine: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: getRealSize(10),
  },

  label: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    lineHeight: getRealSize(20),
    color: '#777777',
    minWidth: getRealSize(80),
  },

  rightContent: {
    maxWidth: getRealSize(200),
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },

  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'flex-end',
  },

  userImg: {
    width: getRealSize(18),
    height: getRealSize(18),
    borderRadius: getRealSize(9),
    marginRight: getRealSize(5),
  },

  userName: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#161616',
    marginRight: getRealSize(5),
  },
  userNameGray: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#999999',
    marginRight: getRealSize(5),
  },
  viewBtnContent: {
    height: getRealSize(26),
    backgroundColor: '#FFFFFF',
    borderWidth: getRealSize(0.8),
    borderColor: '#F0F0F0',
    paddingHorizontal: getRealSize(10),
    alignItems: 'center',
    justifyContent: 'center',
  },
  viewBtnText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(10),
    color: '#555555',
    fontWeight: '400',
  },

  emptyText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#999999',
    flex: 1,
  },

  editBtn: {
    height: getRealSize(26),
  },
  editBtnBox: {
    height: getRealSize(26),
    backgroundColor: '#ffffff',
    borderWidth: getRealSize(0.5),
    borderColor: '#bababa',
    paddingHorizontal: getRealSize(14),
    justifyContent: 'center',
  },
  editBtnText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(10),
    color: '#555555',
    fontWeight: '400',
  },
  tagContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  tag: {
    paddingHorizontal: getRealSize(8),
    paddingVertical: getRealSize(4),
    marginRight: getRealSize(10),
    marginBottom: getRealSize(10),
  },

  tagText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(10),
    fontWeight: '400',
  },

  placeholderCard: {
    backgroundColor: '#ffffff',
    borderRadius: getRealSize(4),
    marginHorizontal: getRealSize(15),
    marginTop: getRealSize(10),
    marginBottom: getRealSize(20),
    padding: getRealSize(30),
    alignItems: 'center',
  },

  placeholderText: {
    fontSize: getRealSize(14),
    color: '#333333',
    marginBottom: getRealSize(10),
  },

  placeholderSubText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#777777',
  },

  // 弹窗样式
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  modalContent: {
    backgroundColor: '#ffffff',
    borderRadius: getRealSize(4),
    padding: getRealSize(20),
    marginHorizontal: getRealSize(30),
    maxWidth: getRealSize(250),
    width: '100%',
  },

  modalTitle: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(16),
    color: '#333333',
    fontWeight: '500',
    marginBottom: getRealSize(10),
    textAlign: 'center',
  },

  modalContentText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#666666',
    lineHeight: getRealSize(36),
    marginBottom: getRealSize(30),
  },

  modalBtn: {
    backgroundColor: '#00ab84',
    borderRadius: getRealSize(4),
    paddingVertical: getRealSize(12),
    paddingHorizontal: getRealSize(30),
    alignSelf: 'center',
  },

  modalBtnText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(14),
    color: '#ffffff',
    textAlign: 'center',
  },

  // 订单模块样式
  orderItemBox: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: getRealSize(10),
  },

  orderItem: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    width: getRealSize(110),
    height: getRealSize(60),
  },

  orderNum: {
    fontFamily: 'Outfit-Regular',
    fontSize: getRealSize(20),
    color: '#030303',
    fontWeight: '400',
  },

  orderName: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(10),
    color: '#8c8c8c',
    fontWeight: '400',
  },

  // 客户动态样式
  dateLineBox: {
    flexDirection: 'column',
    marginTop: getRealSize(10),
  },

  dateLineItem: {
    marginTop: getRealSize(10),
    fontFamily: 'PingFangSC-Regular',
  },
  contentLine1Text: {
    maxWidth: getRealSize(220),
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#303233',
    fontWeight: '400',
  },
  dateLineItemGreen: {
    color: '#61B43E',
  },

  timeBox: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  point: {
    width: getRealSize(11),
    height: getRealSize(11),
    borderRadius: getRealSize(11),
    borderWidth: getRealSize(3),
    borderColor: '#dedede',
  },

  pointGreen: {
    borderColor: '#61B43E',
  },

  timeText: {
    marginLeft: getRealSize(10),
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(13),
    color: '#303233',
    fontWeight: '500',
  },

  timeDesc: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(13),
    color: '#999999',
    fontWeight: '500',
    marginLeft: getRealSize(5),
  },

  timeDescGreen: {
    color: '#61B43E',
  },

  dateLineContentBox: {
    marginTop: getRealSize(10),
    marginLeft: getRealSize(5),
    paddingLeft: getRealSize(15),
  },

  dateLineContentItem: {
    marginBottom: getRealSize(20),
    flexDirection: 'row',
  },

  contentLeft: {
    fontSize: getRealSize(12),
    color: '#aaabb3',
  },

  contentRight: {
    marginLeft: getRealSize(10),
    flex: 1,
  },

  contentLine1: {
    flexDirection: 'row',
    alignItems: 'center',
    fontSize: getRealSize(13),
    color: '#161616',
    lineHeight: getRealSize(18),
    marginRight: getRealSize(10),
  },
  contentIconBox: {
    height: getRealSize(18),
    paddingHorizontal: getRealSize(5),
    backgroundColor: '#F0F0F0',
    marginRight: getRealSize(5),
  },
  contentIconBoxGreen: {
    backgroundColor: '#EBFBDC',
  },
  contentIcon: {
    fontSize: getRealSize(10),
    color: '#aaabb3',

    lineHeight: getRealSize(18),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  contentIconGreen: {
    color: '#61B43E',
  },

  contentLine2: {
    maxWidth: getRealSize(240),
    marginTop: getRealSize(5),
    fontSize: getRealSize(11),
    color: '#999999',
  },

  emptyContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    paddingTop: getRealSize(60),
  },
});

export default OverView;
