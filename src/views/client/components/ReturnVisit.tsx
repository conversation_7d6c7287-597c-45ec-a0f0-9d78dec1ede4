import React, {
  useState,
  useEffect,
  useCallback,
  memo,
  useRef,
  useMemo,
} from 'react';
import { View, Text, StyleSheet, FlatList, Image } from 'react-native';
import { flatListProps } from '@/constant/flatlist_props';
import { ATrack } from '@soyoung/react-native-container';
import Modal from 'react-native-modal';
import { getRealSize } from '@/common/utils';
import api, { FetchModule } from '@/common/api';
import LinearGradient from 'react-native-linear-gradient';
import Empty from '@/components/Empty';
import { Bridge } from '@/common/bridge';
import { ReturnVisitSkeleton } from './ClientDetailSkeleton';
import CommonPopup, {
  CommonPopupRef,
  Options,
  Active,
} from '@/components/CommonPopup';
import EmployeePopup, {
  EmployeePopupRef,
  Active as EmployeeActive,
} from '@/components/EmployeePopup';
import DateInput from '@/components/DateInput';
import { jumpReactNativePage } from '@/common/jumpPage';

// 类型定义
interface UserInfo {
  name: string;
  id: number;
}

interface StatusItem {
  value: number;
  label: string;
}

interface ReturnVisitItem {
  plan_id: string;
  followup_type_desc: string;
  followup_user_name: string;
  plan_time: string;
  title: string;
  content: string;
  is_expire: number;
  plan_status_str: string;
  plan_status: number;
  followup_time: string;
  trans_time: string;
  tenant: {
    name: string;
  };
  followup_outline: string;
  followup_record: string;
  invalid_reason: string;
}

interface ReturnVisitProps {
  customerId: string;
  userInfo?: UserInfo | null;
  currentLoginUserInfo?: UserInfo | null;
  pageShow?: boolean; // 添加页面显示状态
}

const ReturnVisit: React.FC<ReturnVisitProps> = memo(
  ({ customerId, userInfo, currentLoginUserInfo, pageShow }) => {
    // 状态管理
    const [dataList, setDataList] = useState<ReturnVisitItem[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [hasMore, setHasMore] = useState<boolean>(true);
    const [page, setPage] = useState<number>(1);
    const [componentShow, setComponentShow] = useState<boolean>(true);
    const [initialLoading, setInitialLoading] = useState<boolean>(true);
    const [lastPageShow, setLastPageShow] = useState<boolean>(false); // 记录上一次的页面显示状态

    // 过滤器状态
    const [filterStatus, setFilterStatus] = useState<string>('');
    const [status, setStatus] = useState<number>(1);
    const [visitorItem, setVisitorItem] = useState<UserInfo>({
      name: '全部回访人',
      id: NaN,
    });
    const [formatDateResp, setFormatDateResp] = useState<string[] | null>(null);
    const [dateModalVisible, setDateModalVisible] = useState<boolean>(false);
    const [tempDateRange, setTempDateRange] = useState<[string, string] | null>(
      null
    );

    // CommonPopup refs
    const statusPopupRef = useRef<CommonPopupRef>(null);

    // EmployeePopup ref
    const employeePopupRef = useRef<EmployeePopupRef>(null);

    // 状态过滤选项
    const statusFilterObject: StatusItem[] = useMemo(
      () => [
        {
          value: 0,
          label: '全部状态',
        },
        {
          value: 1,
          label: '待回访',
        },
        {
          value: 2,
          label: '已回访',
        },
        {
          value: 4,
          label: '已超时',
        },
        {
          value: 3,
          label: '已作废',
        },
      ],
      []
    );

    // 转换为CommonPopup需要的Options格式
    const statusOptions = useMemo((): Options[] => {
      return statusFilterObject.map((item, index) => ({
        label: item.label,
        value: index,
      }));
    }, []);

    // 当前选中的选项
    const currentStatusActive = useMemo(() => {
      const index = statusFilterObject.findIndex(item => item.value === status);
      return {
        id: index >= 0 ? index : 0,
        name: statusFilterObject[index >= 0 ? index : 0]?.label || '全部状态',
      };
    }, [status]);

    // 监听弹窗显示状态
    useEffect(() => {
      if (filterStatus === 'status') {
        statusPopupRef.current?.open();
      }
    }, [filterStatus]);

    useEffect(() => {
      if (filterStatus === 'visitor') {
        employeePopupRef.current?.open();
      }
    }, [filterStatus]);

    useEffect(() => {
      if (filterStatus === 'date') {
        setDateModalVisible(true);
        // 初始化临时日期范围
        setTempDateRange(
          formatDateResp ? [formatDateResp[0], formatDateResp[1]] : ['', '']
        );
      }
    }, [filterStatus, formatDateResp]);

    // 获取回访记录数据
    const getData = useCallback(
      async (
        isChangeFilter: boolean = false,
        queryStatus?: number,
        queryVisitorId?: number,
        queryDateResp?: string[] | null
      ) => {
        if (loading) return;

        // 使用传入的参数或当前状态值
        const finalStatus = queryStatus !== undefined ? queryStatus : status;
        const finalVisitorId =
          queryVisitorId !== undefined ? queryVisitorId : visitorItem.id;
        const finalDateResp =
          queryDateResp !== undefined ? queryDateResp : formatDateResp;
        const currentPage = isChangeFilter ? 1 : page;

        if (isChangeFilter) {
          setPage(1);
          setHasMore(true);
        }

        setLoading(true);
        try {
          const { errorCode, responseData } = await api.pagefetch({
            path: '/chain-wxapp/v1/customer/getFollowupList',
            params: {
              customer_id: customerId || 0,
              status: finalStatus,
              followup_user_id: finalVisitorId || null,
              plan_date_start: finalDateResp ? finalDateResp[0] : '',
              plan_date_end: finalDateResp ? finalDateResp[1] : '',
              page: currentPage,
              limit: 10,
            },
            method: FetchModule.Method.POST,
            isLoading: false,
          });

          if (errorCode === 0 && responseData) {
            const newList = responseData.followup_plan_list || [];
            setHasMore(responseData.has_more === 1);

            if (isChangeFilter) {
              setDataList(newList);
              setPage(2);
            } else {
              setDataList(prev => [...prev, ...newList]);
              setPage(prev => prev + 1);
            }
          }
        } catch (error) {
          console.error('获取回访记录数据失败:', error);
        } finally {
          setLoading(false);
        }
      },
      [loading, customerId, page, status, visitorItem.id, formatDateResp]
    );

    // 加载更多数据
    const loadMore = useCallback(() => {
      if (hasMore && !loading && componentShow) {
        getData(false);
      }
    }, [hasMore, loading, componentShow, getData]);

    // 过滤器选择
    const filterSelect = useCallback(
      (type: string) => {
        if (filterStatus === type) {
          // 如果点击的是当前已激活的过滤器，关闭对应的弹窗
          if (type === 'status') {
            statusPopupRef.current?.close();
          } else if (type === 'visitor') {
            employeePopupRef.current?.close();
          } else if (type === 'date') {
            setDateModalVisible(false);
          }
          setFilterStatus('');
        } else {
          setFilterStatus(type);
        }
      },
      [filterStatus]
    );

    // 状态选择
    const handleStatusSelect = useCallback(
      (active: Active) => {
        if (!Array.isArray(active)) {
          // 处理取消选中的情况（id为空字符串）
          if (
            active.id === '' ||
            active.id === null ||
            active.id === undefined
          ) {
            // 重置为"全部状态"
            const defaultStatus = 0;
            setStatus(defaultStatus);
            setFilterStatus('');
            getData(true, defaultStatus, visitorItem.id, formatDateResp);
          } else if (active.id !== -1) {
            // 处理正常选中的情况
            const selectedItem = statusFilterObject[active.id as number];
            if (selectedItem) {
              const newStatus = selectedItem.value;
              setStatus(newStatus);
              setFilterStatus('');
              // 直接传递新的状态值
              getData(true, newStatus, visitorItem.id, formatDateResp);
            }
          }
        }
      },
      [formatDateResp, getData, statusFilterObject, visitorItem.id]
    );

    // 回访人选择
    const handleVisitorSelect = useCallback(
      (active: EmployeeActive) => {
        if (!Array.isArray(active)) {
          console.log('active', active);

          // 处理取消选中的情况（id为NaN或空）
          if (
            isNaN(active.id) ||
            active.id === null ||
            active.id === undefined
          ) {
            // 重置为"全部回访人"
            const defaultVisitorItem = {
              name: '全部回访人',
              id: NaN,
            };
            setVisitorItem(defaultVisitorItem);
            setFilterStatus('');
            getData(true, status, NaN, formatDateResp);
          } else {
            // 处理正常选中的情况
            const newVisitorItem = {
              name: active.name,
              id: active.id,
            };
            setVisitorItem(newVisitorItem);
            setFilterStatus('');
            // 直接传递新的访问者ID
            getData(true, status, active.id, formatDateResp);
          }
        }
      },
      [getData, formatDateResp, status]
    );

    // 日期输入处理
    const handleDateInputChange = useCallback(
      (value: string | [string, string]) => {
        if (Array.isArray(value)) {
          setTempDateRange(value);
        }
      },
      []
    );

    // 检查确认按钮是否可用
    const isDateConfirmDisabled = useMemo(() => {
      return !tempDateRange || !tempDateRange[0] || !tempDateRange[1];
    }, [tempDateRange]);

    // 确认日期选择
    const handleDateConfirm = useCallback(() => {
      // 检验开始时间和结束时间都必须有值
      if (!tempDateRange) {
        Bridge.showToast('请选择日期范围');
        return;
      }

      if (!tempDateRange[0]) {
        Bridge.showToast('请选择开始日期');
        return;
      }

      if (!tempDateRange[1]) {
        Bridge.showToast('请选择结束日期');
        return;
      }

      const newDateResp = tempDateRange;
      setFormatDateResp(tempDateRange);
      setDateModalVisible(false);
      setFilterStatus('');
      // 直接传递新的日期范围
      getData(true, status, visitorItem.id, newDateResp);
    }, [tempDateRange, status, visitorItem.id, getData]);

    // 重置日期选择
    const handleDateReset = useCallback(() => {
      setFilterStatus('');
      setTempDateRange(null);
      setFormatDateResp(null);
      setDateModalVisible(false);
      // 重新获取数据，清空日期过滤条件
      getData(true, status, visitorItem.id, null);
    }, [status, visitorItem.id, getData]);

    // 取消日期选择
    const handleDateCancel = useCallback(() => {
      setFilterStatus('');
      setTempDateRange(null);
      setDateModalVisible(false);
    }, []);

    // 处理弹窗关闭
    const handlePopupClose = () => {
      setFilterStatus('');
    };

    // 跳转到回访页面
    const goVisit = useCallback((_item: ReturnVisitItem) => {
      if (_item.plan_status === 3) {
        return;
      }
      jumpReactNativePage(`followup/visitedDetail?id=${_item.plan_id}`);
    }, []);

    // 轻量级刷新：只更新数据，不显示骨架图
    const refreshData = useCallback(async () => {
      if (loading) return;

      setLoading(true);
      try {
        const { errorCode, responseData } = await api.pagefetch({
          path: '/chain-wxapp/v1/customer/getFollowupList',
          params: {
            customer_id: customerId || 0,
            status: status,
            followup_user_id: visitorItem.id || null,
            plan_date_start: formatDateResp ? formatDateResp[0] : '',
            plan_date_end: formatDateResp ? formatDateResp[1] : '',
            page: 1,
            limit: 10,
          },
          method: FetchModule.Method.POST,
          isLoading: false, // 关键：不显示loading
        });

        if (errorCode === 0 && responseData) {
          const newList = responseData.followup_plan_list || [];
          setHasMore(responseData.has_more === 1);
          setDataList(newList);
          setPage(2);
        }
      } catch (error) {
        console.error('刷新回访记录数据失败:', error);
      } finally {
        setLoading(false);
      }
    }, [loading, customerId, status, visitorItem.id, formatDateResp]);

    // 监听页面显示状态变化
    useEffect(() => {
      // 当页面从隐藏变为显示时，且不是初始加载，则刷新数据
      if (pageShow && !lastPageShow && !initialLoading) {
        refreshData();
      }
      setLastPageShow(!!pageShow);
    }, [pageShow, lastPageShow, initialLoading, refreshData]);

    // 渲染过滤器
    const renderFilter = () => (
      <LinearGradient
        colors={['#fff', '#f8f8f8']}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        locations={[0.75, 1]}
      >
        <View style={styles.filter}>
          {/* 状态过滤 */}
          <ATrack
            style={[
              styles.filterItem,
              filterStatus === 'status' && styles.filterItemActive,
              status !== 0 && styles.filterItemGreen,
            ]}
            onPress={() => filterSelect('status')}
          >
            <Text
              style={[
                styles.filterItemText,
                filterStatus === 'status' && styles.filterItemTextActive,
                status !== 0 && styles.filterItemTextGreen,
              ]}
              numberOfLines={1}
            >
              {statusFilterObject.find(item => item.value === status)?.label}
            </Text>
            <Image
              source={{
                uri:
                  status !== 0
                    ? 'https://static.soyoung.com/sy-design/1crobjc81415o1754274733315.png'
                    : 'https://static.soyoung.com/sy-design/3u5f7j35qkn711753259920865.png',
              }}
              style={styles.filterArrow}
            />
          </ATrack>

          {/* 回访人过滤 */}
          <ATrack
            style={[
              styles.filterItem,
              filterStatus === 'visitor' && styles.filterItemActive,
              !isNaN(visitorItem.id) && styles.filterItemGreen,
            ]}
            onPress={() => filterSelect('visitor')}
          >
            <Text
              style={[
                styles.filterItemText,
                filterStatus === 'visitor' && styles.filterItemTextActive,
                !isNaN(visitorItem.id) && styles.filterItemTextGreen,
              ]}
              numberOfLines={1}
            >
              {isNaN(visitorItem.id) ? '全部回访人' : visitorItem.name}
            </Text>
            <Image
              source={{
                uri: !isNaN(visitorItem.id)
                  ? 'https://static.soyoung.com/sy-design/1crobjc81415o1754274733315.png'
                  : 'https://static.soyoung.com/sy-design/3u5f7j35qkn711753259920865.png',
              }}
              style={styles.filterArrow}
            />
          </ATrack>

          {/* 日期过滤 */}
          <ATrack
            style={[
              styles.filterItem,
              filterStatus === 'date' && styles.filterItemActive,
              formatDateResp &&
                formatDateResp.length > 0 &&
                styles.filterItemGreen,
            ]}
            onPress={() => filterSelect('date')}
          >
            <Text
              style={[
                styles.filterItemText,
                filterStatus === 'date' && styles.filterItemTextActive,
                formatDateResp &&
                  formatDateResp.length > 0 &&
                  styles.filterItemTextGreen,
              ]}
              numberOfLines={1}
            >
              {formatDateResp ? '已选日期' : '全部日期'}
            </Text>
            <Image
              source={{
                uri:
                  formatDateResp && formatDateResp.length > 0
                    ? 'https://static.soyoung.com/sy-design/1crobjc81415o1754274733315.png'
                    : 'https://static.soyoung.com/sy-design/3u5f7j35qkn711753259920865.png',
              }}
              style={styles.filterArrow}
            />
          </ATrack>
        </View>
      </LinearGradient>
    );

    const renderContent = (item: ReturnVisitItem) => {
      switch (true) {
        case item.plan_status === 1:
          return item.followup_outline
            ? `提纲：${item.followup_outline}`
            : `暂无回访提纲`;
        case item.plan_status === 2:
          return item.followup_record
            ? `情况：${item.followup_record}`
            : `暂无回访内容`;
        case item.plan_status === 3:
          return item.invalid_reason
            ? `作废原因：${item.invalid_reason}`
            : `暂无作废原因`;
        default:
      }
      return '';
    };

    // 渲染回访记录卡片
    const renderItem = ({ item }: { item: ReturnVisitItem }) => (
      <ATrack style={styles.card} onPress={() => goVisit(item)}>
        <View style={styles.cardHeader}>
          <View
            style={[
              styles.statusTag,
              [2, 3].includes(item.plan_status) && styles.statusTagGray,
              item.plan_status === 1 &&
                item.is_expire === 1 &&
                styles.statusTagRed,
            ]}
          >
            <Text
              style={[
                styles.statusTagText,
                [2, 3].includes(item.plan_status) && styles.statusTagTextGray,
                item.plan_status === 1 &&
                  item.is_expire === 1 &&
                  styles.statusTagTextRed,
              ]}
            >
              {item.plan_status_str}
            </Text>
          </View>
          <Text style={styles.timeText}>
            {item.plan_status === 2 || item.plan_status === 3
              ? item.followup_time
              : item.plan_time}
          </Text>
          <Text style={styles.timeDesc}>{item.trans_time}</Text>
        </View>
        <View style={styles.cardInfo}>
          <Text style={styles.cardValue}>{item.followup_user_name}</Text>
          {item.tenant?.name && item.followup_user_name ? (
            <Text style={styles.cardInfoSeparator}>|</Text>
          ) : null}
          <Text style={styles.cardValue}>{item.tenant?.name}</Text>
        </View>
        <View style={styles.cardContent}>
          <Text style={styles.cardContentText} numberOfLines={2}>
            {renderContent(item)}
          </Text>
        </View>
        <View style={styles.cardFooter}>
          {item.plan_status === 2 ? (
            <ATrack onPress={() => goVisit(item)}>
              <View
                style={[styles.cardFooterButton, styles.cardFooterButtonCancel]}
              >
                <Text
                  style={[
                    styles.cardFooterButtonText,
                    styles.cardFooterButtonCancelText,
                  ]}
                >
                  查看详情
                </Text>
              </View>
            </ATrack>
          ) : null}
          {item.plan_status === 1 ? (
            <ATrack onPress={() => goVisit(item)}>
              <View style={styles.cardFooterButton}>
                <Text style={styles.cardFooterButtonText}>去回访</Text>
              </View>
            </ATrack>
          ) : null}
        </View>
      </ATrack>
    );

    // 渲染列表底部
    const renderFooter = () => (
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          {loading ? '加载中...' : hasMore ? '上拉加载更多' : '没有更多啦'}
        </Text>
      </View>
    );

    const renderEmpty = () => (
      <View style={styles.emptyContainer}>
        <Empty />
      </View>
    );

    // 组件挂载时初始化
    useEffect(() => {
      setComponentShow(true);
      setInitialLoading(true);

      // 如果有当前登录用户信息，设置为默认选中的回访人，并使用该用户ID来获取数据
      if (currentLoginUserInfo) {
        setVisitorItem(currentLoginUserInfo);
        // 使用当前登录用户ID来获取数据
        getData(true, status, currentLoginUserInfo.id, formatDateResp).finally(
          () => {
            setInitialLoading(false);
          }
        );
      } else {
        getData().finally(() => {
          setInitialLoading(false);
        });
      }

      return () => {
        setComponentShow(false);
      };
    }, [customerId, userInfo, currentLoginUserInfo]);

    // 初始加载状态显示骨架屏
    if (initialLoading) {
      return <ReturnVisitSkeleton />;
    }

    return (
      <View style={styles.container}>
        {renderFilter()}

        {/* CommonPopup 组件 */}
        <CommonPopup
          ref={statusPopupRef}
          active={currentStatusActive}
          title='选择状态'
          multiple={false}
          options={statusOptions}
          onClose={handlePopupClose}
          onChange={handleStatusSelect}
        />

        <EmployeePopup
          ref={employeePopupRef}
          active={{
            id: isNaN(visitorItem.id) ? NaN : visitorItem.id,
            name: visitorItem.name,
          }}
          title='选择回访人'
          role=''
          multiple={false}
          onActiveChange={handleVisitorSelect}
          onClose={handlePopupClose}
        />

        {/* 日期选择Modal */}
        <Modal
          isVisible={dateModalVisible}
          onBackdropPress={handleDateCancel}
          onBackButtonPress={handleDateCancel}
          style={styles.dateModal}
        >
          <View style={styles.dateModalContent}>
            <View style={styles.dateModalHeader}>
              <View style={styles.placholder} />
              <Text style={styles.dateModalTitle}>选择时间范围</Text>
              <ATrack onPress={handleDateCancel}>
                <Image
                  style={styles.closeIcon}
                  source={{
                    uri: 'https://static.soyoung.com/sy-design/bzsokyai5osd1753688976847.png',
                  }}
                />
              </ATrack>
            </View>

            <View style={styles.dateInputContainer}>
              <DateInput
                value={tempDateRange || ['', '']}
                placeholder={['开始日期', '结束日期']}
                isRange={true}
                rangeSeparator='至'
                type='year-month-day'
                onChange={handleDateInputChange}
              />
            </View>

            <View style={styles.dateModalFooter}>
              <ATrack
                style={styles.dateModalCancelBtn}
                onPress={handleDateReset}
              >
                <Text style={styles.dateModalCancelText}>重置</Text>
              </ATrack>
              <ATrack
                style={[
                  styles.dateModalConfirmBtn,
                  isDateConfirmDisabled && styles.dateModalConfirmBtnDisabled,
                ]}
                onPress={isDateConfirmDisabled ? undefined : handleDateConfirm}
              >
                <Text
                  style={[
                    styles.dateModalConfirmText,
                    isDateConfirmDisabled &&
                      styles.dateModalConfirmTextDisabled,
                  ]}
                >
                  确认
                </Text>
              </ATrack>
            </View>
          </View>
        </Modal>

        {dataList ? (
          dataList.length > 0 ? (
            <FlatList
              data={dataList}
              renderItem={renderItem}
              keyExtractor={item => item.plan_id}
              onEndReached={loadMore}
              ListFooterComponent={renderFooter}
              showsVerticalScrollIndicator={false}
              {...flatListProps}
            />
          ) : (
            renderEmpty()
          )
        ) : (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>加载中...</Text>
          </View>
        )}
      </View>
    );
  }
);

// 样式定义
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f6f9f9',
  },

  // 过滤器样式
  filter: {
    flexDirection: 'row',
    paddingHorizontal: getRealSize(10),
    paddingVertical: getRealSize(10),
    justifyContent: 'space-between',
  },

  filterItem: {
    height: getRealSize(28),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: getRealSize(10),
    flex: 1,
    backgroundColor: '#F5F5F5',
    marginHorizontal: getRealSize(5),
  },

  filterItemActive: {
    backgroundColor: '#EBFBDC',
  },

  filterItemGreen: {
    backgroundColor: '#EBFBDC',
  },

  filterItemText: {
    maxWidth: getRealSize(80),
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#333333',
    fontWeight: '400',
  },

  filterItemTextActive: {
    color: '#61B43E',
  },

  filterItemTextGreen: {
    color: '#61B43E',
  },

  filterArrow: {
    width: getRealSize(9),
    height: getRealSize(6),
    marginLeft: getRealSize(4),
  },

  card: {
    backgroundColor: '#ffffff',
    marginBottom: getRealSize(10),
    padding: getRealSize(15),
  },

  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: getRealSize(10),
  },

  statusTag: {
    paddingHorizontal: getRealSize(6),
    height: getRealSize(20),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: getRealSize(2),
    backgroundColor: '#EBFBDC',
    marginRight: getRealSize(6),
  },

  statusTagGray: {
    backgroundColor: '#f2f2f2',
  },

  statusTagRed: {
    backgroundColor: '#FFEFEA',
  },

  statusTagText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#61B43E',
    fontWeight: '400',
  },

  statusTagTextGray: {
    color: '#8c8c8c',
  },

  statusTagTextRed: {
    color: '#FE6631',
  },

  timeText: {
    fontFamily: 'PingFangSC-Semibold',
    fontSize: getRealSize(12),
    color: '#333333',
    fontWeight: '600',
    marginRight: getRealSize(5),
  },

  timeDesc: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#777777',
    fontWeight: '400',
  },
  cardInfo: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: getRealSize(8),
  },
  cardValue: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#777777',
    fontWeight: '400',
  },
  cardInfoSeparator: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#f0f0f0',
    fontWeight: '400',
    marginHorizontal: getRealSize(5),
  },
  cardContent: {
    backgroundColor: '#F8F8F8',
    padding: getRealSize(10),
    marginBottom: getRealSize(8),
  },
  cardContentText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#333333',
    fontWeight: '400',
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  cardFooterButton: {
    height: getRealSize(32),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#333333',
    paddingHorizontal: getRealSize(10),
    borderWidth: 1,
    borderColor: '#333333',
    marginLeft: getRealSize(10),
  },
  cardFooterButtonText: {
    fontFamily: 'PingFangSC-Semibold',
    fontSize: getRealSize(12),
    color: '#FFFFFF',
    fontWeight: '600',
  },
  cardFooterButtonCancel: {
    backgroundColor: '#FFFFFF',
    borderColor: '#333333',
  },
  cardFooterButtonCancelText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#333333',
    fontWeight: '400',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // 通用样式
  footer: {
    paddingVertical: getRealSize(15),
    alignItems: 'center',
  },

  footerText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#777777',
  },

  // 添加缺失的样式
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: getRealSize(50),
  },

  loadingText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(14),
    color: '#999999',
  },

  // 日期Modal样式
  dateModal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  dateModalContent: {
    width: '100%',
    height: getRealSize(247),
    backgroundColor: '#ffffff',
    paddingHorizontal: getRealSize(15),
    paddingBottom: getRealSize(20),
  },

  dateModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: getRealSize(15),
  },

  dateModalTitle: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(16), // 32rpx / 2
    color: '#333333',
    fontWeight: '500',
  },

  placholder: {
    width: getRealSize(16),
  },
  closeIcon: {
    width: getRealSize(16),
    height: getRealSize(16),
  },
  dateInputContainer: {
    paddingVertical: getRealSize(20),
  },

  dateModalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: getRealSize(20),
  },

  dateModalCancelBtn: {
    flex: 1,
    height: getRealSize(44),
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: getRealSize(10),
  },

  dateModalCancelText: {
    fontSize: getRealSize(16),
    color: '#666666',
    fontWeight: '500',
  },

  dateModalConfirmBtn: {
    flex: 1,
    height: getRealSize(44),
    backgroundColor: '#333333',
    justifyContent: 'center',
    alignItems: 'center',
  },

  dateModalConfirmText: {
    fontSize: getRealSize(16),
    color: '#ffffff',
    fontWeight: '500',
  },

  dateModalConfirmBtnDisabled: {
    backgroundColor: '#cccccc',
  },

  dateModalConfirmTextDisabled: {
    color: '#ffffff',
  },
});

export default ReturnVisit;
