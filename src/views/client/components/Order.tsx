import React, {
  useState,
  useEffect,
  useCallback,
  forwardRef,
  useImperativeHandle,
  useRef,
} from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';
import { flatListProps } from '@/constant/flatlist_props';
import LinearGradient from 'react-native-linear-gradient';
import Empty from '@/components/Empty';
import { OrderSkeleton } from './ClientDetailSkeleton';
import { ATrack } from '@soyoung/react-native-container';
import { getRealSize } from '@/common/utils';
import api, { FetchModule } from '@/common/api';

// 类型定义
interface ProjectItem {
  order_number: string;
  item_name: string;
  left_times: number;
  spec: string;
  total_transaction_price: string;
  tenant_name: string;
}

interface OrderItem {
  order_id: string;
  order_id_str: string;
  commodity_name: string;
  show_num: number;
  spec: string;
  cash_deposit: string;
  time_and_hospital?: Array<{
    start_time: string;
    hospital_name: string;
  }>;
}

interface OrderProps {
  customerId: string;
  tabActive?: number;
}

export interface OrderRef {
  selectTab: (index: number) => void;
}

// 列表状态接口
interface ListState {
  data: any[];
  loading: boolean;
  hasMore: boolean;
  page: number;
  initialized: boolean;
}

const Order = forwardRef<OrderRef, OrderProps>(
  ({ customerId, tabActive = 0 }, ref) => {
    // 当前选中的tab
    const [currentTab, setCurrentTab] = useState<number>(tabActive);
    const [componentShow, setComponentShow] = useState<boolean>(true);
    const [initialLoading, setInitialLoading] = useState<boolean>(true);
    // 添加tab切换时的loading状态
    const [tabSwitchLoading, setTabSwitchLoading] = useState<boolean>(false);

    // 院内项目列表状态
    const [projectState, setProjectState] = useState<ListState>({
      data: [],
      loading: false,
      hasMore: true,
      page: 1,
      initialized: false,
    });

    // 优享订单列表状态
    const [orderState, setOrderState] = useState<ListState>({
      data: [],
      loading: false,
      hasMore: true,
      page: 1,
      initialized: false,
    });

    // 使用ref来跟踪loading状态，避免useCallback依赖
    const isProjectLoading = useRef(false);
    const isOrderLoading = useRef(false);

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
      selectTab: (index: number) => {
        selectTab(index);
      },
    }));

    // 加载院内项目数据
    const loadProjectData = useCallback(
      async (pageNum: number, reset: boolean = false) => {
        // 使用ref检查loading状态
        if (isProjectLoading.current) return;

        isProjectLoading.current = true;
        setProjectState(prev => ({ ...prev, loading: true }));

        try {
          const response = await api.pagefetch({
            path: '/chain-wxapp/v1/customer/orderItemList',
            params: {
              customer_id: customerId,
              page: pageNum,
              page_size: 10,
            },
            method: FetchModule.Method.POST,
            isLoading: false,
          });

          if (response.errorCode === 0 && response.responseData) {
            const newList = response.responseData.list || [];

            setProjectState(prev => ({
              ...prev,
              data: reset ? newList : [...prev.data, ...newList],
              hasMore:
                response.responseData.total >
                (reset ? newList.length : prev.data.length + newList.length),
              page: pageNum + 1,
              loading: false,
              initialized: true,
            }));
          } else {
            setProjectState(prev => ({ ...prev, loading: false }));
          }
        } catch (error) {
          console.error('获取院内项目数据失败:', error);
          setProjectState(prev => ({ ...prev, loading: false }));
        } finally {
          isProjectLoading.current = false;
        }
      },
      [customerId] // 只依赖customerId
    );

    // 加载优享订单数据
    const loadOrderData = useCallback(
      async (pageNum: number, reset: boolean = false) => {
        // 使用ref检查loading状态
        if (isOrderLoading.current) return;

        isOrderLoading.current = true;
        setOrderState(prev => ({ ...prev, loading: true }));

        try {
          const response = await api.pagefetch({
            path: '/chain-wxapp/v1/customer/getQzyOrderList',
            params: {
              customer_id: customerId,
              page: pageNum,
              page_size: 10,
            },
            isLoading: false,
            method: FetchModule.Method.POST,
          });

          if (response.errorCode === 0 && response.responseData) {
            const newList = response.responseData.list || [];

            setOrderState(prev => ({
              ...prev,
              data: reset ? newList : [...prev.data, ...newList],
              hasMore:
                response.responseData.total >
                (reset ? newList.length : prev.data.length + newList.length),
              page: pageNum + 1,
              loading: false,
              initialized: true,
            }));
          } else {
            setOrderState(prev => ({ ...prev, loading: false }));
          }
        } catch (error) {
          console.error('获取优享订单数据失败:', error);
          setOrderState(prev => ({ ...prev, loading: false }));
        } finally {
          isOrderLoading.current = false;
        }
      },
      [customerId] // 只依赖customerId
    );

    // 选择标签页
    const selectTab = useCallback(
      async (i: number) => {
        if (i === currentTab) return;

        // 如果需要加载新数据，先设置切换loading状态
        const needLoadData =
          (i === 0 && !projectState.initialized) ||
          (i === 1 && !orderState.initialized);

        if (needLoadData) {
          setTabSwitchLoading(true);
        }

        setCurrentTab(i);

        try {
          // 如果对应的列表还没有初始化，则加载数据
          if (i === 0 && !projectState.initialized) {
            await loadProjectData(1, true);
          } else if (i === 1 && !orderState.initialized) {
            await loadOrderData(1, true);
          }
        } finally {
          setTabSwitchLoading(false);
        }
      },
      [
        currentTab,
        projectState.initialized,
        orderState.initialized,
        loadProjectData,
        loadOrderData,
      ]
    );

    // 院内项目加载更多
    const loadMoreProject = useCallback(() => {
      // 增加更严格的判断条件，确保真正需要加载更多数据时才请求
      if (
        projectState.hasMore &&
        !isProjectLoading.current &&
        componentShow &&
        projectState.data.length > 0 && // 确保已有数据
        projectState.initialized // 确保已初始化
      ) {
        loadProjectData(projectState.page);
      }
    }, [
      projectState.hasMore,
      projectState.page,
      componentShow,
      projectState.data.length,
      projectState.initialized,
      loadProjectData,
    ]);

    // 优享订单加载更多
    const loadMoreOrder = useCallback(() => {
      // 增加更严格的判断条件，确保真正需要加载更多数据时才请求
      if (
        orderState.hasMore &&
        !isOrderLoading.current &&
        componentShow &&
        orderState.data.length > 0 && // 确保已有数据
        orderState.initialized // 确保已初始化
      ) {
        loadOrderData(orderState.page);
      }
    }, [
      orderState.hasMore,
      orderState.page,
      componentShow,
      orderState.data.length,
      orderState.initialized,
      loadOrderData,
    ]);

    // 渲染院内项目卡片
    const renderProjectItem = useCallback(
      ({ item }: { item: ProjectItem }) => (
        <View style={styles.card}>
          <View style={styles.cardLine}>
            <Text style={styles.itemName} numberOfLines={1}>
              {item.item_name}
            </Text>
            <View style={styles.frequency}>
              <Text style={styles.frequencyLabel}>剩余数量</Text>
              <Text style={styles.frequencyNumber}>{item.left_times}</Text>
            </View>
          </View>
          <View style={styles.cardLine}>
            <Text />
            <Text style={styles.orderNumber}>{item.order_number}</Text>
          </View>
          <View style={styles.cardLine}>
            <Text style={styles.price}>￥{item.total_transaction_price}</Text>
            <Text style={styles.tenantName} numberOfLines={1}>
              {item.tenant_name}
            </Text>
          </View>
        </View>
      ),
      []
    );

    // 渲染优享订单卡片
    const renderOrderItem = useCallback(
      ({ item }: { item: OrderItem }) => (
        <View style={styles.card}>
          <View style={styles.cardLine}>
            <Text style={styles.orderItemName} numberOfLines={1}>
              {item.commodity_name}
            </Text>
            <View style={styles.frequency}>
              <Text style={styles.frequencyLabel}>次数</Text>
              <Text style={styles.frequencyNumber}>{item.show_num}</Text>
            </View>
          </View>

          <View style={styles.cardLine}>
            <Text style={styles.spec}>{item.spec}</Text>
          </View>

          <View style={styles.cardLine}>
            <Text style={styles.price}>¥{item.cash_deposit}</Text>
            <Text style={styles.orderId}>{item.order_id_str}</Text>
          </View>

          {/* 预约信息 */}
          {item.time_and_hospital && item.time_and_hospital.length > 0 && (
            <View style={styles.reserveList}>
              {item.time_and_hospital.map((reserveItem, index) => (
                <View key={index} style={styles.reserveItem}>
                  <View style={styles.reserveLeft}>
                    <View style={styles.reserveTag}>
                      <Text style={styles.reserveTagText}>预约</Text>
                    </View>
                    <Text style={styles.reserveTime}>
                      {reserveItem.start_time}
                    </Text>
                  </View>
                  <Text style={styles.hospitalName} numberOfLines={1}>
                    {reserveItem.hospital_name}
                  </Text>
                </View>
              ))}
            </View>
          )}
        </View>
      ),
      []
    );

    // 渲染院内项目列表底部
    const renderProjectFooter = useCallback(() => {
      // 只有当数据不为空时才显示footer
      if (projectState.data.length === 0) return null;

      return (
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            {projectState.loading
              ? '加载中...'
              : projectState.hasMore
                ? '上拉加载更多'
                : '没有更多啦'}
          </Text>
        </View>
      );
    }, [projectState.loading, projectState.hasMore, projectState.data.length]);

    // 渲染优享订单列表底部
    const renderOrderFooter = useCallback(() => {
      // 只有当数据不为空时才显示footer
      if (orderState.data.length === 0) return null;

      return (
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            {orderState.loading
              ? '加载中...'
              : orderState.hasMore
                ? '上拉加载更多'
                : '没有更多啦'}
          </Text>
        </View>
      );
    }, [orderState.loading, orderState.hasMore, orderState.data.length]);

    // 渲染空状态
    const renderEmpty = useCallback(
      () => (
        <View style={styles.empty}>
          <Empty />
        </View>
      ),
      []
    );

    // 渲染列表内容
    const renderListContent = useCallback(() => {
      // 如果正在切换tab且需要加载数据，显示骨架屏
      if (tabSwitchLoading) {
        return <OrderSkeleton />;
      }

      // 正常渲染列表
      return (
        <View style={styles.listContainer}>
          {currentTab === 0 ? (
            <FlatList
              data={projectState.data}
              renderItem={renderProjectItem}
              onEndReached={loadMoreProject}
              ListFooterComponent={renderProjectFooter}
              ListEmptyComponent={renderEmpty}
              showsVerticalScrollIndicator={false}
              {...flatListProps}
            />
          ) : (
            <FlatList
              data={orderState.data}
              renderItem={renderOrderItem}
              onEndReached={loadMoreOrder}
              ListFooterComponent={renderOrderFooter}
              ListEmptyComponent={renderEmpty}
              showsVerticalScrollIndicator={false}
              {...flatListProps}
            />
          )}
        </View>
      );
    }, [
      tabSwitchLoading,
      currentTab,
      projectState.data,
      orderState.data,
      renderProjectItem,
      renderOrderItem,
      loadMoreProject,
      loadMoreOrder,
      renderProjectFooter,
      renderOrderFooter,
      renderEmpty,
    ]);

    // 添加一个简单的标记来防止重复初始化
    const hasInitialized = useRef<string>('');

    // 组件挂载时初始化
    useEffect(() => {
      const initKey = `${customerId}-${tabActive}`;

      // 如果已经用相同参数初始化过，则跳过
      if (hasInitialized.current === initKey) {
        return;
      }

      setCurrentTab(tabActive);
      setComponentShow(true);
      setInitialLoading(true);

      // 根据初始tab加载对应数据
      const loadInitialData = async () => {
        try {
          if (tabActive === 0) {
            await loadProjectData(1, true);
          } else {
            await loadOrderData(1, true);
          }
        } finally {
          setInitialLoading(false);
          hasInitialized.current = initKey;
        }
      };

      loadInitialData();

      return () => {
        setComponentShow(false);
      };
    }, [customerId, tabActive]);

    // 初始加载状态显示骨架屏
    if (initialLoading) {
      return <OrderSkeleton />;
    }

    return (
      <View style={styles.container}>
        {/* 标签页导航 */}
        <LinearGradient
          colors={['#fff', '#f8f8f8']}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          locations={[0.75, 1]}
        >
          <View style={styles.tabBox}>
            <View style={styles.tabLeft}>
              <ATrack
                style={[
                  styles.tabItem,
                  currentTab === 0 && styles.tabItemActive,
                ]}
                onPress={() => selectTab(0)}
              >
                <Text
                  style={[
                    styles.tabItemText,
                    currentTab === 0 && styles.tabItemTextActive,
                  ]}
                >
                  院内项目
                </Text>
              </ATrack>

              <ATrack
                style={[
                  styles.tabItem,
                  currentTab === 1 && styles.tabItemActive,
                ]}
                onPress={() => selectTab(1)}
              >
                <Text
                  style={[
                    styles.tabItemText,
                    currentTab === 1 && styles.tabItemTextActive,
                  ]}
                >
                  优享订单
                </Text>
              </ATrack>
            </View>
            <View />
          </View>
        </LinearGradient>

        {/* 数据列表 */}
        {renderListContent()}
      </View>
    );
  }
);

// 样式定义
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },

  tabBox: {
    paddingHorizontal: getRealSize(15),
    paddingVertical: getRealSize(10),
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  tabLeft: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#F0F0F0',
    paddingHorizontal: getRealSize(5),
    paddingVertical: getRealSize(4),
  },
  tabItem: {
    height: getRealSize(28),
    justifyContent: 'center',
    paddingHorizontal: getRealSize(15),
  },

  tabItemActive: {
    backgroundColor: '#EBFBDC',
  },

  tabItemText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#333333',
    fontWeight: '400',
  },

  tabItemTextActive: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#61B43E',
    fontWeight: '400',
  },
  listContainer: {
    flex: 1,
  },

  card: {
    backgroundColor: '#ffffff',
    marginHorizontal: getRealSize(15),
    marginBottom: getRealSize(10),
    padding: getRealSize(15),
  },
  cardLine: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  itemName: {
    fontFamily: 'PingFangSC-Semibold',
    fontSize: getRealSize(13),
    color: '#161616',
    fontWeight: '600',
    marginRight: getRealSize(10),
    maxWidth: getRealSize(240),
  },
  orderItemName: {
    fontFamily: 'PingFangSC-Semibold',
    fontSize: getRealSize(13),
    color: '#161616',
    fontWeight: '600',
    marginRight: getRealSize(10),
    maxWidth: getRealSize(270),
  },
  frequency: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  frequencyLabel: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(11),
    color: '#333333',
    fontWeight: '400',
  },

  frequencyNumber: {
    marginLeft: getRealSize(4),
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(13),
    color: '#61B43E',
    fontWeight: '600',
  },

  spec: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#666666',
    flex: 1,
  },

  orderNumber: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(11),
    color: '#777777',
    fontWeight: '400',
  },

  price: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(12),
    color: '#555555',
    fontWeight: '500',
  },

  tenantName: {
    maxWidth: getRealSize(240),
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(11),
    color: '#777777',
    fontWeight: '400',
  },

  orderId: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#999999',
  },

  reserveList: {
    marginTop: getRealSize(10),
    paddingTop: getRealSize(10),
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },

  reserveItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  reserveLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  reserveTag: {
    backgroundColor: '#61b43e',
    paddingHorizontal: getRealSize(4),
    paddingVertical: getRealSize(2),
    marginRight: getRealSize(6),
  },

  reserveTagText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(10),
    color: '#ffffff',
  },

  reserveTime: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13),
    color: '#333333',
  },

  hospitalName: {
    maxWidth: getRealSize(160),
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#666666',
  },

  footer: {
    paddingVertical: getRealSize(30),
    alignItems: 'center',
  },

  footerText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#999999',
  },

  empty: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: getRealSize(100),
  },

  // 骨架屏样式
  skeletonTabItem: {
    height: getRealSize(28),
    width: getRealSize(80),
    borderRadius: getRealSize(4),
    marginRight: getRealSize(10),
  },

  skeletonItemName: {
    width: getRealSize(140),
    height: getRealSize(16),
  },

  skeletonFrequencyLabel: {
    width: getRealSize(50),
    height: getRealSize(12),
  },

  skeletonFrequencyNumber: {
    width: getRealSize(20),
    height: getRealSize(14),
    marginLeft: getRealSize(4),
  },

  skeletonSpec: {
    width: getRealSize(120),
    height: getRealSize(14),
  },

  skeletonOrderNumber: {
    width: getRealSize(100),
    height: getRealSize(12),
  },

  skeletonPrice: {
    width: getRealSize(80),
    height: getRealSize(14),
  },

  skeletonTenantName: {
    width: getRealSize(90),
    height: getRealSize(12),
  },

  skeletonReserveTag: {
    width: getRealSize(30),
    height: getRealSize(16),
    borderRadius: getRealSize(4),
    marginRight: getRealSize(6),
  },

  skeletonReserveTime: {
    width: getRealSize(120),
    height: getRealSize(14),
  },

  skeletonHospitalName: {
    width: getRealSize(80),
    height: getRealSize(12),
  },
});

export default Order;
