import React, { useState, useEffect, useCallback, memo, useMemo } from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { ATrack } from '@soyoung/react-native-container';
import DatePicker from '@/components/DatePicker';
import { getRealSize } from '@/common/utils';

// https://static.soyoung.com/sy-pre/2x9igo23omk4j-1711613400704.png
interface DateInputProps {
  value?: string | [string, string]; // 单个日期或日期范围
  placeholder?: string[];
  suffixIcon?: string; // 后缀图标
  clearable?: boolean; // 是否显示清除按钮，默认为 true
  onChange: (value: string | [string, string]) => void;
  isRange?: boolean; // 是否为日期范围选择
  rangeSeparator?: string; // 范围分隔符，默认为 '至'
  disabled?: (date: Date) => boolean; // 禁用函数，传入完整日期对象
  convertToTimestamp?: boolean; // 是否转换为MM-DD,MM-DD格式输出
}

// 静态数据提取到组件外部
const DEFAULT_RANGE_SEPARATOR = '至';
const DEFAULT_PLACEHOLDERS = {
  startDate: '开始日期',
  endDate: '结束日期',
  singleDate: '选择日期',
  startPicker: '选择开始日期',
  endPicker: '选择结束日期',
} as const;

// 清除图标 URL
const CLEAR_ICON_URL =
  'https://static.soyoung.com/sy-pre/219l297us0uts-1711617000689.png';

const BirthDayDateInput: React.FC<DateInputProps> = memo(
  ({
    value,
    placeholder = [],
    onChange,
    isRange = false,
    suffixIcon = 'https://static.soyoung.com/sy-pre/2x9igo23omk4j-1711613400704.png', // 后缀图标
    clearable = true, // 默认为 true
    rangeSeparator = DEFAULT_RANGE_SEPARATOR,
    disabled,
    convertToTimestamp = false,
  }) => {
    // 日期格式转换辅助函数
    const convertDateArrayToTimestamp = useCallback(
      (dateArray: [string, string]): string => {
        if (!Array.isArray(dateArray) || dateArray.length !== 2) {
          return '';
        }

        const [startDate, endDate] = dateArray;
        // 如果两个日期都为空，返回空字符串
        if (!startDate && !endDate) {
          return '';
        }

        // 如果只有一个日期，也返回空字符串，但这种情况下不应该调用onChange
        if (!startDate || !endDate) {
          return '';
        }

        // 格式化为MM-DD格式
        const formatToMonthDay = (dateStr: string): string => {
          // 直接解析MM-DD或MM/DD格式，不依赖Date对象
          const parts = dateStr.split(/[-/]/);

          if (parts.length >= 2) {
            // 取最后两个部分作为月和日
            const month = parseInt(parts[parts.length - 2], 10);
            const day = parseInt(parts[parts.length - 1], 10);

            if (
              !isNaN(month) &&
              !isNaN(day) &&
              month >= 1 &&
              month <= 12 &&
              day >= 1 &&
              day <= 31
            ) {
              return `${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
            }
          }

          // 如果无法解析，返回原始字符串
          return dateStr;
        };

        const formattedStart = formatToMonthDay(startDate);
        const formattedEnd = formatToMonthDay(endDate);

        return `${formattedStart},${formattedEnd}`;
      },
      []
    );

    // MM-DD格式字符串转换为日期数组辅助函数
    const convertTimestampToDateArray = useCallback(
      (timestampStr: string): [string, string] => {
        if (!timestampStr || typeof timestampStr !== 'string') {
          return ['', ''];
        }

        const dateParts = timestampStr.split(',');
        if (dateParts.length !== 2) {
          return ['', ''];
        }

        try {
          // 解析MM-DD格式
          const parseMonthDay = (dateStr: string): string => {
            const trimmed = dateStr.trim();
            const parts = trimmed.split('-');
            if (parts.length === 2) {
              const month = parseInt(parts[0], 10);
              const day = parseInt(parts[1], 10);
              if (
                !isNaN(month) &&
                !isNaN(day) &&
                month >= 1 &&
                month <= 12 &&
                day >= 1 &&
                day <= 31
              ) {
                return `${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
              }
            }
            return trimmed;
          };

          const startDate = parseMonthDay(dateParts[0]);
          const endDate = parseMonthDay(dateParts[1]);

          return [startDate, endDate];
        } catch {
          return ['', ''];
        }
      },
      []
    );

    // 初始化状态
    const initializeDates = useCallback(() => {
      let startDate = '';
      let endDate = '';

      if (isRange && Array.isArray(value)) {
        startDate = value[0] || '';
        endDate = value[1] || '';
      } else if (!isRange && typeof value === 'string') {
        // 如果是时间戳模式且是范围选择，尝试解析时间戳字符串
        if (convertToTimestamp && value && value.includes(',')) {
          const dateArray = convertTimestampToDateArray(value);
          startDate = dateArray[0];
          endDate = dateArray[1];
        } else {
          startDate = value || '';
        }
      } else if (
        isRange &&
        typeof value === 'string' &&
        convertToTimestamp &&
        value
      ) {
        // 处理范围选择时传入时间戳字符串的情况
        const dateArray = convertTimestampToDateArray(value);
        startDate = dateArray[0];
        endDate = dateArray[1];
      }

      return { startDate, endDate };
    }, [isRange, value, convertToTimestamp, convertTimestampToDateArray]);

    const initialDates = useMemo(() => initializeDates(), [initializeDates]);
    const [showPicker, setShowPicker] = useState(false);
    const [currentPickerType, setCurrentPickerType] = useState<'start' | 'end'>(
      'start'
    );
    const [startDate, setStartDate] = useState(initialDates.startDate);
    const [endDate, setEndDate] = useState(initialDates.endDate);

    // 监听外部value变化
    useEffect(() => {
      const dates = initializeDates();
      setStartDate(dates.startDate);
      setEndDate(dates.endDate);
    }, [initializeDates]);

    // 格式化日期显示（固定为month-day格式）
    const formatDate = useCallback((dateString: string): string => {
      if (!dateString) return '';

      // 针对月-日格式进行专门处理
      const parts = dateString.split(/[-/]/);
      if (parts.length === 2) {
        const month = parseInt(parts[0], 10);
        const day = parseInt(parts[1], 10);
        if (
          !isNaN(month) &&
          !isNaN(day) &&
          month >= 1 &&
          month <= 12 &&
          day >= 1 &&
          day <= 31
        ) {
          return `${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}`;
        }
      }
      // 如果格式不正确，尝试用Date解析
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return dateString;
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${month}/${day}`;
    }, []);

    // 打开日期选择器
    const openPicker = useCallback((pickerType: 'start' | 'end' = 'start') => {
      setCurrentPickerType(pickerType);
      setShowPicker(true);
    }, []);

    // 关闭日期选择器
    const closePicker = useCallback(() => {
      setShowPicker(false);
    }, []);

    // 确认日期选择
    const handleDateConfirm = useCallback(
      (selectedDate: string) => {
        if (isRange) {
          if (currentPickerType === 'start') {
            setStartDate(selectedDate);
            // 使用函数式更新确保获取最新的endDate
            setTimeout(() => {
              // 如果两个日期都有值，检查是否需要自动对换
              if (selectedDate && endDate) {
                // 将MM-DD格式转换为可比较的数字格式
                const parseMonthDay = (dateStr: string) => {
                  const parts = dateStr.split(/[-/]/);
                  if (parts.length >= 2) {
                    const month = parseInt(parts[parts.length - 2], 10);
                    const day = parseInt(parts[parts.length - 1], 10);
                    return month * 100 + day; // 例如：08-07 -> 807
                  }
                  return 0;
                };

                const startValue = parseMonthDay(selectedDate);
                const endValue = parseMonthDay(endDate);

                if (startValue > endValue) {
                  // 自动对换开始时间和结束时间

                  const finalResult: [string, string] = [endDate, selectedDate];
                  const outputValue = convertToTimestamp
                    ? convertDateArrayToTimestamp(finalResult)
                    : finalResult;
                  onChange(outputValue);
                  return;
                }

                // 只有当两个日期都有值时才调用onChange
                const finalResult: [string, string] = [selectedDate, endDate];
                const outputValue = convertToTimestamp
                  ? convertDateArrayToTimestamp(finalResult)
                  : finalResult;
                onChange(outputValue);
              }
              // 如果只选择了开始日期，endDate为空，则不调用onChange，避免清空
            }, 0);
          } else {
            setEndDate(selectedDate);
            // 使用函数式更新确保获取最新的startDate
            setTimeout(() => {
              // 如果两个日期都有值，检查是否需要自动对换
              if (startDate && selectedDate) {
                // 将MM-DD格式转换为可比较的数字格式
                const parseMonthDay = (dateStr: string) => {
                  const parts = dateStr.split(/[-/]/);
                  if (parts.length >= 2) {
                    const month = parseInt(parts[parts.length - 2], 10);
                    const day = parseInt(parts[parts.length - 1], 10);
                    return month * 100 + day; // 例如：08-07 -> 807
                  }
                  return 0;
                };

                const startValue = parseMonthDay(startDate);
                const endValue = parseMonthDay(selectedDate);

                if (startValue > endValue) {
                  // 自动对换开始时间和结束时间

                  const finalResult: [string, string] = [
                    selectedDate,
                    startDate,
                  ];
                  const outputValue = convertToTimestamp
                    ? convertDateArrayToTimestamp(finalResult)
                    : finalResult;
                  onChange(outputValue);
                  return;
                }

                // 只有当两个日期都有值时才调用onChange
                const finalResult: [string, string] = [startDate, selectedDate];
                const outputValue = convertToTimestamp
                  ? convertDateArrayToTimestamp(finalResult)
                  : finalResult;
                onChange(outputValue);
              }
              // 如果只选择了结束日期，startDate为空，则不调用onChange，避免清空
            }, 0);
          }
        } else {
          setStartDate(selectedDate);
          onChange(selectedDate);
        }

        closePicker();
      },
      [
        isRange,
        currentPickerType,
        startDate,
        endDate,
        onChange,
        closePicker,
        convertToTimestamp,
        convertDateArrayToTimestamp,
      ]
    );

    // 获取当前选择器显示的日期
    const getCurrentPickerValue = useCallback((): string => {
      return currentPickerType === 'start' ? startDate : endDate;
    }, [currentPickerType, startDate, endDate]);

    // 获取当前选择器标题
    const getCurrentPickerTitle = useCallback((): string => {
      if (currentPickerType === 'start') {
        return placeholder[0] || DEFAULT_PLACEHOLDERS.startPicker;
      } else {
        return placeholder[1] || DEFAULT_PLACEHOLDERS.endPicker;
      }
    }, [currentPickerType, placeholder]);

    // 缓存开始日期显示文本
    const startDateDisplayText = useMemo(() => {
      return startDate
        ? formatDate(startDate)
        : placeholder[0] || DEFAULT_PLACEHOLDERS.startDate;
    }, [startDate, formatDate, placeholder]);

    // 缓存结束日期显示文本
    const endDateDisplayText = useMemo(() => {
      return endDate
        ? formatDate(endDate)
        : placeholder[1] || DEFAULT_PLACEHOLDERS.endDate;
    }, [endDate, formatDate, placeholder]);

    // 缓存单日期显示文本
    const singleDateDisplayText = useMemo(() => {
      return startDate
        ? formatDate(startDate)
        : placeholder[0] || DEFAULT_PLACEHOLDERS.singleDate;
    }, [startDate, formatDate, placeholder]);

    // 清除日期函数
    const clearDate = useCallback(
      (dateType?: 'start' | 'end') => {
        if (isRange) {
          if (dateType === 'start') {
            setStartDate('');
            const result: [string, string] = ['', endDate];
            // 如果需要时间戳格式，保持一致性
            if (convertToTimestamp) {
              // 如果结束日期也为空，返回空字符串；否则转换为时间戳
              const outputValue = endDate
                ? convertDateArrayToTimestamp(result)
                : '';
              onChange(outputValue);
            } else {
              onChange(result);
            }
          } else if (dateType === 'end') {
            setEndDate('');
            const result: [string, string] = [startDate, ''];
            // 如果需要时间戳格式，保持一致性
            if (convertToTimestamp) {
              // 如果开始日期也为空，返回空字符串；否则转换为时间戳
              const outputValue = startDate
                ? convertDateArrayToTimestamp(result)
                : '';
              onChange(outputValue);
            } else {
              onChange(result);
            }
          } else {
            // 清除所有日期
            setStartDate('');
            setEndDate('');
            const result: [string, string] = ['', ''];
            const outputValue = convertToTimestamp ? '' : result;
            onChange(outputValue);
          }
        } else {
          setStartDate('');
          onChange('');
        }
      },
      [
        isRange,
        startDate,
        endDate,
        onChange,
        convertToTimestamp,
        convertDateArrayToTimestamp,
      ]
    );

    // 判断是否显示清除按钮
    const shouldShowClear = useCallback(
      (dateType?: 'start' | 'end') => {
        if (!clearable) return false;

        if (isRange) {
          if (dateType === 'start') {
            return !!startDate;
          } else if (dateType === 'end') {
            return !!endDate;
          }
          return false;
        } else {
          return !!startDate;
        }
      },
      [clearable, isRange, startDate, endDate]
    );

    return (
      <View style={styles.container}>
        {isRange ? (
          <View style={styles.rangeContainer}>
            {/* 开始日期 */}
            <View style={styles.dateButtonWrapper}>
              <ATrack
                style={styles.dateButton}
                onPress={() => openPicker('start')}
              >
                <Text
                  style={[
                    styles.dateText,
                    !startDate && styles.placeholderText,
                  ]}
                >
                  {startDateDisplayText}
                </Text>
                <View style={styles.iconContainer}>
                  {shouldShowClear('start') ? (
                    <ATrack
                      style={styles.clearButton}
                      onPress={() => clearDate('start')}
                    >
                      <Image
                        source={{ uri: CLEAR_ICON_URL }}
                        style={styles.clearIcon}
                      />
                    </ATrack>
                  ) : suffixIcon ? (
                    <Image
                      source={{ uri: suffixIcon }}
                      style={styles.suffixIcon}
                    />
                  ) : null}
                </View>
              </ATrack>
            </View>

            {/* 分隔符 */}
            <Text style={styles.separator}>{rangeSeparator}</Text>

            {/* 结束日期 */}
            <View style={styles.dateButtonWrapper}>
              <ATrack
                style={styles.dateButton}
                onPress={() => openPicker('end')}
              >
                <Text
                  style={[styles.dateText, !endDate && styles.placeholderText]}
                >
                  {endDateDisplayText}
                </Text>
                <View style={styles.iconContainer}>
                  {shouldShowClear('end') ? (
                    <ATrack
                      style={styles.clearButton}
                      onPress={() => clearDate('end')}
                    >
                      <Image
                        source={{ uri: CLEAR_ICON_URL }}
                        style={styles.clearIcon}
                      />
                    </ATrack>
                  ) : suffixIcon ? (
                    <Image
                      source={{ uri: suffixIcon }}
                      style={styles.suffixIcon}
                    />
                  ) : null}
                </View>
              </ATrack>
            </View>
          </View>
        ) : (
          <View style={styles.singleDateButtonWrapper}>
            <ATrack
              style={styles.singleDateButton}
              onPress={() => openPicker('start')}
            >
              <Text
                style={[styles.dateText, !startDate && styles.placeholderText]}
              >
                {singleDateDisplayText}
              </Text>
              <View style={styles.iconContainer}>
                {shouldShowClear() ? (
                  <ATrack
                    style={styles.clearButton}
                    onPress={() => clearDate()}
                  >
                    <Image
                      source={{ uri: CLEAR_ICON_URL }}
                      style={styles.clearIcon}
                    />
                  </ATrack>
                ) : suffixIcon ? (
                  <Image
                    source={{ uri: suffixIcon }}
                    style={styles.suffixIcon}
                  />
                ) : null}
              </View>
            </ATrack>
          </View>
        )}

        {/* 日期选择器 */}
        <DatePicker
          visible={showPicker}
          value={getCurrentPickerValue()}
          title={getCurrentPickerTitle()}
          type='month-day'
          disabled={disabled}
          onConfirm={handleDateConfirm}
          onCancel={closePicker}
        />
      </View>
    );
  }
);

BirthDayDateInput.displayName = 'BirthDayDateInput';

const styles = StyleSheet.create({
  container: {
    // 容器样式
  },
  rangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateButtonWrapper: {
    flex: 1,
    borderBottomWidth: 1,
    borderBottomColor: '#DEDEDE',
  },
  singleDateButtonWrapper: {
    borderBottomWidth: 1,
    borderBottomColor: '#DEDEDE',
  },
  dateButton: {
    height: getRealSize(40),
    paddingHorizontal: getRealSize(12),
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: getRealSize(15),
    height: getRealSize(15),
    justifyContent: 'center',
    alignItems: 'center',
  },
  clearButton: {
    width: getRealSize(15),
    height: getRealSize(15),
    justifyContent: 'center',
    alignItems: 'center',
  },
  clearIcon: {
    width: getRealSize(14),
    height: getRealSize(14),
  },
  suffixIcon: {
    width: getRealSize(15),
    height: getRealSize(15),
  },
  singleDateButton: {
    height: getRealSize(40),
    paddingHorizontal: getRealSize(12),
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
  },
  separator: {
    fontSize: getRealSize(14),
    color: '#666666',
    marginHorizontal: getRealSize(8),
  },
  dateText: {
    fontSize: getRealSize(14),
    lineHeight: getRealSize(40),
    color: '#333333',
    flex: 1,
  },
  placeholderText: {
    color: '#999999',
  },
});

export default BirthDayDateInput;
