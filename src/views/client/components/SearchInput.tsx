import React, { memo } from 'react';
import { View, TextInput, Image, StyleSheet, Platform } from 'react-native';
import { ATrack } from '@soyoung/react-native-container';
import { getRealSize } from '@/common/utils';

interface SearchInputProps {
  value: string;
  onChangeText: (text: string) => void;
  onSubmit: () => void;
  onClear: () => void;
  placeholder?: string;
}

// 静态数据提取到组件外部
const SEARCH_ICON_URI =
  'https://static.soyoung.com/sy-design/2o36ex5wcu3qi1752723968588.png';
const CLEAR_ICON_URI =
  'https://static.soyoung.com/sy-pre/20240423-164420-1713859800679.png';
const DEFAULT_PLACEHOLDER = '请输入姓名、手机号或尾号搜索';

const SearchInput: React.FC<SearchInputProps> = memo(
  ({
    value,
    onChangeText,
    onSubmit,
    onClear,
    placeholder = DEFAULT_PLACEHOLDER,
  }) => {
    const hasValue = Boolean(value);

    return (
      <View style={styles.container}>
        <View style={styles.inputContainer}>
          <Image source={{ uri: SEARCH_ICON_URI }} style={styles.searchIcon} />
          <TextInput
            style={styles.textInput}
            value={value}
            onChangeText={onChangeText}
            placeholder={placeholder}
            placeholderTextColor='#AAABB3'
            onSubmitEditing={onSubmit}
            returnKeyType='search'
          />
          <ATrack style={styles.clearButton} onPress={onClear}>
            {hasValue && (
              <Image
                source={{ uri: CLEAR_ICON_URI }}
                style={styles.clearIcon}
              />
            )}
          </ATrack>
        </View>
      </View>
    );
  }
);

SearchInput.displayName = 'SearchInput';

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: getRealSize(38),
    backgroundColor: '#f6f9f9',
    paddingHorizontal: getRealSize(11),
  },
  searchIcon: {
    width: getRealSize(16),
    height: getRealSize(16),
    marginRight: getRealSize(5),
  },
  textInput: {
    flex: 1,
    fontSize: Platform.OS === 'ios' ? getRealSize(15) : getRealSize(14),
    color: '#161616',
    padding: 0,
  },
  clearButton: {
    width: getRealSize(38),
    height: getRealSize(38),
    justifyContent: 'center',
    alignItems: 'center',
  },
  clearIcon: {
    width: getRealSize(16),
    height: getRealSize(16),
  },
});

export default SearchInput;
