import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';
import { flatListProps } from '@/constant/flatlist_props';
import { getRealSize } from '@/common/utils';
import api, { FetchModule } from '@/common/api';
import Empty from '@/components/Empty';
import { ImplementSkeleton } from './ClientDetailSkeleton';
import LinearGradient from 'react-native-linear-gradient';
// 类型定义
interface ImplementItem {
  executed_date_desc: string;
  date_desc: string;
  category_obj?: {
    category_name: string;
  };
  item_obj?: {
    item_name: string;
  };
  quantity: number;
  tenant_obj?: {
    tenant_name: string;
  };
  medical_department_obj?: {
    department_name: string;
  };
  amount: string;
}

interface ImplementProps {
  customerId: string;
}

const Implement: React.FC<ImplementProps> = ({ customerId }) => {
  // 状态管理
  const [dataList, setDataList] = useState<ImplementItem[]>([]);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [page, setPage] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [initialLoading, setInitialLoading] = useState<boolean>(true);

  // 获取实施记录数据 - 优化版本
  const getData = useCallback(
    async (pageNum: number, reset: boolean = false) => {
      if (loading) return; // 防止重复请求

      setLoading(true);

      try {
        const response = await api.pagefetch({
          path: '/chain-wxapp/v1/customer/executionRecordList',
          params: {
            customer_id: customerId,
            page: pageNum,
            page_size: 10,
          },
          isLoading: false,
          method: FetchModule.Method.POST,
        });

        if (response.errorCode === 0 && response.responseData) {
          const newData = response.responseData.list || [];

          if (reset) {
            // 重置数据
            setDataList(newData);
            setPage(2); // 下次请求第2页
          } else {
            // 使用函数式更新避免依赖问题
            setDataList(prevData => {
              const existingKeys = new Set(
                prevData.map(
                  (item: ImplementItem) =>
                    `${item.executed_date_desc}_${item.item_obj?.item_name}`
                )
              );
              const filteredNewData = newData.filter(
                (item: ImplementItem) =>
                  !existingKeys.has(
                    `${item.executed_date_desc}_${item.item_obj?.item_name}`
                  )
              );
              return [...prevData, ...filteredNewData];
            });
            setPage(pageNum + 1);
          }

          // 检查是否还有更多数据
          const totalLoaded = reset
            ? newData.length
            : dataList.length + newData.length;
          setHasMore(response.responseData.total > totalLoaded);
        }
      } catch (error) {
        console.error('获取实施记录数据失败:', error);
      } finally {
        setLoading(false);
        if (reset) {
          setInitialLoading(false);
        }
      }
    },
    [customerId, loading, dataList.length] // 移除dataList依赖，只保留length
  );

  // 加载更多数据
  const loadMore = useCallback(() => {
    if (hasMore && !loading) {
      getData(page);
    }
  }, [hasMore, loading, page, getData]);

  // 渲染实施记录卡片 - 优化版本
  const renderItem = useCallback(
    ({ item }: { item: ImplementItem }) => (
      <View style={styles.card}>
        <View style={styles.categoryTag}>
          <Text style={styles.categoryTagText}>
            {item.category_obj?.category_name || ''}
          </Text>
        </View>
        {/* 第一行：日期和分类标签 */}
        <View style={styles.cardLine}>
          <Text style={styles.dateText}>
            {item.executed_date_desc} {item.date_desc}
          </Text>
        </View>

        {/* 第二行：项目名称和数量 */}
        <View style={styles.cardLine}>
          <Text style={styles.itemName} numberOfLines={1}>
            {item.item_obj?.item_name || ''}
          </Text>
          <Text style={styles.quantity}>×{item.quantity}</Text>
        </View>

        {/* 第三行：机构名称和金额 */}
        <View style={styles.cardLine}>
          <Text style={styles.tenantName} numberOfLines={1}>
            {item.tenant_obj?.tenant_name || ''}
          </Text>
          <Text style={styles.amount}>¥{item.amount}</Text>
        </View>
      </View>
    ),
    []
  );

  // 优化的 keyExtractor
  const keyExtractor = useCallback(
    (item: ImplementItem, index: number) =>
      `${item.executed_date_desc}_${item.item_obj?.item_name}_${index}`,
    []
  );

  // 优化的 getItemLayout
  const getItemLayout = useCallback(
    (data: ImplementItem[] | null | undefined, index: number) => ({
      length: getRealSize(91), // 卡片高度 + margin
      offset: getRealSize(91) * index,
      index,
    }),
    []
  );

  // 渲染列表底部
  const renderFooter = useCallback(() => {
    if (loading && dataList.length > 0) {
      return (
        <View style={styles.footer}>
          <Text style={styles.footerText}>加载中...</Text>
        </View>
      );
    }

    if (!hasMore && dataList.length > 0) {
      return (
        <View style={styles.footer}>
          <Text style={styles.footerText}>没有更多啦</Text>
        </View>
      );
    }

    return null;
  }, [loading, hasMore, dataList.length]);

  // 渲染空状态
  const renderEmpty = useCallback(
    () => (
      <View style={styles.empty}>
        <Empty />
      </View>
    ),
    []
  );

  // 组件挂载时初始化
  useEffect(() => {
    setDataList([]);
    setPage(1);
    setHasMore(true);
    setInitialLoading(true);
    getData(1, true);
  }, [customerId]); // 只依赖customerId

  // 初始加载状态显示骨架屏
  if (initialLoading) {
    return <ImplementSkeleton />;
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#ffffff', '#f8f8f8']}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        locations={[0.1, 1]}
      >
        <View style={{ height: getRealSize(10) }} />
      </LinearGradient>
      <FlatList
        data={dataList}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        onEndReached={loadMore}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={
          dataList.length === 0 ? styles.emptyContainer : styles.listContent
        }
        getItemLayout={getItemLayout}
        // 避免不必要的重新渲染
        extraData={dataList.length}
        {...flatListProps}
      />
    </View>
  );
};

// 样式定义
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },

  listContent: {
    paddingTop: getRealSize(10),
    paddingBottom: getRealSize(20),
  },

  emptyContainer: {
    flex: 1,
  },

  card: {
    backgroundColor: '#ffffff',
    marginHorizontal: getRealSize(15),
    height: getRealSize(81),
    marginTop: getRealSize(10),
    paddingHorizontal: getRealSize(12),
    paddingVertical: getRealSize(9),
    position: 'relative',
  },

  cardLine: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getRealSize(6),
  },

  dateText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(11),
    color: '#777777',
    letterSpacing: 0,
    fontWeight: '400',
  },

  categoryTag: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: '#EBFBDC',
    paddingHorizontal: getRealSize(4),
    paddingVertical: getRealSize(2),
  },

  categoryTagText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(10),
    color: '#61B43E',
    letterSpacing: 0,
    fontWeight: '400',
  },

  itemName: {
    maxWidth: getRealSize(300),
    fontFamily: 'PingFangSC-Semibold',
    fontSize: getRealSize(13),
    color: '#161616',
    letterSpacing: 0,
    fontWeight: '600',
  },

  quantity: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(11),
    color: '#AAABB3',
    letterSpacing: 0,
    fontWeight: '400',
  },

  tenantName: {
    maxWidth: getRealSize(280),
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(11),
    color: '#777777',
    letterSpacing: 0,
    fontWeight: '400',
    flex: 1,
  },

  amount: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(12),
    color: '#555555',
    letterSpacing: 0,
    fontWeight: '500',
  },

  footer: {
    paddingVertical: getRealSize(15),
    alignItems: 'center',
  },

  footerText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#999999',
  },

  empty: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: getRealSize(50),
  },
});

export default Implement;
