import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, Text, StyleSheet, Image, LayoutChangeEvent } from 'react-native';
import { ATrack } from '@soyoung/react-native-container';
import { getRealSize, hexToRgb } from '@/common/utils';
import { Tag } from '../types/detail';
/**
 * TagList 组件 Props
 */
interface TagListProps {
  /** 标签列表数据 */
  list: Tag[];
}

/**
 * TagList 标签列表组件
 *
 * 功能特性：
 * - 标签列表展示，支持动态颜色
 * - 高度检测：超过2行自动显示"更多/收起"按钮
 * - 展开/收起切换功能
 * - 使用 hexToRgb 处理颜色透明度
 * - 响应式尺寸适配
 *
 * @param props - 组件属性
 * @returns JSX.Element
 */

const TagList: React.FC<TagListProps> = ({ list }) => {
  const [showAll, setShowAll] = useState<boolean>(false);
  const [allVisible, setAllVisible] = useState<boolean>(false);
  const tagListRef = useRef<View>(null);

  // 调试日志
  useEffect(() => {
    console.log('TagList received data:', list);
    console.log('TagList length:', list?.length);
  }, [list]);

  // 检查高度的方法
  const checkHeight = useCallback(() => {
    if (tagListRef.current) {
      tagListRef.current.measure((x, y, width, height) => {
        console.log('TagList height check:', { height });
        // 计算两行标签的准确高度
        // 考虑到实际标签可能比minHeight更高，使用更宽松的阈值
        // 单行约20+8=28，两行约28*2-8=48，为了确保准确性，使用45作为阈值
        const twoLineHeight = getRealSize(60);
        console.log(
          'Two line height threshold:',
          twoLineHeight,
          'actual height:',
          height
        );

        if (height && height > twoLineHeight) {
          setAllVisible(true);
        } else {
          setAllVisible(false);
          setShowAll(false);
        }
      });
    }
  }, []);

  // 切换展开/收起
  const toggleShowAll = useCallback(() => {
    setShowAll(!showAll);
  }, [showAll]);

  // 当列表变化时重新检查高度
  useEffect(() => {
    // 延迟检查，确保布局完成
    const timer = setTimeout(() => {
      checkHeight();
    }, 100);
    return () => clearTimeout(timer);
  }, [list, checkHeight]);

  // 布局变化时检查高度
  const onLayout = useCallback(
    (_event: LayoutChangeEvent) => {
      checkHeight();
    },
    [checkHeight]
  );

  // 渲染单个标签
  const renderTag = (item: Tag) => {
    const tagStyle = item.tag_color
      ? {
          backgroundColor: hexToRgb(item.tag_color, 0.1),
          color: item.tag_color,
          borderColor: 'transparent',
        }
      : {};

    return (
      <View key={item.tag_id} style={[styles.tag, tagStyle]}>
        <Text style={[styles.tagText, { color: tagStyle.color || '#555555' }]}>
          {item.name}
        </Text>
      </View>
    );
  };

  return (
    <View>
      <View style={[styles.tagMain, !showAll ? styles.tagLine2 : {}]}>
        <View ref={tagListRef} style={styles.tagList} onLayout={onLayout}>
          {list.map(renderTag)}
        </View>
      </View>

      {allVisible ? (
        <ATrack style={styles.allBtn} onPress={toggleShowAll}>
          <View style={styles.allBtnBox}>
            <Text style={styles.allBtnText}>{showAll ? '收起' : '更多'}</Text>
            <Image
              style={styles.arrow}
              source={{
                uri: showAll
                  ? 'https://static.soyoung.com/sy-pre/17557ypynfxeg-1712639400698.png'
                  : 'https://static.soyoung.com/sy-pre/30vja3jbwjd1r-1712639400698.png',
              }}
            />
          </View>
        </ATrack>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  tagMain: {
    overflow: 'hidden',
  },
  tagLine2: {
    maxHeight: getRealSize(50),
  },
  tagList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'flex-start', // 改为顶部对齐
    // 移除负边距，避免布局问题
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: getRealSize(18), // 增加最小高度确保可见
    paddingHorizontal: getRealSize(8), // 稍微增加内边距
    backgroundColor: '#f2f2f2',
    borderWidth: 1,
    borderColor: 'transparent',
    marginRight: getRealSize(8), // 20rpx / 2
    marginBottom: getRealSize(8), // 调整底部边距
  },
  tagText: {
    fontSize: getRealSize(10), // 增加字体大小确保可见
    color: '#555555',
    lineHeight: getRealSize(18), // 添加行高
  },
  allBtn: {
    marginTop: getRealSize(10), // 20rpx / 2
  },
  allBtnBox: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: getRealSize(43), // 86rpx / 2
    height: getRealSize(18), // 36rpx / 2
    borderWidth: getRealSize(1), // 2rpx / 2
    borderColor: '#f0f0f0',
  },
  allBtnText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(10), // 20rpx / 2
    color: '#555555',
  },
  arrow: {
    width: getRealSize(7), // 14rpx / 2
    height: getRealSize(5), // 10rpx / 2
    marginLeft: getRealSize(2), // 4rpx / 2
  },
});

export default TagList;
