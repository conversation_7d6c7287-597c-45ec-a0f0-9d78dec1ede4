import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';
import { flatListProps } from '@/constant/flatlist_props';
import Empty from '@/components/Empty';
import { VisitSkeleton } from './ClientDetailSkeleton';
import { getRealSize } from '@/common/utils';
import api, { FetchModule } from '@/common/api';
import LinearGradient from 'react-native-linear-gradient';

// 类型定义
interface UserInfo {
  name: string;
  id: number;
}

interface VisitItem {
  visit_id: string;
  visit_status: number;
  confirm_label: string;
  time_str: string;
  services_str: string[];
  time_desc: string;
  tenant_name: string;
}

interface VisitProps {
  customerId: string;
  userInfo?: UserInfo | null;
}

const Visit: React.FC<VisitProps> = ({ customerId, userInfo: _userInfo }) => {
  // 状态管理
  const [dataList, setDataList] = useState<VisitItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [page, setPage] = useState<number>(1);
  const [componentShow, setComponentShow] = useState<boolean>(true);
  const [initialLoading, setInitialLoading] = useState<boolean>(true);

  // 获取回访数据 - 优化版本
  const getData = useCallback(
    async (pageNum: number, reset: boolean = false) => {
      if (loading) return;
      setLoading(true);

      try {
        const response = await api.pagefetch({
          path: '/chain-wxapp/v1/customer/visitList',
          params: {
            customer_id: customerId,
            page: pageNum,
            size: 10,
          },
          method: FetchModule.Method.POST,
        });

        if (response.errorCode === 0 && response.responseData) {
          const newData = response.responseData.data || [];
          const total = response.responseData.total || 0;

          if (reset) {
            // 重置数据 - 批量更新状态
            setDataList(newData);
            setPage(2);
            setHasMore(total > newData.length);
          } else {
            // 追加数据 - 优化更新逻辑
            setDataList(prevData => {
              const existingIds = new Set(prevData.map(item => item.visit_id));
              const filteredNewData = newData.filter(
                (item: VisitItem) => !existingIds.has(item.visit_id)
              );
              const updatedData = [...prevData, ...filteredNewData];

              // 同步更新相关状态
              setHasMore(total > updatedData.length);
              setPage(pageNum + 1);

              return updatedData;
            });
          }
        }
      } catch (error) {
        console.error('获取回访数据失败:', error);
      } finally {
        setLoading(false);
      }
    },
    [customerId, loading]
  );

  // 加载更多数据 - 优化版本
  const loadMore = useCallback(() => {
    if (hasMore && !loading && componentShow) {
      getData(page, false);
    }
  }, [hasMore, loading, componentShow, page, getData]);

  // 获取确认标签样式类型
  const getConfirmTagType = (
    confirmLabel: string
  ): 'green' | 'orange' | 'gray' => {
    if (confirmLabel === '预约已确认') return 'green';
    if (confirmLabel === '预约待确认') return 'orange';
    return 'gray';
  };

  // 渲染回访记录卡片 - 参照 Vue 版本布局
  const renderItem = useCallback(({ item }: { item: VisitItem }) => {
    const [datePart, timePart] = item.time_str.split(' ');
    const statusType = getConfirmTagType(item.confirm_label);
    const isGrayCard = item.visit_status === 6 || item.visit_status === 7;

    return (
      <View style={[styles.card, isGrayCard && styles.grayCard]}>
        {/* 右上角状态标签 */}
        <View style={[styles.statusTag, styles[`statusTag_${statusType}`]]}>
          <Text style={[styles.statusText, styles[`statusText_${statusType}`]]}>
            {item.confirm_label}
          </Text>
        </View>

        {/* 第一行：日期 */}
        <View style={styles.line}>
          <Text style={styles.dateText}>{datePart}</Text>
        </View>

        {/* 第二行：时间 + 服务名称 */}
        <View style={styles.line}>
          <Text style={[styles.timeText, isGrayCard && styles.grayTimeText]}>
            {timePart}
          </Text>
          <View style={styles.nameBox}>
            {item.services_str && item.services_str.length > 0 && (
              <>
                <Text
                  style={[styles.nameText, isGrayCard && styles.grayNameText]}
                  numberOfLines={1}
                >
                  {item.services_str[0]}
                </Text>
                {item.services_str.length > 1 && (
                  <Text
                    style={[styles.nameText, isGrayCard && styles.grayNameText]}
                  >
                    等{item.services_str.length}个
                  </Text>
                )}
              </>
            )}
          </View>
        </View>

        {/* 第三行：描述 + 医院名称 */}
        <View style={styles.line}>
          <Text
            style={[
              styles.descriptionText,
              isGrayCard && styles.grayDescriptionText,
            ]}
          >
            {item.time_desc}
          </Text>
          <Text style={styles.hospitalNameText}>{item.tenant_name}</Text>
        </View>
      </View>
    );
  }, []);

  // 优化的 keyExtractor
  const keyExtractor = useCallback((item: VisitItem) => item.visit_id, []);

  // 修正的 getItemLayout - 计算准确的高度
  const ITEM_HEIGHT = getRealSize(75) + getRealSize(10); // 卡片高度 + marginTop
  const getItemLayout = useCallback(
    (data: VisitItem[] | null | undefined, index: number) => ({
      length: ITEM_HEIGHT,
      offset: ITEM_HEIGHT * index,
      index,
    }),
    []
  );

  // 渲染列表底部
  const renderFooter = () => (
    <View style={styles.footer}>
      <Text style={styles.footerText}>
        {loading ? '加载中...' : hasMore ? '上拉加载更多' : '没有更多啦'}
      </Text>
    </View>
  );

  // 渲染空状态
  const renderEmpty = () => (
    <View style={styles.empty}>
      <Empty />
    </View>
  );

  // 组件挂载时初始化
  useEffect(() => {
    const initData = async () => {
      setComponentShow(true);
      setPage(1);
      setDataList([]);
      setHasMore(true);
      setLoading(true);
      setInitialLoading(true);

      try {
        const response = await api.pagefetch({
          path: '/chain-wxapp/v1/customer/visitList',
          params: {
            customer_id: customerId,
            page: 1,
            size: 10,
          },
          method: FetchModule.Method.POST,
          isLoading: false,
        });

        if (response.errorCode === 0 && response.responseData) {
          const newData = response.responseData.data || [];
          const total = response.responseData.total || 0;
          setDataList(newData);
          setPage(2); // 下次加载第2页
          setHasMore(total > newData.length);
        }
      } catch (error) {
        console.error('初始化加载数据失败:', error);
      } finally {
        setLoading(false);
        setInitialLoading(false);
      }
    };

    initData();

    return () => {
      setComponentShow(false);
    };
  }, [customerId]);

  // 初始加载状态显示骨架屏
  if (initialLoading) {
    return <VisitSkeleton />;
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#ffffff', '#f8f8f8']}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        locations={[0.1, 1]}
      >
        <View style={{ height: getRealSize(10) }} />
      </LinearGradient>
      {dataList ? (
        dataList.length > 0 ? (
          <FlatList
            data={dataList}
            renderItem={renderItem}
            keyExtractor={keyExtractor}
            onEndReached={loadMore}
            onEndReachedThreshold={0.9}
            ListFooterComponent={renderFooter}
            ListEmptyComponent={renderEmpty}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContent}
            getItemLayout={getItemLayout}
            {...flatListProps}
          />
        ) : (
          renderEmpty()
        )
      ) : (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      )}
    </View>
  );
};

// 样式定义
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f6f9f9',
  },
  listContent: {
    paddingHorizontal: getRealSize(15),
    paddingTop: getRealSize(10),
  },
  card: {
    backgroundColor: '#ffffff',
    marginTop: getRealSize(10),
    width: getRealSize(345), // 690rpx / 2
    height: getRealSize(75), // 240rpx / 2
    padding: getRealSize(10), // 20rpx / 2
    position: 'relative',
    alignSelf: 'center',
  },
  grayCard: {
    // 灰色卡片样式，应用于 visit_status === 6 || 7
  },
  line: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    fontFamily: 'PingFangSC-Regular',
  },
  dateText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(11), // 22rpx / 2
    color: '#AAABB3',
    lineHeight: getRealSize(16), // 32rpx / 2
    fontWeight: '400',
  },
  timeText: {
    fontFamily: 'PingFangSC-Semibold',
    fontSize: getRealSize(18), // 36rpx / 2
    lineHeight: getRealSize(25), // 50rpx / 2
    color: '#161616',
    fontWeight: '600',
  },
  grayTimeText: {
    color: '#999999',
  },
  nameBox: {
    flexDirection: 'row',
    fontFamily: 'PingFangSC-Semibold',
    fontSize: getRealSize(14), // 28rpx / 2
    color: '#161616',
    fontWeight: '600',
  },
  nameText: {
    maxWidth: getRealSize(185), // 370rpx / 2
    fontFamily: 'PingFangSC-Semibold',
    fontSize: getRealSize(14), // 28rpx / 2
    color: '#161616',
    fontWeight: '600',
  },
  grayNameText: {
    color: '#999',
  },
  descriptionText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(10), // 20rpx / 2
    color: '#61B43E',
    flexShrink: 0,
    marginRight: getRealSize(5), // 10rpx / 2
  },
  grayDescriptionText: {
    color: '#AAABB3',
  },
  hospitalNameText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(9), // 18rpx / 2
    color: '#AAABB3',
  },
  statusTag: {
    position: 'absolute',
    right: 0,
    top: 0,
    lineHeight: getRealSize(20), // 40rpx / 2
    paddingHorizontal: getRealSize(10), // 20rpx / 2
    fontSize: getRealSize(10), // 20rpx / 2
  },
  // 动态样式 - 通过 styles[`statusTag_${statusType}`] 引用
  statusTag_green: {
    backgroundColor: '#EBFBDC',
  },
  statusTag_orange: {
    backgroundColor: 'rgba(255, 115, 0, 0.17)',
  },
  statusTag_gray: {
    backgroundColor: 'rgba(170, 171, 179, 0.17)',
  },
  statusText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(10),
    lineHeight: getRealSize(14),
  },
  // 动态样式 - 通过 styles[`statusText_${statusType}`] 引用
  statusText_green: {
    color: '#61B43E',
  },
  statusText_orange: {
    color: '#FF7A0D',
  },
  statusText_gray: {
    color: '#AAABB3',
  },

  footer: {
    paddingVertical: getRealSize(15),
    alignItems: 'center',
  },
  footerText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12),
    color: '#777777',
  },
  empty: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: getRealSize(50),
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: getRealSize(50),
  },
  loadingText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(14),
    color: '#999999',
  },
});

export default Visit;
