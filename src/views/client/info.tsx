import React, { Component } from 'react';
import { ClientInfoContainer } from './containers';

interface PageProps {
  route: {
    params: {
      params?: {
        id?: string;
        [key: string]: any;
      };
    };
  };
  insets: {
    bottom: number;
    left: number;
    right: number;
    top: number;
  };
}

export default class Page extends Component<PageProps> {
  constructor(props: PageProps) {
    super(props);
    this.state = {
      pageShow: true,
    };
  }

  // 页面埋点名称
  soyoungPageName() {
    return 'sy_client_user_info_page';
  }

  /** 页面埋点信息 */
  soyoungPageInfo() {
    return {};
  }

  // 页面显示时触发
  didAppear() {
    // 页面显示埋点或其他逻辑
  }

  // 页面隐藏时触发
  willDisappear() {
    // 页面隐藏埋点或其他逻辑
  }

  render() {
    return <ClientInfoContainer {...this.props} />;
  }
}
