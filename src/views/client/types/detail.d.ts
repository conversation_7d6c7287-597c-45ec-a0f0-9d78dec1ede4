// 类型定义
export interface UserInfo {
  name: string;
  id: number;
}

export interface Tag {
  tag_id: string;
  name: string;
  tag_color?: string;
}

export interface CustomerPrivate {
  external_user_info?: {
    avatar: string;
    wx_name: string;
  };
  last_add_staff?: {
    avatar: string;
    staff_name: string;
  };
  add_staff_num?: number;
  last_joined_group?: {
    avatar?: string;
    group_name: string;
    joined_group_num: number;
  };
  joined_group_num?: number;
}

export interface CustomerInfo {
  customer_id: string;
  customer_id_str?: string;
  realname: string;
  mobile: string;
  gender?: number;
  age?: number;
  end_mobile?: string;
}

export interface PageInfo {
  customer?: CustomerInfo;
  tags?: {
    dynamic_tag_list: Array<Tag>;
    user_tag_list: Array<Tag>;
    allergy_tag_list: Array<Tag>;
  };
  add_c_status?: number;
  show_phone_contact?: boolean;
  customer_private?: CustomerPrivate;
  get_dynamic_res?: Array<{
    date?: string;
    isGreen?: boolean;
    [key: string]: any;
  }>;
  order_num?: {
    product_num: number;
    enable_exec_num: number;
    yx_order_num: number;
  };
}
