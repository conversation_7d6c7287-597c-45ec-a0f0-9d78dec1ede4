# updateLoginExtInfo 类型系统

这个类型系统为 `updateLoginExtInfo` bridge 方法提供了完整的 TypeScript 类型支持，确保类型安全和开发体验。

## 概述

`updateLoginExtInfo` 方法有两个不同的 `action` 类型：
- `'ext_info'` - 用于更新协同信息（穿越相关）
- `'params_info'` - 用于更新基本参数信息（token、tenant_id等）

## 类型定义

### 核心接口

```typescript
// 协同信息接口 - 用于 action: 'ext_info'
interface ExtInfo {
  cross_token?: string; // 穿越人的 token
  cross_to_uid?: string; // 穿越人的 uid
  cross_permissions?: string[]; // 穿越人的权限列表
  cross_roles?: Array<{ id: string; [key: string]: any }>; // 穿越人的角色列表
  cross_tenant_id?: string; // 穿越人的门店ID
  cross_tenant_user_id?: string; // 穿越人的门店用户ID
}

// 基本参数信息接口 - 用于 action: 'params_info'
interface ParamsInfo {
  token?: string; // 用户token
  tenant_id?: string; // 门店ID
  user_id?: string; // 用户ID
  tenant_user_id?: string; // 门店用户ID
  permissions?: string[]; // 权限列表
  roles?: Array<{ id: string; [key: string]: any }>; // 角色列表
}
```

## 使用方法

### 1. 类型安全的调用函数

```typescript
import { updateLoginExtInfo } from '../common/updateLoginExtInfo';

// 更新协同信息 - TypeScript 会自动推断 info 的类型为 ExtInfo
await updateLoginExtInfo('ext_info', {
  cross_token: 'new_token',
  cross_to_uid: 'user_123',
  cross_permissions: ['read', 'write'],
  cross_roles: [{ id: 'admin' }],
  cross_tenant_id: 'tenant_456',
});

// 更新基本参数信息 - TypeScript 会自动推断 info 的类型为 ParamsInfo
await updateLoginExtInfo('params_info', {
  token: 'new_token',
  tenant_id: 'tenant_123',
  user_id: 'user_456',
});
```

### 2. 便捷函数

```typescript
import { 
  updateExtInfo, 
  updateParamsInfo, 
  clearExtInfo, 
  setExtInfo, 
  setParamsInfo 
} from '../common/updateLoginExtInfo';

// 更新协同信息
await updateExtInfo({
  cross_token: 'new_cross_token',
  cross_to_uid: 'cross_user_123',
  cross_permissions: ['cross_read', 'cross_write'],
});

// 更新基本参数信息
await updateParamsInfo({
  token: 'new_token',
  tenant_id: 'new_tenant_123',
});

// 清理协同信息
await clearExtInfo();

// 设置完整的协同信息
await setExtInfo({
  cross_token: 'complete_cross_token',
  cross_to_uid: 'complete_cross_user',
  cross_permissions: ['complete_permissions'],
  cross_roles: [{ id: 'complete_role' }],
  cross_tenant_id: 'complete_tenant',
});

// 设置基本参数信息
await setParamsInfo({
  token: 'complete_token',
  tenant_id: 'complete_tenant',
});
```

## 类型安全特性

### 1. 自动类型推断

当你使用 `updateLoginExtInfo` 函数时，TypeScript 会根据 `action` 参数自动推断 `info` 的类型：

```typescript
// action 为 'ext_info' 时，info 必须是 ExtInfo 类型
await updateLoginExtInfo('ext_info', {
  cross_token: 'token', // ✅ 正确
  token: 'token', // ❌ 错误：token 不是 ExtInfo 的字段
});

// action 为 'params_info' 时，info 必须是 ParamsInfo 类型
await updateLoginExtInfo('params_info', {
  token: 'token', // ✅ 正确
  cross_token: 'token', // ❌ 错误：cross_token 不是 ParamsInfo 的字段
});
```

### 2. 字段提示和检查

TypeScript 会提供完整的字段提示和类型检查：

```typescript
const extInfo: ExtInfo = {
  cross_token: 'example_token',
  cross_to_uid: 'example_user',
  cross_permissions: ['example_permission'],
};

// ✅ 正确 - TypeScript 会提供字段提示
console.log(extInfo.cross_token);
console.log(extInfo.cross_to_uid);

// ❌ 错误 - TypeScript 会报错
// console.log(extInfo.token); // 错误：token 不是 ExtInfo 的字段
```

## 迁移现有代码

### 从直接调用 jsApi.toNative 迁移

**之前：**
```typescript
jsApi.toNative('updateLoginExtInfo', {
  action: 'ext_info',
  info: {
    cross_token: 'token',
    cross_to_uid: 'user',
  },
});
```

**之后：**
```typescript
import { updateLoginExtInfo } from '../common/updateLoginExtInfo';

await updateLoginExtInfo('ext_info', {
  cross_token: 'token',
  cross_to_uid: 'user',
});
```

### 从直接调用 jsApi.toNative 迁移（使用便捷函数）

**之前：**
```typescript
jsApi.toNative('updateLoginExtInfo', {
  action: 'ext_info',
  info: {
    cross_token: undefined,
    cross_permissions: undefined,
    cross_roles: undefined,
    cross_tenant_id: undefined,
    cross_to_uid: undefined,
  },
});
```

**之后：**
```typescript
import { clearExtInfo } from '../common/updateLoginExtInfo';

await clearExtInfo();
```

## 扩展类型

如果需要添加新的字段，可以扩展相应的接口：

```typescript
// 在 src/types/updateLoginExtInfo.ts 中扩展
export interface ExtInfo {
  cross_token?: string;
  cross_to_uid?: string;
  cross_permissions?: string[];
  cross_roles?: Array<{ id: string; [key: string]: any }>;
  cross_tenant_id?: string;
  cross_tenant_user_id?: string;
  // 添加新字段
  cross_new_field?: string;
}
```

## 注意事项

1. **类型安全**：使用这个类型系统可以避免在运行时出现字段错误
2. **开发体验**：IDE 会提供完整的字段提示和自动补全
3. **重构友好**：当你修改一个地方的字段时，其他地方会得到相应的类型错误提示
4. **向后兼容**：所有字段都是可选的，不会破坏现有代码

## 相关文件

- `src/types/updateLoginExtInfo.ts` - 类型定义
- `src/common/updateLoginExtInfo.ts` - 工具函数
- `src/examples/updateLoginExtInfo-usage.ts` - 使用示例
- `src/common/getNativeLoginInfo.tsx` - 已更新使用新类型
