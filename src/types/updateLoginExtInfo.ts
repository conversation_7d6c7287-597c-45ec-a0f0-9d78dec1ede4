// updateLoginExtInfo 相关的类型定义

/**
 * 协同信息接口 - 用于 action: 'ext_info'
 */
export interface ExtInfo {
  cross_token?: string; // 穿越人的 token
  cross_to_uid?: string; // 穿越人的 uid
  cross_permissions?: string[]; // 穿越人的权限列表
  cross_roles?: Array<{ id: string; [key: string]: any }>; // 穿越人的角色列表
  cross_tenant_id?: string; // 穿越人的门店ID
  cross_tenant_user_id?: string; // 穿越人的门店用户ID
}

/**
 * 基本参数信息接口 - 用于 action: 'params_info'
 */
export interface ParamsInfo {
  token?: string; // 用户token
  tenant_id?: string; // 门店ID
  user_id?: string; // 用户ID
  tenant_user_id?: string; // 门店用户ID
  permissions?: string[]; // 权限列表
  roles?: Array<{ id: string; [key: string]: any }>; // 角色列表
}

/**
 * updateLoginExtInfo 的 action 类型
 */
export type UpdateLoginExtInfoAction = 'ext_info' | 'params_info';

/**
 * updateLoginExtInfo 的完整参数接口
 */
export interface UpdateLoginExtInfoParams {
  action: UpdateLoginExtInfoAction;
  info: ExtInfo | ParamsInfo;
}

/**
 * 根据 action 类型获取对应的 info 类型
 */
export type UpdateLoginExtInfoInfo<T extends UpdateLoginExtInfoAction> =
  T extends 'ext_info' ? ExtInfo : T extends 'params_info' ? ParamsInfo : never;

/**
 * 类型安全的 updateLoginExtInfo 调用函数类型
 */
export type UpdateLoginExtInfoFunction = {
  <T extends UpdateLoginExtInfoAction>(params: {
    action: T;
    info: UpdateLoginExtInfoInfo<T>;
  }): Promise<any>;
};

/**
 * 扩展的 ExtInfo 接口（包含所有可能的协同字段）
 */
export interface ExtendedExtInfo extends ExtInfo {
  // 可以在这里添加更多协同相关的字段
  [key: string]: any;
}

/**
 * 扩展的 ParamsInfo 接口（包含所有可能的基本参数字段）
 */
export interface ExtendedParamsInfo extends ParamsInfo {
  // 可以在这里添加更多基本参数相关的字段
  [key: string]: any;
}
