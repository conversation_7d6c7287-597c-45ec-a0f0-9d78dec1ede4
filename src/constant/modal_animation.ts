/**
 * 模态框动画配置
 * 统一定义模态框的动画时间和行为
 */
const modalAnimation = {
  /** 模态框弹入动画时长 (毫秒) */
  animationInTiming: 250,
  /** 模态框退出动画时长 (毫秒) */
  animationOutTiming: 250,
  /** 背景遮罩弹入过渡时长 (毫秒) */
  backdropTransitionInTiming: 250,
  /** 背景遮罩退出过渡时长 (毫秒) */
  backdropTransitionOutTiming: 250,
  /** 动画过程中是否隐藏模态框内容 */
  hideModalContentWhileAnimating: true,
  /** 是否使用原生驱动进行动画 (提升性能) */
  useNativeDriver: true,
  useNativeDriverForBackdrop: true,
  /** 背景遮罩透明度 */
  backdropOpacity: 0.7,
};

export { modalAnimation };
