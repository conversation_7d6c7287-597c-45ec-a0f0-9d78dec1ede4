/**
 * FlatList 性能优化配置
 * 该配置用于提升长列表的滚动性能和用户体验
 */
export const flatListProps = {
  // 触发 onEndReached 的阈值，0.2 表示距离底部 20% 时触发
  onEndReachedThreshold: 0.2,

  // 是否移除屏幕外的子视图，设为 false 可避免某些情况下的闪烁
  removeClippedSubviews: false,

  // 每批次最大渲染项目数，较小的值可以减少主线程阻塞
  maxToRenderPerBatch: 5,

  // 批次更新的时间间隔（毫秒），控制渲染频率
  updateCellsBatchingPeriod: 100,

  // 初始渲染的项目数量，影响首屏加载性能
  initialNumToRender: 5,

  // 窗口大小，决定在可视区域外保持多少个项目
  windowSize: 5,
};
