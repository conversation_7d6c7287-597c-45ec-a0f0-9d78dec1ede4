import { DeviceEventEmitter } from 'react-native';
import jsApi from '@soyoung/react-native-jsapi';

const CROSS_EVENT_NAME = 'cross_status_change';

const NotifyCrossStatusChangeFunc = ({ status }: { status: boolean }) => {
  // 使用React Native的DeviceEventEmitter发送事件
  DeviceEventEmitter.emit(CROSS_EVENT_NAME, { status });

  // 通知 Native
  jsApi.toNative('nativeNotify' as any, {
    event_id: CROSS_EVENT_NAME,
    event_info: {
      status,
    },
  });
};

// 添加监听器的辅助函数
const addCrossStatusListener = (
  callback: (info: { status: boolean }) => void
) => {
  return DeviceEventEmitter.addListener(CROSS_EVENT_NAME, callback);
};

// 移除监听器的辅助函数
const removeCrossStatusListener = (subscription: any) => {
  if (subscription && subscription.remove) {
    subscription.remove();
  }
};

export {
  CROSS_EVENT_NAME,
  NotifyCrossStatusChangeFunc,
  addCrossStatusListener,
  removeCrossStatusListener,
};
