import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useCallback,
  useEffect,
} from 'react';
import {
  View,
  Text,
  Image,
  TextInput,
  ScrollView,
  StyleSheet,
  Platform,
} from 'react-native';
import Modal from 'react-native-modal';
import { ATrack } from '@soyoung/react-native-container';
import { getRealSize } from '@/common/utils';
import api, { FetchModule } from '@/common/api';
import Empty from './Empty';
import { modalAnimation } from '../constant/modal_animation';

/**
 * 项目选择弹窗组件
 * 支持搜索、分类标签页、单选/多选模式
 */

export interface ActiveItem {
  id: number;
  name: string;
}

interface ProjectCategory {
  id: number;
  name: string;
  children: ActiveItem[];
}

export type Active = ActiveItem | ActiveItem[];

export interface ProjectPopupProps {
  /** 当前选中的项目 */
  active?: Active;
  /** 弹窗标题 */
  title?: string;
  /** 是否多选模式 */
  multiple?: boolean;
  /** 选中值变化回调 */
  onActiveChange?: (active: Active) => void;
  /** 关闭弹窗回调 */
  onClose?: () => void;
}

export interface ProjectPopupRef {
  open: () => void;
  close: () => void;
}

const ProjectPopup = forwardRef<ProjectPopupRef, ProjectPopupProps>(
  ({ active, title = '', multiple = false, onActiveChange, onClose }, ref) => {
    const [visible, setVisible] = useState(false);
    const [listActive, setListActive] = useState<Active>(
      multiple
        ? Array.isArray(active)
          ? active
          : []
        : !Array.isArray(active)
          ? active || { id: NaN, name: '' }
          : { id: NaN, name: '' }
    );
    const [currentTab, setCurrentTab] = useState(0);
    const [searchValue, setSearchValue] = useState('');
    const [projectList, setProjectList] = useState<ProjectCategory[]>([]);
    const [originProjectList, setOriginProjectList] = useState<
      ProjectCategory[]
    >([]);

    // 同步外部 active 到内部状态
    useEffect(() => {
      if (multiple) {
        setListActive(Array.isArray(active) ? active : []);
      } else {
        setListActive(
          !Array.isArray(active)
            ? active || { id: NaN, name: '' }
            : { id: NaN, name: '' }
        );
      }
    }, [active, multiple]);

    // 格式化项目列表数据
    const formatList = useCallback((data: any[]): ProjectCategory[] => {
      return data.map(item => ({
        id: item.category_id,
        name: item.category_name,
        children: item.List,
      }));
    }, []);

    // 获取项目数据
    const getData = useCallback(async () => {
      try {
        const response = await api.pagefetch({
          path: '/chain-wxapp/v1/product/getProductList',
          params: {},
          method: FetchModule.Method.POST,
        });
        if (response.errorCode === 0) {
          const formattedList = formatList(response.responseData || []);
          setOriginProjectList(formattedList);
          setProjectList(formattedList);
        }
      } catch (error) {
        console.error('获取项目列表失败:', error);
      }
    }, [formatList]);

    // 搜索功能
    const searchTree = useCallback(
      (tree: ProjectCategory[], searchString: string): ProjectCategory[] => {
        if (!searchString.trim()) return tree;

        const result: ProjectCategory[] = [];
        for (const item of tree) {
          if (item.name.toLowerCase().includes(searchString.toLowerCase())) {
            result.push(item);
          } else if (item.children) {
            const childrenResult = searchTree(
              item.children.map(child => ({
                id: child.id,
                name: child.name,
                children: [],
              })),
              searchString
            );
            if (childrenResult.length > 0) {
              // 将匹配的子项目转换回 ActiveItem 格式
              const matchedChildren = childrenResult.map(child => ({
                id: child.id,
                name: child.name,
              }));
              result.push({ ...item, children: matchedChildren });
            }
          }
        }
        return result;
      },
      []
    );

    // 处理搜索
    const handleClickToSearch = useCallback(() => {
      if (searchValue.trim()) {
        const filteredList = searchTree(originProjectList, searchValue.trim());
        setProjectList(filteredList);
        setCurrentTab(0);
      } else {
        setProjectList(originProjectList);
      }
    }, [searchValue, originProjectList, searchTree]);

    // 清除搜索
    const handleClearIcon = useCallback(() => {
      setSearchValue('');
      setProjectList(originProjectList);
    }, [originProjectList]);

    // 处理标签页点击
    const handleClickTab = useCallback((index: number) => {
      setCurrentTab(index);
    }, []);

    // 更新选中状态
    const updateActive = useCallback(
      (data: ActiveItem) => {
        if (multiple) {
          const currentList = listActive as ActiveItem[];
          const existingIndex = currentList.findIndex(
            item => item.id === data.id
          );

          let newList: ActiveItem[];
          if (existingIndex >= 0) {
            // 已存在，移除
            newList = currentList.filter(item => item.id !== data.id);
          } else {
            // 不存在，添加
            newList = [...currentList, data];
          }
          setListActive(newList);
        } else {
          // 单选模式
          if ((listActive as ActiveItem).id === data.id) {
            setListActive({ id: NaN, name: '' });
          } else {
            setListActive(data);
          }
        }
      },
      [multiple, listActive]
    );

    // 打开弹窗
    const open = useCallback(() => {
      setVisible(true);
      getData();
    }, [getData]);

    // 关闭弹窗
    const close = useCallback(() => {
      setVisible(false);
      setSearchValue(''); // 清空搜索框
      setProjectList(originProjectList); // 重置数据为未搜索状态
      // 重置选中状态到初始值
      if (multiple) {
        setListActive(Array.isArray(active) ? active : []);
      } else {
        setListActive(
          !Array.isArray(active)
            ? active || { id: NaN, name: '' }
            : { id: NaN, name: '' }
        );
      }
      onClose?.();
    }, [onClose, originProjectList, active, multiple]);

    // 取消操作
    const handleCancel = useCallback(() => {
      // 重置选中状态到初始值
      if (multiple) {
        setListActive(Array.isArray(active) ? active : []);
      } else {
        setListActive(
          !Array.isArray(active)
            ? active || { id: NaN, name: '' }
            : { id: NaN, name: '' }
        );
      }
      close();
    }, [close, active, multiple]);

    // 确认操作
    const handleConfirm = useCallback(() => {
      onActiveChange?.(listActive);
      close();
    }, [listActive, onActiveChange, close]);

    // 暴露给父组件的方法
    useImperativeHandle(
      ref,
      () => ({
        open,
        close,
      }),
      [open, close]
    );

    // 检查项目是否被选中
    const isProjectSelected = useCallback(
      (projectId: number): boolean => {
        if (multiple) {
          return (listActive as ActiveItem[]).some(
            item => item.id === projectId
          );
        } else {
          return (listActive as ActiveItem).id === projectId;
        }
      },
      [listActive, multiple]
    );

    // 检查分类是否有选中项目
    const hasCategorySelected = useCallback(
      (categoryChildren: any[]): boolean => {
        return categoryChildren.some(child => isProjectSelected(child.id));
      },
      [isProjectSelected]
    );

    const renderContent = () => {
      return (
        <>
          {/* 顶部区域 */}
          <View style={styles.topSection}>
            {/* 标题栏 */}
            <View style={styles.titleBar}>
              <ATrack onPress={handleCancel}>
                <Text style={styles.cancelText}>取消</Text>
              </ATrack>
              <Text style={styles.titleText}>{title}</Text>
              <ATrack onPress={handleConfirm}>
                <Text style={styles.confirmText}>确认</Text>
              </ATrack>
            </View>

            {/* 搜索栏 */}
            <View style={styles.searchContainer}>
              <Image
                source={{
                  uri: 'https://static.soyoung.com/sy-pre/1y4a2d4fr2l31-1711955400686.png',
                }}
                style={styles.searchIcon}
                resizeMode='contain'
              />
              <TextInput
                style={styles.searchInput}
                placeholder='请输入项目名称'
                placeholderTextColor='#DEDEDE'
                value={searchValue}
                onChangeText={setSearchValue}
                onSubmitEditing={handleClickToSearch}
                returnKeyType='done'
              />
              {searchValue.length > 0 && (
                <ATrack
                  style={styles.clearIconWrapper}
                  onPress={handleClearIcon}
                >
                  <Image
                    source={{
                      uri: 'https://static.soyoung.com/sy-pre/219l297us0uts-1711617000689.png',
                    }}
                    style={styles.clearIcon}
                    resizeMode='contain'
                  />
                </ATrack>
              )}
            </View>
          </View>

          {/* 主体内容 */}
          <View style={styles.bodySection}>
            {projectList.length > 0 ? (
              <>
                {/* 左侧分类标签 */}
                <ScrollView
                  style={styles.tabContainer}
                  showsVerticalScrollIndicator={false}
                >
                  {projectList.map((item, index) => (
                    <ATrack
                      key={index}
                      style={[
                        styles.tabItem,
                        currentTab === index && styles.tabItemActive,
                      ]}
                      onPress={() => handleClickTab(index)}
                    >
                      {hasCategorySelected(item.children || []) && (
                        <View style={styles.tabItemPoint} />
                      )}
                      <Text
                        style={[
                          styles.tabItemText,
                          currentTab === index && styles.tabItemTextActive,
                        ]}
                      >
                        {item.name}
                      </Text>
                    </ATrack>
                  ))}
                </ScrollView>

                {/* 右侧项目列表 */}
                <ScrollView
                  style={styles.mainContainer}
                  showsVerticalScrollIndicator={false}
                >
                  <View style={styles.mainContent}>
                    <View style={styles.projectButtonList}>
                      {(projectList[currentTab]?.children || []).map(
                        (project: any) => (
                          <ATrack
                            key={project.id}
                            style={styles.projectButton}
                            onPress={() =>
                              updateActive({
                                id: project.id,
                                name: project.name,
                              })
                            }
                          >
                            <View
                              style={[
                                styles.projectButtonContent,
                                isProjectSelected(project.id) &&
                                  styles.projectButtonContentActive,
                              ]}
                            >
                              <Text
                                style={[
                                  styles.projectButtonText,
                                  isProjectSelected(project.id) &&
                                    styles.projectButtonTextActive,
                                ]}
                              >
                                {project.name}
                              </Text>
                              {isProjectSelected(project.id) && (
                                <Image
                                  source={{
                                    uri: 'https://static.soyoung.com/sy-design/3o6q6zpv0xqzv1753259861585.png',
                                  }}
                                  style={styles.checkIcon}
                                />
                              )}
                            </View>
                          </ATrack>
                        )
                      )}
                    </View>
                  </View>
                </ScrollView>
              </>
            ) : (
              <Empty />
            )}
          </View>
        </>
      );
    };

    return (
      <Modal
        {...modalAnimation}
        isVisible={visible}
        onBackdropPress={handleCancel}
        animationIn='slideInUp'
        animationOut='slideOutDown'
        style={styles.modal}
        useNativeDriver={true}
        avoidKeyboard={false}
        statusBarTranslucent={Platform.OS === 'android'}
      >
        {Platform.OS === 'android' ? (
          <View
            style={{
              flex: 1,
              justifyContent: 'flex-end', // 使用固定定位避免键盘影响
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
            }}
          >
            <View
              style={{
                ...styles.container,
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
              }}
            >
              {renderContent()}
            </View>
          </View>
        ) : (
          <View style={styles.container}>{renderContent()}</View>
        )}
      </Modal>
    );
  }
);

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  container: {
    width: '100%',
    height: getRealSize(547), // 1094rpx
    backgroundColor: '#ffffff',
  },
  topSection: {
    paddingBottom: getRealSize(10), // 20rpx
  },
  titleBar: {
    height: getRealSize(44), // 88rpx
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: getRealSize(15), // 30rpx
  },
  cancelText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15), // 30rpx
    color: '#999999',
    fontWeight: '400',
  },
  titleText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(16), // 32rpx
    color: '#333333',
    fontWeight: '500',
  },
  confirmText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15), // 30rpx
    color: '#61B43E',
    fontWeight: '400',
  },
  searchContainer: {
    height: getRealSize(36), // 88rpx
    backgroundColor: '#f8f8f8',
    borderRadius: getRealSize(4), // 8rpx
    marginHorizontal: getRealSize(15), // 30rpx
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getRealSize(10), // 20rpx
  },
  searchIcon: {
    width: getRealSize(14), // 28rpx
    height: getRealSize(14), // 28rpx
    marginRight: getRealSize(5), // 10rpx
  },
  searchInput: {
    flex: 1,
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(14), // 28rpx
    color: '#333333',
    padding: 0,
  },
  clearIconWrapper: {
    padding: getRealSize(5), // 10rpx
  },
  clearIcon: {
    width: getRealSize(12), // 24rpx
    height: getRealSize(12), // 24rpx
  },
  bodySection: {
    flex: 1,
    flexDirection: 'row',
  },
  tabContainer: {
    width: getRealSize(90), // 190rpx
    maxWidth: getRealSize(90),
    minWidth: getRealSize(90),
    backgroundColor: '#f8f8f8',
  },
  tabItem: {
    height: getRealSize(44), // 88rpx
    paddingHorizontal: getRealSize(10), // 20rpx
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  tabItemTextActive: {
    color: '#61B43E',
  },
  tabItemActive: {
    backgroundColor: '#ffffff',
  },
  tabItemPoint: {
    width: getRealSize(4), // 8rpx
    height: getRealSize(4), // 8rpx
    backgroundColor: '#61B43E',
    borderRadius: getRealSize(2), // 4rpx
    marginRight: getRealSize(5), // 10rpx
  },
  tabItemText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12), // 24rpx
    color: '#333333',
    fontWeight: '400',
    textAlign: 'center',
  },
  mainContainer: {
    flex: 1,
  },
  mainContent: {
    padding: getRealSize(15), // 30rpx
  },
  projectButtonList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  projectButton: {
    marginRight: getRealSize(10), // 20rpx
    marginBottom: getRealSize(10), // 20rpx
  },
  projectButtonContent: {
    minHeight: getRealSize(30), // 60rpx
    backgroundColor: '#f8f8f8',
    paddingVertical: getRealSize(8), // 16rpx
    paddingHorizontal: getRealSize(12), // 24rpx
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: getRealSize(2), // 2rpx
    borderColor: 'transparent',
    position: 'relative',
    width: '100%',
  },
  projectButtonContentActive: {
    backgroundColor: '#ffffff',
    borderWidth: getRealSize(2), // 2rpx
    borderColor: '#333333',
  },
  projectButtonText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(12), // 24rpx
    color: '#333333',
    fontWeight: '400',
    textAlign: 'center',
  },
  projectButtonTextActive: {
    color: '#333333',
  },
  checkIcon: {
    position: 'absolute',
    bottom: getRealSize(-1),
    right: getRealSize(-1),
    width: getRealSize(18),
    height: getRealSize(13),
  },
});

export default ProjectPopup;
