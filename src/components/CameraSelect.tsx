import { getRealSize } from '@/common/utils';
import React, { useCallback, useState } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Text,
  Image,
  ActivityIndicator,
} from 'react-native';
import Modal from 'react-native-modal';
import { modalAnimation } from '@/constant/modal_animation';
import { ATrack } from '@soyoung/react-native-container';
import ImagePicker, {
  ImageOrVideo,
} from '@soyoung/react-native-image-crop-picker';
import { getNativeLoginInfo } from '@/common/getNativeLoginInfo';
import { FetchModule, nativeGetEnvDomain } from '@soyoung/react-native-base';
import { Bridge } from '@/common/bridge';
export interface FileItem {
  filename: string;
  height: number;
  mime: string;
  modificationDate: string;
  path: string;
  size: number;
  width: number;
}
// 配置信息（对应 sy-chain 中的 config）
const UPLOAD_CONFIG = {
  sys: '8',
  appId: '125',
  lver: '3.0.0',
  uploadUrl: 'chain-wxapp/v1/upload/file',
};
// 图片数据类型定义
export interface ImageItem {
  img_url?: string; // 图片URL（普通图片或封面图）
  video_url?: string; // 视频文件URL
  type: 1 | 2; // 1: 图片, 2: 视频
  contentType: 'image' | 'video'; // 内容类型：图片或视频
}
export interface CommonSelectOption {
  label: string;
  value: string | number;
}

export interface ActiveItem {
  id: number | string;
  name: string;
}

interface CameraSelectProps {
  openPanel: boolean;
  onClose: () => void;
  onResult?: (result: any) => void; // 选择结果回调
  mediaType?: 'photo' | 'video' | 'any'; // 媒体类型
  multiple?: boolean; // 是否支持多选
  maxFiles?: number; // 最大文件数
  minFiles?: number; // 最小文件数
}

const CameraSelect: React.FC<CameraSelectProps> = ({
  openPanel,
  onClose,
  onResult,
  mediaType = 'photo',
  multiple = false,
  maxFiles = 1,
  minFiles = 1,
}) => {
  // 添加 loading 状态管理
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [loadingText, setLoadingText] = useState<string>('');

  // 上传单张图片的方法
  const uploadSingleImage = useCallback(
    async (fileItem: ImageOrVideo): Promise<string> => {
      return new Promise(async (resolve, reject) => {
        try {
          // 获取登录信息
          const loginInfo = await getNativeLoginInfo();
          const domain = await nativeGetEnvDomain(
            FetchModule.API_DOMAIN_TYPE.API
          ).catch(() => 'https://prime-crm.soyoung.com/');

          // 创建 FormData
          const formData = new FormData();

          // 添加文件
          formData.append('file', {
            uri: fileItem.path,
            type: fileItem.mime,
            name: fileItem.filename || fileItem.path.split('/').pop(),
          } as any);

          // 添加其他参数（对应 sy-chain 中的 formData 参数）
          formData.append('_time', Date.now().toString());
          formData.append('sys', UPLOAD_CONFIG.sys);
          formData.append('app_id', UPLOAD_CONFIG.appId);
          formData.append('lver', UPLOAD_CONFIG.lver);
          formData.append('user_id', loginInfo.user_id || '');
          formData.append('token', loginInfo.token || '');
          formData.append('tenant_id', loginInfo.tenant_id || '');
          formData.append('lat', loginInfo.lat || '');
          formData.append('lng', loginInfo.lng || '');
          formData.append('staff_user_id', loginInfo.staff_user_id || '');

          // 发送上传请求
          const response = await fetch(`${domain}${UPLOAD_CONFIG.uploadUrl}`, {
            method: 'POST',
            body: formData,
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          });

          const result = await response.json();

          if (result?.errorCode === 0) {
            resolve(result.responseData);
          } else {
            if (__DEV__) {
              // eslint-disable-next-line no-console
              console.log('result', `${domain}${UPLOAD_CONFIG.uploadUrl}`);
            }
            reject(new Error(result.errorMsg || '上传失败'));
          }
        } catch (error) {
          reject(error);
        }
      });
    },
    []
  );

  // 批量上传图片的方法（修复索引映射问题）
  const batchUploadImages = useCallback(
    async (
      fileList: ImageOrVideo[],
      originalFileCount?: number // 新增参数：原始文件数量（用于进度显示）
    ): Promise<(string | null)[] | undefined> => {
      if (fileList.length === 0) return undefined;

      // 使用原始文件数量显示进度，如果未提供则使用文件列表长度
      const displayFileCount = originalFileCount || fileList.length;

      // 显示加载提示
      setLoadingText(`上传中 0/${displayFileCount}`);

      const batchSize = 3;
      const uploadResults: (string | null)[] = new Array(fileList.length).fill(
        null
      ); // 保持索引对应
      let uploadedCount = 0;
      let processedOriginalFiles = 0; // 记录已处理的原始文件数

      for (let i = 0; i < fileList.length; i += batchSize) {
        const batch = fileList.slice(i, i + batchSize);
        const batchPromises = batch.map((fileItem: ImageOrVideo, batchIndex) =>
          uploadSingleImage(fileItem).then(
            url => ({
              status: 'fulfilled' as const,
              value: url,
              index: i + batchIndex,
            }),
            error => ({
              status: 'rejected' as const,
              reason: error,
              index: i + batchIndex,
            })
          )
        );

        const batchResults = await Promise.all(batchPromises);

        // 将结果按正确的索引位置保存
        batchResults.forEach(result => {
          if (result.status === 'fulfilled') {
            uploadResults[result.index] = result.value;
          } else {
            uploadResults[result.index] = null;
            // 上传失败时输出错误日志
            if (__DEV__) {
              // eslint-disable-next-line no-console
              console.log('上传失败的文件:', result.reason);
            }
          }
        });

        uploadedCount += batch.length;

        // 如果有原始文件数量参数，计算已处理的原始文件数
        if (originalFileCount) {
          // 根据当前批次推算已处理的原始文件数
          // 这里简化处理：每处理完一批文件，就增加对应的原始文件计数
          processedOriginalFiles = Math.min(
            Math.ceil(uploadedCount / (fileList.length / originalFileCount)),
            originalFileCount
          );
          setLoadingText(
            `上传中 ${processedOriginalFiles}/${displayFileCount}`
          );
        } else {
          // 更新进度提示
          setLoadingText(`上传中 ${uploadedCount}/${displayFileCount}`);
        }
      }

      setLoadingText('上传完成');

      const successCount = uploadResults.filter(url => url !== null).length;
      const totalCount = fileList.length;
      const failCount = totalCount - successCount;

      if (failCount > 0) {
        // 如果有原始文件数量，使用原始文件数量计算成功和失败数
        if (originalFileCount) {
          const originalSuccessCount = Math.floor(
            successCount * (originalFileCount / fileList.length)
          );
          const originalFailCount = originalFileCount - originalSuccessCount;
          Bridge.showToast(
            `成功上传${originalSuccessCount}张，失败${originalFailCount}张`
          );
        } else {
          Bridge.showToast(`成功上传${successCount}张，失败${failCount}张`);
        }
      } else {
        // 成功上传时，使用原始文件数量显示
        const displaySuccessCount = originalFileCount || successCount;
        Bridge.showToast(
          `成功上传${displaySuccessCount}${mediaType === 'video' ? '个' : '张'}${mediaType === 'video' ? '视频' : '图片'}`
        );
      }
      return uploadResults;
    },
    [uploadSingleImage, mediaType]
  );

  // 打开相册
  const handleOpenGallery = () => {
    const pickerOptions: any = {
      multiple,
      maxFiles,
      minFiles,
      mediaType,
    };

    // 如果是视频类型，添加视频封面配置
    if (mediaType === 'video') {
      pickerOptions.videoCover = {
        enabled: true,
      };
    }

    ImagePicker.openPicker(pickerOptions)
      .then(result => {
        if ((result as ImageOrVideo[]).length > maxFiles) {
          Bridge.showToast(`最多上传${maxFiles}张图片或视频`);
          return;
        }
        setIsLoading(true);
        setLoadingText('正在处理...');
        uploadImages(result);
      })
      .catch(error => {
        if (__DEV__) {
          // eslint-disable-next-line no-console
          console.log('Gallery picker error:', error);
        }
        onClose();
      });
  };

  // 打开相机
  const handleOpenCamera = () => {
    const cameraOptions: any = {
      mediaType,
    };

    // 如果是视频类型，添加视频封面配置
    if (mediaType === 'video') {
      cameraOptions.videoCover = {
        enabled: true,
      };
    }

    ImagePicker.openCamera(cameraOptions)
      .then(result => {
        setIsLoading(true);
        setLoadingText('正在处理...');
        // 统一处理为数组格式
        const resultArray = Array.isArray(result) ? result : [result];
        uploadImages(resultArray);
      })
      .catch(error => {
        if (__DEV__) {
          // eslint-disable-next-line no-console
          console.log('Camera picker error:', error);
        }
        onClose();
      });
  };

  const uploadImages = async (result: ImageOrVideo | ImageOrVideo[]) => {
    const resultArray = Array.isArray(result) ? result : [result];
    const allFilesToUpload: ImageOrVideo[] = [];
    const videoCoverMap: { [videoIndex: number]: number } = {}; // 记录视频对应的封面图在上传列表中的位置

    // 处理文件列表，提取视频封面
    resultArray.forEach((item, index) => {
      allFilesToUpload.push(item);

      // 如果是视频且有封面图，添加封面图到上传列表
      if (mediaType === 'video' && (item as any).videoCover?.path) {
        const coverItem = {
          ...item,
          path: (item as any).videoCover.path,
          filename: `video_cover_${index}_${Date.now()}.jpg`,
          mime: 'image/jpeg',
        } as ImageOrVideo;
        console.log('coverItem', coverItem);
        videoCoverMap[index] = allFilesToUpload.length; // 记录封面图位置
        allFilesToUpload.push(coverItem);
      }
    });

    // 批量上传所有文件（包括视频和封面图）
    const uploadedUrls = await batchUploadImages(
      allFilesToUpload,
      resultArray.length
    );

    if (uploadedUrls?.length) {
      const organizedResults: ImageItem[] = [];

      resultArray.forEach((_, index) => {
        if (mediaType === 'video') {
          // 视频模式：合并视频和封面图URL
          const videoUrl = uploadedUrls[index]; // 直接获取视频URL
          const coverIndex = videoCoverMap[index];
          const coverUrl =
            coverIndex !== undefined ? uploadedUrls[coverIndex] : null;

          // 只有视频上传成功才添加到结果中
          if (videoUrl) {
            organizedResults.push({
              video_url: videoUrl,
              img_url: coverUrl || undefined, // 封面图可能为null，转为undefined
              type: 2,
              contentType: 'video',
            });
          }
        } else {
          // 图片模式：使用img_url字段
          const imageUrl = uploadedUrls[index]; // 直接获取图片URL
          if (imageUrl) {
            organizedResults.push({
              img_url: imageUrl,
              type: 1,
              contentType: 'image',
            });
          }
        }
      });

      onResult?.(organizedResults);
    }
    // 关闭 loading 状态
    setIsLoading(false);
    setLoadingText('');
    onClose();
  };

  return (
    <Modal
      isVisible={openPanel}
      {...modalAnimation}
      deviceWidth={Dimensions.get('window').width}
      animationIn='slideInUp'
      animationOut='slideOutDown'
      style={styles.modal}
      useNativeDriver={true}
      onBackdropPress={isLoading ? undefined : onClose}
    >
      <View style={styles.modalContent}>
        {isLoading ? (
          // Loading 界面
          <View style={styles.loadingContainer}>
            <ActivityIndicator size='large' color='#333' />
            <Text style={styles.loadingText}>{loadingText}</Text>
          </View>
        ) : (
          // 正常选择界面
          <>
            <View style={styles.header}>
              <View style={styles.placholder} />
              <Text style={styles.headerTitle}>
                {mediaType === 'photo' ? '选择图片' : '选择视频'}
              </Text>
              <ATrack onPress={onClose}>
                <Image
                  style={styles.closeIcon}
                  source={{
                    uri: 'https://static.soyoung.com/sy-design/bzsokyai5osd1753688976847.png',
                  }}
                />
              </ATrack>
            </View>
            <View style={styles.optionContainer}>
              <View style={styles.optionItem}>
                <ATrack style={styles.optionButton} onPress={handleOpenCamera}>
                  <Text style={styles.optionText}>拍照</Text>
                </ATrack>
              </View>
              <View style={[styles.optionItem, styles.lastOptionItem]}>
                <ATrack style={styles.optionButton} onPress={handleOpenGallery}>
                  <Text style={styles.optionText}>从手机相册选择</Text>
                </ATrack>
              </View>
            </View>
          </>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalContent: {
    backgroundColor: '#fff',
    paddingBottom: getRealSize(34), // 适配安全区域
  },
  placholder: {
    width: getRealSize(16),
  },
  header: {
    paddingHorizontal: getRealSize(15),
    paddingVertical: getRealSize(20),
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: getRealSize(16),
    fontWeight: '600',
    color: '#333',
  },
  closeIcon: {
    width: getRealSize(16),
    height: getRealSize(16),
  },
  optionContainer: {
    paddingHorizontal: getRealSize(15),
  },
  optionItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  lastOptionItem: {
    borderBottomWidth: 0,
  },
  optionButton: {
    paddingVertical: getRealSize(18),
    paddingHorizontal: getRealSize(20),
    alignItems: 'center',
  },
  optionText: {
    fontSize: getRealSize(16),
    color: '#333',
  },
  loadingContainer: {
    paddingVertical: getRealSize(60),
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: getRealSize(16),
    color: '#333',
    marginTop: getRealSize(12),
    textAlign: 'center',
  },
});

export default CameraSelect;
