import React, { useEffect, useState, useCallback } from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import { getRealSize } from '../common/utils';
import { ATrack } from '@soyoung/react-native-container';

/**
 * 单选下拉组件
 * 用于展示单选选择器，支持显示选中值和展开状态指示
 */

export interface SingleSelectItem {
  id: number;
  name: string;
}

export interface SingleSelectProps {
  /** 当前选中的项 */
  active?: SingleSelectItem;
  /** 是否展开面板状态 */
  openPanel?: boolean;
  /** 占位符文字 */
  placeholder?: string;
  /** 点击回调 */
  onPress?: () => void;
}

const SingleSelect: React.FC<SingleSelectProps> = ({
  active = { id: NaN, name: '' },
  placeholder = '请选择',
  onPress,
}) => {
  const [compActive, setCompActive] = useState<SingleSelectItem>(active);

  // 监听 active 变化并同步到内部状态
  useEffect(() => {
    setCompActive(active || { id: NaN, name: '' });
  }, [active]);

  const handleOpen = useCallback(() => {
    onPress?.();
  }, [onPress]);

  // 判断是否有有效选中值
  const hasValidValue = compActive.id || compActive.id === 0;

  return (
    <ATrack style={styles.container} onPress={handleOpen}>
      {/* 显示选中值或占位符 */}
      {hasValidValue ? (
        <Text style={styles.selectedText} numberOfLines={1}>
          {compActive.name}
        </Text>
      ) : (
        <Text style={styles.placeholderText}>{placeholder}</Text>
      )}

      {/* 箭头指示器 */}
      <View style={styles.arrowWrapper}>
        <Image
          source={{
            uri: 'https://static.soyoung.com/sy-design/6eboz32amcrz1753416667918.png', // 展开状态
          }}
          style={styles.arrowImage}
          resizeMode='contain'
        />
      </View>
    </ATrack>
  );
};

const styles = StyleSheet.create({
  container: {
    height: getRealSize(38), // 76rpx
    backgroundColor: '#f8f8f8',
    paddingLeft: getRealSize(10), // 20rpx
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedText: {
    flex: 1,
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13), // 30rpx
    color: '#161616',
    fontWeight: '400',
    marginRight: getRealSize(5),
  },
  placeholderText: {
    flex: 1,
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13), // 30rpx
    color: '#dedede',
    fontWeight: '400',
    marginRight: getRealSize(5),
  },
  arrowWrapper: {
    width: getRealSize(38), // 76rpx
    height: getRealSize(38), // 76rpx
    justifyContent: 'center',
    alignItems: 'center',
  },
  arrowImage: {
    width: getRealSize(9), // 18rpx
    height: getRealSize(9), // 18rpx
  },
});

export default SingleSelect;
