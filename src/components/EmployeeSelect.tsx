import React, { useState, useRef, useEffect, useCallback } from 'react';
import { View } from 'react-native';
import SingleSelect, { SingleSelectItem } from './SingleSelect';
import MultiSelect, { MultiSelectItem } from './MultiSelect';
import EmployeePopup, {
  EmployeePopupRef,
  ActiveItem,
  Active,
} from './EmployeePopup';

/**
 * 员工选择组件
 * 支持单选/多选模式，整合了 SingleSelect、MultiSelect 和 EmployeePopup
 */

export interface EmployeeSelectProps {
  /** 当前选中的员工 */
  active?: Active;
  /** 选择器标题 */
  title?: string;
  /** 员工角色筛选 */
  role?: string;
  /** 是否多选模式 */
  multiple?: boolean;
  /** 选中值变化回调 */
  onActiveChange?: (active: Active) => void;
  /** 选择变化回调（与 onActiveChange 相同，提供兼容性） */
  onChange?: (active: Active) => void;
}

const EmployeeSelect: React.FC<EmployeeSelectProps> = ({
  active,
  title = '',
  role = '',
  multiple = false,
  onActiveChange,
  onChange,
}) => {
  const [compActive, setCompActive] = useState<Active>(() => {
    if (multiple) {
      return Array.isArray(active) && active.length > 0 ? active : [];
    } else {
      return !Array.isArray(active) && active?.name
        ? active
        : { id: NaN, name: '' };
    }
  });

  const [openPanel, setOpenPanel] = useState(false);
  const employeePopupRef = useRef<EmployeePopupRef>(null);

  // 监听外部 active 变化并同步到内部状态
  useEffect(() => {
    if (multiple) {
      setCompActive(Array.isArray(active) && active.length > 0 ? active : []);
    } else {
      setCompActive(
        !Array.isArray(active) && active?.name ? active : { id: NaN, name: '' }
      );
    }
  }, [active, multiple]);

  // 处理打开弹窗
  const handleOpen = useCallback(() => {
    setOpenPanel(true);
    employeePopupRef.current?.open();
  }, []);

  // 处理关闭弹窗
  const handleClose = useCallback(() => {
    setOpenPanel(false);
  }, []);

  // 处理多选模式下的删除操作
  const handleRemove = useCallback(
    (id: number) => {
      if (multiple && Array.isArray(compActive)) {
        const newActive = compActive.filter(
          (item: MultiSelectItem) => item.id !== id
        );
        setCompActive(newActive);

        // 触发回调
        onActiveChange?.(newActive);
        onChange?.(newActive);
      }
    },
    [compActive, multiple, onActiveChange, onChange]
  );

  // 处理选择变化
  const handleActiveChange = useCallback(
    (newActive: Active) => {
      setCompActive(newActive);

      // 触发回调
      onActiveChange?.(newActive);
      onChange?.(newActive);
    },
    [onActiveChange, onChange]
  );

  return (
    <View>
      {/* 单选模式显示 SingleSelect */}
      {!multiple && (
        <SingleSelect
          openPanel={openPanel}
          active={compActive as SingleSelectItem}
          onPress={handleOpen}
        />
      )}

      {/* 多选模式显示 MultiSelect */}
      {multiple && (
        <MultiSelect
          buttonText={`选择${title}`}
          active={compActive as MultiSelectItem[]}
          onPress={handleOpen}
          onRemove={handleRemove}
        />
      )}

      {/* 员工选择弹窗 */}
      <EmployeePopup
        ref={employeePopupRef}
        active={compActive}
        title={title}
        role={role}
        multiple={multiple}
        onActiveChange={handleActiveChange}
        onClose={handleClose}
      />
    </View>
  );
};

export default EmployeeSelect;

// 导出类型供外部使用
export type { ActiveItem, Active };
