import React, { memo, useCallback, useMemo } from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { ATrack } from '@soyoung/react-native-container';
import { getRealSize } from '../common/utils';

interface Option {
  label: string;
  value: number | string;
}

interface ButtonSelectProps {
  options: Option[];
  value: (string | number)[] | string | number | null;
  multiple?: boolean;
  noCancle?: boolean;
  onChange: (value: (string | number)[] | string | number | null) => void;
}

// 提取按钮组件
const SelectButton = memo<{
  option: Option;
  isActive: boolean;
  onPress: (value: string | number) => void;
}>(({ option, isActive, onPress }) => (
  <ATrack style={styles.buttonContainer} onPress={() => onPress(option.value)}>
    <View style={[styles.button, isActive && styles.activeButton]}>
      <Text
        style={[styles.buttonText, isActive && styles.activeButtonText]}
        numberOfLines={1}
      >
        {option.label}
      </Text>
      {isActive ? (
        <Image
          source={{
            uri: 'https://static.soyoung.com/sy-design/3o6q6zpv0xqzv1753259861585.png',
          }}
          style={styles.checkIcon}
        />
      ) : null}
    </View>
  </ATrack>
));

SelectButton.displayName = 'SelectButton';

const ButtonSelect: React.FC<ButtonSelectProps> = memo(
  ({ options, value, multiple = false, noCancle = false, onChange }) => {
    // 缓存激活状态检查函数
    const isActive = useCallback(
      (optionValue: string | number): boolean => {
        if (multiple) {
          return Array.isArray(value) && value.includes(optionValue);
        } else {
          return value === optionValue;
        }
      },
      [multiple, value]
    );

    // 处理点击事件
    const handlePress = useCallback(
      (optionValue: string | number) => {
        if (!multiple) {
          // 单选模式
          if (value === optionValue) {
            if (noCancle) return;
            onChange(null);
          } else {
            onChange(optionValue);
          }
        } else {
          // 多选模式
          const currentValues = Array.isArray(value) ? value : [];
          if (currentValues.includes(optionValue)) {
            onChange(currentValues.filter(v => v !== optionValue));
          } else {
            onChange([...currentValues, optionValue]);
          }
        }
      },
      [multiple, value, noCancle, onChange]
    );

    // 缓存渲染的按钮列表
    const renderButtons = useMemo(() => {
      return options.map((option, index) => (
        <SelectButton
          key={`${option.value}-${index}`}
          option={option}
          isActive={isActive(option.value)}
          onPress={handlePress}
        />
      ));
    }, [options, isActive, handlePress]);

    return <View style={styles.container}>{renderButtons}</View>;
  }
);

ButtonSelect.displayName = 'ButtonSelect';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  buttonContainer: {
    marginBottom: getRealSize(10),
    marginHorizontal: getRealSize(5),
  },
  button: {
    width: getRealSize(78),
    height: getRealSize(38),
    backgroundColor: '#f5f5f5',
    borderWidth: 2,
    borderColor: 'transparent',
    borderStyle: 'solid',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  activeButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#333333',
    borderStyle: 'solid',
  },
  buttonText: {
    fontSize: getRealSize(13),
    color: '#333333',
    textAlign: 'center',
  },
  activeButtonText: {},
  checkIcon: {
    position: 'absolute',
    bottom: getRealSize(-1),
    right: getRealSize(-1),
    width: getRealSize(18),
    height: getRealSize(13),
  },
});

export default ButtonSelect;
