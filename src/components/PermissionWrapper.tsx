import React from 'react';
import { View, Text, Image, StyleSheet, SafeAreaView } from 'react-native';
import { NavigationProp } from '@react-navigation/native';
import { getRealSize } from '@/common/utils';
import { ATrack } from '@soyoung/react-native-container';
import { back } from '@soyoung/react-native-base';
import { switchTab } from '@/common/jumpPage';

interface PermissionWrapperProps {
  navigation?: NavigationProp<any>;
  hasPermission: boolean;
  bgColor?: string;
  children?: React.ReactNode;
  _permissionKey?: string;
  tabBar?: boolean;
}

/**
 * 权限包装组件
 * 根据权限状态决定显示内容还是无权限页面
 */
const PermissionWrapper: React.FC<PermissionWrapperProps> = ({
  navigation: _navigation,
  hasPermission,
  children,
  bgColor = '#ffffff',
  _permissionKey,
  tabBar = false,
}) => {
  const handleBackToHome = () => {
    if (tabBar) {
      switchTab('RN:APPENTRY');
    } else {
      back();
    }
  };

  if (!hasPermission) {
    return (
      <View style={[styles.content, { backgroundColor: bgColor }]}>
        {/* 锁图标 */}
        <View style={styles.iconContainer}>
          <Image
            source={{
              uri: 'https://static.soyoung.com/sy-design/hwkmz54krx581754309175651.png',
            }}
            style={styles.icon}
          />
        </View>

        {/* 无权限文本 */}
        <Text style={styles.permissionText}>暂无权限</Text>

        {/* 回到首页按钮 */}
        <ATrack style={styles.buttonContainer} onPress={handleBackToHome}>
          <View style={styles.button}>
            <Text style={styles.buttonText}>返回</Text>
          </View>
        </ATrack>
      </View>
    );
  }

  return <View style={{ flex: 1 }}>{children}</View>;
};

const styles = StyleSheet.create({
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: getRealSize(20),
  },
  iconContainer: {
    marginBottom: getRealSize(20),
  },
  icon: {
    width: getRealSize(35),
    height: getRealSize(35),
  },
  permissionText: {
    fontSize: getRealSize(14),
    color: '#030303',
    fontWeight: '500',
    fontFamily: 'PingFangSC-Medium',
    marginBottom: getRealSize(30),
  },
  buttonContainer: {
    width: getRealSize(170),
    height: getRealSize(38),
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    width: '100%',
    height: getRealSize(44),
    borderWidth: getRealSize(1),
    borderColor: '#333333',
    borderRadius: getRealSize(4),
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    fontSize: getRealSize(12),
    color: '#333333',
    fontFamily: 'PingFangSC-Regular',
  },
});

export default PermissionWrapper;
