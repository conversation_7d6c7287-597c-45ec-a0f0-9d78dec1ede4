import React, { useState, useRef, useCallback, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import SingleSelect from './SingleSelect';
import MultiSelect from './MultiSelect';
import CommonPopup, {
  CommonPopupRef,
  Options,
  ActiveItem,
  Active,
} from './CommonPopup';

/**
 * 通用选择组件属性
 */
export interface CommonSelectProps {
  /** 当前激活的项 */
  active: Active;
  /** 选择器标题 */
  title: string;
  /** 是否多选模式 */
  multiple?: boolean;
  /** 选项列表 */
  options: Options[];
  /** 激活状态变更回调 */
  onActiveChange?: (active: Active) => void;
  /** 选择变更回调 */
  onChange?: (active: Active) => void;
}

export interface CommonSelectOption {
  label: string;
  value: string | number;
}

/**
 * 通用选择组件
 * 整合了 SingleSelect、MultiSelect 和 CommonPopup，提供统一的选择器入口
 * 支持单选/多选模式切换，包含完整的弹窗选择功能
 */
const CommonSelect: React.FC<CommonSelectProps> = ({
  active,
  title,
  multiple = false,
  options,
  onActiveChange,
  onChange,
}) => {
  const [openPanel, setOpenPanel] = useState(false);
  const [compActive, setCompActive] = useState<Active>(active);
  const commonPopupRef = useRef<CommonPopupRef>(null);
  const prevActiveRef = useRef<Active>(active);

  // 监听外部 active 变化
  useEffect(() => {
    if (multiple) {
      const activeArray = active as ActiveItem[];
      const newActive = activeArray.length > 0 ? activeArray : [];
      setCompActive(newActive);
      prevActiveRef.current = newActive;
    } else {
      const activeItem = active as ActiveItem;
      const newActive =
        activeItem && activeItem.name ? activeItem : { id: NaN, name: '' };
      setCompActive(newActive);
      prevActiveRef.current = newActive;
    }
  }, [active, multiple]);

  // 监听内部状态变化并向外通知
  useEffect(() => {
    const currentStr = JSON.stringify(compActive);
    const prevStr = JSON.stringify(prevActiveRef.current);

    // 只有当compActive发生实际变化且不是由外部active变化引起的时候才通知
    if (currentStr !== prevStr && currentStr !== JSON.stringify(active)) {
      onActiveChange?.(compActive);
      onChange?.(compActive);
      prevActiveRef.current = compActive;
    }
  }, [compActive]); // 只依赖compActive

  /**
   * 打开弹窗
   */
  const handleOpen = useCallback(() => {
    setOpenPanel(true);
    commonPopupRef.current?.open();
  }, []);

  /**
   * 关闭弹窗
   */
  const handleClose = useCallback(() => {
    setOpenPanel(false);
  }, []);

  /**
   * 移除选中项（仅多选模式）
   */
  const handleRemove = useCallback(
    (id: number) => {
      if (multiple) {
        const activeArray = compActive as ActiveItem[];
        const newActive = activeArray.filter(item => item.id !== id);
        setCompActive(newActive);
      } else {
        const activeItem = compActive as ActiveItem;
        const newActive =
          activeItem && activeItem.name ? activeItem : { id: NaN, name: '' };
        setCompActive(newActive);
      }
    },
    [compActive, multiple]
  );

  /**
   * 弹窗选择完成
   */
  const handlePopupChange = useCallback((newActive: Active) => {
    setCompActive(newActive);
    setOpenPanel(false);
  }, []);

  return (
    <View style={styles.container}>
      {/* 根据模式显示不同的选择器 */}
      {!multiple ? (
        <SingleSelect
          openPanel={openPanel}
          active={compActive as ActiveItem}
          onPress={handleOpen}
        />
      ) : (
        <MultiSelect
          buttonText={`选择${title}`}
          active={compActive as ActiveItem[]}
          onPress={handleOpen}
          onRemove={handleRemove}
        />
      )}

      {/* 通用弹窗 */}
      <CommonPopup
        ref={commonPopupRef}
        active={compActive}
        title={title}
        multiple={multiple}
        options={options}
        onClose={handleClose}
        onActiveChange={handlePopupChange}
        onChange={handlePopupChange}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
});

export default CommonSelect;

// 导出相关类型
export type { Options, ActiveItem, Active };
