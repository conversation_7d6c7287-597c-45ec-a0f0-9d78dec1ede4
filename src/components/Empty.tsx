import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import { getRealSize } from '@/common/utils';

interface EmptyProps {
  /**
   * 自定义文本内容
   */
  text?: string;
  /**
   * 自定义图片URI
   */
  imageUri?: string;
  /**
   * 自定义样式
   */
  style?: any;
}

/**
 * 空状态组件
 * 用于显示无数据时的空状态提示
 */
const Empty: React.FC<EmptyProps> = ({
  text = '暂无相关数据',
  imageUri = 'https://static.soyoung.com/sy-design/aqnomvpf3ki11753429315696.png',
  style,
}) => {
  return (
    <View style={[styles.empty, style]}>
      <Image
        source={{
          uri: imageUri,
        }}
        style={styles.emptyImg}
      />
      <Text style={styles.emptyText}>{text}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  empty: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyImg: {
    width: getRealSize(35),
    height: getRealSize(35),
    marginBottom: getRealSize(10),
  },
  emptyText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(14),
    color: '#030303',
    letterSpacing: 0,
    textAlign: 'center',
    fontWeight: '500',
    paddingBottom: getRealSize(60),
  },
});

export default Empty;
