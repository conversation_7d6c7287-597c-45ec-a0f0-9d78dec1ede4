import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useCallback,
  useEffect,
  useMemo,
} from 'react';
import {
  View,
  Text,
  Image,
  TextInput,
  FlatList,
  StyleSheet,
  ListRenderItem,
  Platform,
} from 'react-native';
import { flatListProps } from '@/constant/flatlist_props';
import Modal from 'react-native-modal';
import { modalAnimation } from '@/constant/modal_animation';
import { ATrack } from '@soyoung/react-native-container';
import { getRealSize } from '../common/utils';
import api, { FetchModule } from '../common/api';
import Empty from './Empty';

/**
 * 员工选择弹窗组件
 * 支持搜索、单选/多选模式、分页加载
 */

export interface ActiveItem {
  id: number;
  name: string;
}

export type Active = ActiveItem | ActiveItem[];

export interface StaffPopupProps {
  /** 当前选中的员工 */
  active?: Active;
  /** 弹窗标题 */
  title?: string;
  /** 是否多选模式 */
  multiple?: boolean;
  /** 选中值变化回调 */
  onActiveChange?: (active: Active) => void;
  /** 关闭弹窗回调 */
  onClose?: () => void;
}

interface StaffItem {
  staff_user_id: number;
  staff_name: string;
  avatar: string;
  departments: string;
}

export interface StaffPopupRef {
  open: () => void;
  close: () => void;
}

const StaffPopup = forwardRef<StaffPopupRef, StaffPopupProps>(
  ({ active, title = '', multiple = false, onActiveChange, onClose }, ref) => {
    const [visible, setVisible] = useState(false);
    const [listActive, setListActive] = useState<Active>(
      multiple
        ? Array.isArray(active)
          ? active
          : []
        : !Array.isArray(active)
          ? active || { id: NaN, name: '' }
          : { id: NaN, name: '' }
    );
    const [currentPage, setCurrentPage] = useState(1);
    const [searchValue, setSearchValue] = useState('');
    const [userList, setUserList] = useState<StaffItem[]>([]);
    const [hasMore, setHasMore] = useState(false);
    const [loading, setLoading] = useState(false);

    // 同步外部 active 到内部状态
    useEffect(() => {
      if (multiple) {
        setListActive(Array.isArray(active) ? active : []);
      } else {
        setListActive(
          !Array.isArray(active)
            ? active || { id: NaN, name: '' }
            : { id: NaN, name: '' }
        );
      }
    }, [active, multiple]);

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
      open: () => {
        setVisible(true);
        setCurrentPage(1);
        setUserList([]);
        getData();
      },
      close: () => {
        setVisible(false);
        setSearchValue('');
        setCurrentPage(1);
        setUserList([]);
        // 重置选中状态到初始值
        if (multiple) {
          setListActive(Array.isArray(active) ? active : []);
        } else {
          setListActive(
            !Array.isArray(active)
              ? active || { id: NaN, name: '' }
              : { id: NaN, name: '' }
          );
        }
        onClose?.();
      },
    }));

    // 更新选中状态
    const updateActive = useCallback(
      (data: ActiveItem) => {
        if (multiple && Array.isArray(listActive)) {
          const exists = listActive.find(item => item.id === data.id);
          let newActive: ActiveItem[];

          if (exists) {
            // 如果已选中，则取消选中
            newActive = listActive.filter(item => item.id !== data.id);
          } else {
            // 如果未选中，则添加选中
            newActive = [...listActive, data];
          }
          setListActive(newActive);
        } else {
          // 单选模式
          if ((listActive as ActiveItem).id === data.id) {
            setListActive({ id: NaN, name: '' });
          } else {
            setListActive(data);
          }
        }
      },
      [listActive, multiple]
    );

    // 获取员工数据
    const getData = useCallback(
      async (page = 1, isLoadMore = false, customSearchValue?: string) => {
        try {
          setLoading(true);
          const params: Record<string, string | number> = {
            page,
            staff_name:
              customSearchValue !== undefined ? customSearchValue : searchValue,
          };

          const { responseData, errorCode } = await api.pagefetch({
            path: '/chain-wxapp/v1/customer/getStaffList',
            params,
            method: FetchModule.Method.POST,
            isLoading: false,
          });

          if (errorCode === 0) {
            const newList = responseData?.list || [];
            const newHasMore = !!responseData?.has_more || false;

            if (isLoadMore) {
              setUserList(prev => [...prev, ...newList]);
            } else {
              setUserList(newList);
            }

            setHasMore(newHasMore);
            setCurrentPage(page);
          }
        } catch (error) {
          // console.error('获取员工列表失败:', error);
        } finally {
          setLoading(false);
        }
      },
      [searchValue]
    );

    // 搜索
    const handleClickToSearch = useCallback(() => {
      setCurrentPage(1);
      setUserList([]);
      getData(1, false);
    }, [getData]);

    // 清除搜索
    const handleClearIcon = useCallback(() => {
      setSearchValue('');
      setCurrentPage(1);
      setUserList([]);
      // 清除后重新获取数据，直接传入空字符串
      getData(1, false, '');
    }, [getData]);

    // 加载更多数据
    const loadMoreData = useCallback(() => {
      if (hasMore && !loading) {
        getData(currentPage + 1, true);
      }
    }, [hasMore, loading, currentPage, getData]);

    // 取消操作
    const handleCancel = useCallback(() => {
      setVisible(false);
      setSearchValue('');
      setCurrentPage(1);
      setUserList([]);
      // 重置选中状态到初始值
      if (multiple) {
        setListActive(Array.isArray(active) ? active : []);
      } else {
        setListActive(
          !Array.isArray(active)
            ? active || { id: NaN, name: '' }
            : { id: NaN, name: '' }
        );
      }
      onClose?.();
    }, [onClose, active, multiple]);

    // 确认操作
    const handleConfirm = useCallback(() => {
      onActiveChange?.(listActive);
      setVisible(false);
      setSearchValue('');
      setCurrentPage(1);
      setUserList([]);
      onClose?.();
    }, [listActive, onActiveChange, onClose]);

    // 检查是否选中
    const isSelected = useCallback(
      (item: StaffItem) => {
        if (multiple && Array.isArray(listActive)) {
          return listActive.some(
            selectedItem => selectedItem.id === item.staff_user_id
          );
        } else {
          return (
            !Array.isArray(listActive) && listActive?.id === item.staff_user_id
          );
        }
      },
      [listActive, multiple]
    );

    // FlatList 触底加载更多
    const handleEndReached = useCallback(() => {
      if (hasMore && !loading) {
        loadMoreData();
      }
    }, [hasMore, loading, loadMoreData]);

    // FlatList 数据项唯一标识
    const keyExtractor = useCallback((item: StaffItem) => {
      return `staff_${item.staff_user_id}`;
    }, []);

    // 渲染员工列表项
    const renderStaffItem: ListRenderItem<StaffItem> = useCallback(
      ({ item }) => (
        <ATrack
          onPress={() =>
            updateActive({
              id: item.staff_user_id,
              name: item.staff_name,
            })
          }
        >
          <View
            style={[styles.userCard, isSelected(item) && styles.userCardActive]}
          >
            {/* 头像部分 */}
            <View style={styles.userCardAvatar}>
              {item.avatar ? (
                <Image
                  source={{ uri: item.avatar }}
                  style={styles.userCardAvatarImage}
                />
              ) : (
                <View style={styles.userCardDefaultAvatar}>
                  <Text style={styles.userCardDefaultAvatarText}>
                    {item.staff_name?.slice(0, 1) || ''}
                  </Text>
                </View>
              )}
            </View>

            {/* 信息部分 */}
            <View style={styles.userCardInfo}>
              <Text style={styles.userCardTitle}>{item.staff_name}</Text>
              {item.departments ? (
                <Text style={styles.userCardSubtitle} numberOfLines={1}>
                  {item.departments}
                </Text>
              ) : null}
            </View>
          </View>
        </ATrack>
      ),
      [updateActive, isSelected]
    );

    // 渲染列表底部组件
    const renderFooter = useCallback(() => {
      return (
        <View style={styles.loadMoreContainer}>
          <Text style={styles.loadMoreText}>
            {hasMore ? '加载中...' : '没有更多啦'}
          </Text>
        </View>
      );
    }, [hasMore]);

    // FlatList extraData 优化
    const flatListExtraData = useMemo(
      () => ({
        listActive,
      }),
      [listActive]
    );

    return (
      <Modal
        isVisible={visible}
        {...modalAnimation}
        onBackdropPress={handleCancel}
        animationIn='slideInUp'
        animationOut='slideOutDown'
        style={styles.modal}
        useNativeDriver={true}
        avoidKeyboard={false}
        statusBarTranslucent={Platform.OS === 'android'}
      >
        <View style={styles.container}>
          {/* 顶部栏 */}
          <View style={styles.topSection}>
            {/* 标题栏 */}
            <View style={styles.titleBar}>
              <ATrack onPress={handleCancel}>
                <Text style={styles.cancelText}>取消</Text>
              </ATrack>
              <Text style={styles.titleText}>{title}</Text>
              <ATrack onPress={handleConfirm}>
                <Text style={styles.confirmText}>确认</Text>
              </ATrack>
            </View>

            {/* 搜索栏 */}
            <View style={styles.searchContainer}>
              <Image
                source={{
                  uri: 'https://static.soyoung.com/sy-pre/1y4a2d4fr2l31-1711955400686.png',
                }}
                style={styles.searchIcon}
              />
              <TextInput
                style={styles.searchInput}
                value={searchValue}
                onChangeText={setSearchValue}
                placeholder='请输入员工名称'
                placeholderTextColor='#DEDEDE'
                onSubmitEditing={handleClickToSearch}
                returnKeyType='done'
              />
              {searchValue.length > 0 && (
                <ATrack onPress={handleClearIcon}>
                  <View style={styles.clearIconWrapper}>
                    <Image
                      source={{
                        uri: 'https://static.soyoung.com/sy-pre/219l297us0uts-1711617000689.png',
                      }}
                      style={styles.clearIcon}
                    />
                  </View>
                </ATrack>
              )}
            </View>
          </View>

          {/* 员工列表 */}
          {userList.length > 0 ? (
            <FlatList
              style={styles.scrollView}
              data={userList}
              keyExtractor={keyExtractor}
              renderItem={renderStaffItem}
              showsVerticalScrollIndicator={false}
              onEndReached={handleEndReached}
              ListFooterComponent={renderFooter}
              extraData={flatListExtraData}
              getItemLayout={(data, index) => ({
                length: getRealSize(100), // userCard 高度 90 + marginTop 10
                offset: getRealSize(100) * index,
                index,
              })}
              contentContainerStyle={styles.listContainer}
              maintainVisibleContentPosition={{
                minIndexForVisible: 0,
              }}
              keyboardShouldPersistTaps='handled'
              overScrollMode='never'
              bounces={false}
              {...flatListProps}
            />
          ) : (
            <Empty />
          )}
        </View>
      </Modal>
    );
  }
);

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  container: {
    width: '100%',
    backgroundColor: '#ffffff',
    height: getRealSize(547),
  },
  topSection: {
    paddingBottom: getRealSize(10), // 20rpx
  },
  titleBar: {
    height: getRealSize(44), // 88rpx
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: getRealSize(15), // 30rpx
  },
  cancelText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15), // 30rpx
    color: '#999999',
    fontWeight: '400',
  },
  titleText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(16), // 32rpx
    color: '#333333',
    fontWeight: '500',
  },
  confirmText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15), // 30rpx
    color: '#61B43E',
    fontWeight: '400',
  },
  searchContainer: {
    height: getRealSize(36), // 88rpx
    backgroundColor: '#f8f8f8',
    marginHorizontal: getRealSize(15), // 30rpx
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getRealSize(10), // 20rpx
  },
  searchIcon: {
    width: getRealSize(14), // 28rpx
    height: getRealSize(14), // 28rpx
    marginRight: getRealSize(5), // 10rpx
  },
  searchInput: {
    flex: 1,
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(14), // 28rpx
    color: '#333333',
    padding: 0,
  },
  clearIconWrapper: {
    padding: getRealSize(5), // 10rpx
  },
  clearIcon: {
    width: getRealSize(12), // 24rpx
    height: getRealSize(12), // 24rpx
  },
  scrollView: {
    flex: 1,
    backgroundColor: '#f6f9f9',
  },
  listContainer: {
    paddingHorizontal: getRealSize(15),
    paddingBottom: getRealSize(10),
  },
  // UserCard 样式
  userCard: {
    height: getRealSize(90),
    padding: getRealSize(15), // 30rpx
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderWidth: getRealSize(1), // 2rpx
    borderColor: 'transparent',
    marginTop: getRealSize(10),
  },
  userCardActive: {
    backgroundColor: '#EBFBDC',
    borderColor: '#61B43E',
  },
  userCardAvatar: {
    width: getRealSize(40), // 80rpx
    height: getRealSize(40), // 80rpx
    borderRadius: getRealSize(20), // 40rpx
    overflow: 'hidden',
    marginRight: getRealSize(11), // 22rpx
    flexShrink: 0,
  },
  userCardAvatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: getRealSize(20), // 40rpx
  },
  userCardDefaultAvatar: {
    width: '100%',
    height: '100%',
    borderRadius: getRealSize(20), // 40rpx
    backgroundColor: '#61B43E',
    justifyContent: 'center',
    alignItems: 'center',
  },
  userCardDefaultAvatarText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(18), // 36rpx
    color: '#ffffff',
    fontWeight: '500',
    textAlign: 'center',
  },
  userCardInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  userCardTitle: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(14), // 28rpx
    lineHeight: getRealSize(20), // 40rpx
    color: '#333333',
    fontWeight: '500',
    marginBottom: getRealSize(4), // 8rpx
  },
  userCardSubtitle: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(11), // 22rpx
    lineHeight: getRealSize(16), // 32rpx
    color: '#777777',
    fontWeight: '400',
  },
  loadMoreContainer: {
    paddingVertical: getRealSize(32),
    alignItems: 'center',
  },
  loadMoreText: {
    fontSize: getRealSize(14),
    color: '#999999',
  },
});

export default StaffPopup;
