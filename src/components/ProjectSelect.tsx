import React, { useState, useRef, useEffect, useCallback } from 'react';
import { View } from 'react-native';
import SingleSelect, { SingleSelectItem } from './SingleSelect';
import MultiSelect, { MultiSelectItem } from './MultiSelect';
import ProjectPopup, {
  ProjectPopupRef,
  ActiveItem,
  Active,
} from './ProjectPopup';

/**
 * 项目选择组件
 * 支持单选/多选模式，整合了 SingleSelect、MultiSelect 和 ProjectPopup
 */

export interface ProjectSelectProps {
  /** 当前选中的项目 */
  active?: Active;
  /** 选择器标题 */
  title?: string;
  /** 是否多选模式 */
  multiple?: boolean;
  /** 选中值变化回调 */
  onActiveChange?: (active: Active) => void;
  /** 选择变化回调（与 onActiveChange 相同，提供兼容性） */
  onChange?: (active: Active) => void;
}

const ProjectSelect: React.FC<ProjectSelectProps> = ({
  active,
  title = '',
  multiple = false,
  onActiveChange,
  onChange,
}) => {
  const [compActive, setCompActive] = useState<Active>(() => {
    if (multiple) {
      return Array.isArray(active) && active.length > 0 ? active : [];
    } else {
      return !Array.isArray(active) && active?.name
        ? active
        : { id: NaN, name: '' };
    }
  });

  const [openPanel, setOpenPanel] = useState(false);
  const projectPopupRef = useRef<ProjectPopupRef>(null);

  // 监听外部 active 变化并同步到内部状态
  useEffect(() => {
    if (multiple) {
      setCompActive(Array.isArray(active) && active.length > 0 ? active : []);
    } else {
      setCompActive(
        !Array.isArray(active) && active?.name ? active : { id: NaN, name: '' }
      );
    }
  }, [active, multiple]);

  // 处理打开弹窗
  const handleOpen = useCallback(() => {
    setOpenPanel(true);
    projectPopupRef.current?.open();
  }, []);

  // 处理关闭弹窗
  const handleClose = useCallback(() => {
    setOpenPanel(false);
  }, []);

  // 处理多选模式下的删除操作
  const handleRemove = useCallback(
    (id: number) => {
      if (multiple && Array.isArray(compActive)) {
        const newActive = compActive.filter(
          (item: MultiSelectItem) => item.id !== id
        );
        setCompActive(newActive);

        // 触发回调
        onActiveChange?.(newActive);
        onChange?.(newActive);
      }
    },
    [multiple, compActive, onActiveChange, onChange]
  );

  // 处理弹窗确认选择
  const handlePopupChange = useCallback(
    (newActive: Active) => {
      setCompActive(newActive);

      // 触发回调
      onActiveChange?.(newActive);
      onChange?.(newActive);
    },
    [onActiveChange, onChange]
  );

  return (
    <View>
      {/* 根据 multiple 参数渲染不同的选择器 */}
      {multiple ? (
        <MultiSelect
          active={compActive as MultiSelectItem[]}
          buttonText={title}
          onPress={handleOpen}
          onRemove={handleRemove}
        />
      ) : (
        <SingleSelect
          active={compActive as SingleSelectItem}
          openPanel={openPanel}
          onPress={handleOpen}
        />
      )}

      {/* 项目选择弹窗 */}
      <ProjectPopup
        ref={projectPopupRef}
        active={compActive}
        title={title}
        multiple={multiple}
        onActiveChange={handlePopupChange}
        onClose={handleClose}
      />
    </View>
  );
};

export default ProjectSelect;

// 导出类型供外部使用
export type { ActiveItem, Active };
