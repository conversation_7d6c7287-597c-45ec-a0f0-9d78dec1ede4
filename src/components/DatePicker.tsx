import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
} from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Dimensions,
  Image,
} from 'react-native';
import { ATrack } from '@soyoung/react-native-container';
import Modal from 'react-native-modal';
import { modalAnimation } from '@/constant/modal_animation';
import { getRealSize } from '../common/utils';

const { height: screenHeight } = Dimensions.get('window');

// 缓存 getRealSize 结果
const ITEM_HEIGHT = getRealSize(44);
const INDICATOR_TOP = getRealSize(88);
const INDICATOR_BOTTOM = getRealSize(132);

// 日期选择器类型
export type DatePickerType =
  | 'year' // 只选年份: 2024
  | 'month' // 只选月份: 01
  | 'day' // 只选日期: 15
  | 'year-month' // 年-月: 2024-01
  | 'month-day' // 月-日: 01-15
  | 'year-month-day'; // 年-月-日: 2024-01-15 (默认)

interface DatePickerProps {
  visible: boolean;
  value?: string; // 格式根据type而定
  onConfirm: (date: string) => void;
  onCancel: () => void;
  title?: string;
  type?: DatePickerType; // 日期选择器类型
  disabled?: (date: Date) => boolean; // 禁用函数，传入完整日期对象
  yearMonthList?: string[]; // 新增，允许外部传入可选年月
}

// 提取 PickerItem 组件，使用 React.memo 优化渲染
const PickerItem = React.memo<{
  item: number;
  unit: string;
  isSelected: boolean;
  onPress: () => void;
}>(({ item, unit, isSelected, onPress }) => (
  <ATrack onPress={onPress}>
    <View
      style={[
        styles.pickerItem,
        { height: ITEM_HEIGHT },
        isSelected && styles.selectedPickerItem,
      ]}
    >
      <Text
        style={[
          styles.pickerItemText,
          isSelected && styles.selectedPickerItemText,
        ]}
      >
        {item}
        {unit}
      </Text>
    </View>
  </ATrack>
));

const DatePicker: React.FC<DatePickerProps> = ({
  visible,
  value,
  onConfirm,
  onCancel,
  title = '选择日期',
  type = 'year-month-day',
  disabled,
  yearMonthList, // 新增
}) => {
  const yearScrollViewRef = useRef<ScrollView>(null);
  const monthScrollViewRef = useRef<ScrollView>(null);
  const dayScrollViewRef = useRef<ScrollView>(null);

  // 解析初始日期
  const parseDate = useCallback(
    (
      dateString?: string,
      dateType: DatePickerType = 'year-month-day'
    ): Date | null => {
      if (!dateString) return null;

      const currentDate = new Date();
      let year = currentDate.getFullYear();
      let month = currentDate.getMonth();
      let day = currentDate.getDate();

      const cleanDate = dateString.replace(/[/-]/g, '-');
      const parts = cleanDate.split('-');

      switch (dateType) {
        case 'year':
          if (parts.length === 1) {
            const y = parseInt(parts[0], 10);
            if (!isNaN(y)) year = y;
          }
          break;
        case 'month':
          if (parts.length === 1) {
            const m = parseInt(parts[0], 10);
            if (!isNaN(m) && m >= 1 && m <= 12) month = m - 1;
          }
          break;
        case 'day':
          if (parts.length === 1) {
            const d = parseInt(parts[0], 10);
            if (!isNaN(d) && d >= 1 && d <= 31) day = d;
          }
          break;
        case 'year-month':
          if (parts.length === 2) {
            const y = parseInt(parts[0], 10);
            const m = parseInt(parts[1], 10);
            if (!isNaN(y)) year = y;
            if (!isNaN(m) && m >= 1 && m <= 12) month = m - 1;
          }
          break;
        case 'month-day':
          if (parts.length === 2) {
            const m = parseInt(parts[0], 10);
            const d = parseInt(parts[1], 10);
            if (!isNaN(m) && m >= 1 && m <= 12) month = m - 1;
            if (!isNaN(d) && d >= 1 && d <= 31) day = d;
          }
          break;
        case 'year-month-day':
        default:
          if (parts.length === 3) {
            const y = parseInt(parts[0], 10);
            const m = parseInt(parts[1], 10);
            const d = parseInt(parts[2], 10);
            if (!isNaN(y)) year = y;
            if (!isNaN(m) && m >= 1 && m <= 12) month = m - 1;
            if (!isNaN(d) && d >= 1 && d <= 31) day = d;
          }
          break;
      }

      return new Date(year, month, day);
    },
    []
  );

  const initialDate = parseDate(value, type) || new Date();
  const [selectedYear, setSelectedYear] = useState(initialDate.getFullYear());
  const [selectedMonth, setSelectedMonth] = useState(
    initialDate.getMonth() + 1
  );
  const [selectedDay, setSelectedDay] = useState(initialDate.getDate());

  // 根据类型判断需要显示的选择器
  const shouldShowYear = useCallback(() => {
    return ['year', 'year-month', 'year-month-day'].includes(type);
  }, [type]);

  const shouldShowMonth = useCallback(() => {
    return ['month', 'year-month', 'month-day', 'year-month-day'].includes(
      type
    );
  }, [type]);

  const shouldShowDay = useCallback(() => {
    return ['day', 'month-day', 'year-month-day'].includes(type);
  }, [type]);

  // 使用 useMemo 缓存年份列表计算
  const years = useMemo((): number[] => {
    if (yearMonthList && yearMonthList.length > 0) {
      // 只显示yearMonthList里有的年份
      return Array.from(
        new Set(yearMonthList.map(item => Number(item.split('-')[0])))
      ).sort((a, b) => a - b);
    }
    const yearsList: number[] = [];
    for (let i = 2010; i <= 2040; i++) {
      if (!disabled) {
        yearsList.push(i);
      } else {
        // 检查该年份是否有任何可选的日期
        const checkDate = new Date(i, selectedMonth - 1, selectedDay);
        if (!disabled(checkDate)) {
          yearsList.push(i);
        }
      }
    }
    return yearsList;
  }, [yearMonthList, disabled, selectedMonth, selectedDay]);

  // 使用 useMemo 缓存月份列表计算
  const months = useMemo((): number[] => {
    if (yearMonthList && yearMonthList.length > 0) {
      // 只显示当前选中年份下，yearMonthList里有的月份
      return yearMonthList
        .filter(item => Number(item.split('-')[0]) === selectedYear)
        .map(item => Number(item.split('-')[1]))
        .sort((a, b) => a - b);
    }
    const monthsList: number[] = [];
    for (let i = 1; i <= 12; i++) {
      if (!disabled) {
        monthsList.push(i);
      } else {
        // 检查该月份是否有任何可选的日期
        const checkDate = new Date(selectedYear, i - 1, selectedDay);
        if (!disabled(checkDate)) {
          monthsList.push(i);
        }
      }
    }
    return monthsList;
  }, [yearMonthList, selectedYear, disabled, selectedDay]);

  // 使用 useMemo 缓存天数列表计算
  const days = useMemo((): number[] => {
    const daysInMonth = new Date(selectedYear, selectedMonth, 0).getDate();
    const daysList: number[] = [];
    for (let i = 1; i <= daysInMonth; i++) {
      if (!disabled) {
        daysList.push(i);
      } else {
        // 检查该日期是否可选
        const checkDate = new Date(selectedYear, selectedMonth - 1, i);
        if (!disabled(checkDate)) {
          daysList.push(i);
        }
      }
    }
    return daysList;
  }, [disabled, selectedYear, selectedMonth]);

  // 处理年份选择
  const handleYearSelect = useCallback(
    (year: number) => {
      setSelectedYear(year);

      // 获取新年份下的可选月份列表
      if (disabled) {
        const availableMonths: number[] = [];
        for (let i = 1; i <= 12; i++) {
          const checkDate = new Date(year, i - 1, selectedDay);
          if (!disabled(checkDate)) {
            availableMonths.push(i);
          }
        }

        // 如果当前选中的月份不在可选列表中，选择第一个可选的月份
        if (
          availableMonths.length > 0 &&
          !availableMonths.includes(selectedMonth)
        ) {
          setSelectedMonth(availableMonths[0]);
        }
      }

      // 检查当前选择的日期是否有效
      const maxDay = new Date(year, selectedMonth, 0).getDate();
      if (selectedDay > maxDay) {
        setSelectedDay(maxDay);
      }
    },
    [disabled, selectedMonth, selectedDay]
  );

  // 处理月份选择
  const handleMonthSelect = useCallback(
    (month: number) => {
      setSelectedMonth(month);

      // 获取新月份下的可选日期列表
      if (disabled) {
        const daysInMonth = new Date(selectedYear, month, 0).getDate();
        const availableDays: number[] = [];
        for (let i = 1; i <= daysInMonth; i++) {
          const checkDate = new Date(selectedYear, month - 1, i);
          if (!disabled(checkDate)) {
            availableDays.push(i);
          }
        }

        // 如果当前选中的日期不在可选列表中，选择第一个可选的日期
        if (availableDays.length > 0 && !availableDays.includes(selectedDay)) {
          setSelectedDay(availableDays[0]);
        }
      } else {
        // 检查当前选择的日期是否有效
        const maxDay = new Date(selectedYear, month, 0).getDate();
        if (selectedDay > maxDay) {
          setSelectedDay(maxDay);
        }
      }
    },
    [disabled, selectedYear, selectedDay]
  );

  // 处理日期选择
  const handleDaySelect = useCallback((day: number) => {
    setSelectedDay(day);
  }, []);

  // 确认选择
  const handleConfirm = useCallback(() => {
    const month = selectedMonth.toString().padStart(2, '0');
    const day = selectedDay.toString().padStart(2, '0');

    let dateString: string;

    switch (type) {
      case 'year':
        dateString = selectedYear.toString();
        break;
      case 'month':
        dateString = month;
        break;
      case 'day':
        dateString = day;
        break;
      case 'year-month':
        dateString = `${selectedYear}-${month}`;
        break;
      case 'month-day':
        dateString = `${month}-${day}`;
        break;
      case 'year-month-day':
      default:
        dateString = `${selectedYear}-${month}-${day}`;
        break;
    }

    onConfirm(dateString);
  }, [selectedYear, selectedMonth, selectedDay, type, onConfirm]);

  // 滚动到选中的值
  const scrollToSelectedValues = useCallback(() => {
    // 滚动年份
    const yearIndex = years.findIndex(year => year === selectedYear);
    if (yearIndex >= 0 && yearScrollViewRef.current) {
      yearScrollViewRef.current.scrollTo({
        y: yearIndex * ITEM_HEIGHT,
        animated: false,
      });
    }

    // 滚动月份
    const monthIndex = months.findIndex(month => month === selectedMonth);
    if (monthIndex >= 0 && monthScrollViewRef.current) {
      monthScrollViewRef.current.scrollTo({
        y: monthIndex * ITEM_HEIGHT,
        animated: false,
      });
    }

    // 滚动日期
    const dayIndex = days.findIndex(day => day === selectedDay);
    if (dayIndex >= 0 && dayScrollViewRef.current) {
      dayScrollViewRef.current.scrollTo({
        y: dayIndex * ITEM_HEIGHT,
        animated: false,
      });
    }
  }, [selectedYear, selectedMonth, selectedDay, years, months, days]);

  // 处理滚动结束时的选中状态更新
  const handleScrollEnd = useCallback(
    (event: any, data: number[], onSelect: (value: number) => void) => {
      const scrollY = event.nativeEvent.contentOffset.y;
      const index = Math.round(scrollY / ITEM_HEIGHT);

      if (index >= 0 && index < data.length) {
        onSelect(data[index]);
      }
    },
    []
  );

  // 监听props变化
  useEffect(() => {
    if (value) {
      const date = parseDate(value, type) || new Date();
      setSelectedYear(date.getFullYear());
      setSelectedMonth(date.getMonth() + 1);
      setSelectedDay(date.getDate());
    }
  }, [value, type, parseDate]);

  // 修复：只在 disabled 函数变化时执行，避免循环依赖
  useEffect(() => {
    if (disabled) {
      // 检查当前选中的年份是否可选
      const availableYears = years.filter(year => {
        const checkDate = new Date(year, selectedMonth - 1, selectedDay);
        return !disabled(checkDate);
      });

      if (availableYears.length > 0 && !availableYears.includes(selectedYear)) {
        setSelectedYear(availableYears[0]);
        return;
      }

      // 检查当前选中的月份是否可选
      const availableMonths = months.filter(month => {
        const checkDate = new Date(selectedYear, month - 1, selectedDay);
        return !disabled(checkDate);
      });

      if (
        availableMonths.length > 0 &&
        !availableMonths.includes(selectedMonth)
      ) {
        setSelectedMonth(availableMonths[0]);
        return;
      }

      // 检查当前选中的日期是否可选
      const availableDays = days.filter(day => {
        const checkDate = new Date(selectedYear, selectedMonth - 1, day);
        return !disabled(checkDate);
      });

      if (availableDays.length > 0 && !availableDays.includes(selectedDay)) {
        setSelectedDay(availableDays[0]);
      }
    }
  }, [disabled]); // 只依赖 disabled 函数

  // 监听Modal显示状态
  useEffect(() => {
    if (visible) {
      // 延迟执行滚动，确保Modal完全显示
      const timer = setTimeout(() => {
        scrollToSelectedValues();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [visible, scrollToSelectedValues]);

  // 渲染滚动选择器
  const renderScrollPicker = useCallback(
    (
      data: number[],
      selectedValue: number,
      onSelect: (value: number) => void,
      unit: string,
      scrollViewRef: React.RefObject<ScrollView>
    ) => {
      return (
        <View style={styles.pickerContainer}>
          <ScrollView
            ref={scrollViewRef}
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            snapToInterval={ITEM_HEIGHT}
            decelerationRate='fast'
            onMomentumScrollEnd={event =>
              handleScrollEnd(event, data, onSelect)
            }
          >
            {/* 添加顶部空白占位 */}
            <View style={{ height: ITEM_HEIGHT * 2 }} />

            {data.map(item => (
              <PickerItem
                key={item}
                item={item}
                unit={unit}
                isSelected={selectedValue === item}
                onPress={() => onSelect(item)}
              />
            ))}

            {/* 添加底部空白占位 */}
            <View style={{ height: ITEM_HEIGHT * 2 }} />
          </ScrollView>

          {/* 选中指示器 */}
          <View style={styles.indicator} />
          <View style={[styles.indicator, { top: INDICATOR_BOTTOM }]} />
        </View>
      );
    },
    [handleScrollEnd]
  );

  return (
    <Modal
      isVisible={visible}
      {...modalAnimation}
      onBackdropPress={onCancel}
      onBackButtonPress={onCancel}
      style={styles.modal}
      backdropOpacity={0.5}
      animationIn='slideInUp'
      animationOut='slideOutDown'
      useNativeDriverForBackdrop={true}
      useNativeDriver={false}
    >
      <View style={styles.container}>
        {/* 标题栏 */}
        <View style={styles.header}>
          <View style={styles.placeholder} />
          <Text style={styles.title}>{title}</Text>
          <ATrack onPress={onCancel}>
            <Image
              style={styles.cancelButton}
              source={{
                uri: 'https://static.soyoung.com/sy-pre/1t4hktog3apho-1712556600714.png',
              }}
            />
          </ATrack>
        </View>

        {/* 日期选择器 */}
        <View style={styles.pickerWrapper}>
          {shouldShowYear() &&
            renderScrollPicker(
              years,
              selectedYear,
              handleYearSelect,
              '年',
              yearScrollViewRef
            )}
          {shouldShowMonth() &&
            renderScrollPicker(
              months,
              selectedMonth,
              handleMonthSelect,
              '月',
              monthScrollViewRef
            )}
          {shouldShowDay() &&
            renderScrollPicker(
              days,
              selectedDay,
              handleDaySelect,
              '日',
              dayScrollViewRef
            )}
        </View>

        {/* 确认按钮 */}
        <View style={styles.footer}>
          <ATrack onPress={onCancel}>
            <View style={styles.footerCancelButton}>
              <Text style={styles.footerCancelText}>取消</Text>
            </View>
          </ATrack>
          <ATrack onPress={handleConfirm}>
            <View style={styles.footerConfirmButton}>
              <Text style={styles.footerConfirmText}>确定</Text>
            </View>
          </ATrack>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  container: {
    backgroundColor: '#ffffff',
    maxHeight: screenHeight * 0.6,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: getRealSize(20),
    paddingVertical: getRealSize(15),
  },
  cancelButton: {
    width: getRealSize(20),
    height: getRealSize(20),
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: getRealSize(18),
    color: '#333333',
    fontWeight: '600',
  },
  placeholder: {
    width: getRealSize(30),
  },
  pickerWrapper: {
    flexDirection: 'row',
    height: getRealSize(220),
    paddingHorizontal: getRealSize(10),
  },
  pickerContainer: {
    flex: 1,
    position: 'relative',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    alignItems: 'center',
  },
  pickerItem: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: getRealSize(44),
  },
  selectedPickerItem: {
    // 选中状态样式由指示器显示
  },
  pickerItemText: {
    fontSize: getRealSize(16),
    color: '#999999',
    textAlign: 'center',
  },
  selectedPickerItemText: {
    fontSize: getRealSize(18),
    color: '#333333',
    fontWeight: '600',
  },
  indicator: {
    position: 'absolute',
    left: getRealSize(-10),
    right: getRealSize(-10),
    height: getRealSize(1),
    backgroundColor: '#F0F0F0',
    top: INDICATOR_TOP, // 选中区域的上边线
  },
  footer: {
    padding: getRealSize(20),
    paddingBottom: getRealSize(30),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  footerCancelButton: {
    width: getRealSize(155),
    height: getRealSize(42),
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#333333',
  },
  footerCancelText: {
    fontSize: getRealSize(13),
    color: '#333333',
    fontWeight: '500',
  },
  footerConfirmButton: {
    width: getRealSize(155),
    height: getRealSize(42),
    backgroundColor: '#333333',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#333333',
  },
  footerConfirmText: {
    fontSize: getRealSize(13),
    color: '#ffffff',
    fontWeight: '500',
  },
});

export default DatePicker;
