import React, { useMemo, useRef, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  Image,
  StyleSheet,
  Dimensions,
} from 'react-native';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import { ATrack } from '@soyoung/react-native-container';
import { getRealSize } from '@/common/utils';

dayjs.extend(isSameOrBefore);

interface DateSelectProps {
  value: string;
  startDate?: string;
  endDate?: string;
  showPanel?: boolean;
  showPointList?: string[];
  onChange?: (date: string) => void;
  onClickPanel?: () => void;
}

const DateSelect: React.FC<DateSelectProps> = ({
  value,
  startDate,
  endDate,
  showPanel = false,
  showPointList = [],
  onChange,
  onClickPanel,
}) => {
  const scrollViewRef = useRef<ScrollView>(null);

  // 每个日期项的宽度（包括padding和margin）
  const ITEM_WIDTH = getRealSize(58) + getRealSize(8); // minWidth + marginHorizontal * 2

  // 直接使用 useMemo 计算日期列表，不再使用 state
  const dateList = useMemo(() => {
    const today = dayjs();
    const start = startDate ? dayjs(startDate) : today;
    const end = endDate ? dayjs(endDate) : start.add(6, 'day');
    const dates = [];
    for (
      let date = start;
      date.isSameOrBefore(end, 'day');
      date = date.add(1, 'day')
    ) {
      const formattedDate = date.format('YYYY-MM-DD');
      const isToday = date.isSame(today, 'day');

      dates.push({
        date: formattedDate,
        showDate: date.format('MM/DD'),
        day: isToday
          ? '今日'
          : date.isSame(today.add(1, 'day'), 'day')
            ? '明日'
            : ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][
                date.day()
              ],
        isToday: isToday,
        hasPoint: showPointList.includes(formattedDate),
      });
    }

    return dates;
  }, [startDate, endDate, showPointList]);

  // 使用 useMemo 计算当前选中的索引
  const activeIndex = useMemo(() => {
    const selectedIndex = dateList.findIndex(item => item.date === value);
    return selectedIndex >= 0 ? selectedIndex : 0;
  }, [value, dateList]);

  // 滚动到指定索引的函数
  const scrollToIndex = (index: number, animated: boolean = true) => {
    if (!scrollViewRef.current || index < 0 || index >= dateList.length) {
      return;
    }

    const screenWidth = showPanel
      ? Dimensions.get('window').width - getRealSize(108)
      : Dimensions.get('window').width;

    // 计算目标位置，让选中的日期尽量居中
    const targetX = index * ITEM_WIDTH - (screenWidth - ITEM_WIDTH) / 2;

    // 确保不会滚动到负数位置或超出内容范围
    const maxScrollX = Math.max(0, dateList.length * ITEM_WIDTH - screenWidth);
    const scrollX = Math.max(0, Math.min(targetX, maxScrollX));

    scrollViewRef.current.scrollTo({
      x: scrollX,
      animated,
    });
  };

  // 监听 activeIndex 变化，自动滚动到选中日期
  useEffect(() => {
    if (activeIndex >= 0) {
      // 使用 setTimeout 确保组件已经渲染完成
      const timer = setTimeout(() => {
        scrollToIndex(activeIndex, true);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [activeIndex]);

  const handleDateClick = (date: string, index: number) => {
    onChange?.(date);
    // 点击时也执行滚动动画
    setTimeout(() => {
      scrollToIndex(index, true);
    }, 50);
  };

  const handlePanelClick = () => {
    onClickPanel?.();
  };

  return (
    <View style={styles.dateSelect}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        showsHorizontalScrollIndicator={false}
        style={[
          styles.dateScroll,
          {
            width: showPanel
              ? Dimensions.get('window').width - getRealSize(108)
              : Dimensions.get('window').width,
          },
        ]}
        // 添加一些滚动相关的优化属性
        decelerationRate='fast'
        snapToInterval={ITEM_WIDTH}
        snapToAlignment='start'
      >
        {dateList.map((item, index) => (
          <ATrack
            key={item.date}
            style={[
              styles.dateItem,
              index === activeIndex && styles.dateItemActive,
              item.isToday && styles.today,
            ]}
            onPress={() => handleDateClick(item.date, index)}
          >
            {item.hasPoint && (
              <View
                style={[
                  styles.datePoint,
                  index === activeIndex && styles.datePointActive,
                ]}
              />
            )}
            <Text
              style={[
                styles.dateTitle,
                index === activeIndex && styles.dateTitleActive,
              ]}
            >
              {item.showDate}
            </Text>
            <Text
              style={[
                styles.dateSubtitle,
                index === activeIndex && styles.dateSubtitleActive,
              ]}
            >
              {item.day}
            </Text>
          </ATrack>
        ))}
      </ScrollView>

      {showPanel && (
        <ATrack style={styles.calendarPanel} onPress={handlePanelClick}>
          <Image
            style={styles.calendarIcon}
            source={{
              uri: 'https://static.soyoung.com/sy-pre/2x9igo23omk4j-1711613400704.png',
            }}
            resizeMode='contain'
          />
          <Text style={styles.calendarText}>日历</Text>
        </ATrack>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  dateSelect: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
  },
  dateScroll: {
    flex: 1,
  },
  dateItem: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: getRealSize(12),
    paddingVertical: getRealSize(8),
    marginHorizontal: getRealSize(4),
    minWidth: getRealSize(58),
    height: getRealSize(58),
    backgroundColor: '#ebfbdc',
    position: 'relative',
  },
  dateItemActive: {
    backgroundColor: '#a9ea6a',
  },
  today: {
    // 今日特殊样式
  },
  datePoint: {
    position: 'absolute',
    top: getRealSize(6),
    right: getRealSize(6),
    width: getRealSize(6),
    height: getRealSize(6),
    borderRadius: getRealSize(3),
    backgroundColor: '#61B43E',
  },
  datePointActive: {
    backgroundColor: '#ffffff',
  },
  dateTitle: {
    fontSize: getRealSize(11),
    fontWeight: '500',
    color: '#61B43E',
    marginBottom: getRealSize(2),
  },
  dateTitleActive: {
    color: '#030303',
  },
  dateSubtitle: {
    fontSize: getRealSize(13),
    color: '#61B43E',
    fontWeight: '500',
  },
  dateSubtitleActive: {
    color: '#030303',
  },
  calendarPanel: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    width: getRealSize(54),
    height: getRealSize(54),
    marginLeft: getRealSize(0),
  },
  calendarIcon: {
    width: getRealSize(15),
    height: getRealSize(15),
    marginBottom: getRealSize(4),
  },
  calendarText: {
    fontSize: getRealSize(12),
    color: '#222',
  },
});

export default DateSelect;
