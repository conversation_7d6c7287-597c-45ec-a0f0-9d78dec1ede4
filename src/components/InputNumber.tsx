import React, { useState, useEffect, memo, useCallback, useMemo } from 'react';
import { View, TextInput, Text, StyleSheet, Alert } from 'react-native';
import { getRealSize } from '../common/utils';

interface Attribute {
  placeholder?: string[];
  range_separator?: string;
  range?: number;
  fixed?: number[];
}

interface InputNumberProps {
  value: string;
  attribute: Attribute;
  onChange: (value: string) => void;
}

// 静态数据提取到组件外部
const PLACEHOLDER_COLOR = '#DEDEDE';
const DEFAULT_SEPARATOR = '-';

// 辅助函数提取到组件外部
const toFixedWithoutRounding = (num: number, fixed: number): string => {
  const parts = num.toString().split('.');
  if (parts.length < 2 || parts[1].length <= fixed) {
    return num.toFixed(fixed);
  }
  return `${parts[0]}.${parts[1].substr(0, fixed)}`;
};

const InputNumber: React.FC<InputNumberProps> = memo(
  ({ value, attribute, onChange }) => {
    const [values, setValues] = useState<string[]>(
      value ? value.split(',') : ['', '']
    );

    // 缓存是否为范围输入
    const isRange = useMemo(() => {
      return attribute.range === 1;
    }, [attribute.range]);

    // 缓存分隔符
    const separator = useMemo(() => {
      return attribute.range_separator || DEFAULT_SEPARATOR;
    }, [attribute.range_separator]);

    // 缓存占位符
    const placeholders = useMemo(() => {
      return attribute.placeholder || [];
    }, [attribute.placeholder]);

    // 同步外部value变化
    useEffect(() => {
      setValues(value ? value.split(',') : ['', '']);
    }, [value]);

    // 获取键盘类型
    const getKeyboardType = useCallback(
      (index: number) => {
        const fixed = attribute.fixed || [0];
        return fixed[index] === 0 ? 'number-pad' : 'decimal-pad';
      },
      [attribute.fixed]
    );

    // 处理文本变化
    const handleChange = useCallback(
      (text: string, index: number) => {
        const newValues = [...values];

        // 处理多个小数点的情况
        const decimalMatches = text.match(/\./g);
        const multipleDecimalPoints = decimalMatches
          ? decimalMatches.length > 1
          : false;
        if (multipleDecimalPoints) {
          const parts = text.split('.');
          const newValue = `${parts[0]}.${parts[1]}`;
          const val = toFixedWithoutRounding(+newValue, 2);
          newValues[index] = val;
        } else if (
          attribute?.fixed &&
          attribute.fixed[index] !== undefined &&
          /^\d+\.\d{3,}$/.test(text)
        ) {
          const fixedValue = attribute.fixed[index];
          if (fixedValue !== undefined) {
            const val = toFixedWithoutRounding(+text, fixedValue);
            newValues[index] = val;
          }
        } else {
          newValues[index] = text;
        }

        // 如果是第一个输入框且第二个输入框为空，清空第二个
        if (index === 0 && !newValues[1] && +newValues[1] !== 0) {
          newValues[1] = '';
        }

        setValues(newValues);

        const result =
          !newValues[0] && !newValues[1] ? '' : newValues.join(',');
        onChange(result);
      },
      [values, attribute, onChange]
    );

    // 处理失去焦点
    const handleBlur = useCallback(
      (index: number) => {
        if (values[0] && values[1] && +values[0] > +values[1]) {
          Alert.alert(
            '输入错误',
            index
              ? `${placeholders[1]}不能低于${placeholders[0]}`
              : `${placeholders[0]}不能高于${placeholders[1]}`
          );

          const newValues = [...values];
          newValues[index] = '';
          setValues(newValues);
          onChange(newValues.join(','));
        } else if (values[index].endsWith('.')) {
          const newValues = [...values];
          newValues[index] = values[index].slice(0, -1);
          setValues(newValues);
        }
      },
      [values, placeholders, onChange]
    );

    return (
      <View style={styles.container}>
        <TextInput
          style={styles.input}
          value={values[0]}
          placeholder={placeholders[0] || ''}
          placeholderTextColor={PLACEHOLDER_COLOR}
          keyboardType={getKeyboardType(0)}
          onChangeText={text => handleChange(text, 0)}
          onBlur={() => handleBlur(0)}
        />

        {isRange && (
          <>
            <Text style={styles.separator}>{separator}</Text>
            <TextInput
              style={styles.input}
              value={values[1]}
              placeholder={placeholders[1] || ''}
              placeholderTextColor={PLACEHOLDER_COLOR}
              keyboardType={getKeyboardType(1)}
              onChangeText={text => handleChange(text, 1)}
              onBlur={() => handleBlur(1)}
            />
          </>
        )}
      </View>
    );
  }
);

InputNumber.displayName = 'InputNumber';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    height: getRealSize(38),
  },
  input: {
    flex: 1,
    height: getRealSize(38),
    borderBottomWidth: 1,
    borderBottomColor: '#DEDEDE',
    textAlign: 'center',
    fontSize: getRealSize(13),
    color: '#161616',
  },
  separator: {
    width: getRealSize(32),
    textAlign: 'center',
    fontSize: getRealSize(13),
    color: '#333333',
  },
});

export default InputNumber;
