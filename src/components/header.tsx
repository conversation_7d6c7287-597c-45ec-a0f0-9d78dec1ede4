import React, { memo, useEffect, useCallback } from 'react';
import {
  View,
  ImageBackground,
  StyleSheet,
  Text,
  Platform,
  // StatusBar,
} from 'react-native';
import { getRealSize } from '../common/utils';
import { ATrack } from '@soyoung/react-native-container';
import { back } from '@soyoung/react-native-base';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export default memo(
  (props: {
    title: string;
    bgColor?: string;
    hideBack?: boolean;
    zIndex?: number;
    rightSlot?: JSX.Element;
  }) => {
    const navigation = useNavigation();
    const insets = useSafeAreaInsets();
    useEffect(() => {
      if (Platform.OS === 'ios') {
        return;
      }
      const unsubscribe = navigation.addListener('focus', () => {
        // StatusBar.setBarStyle('dark-content');
        // StatusBar.setBackgroundColor('#fff');
      });

      return unsubscribe;
    }, [navigation]);

    const handleBack = useCallback(() => {
      back();
    }, []);

    return (
      <View
        style={{
          ...styles.headerMain,
          backgroundColor: props.bgColor || 'transparnet',
          zIndex: props.zIndex || 1,
          paddingTop: getRealSize(insets.top),
        }}
      >
        <View style={styles.container}>
          {!props.hideBack && (
            <ATrack style={styles.image} onPress={handleBack}>
              <ImageBackground
                source={{
                  uri: 'https://static.soyoung.com/sy-design/3cj8rc3ipek931726715721972.png',
                }}
                style={styles.image}
              />
            </ATrack>
          )}
          <Text
            style={{
              ...styles.title,
            }}
          >
            {props.title}
          </Text>
          {props.rightSlot ? (
            <View
              style={{
                ...styles.right,
              }}
            >
              {props.rightSlot}
            </View>
          ) : null}
        </View>
      </View>
    );
  }
);

const styles = StyleSheet.create({
  headerMain: {
    width: '100%',
    overflow: 'hidden',
    position: 'relative',
    // paddingTop: getRealSize(40),
  },
  container: {
    height: getRealSize(44),
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  image: {
    width: getRealSize(44),
    height: getRealSize(44),
    justifyContent: 'flex-start',
    alignItems: 'center',
    position: 'absolute',
    left: 0,
    top: 0,
  },
  title: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(17),
    color: '#030303',
    letterSpacing: 0,
    fontWeight: 'bold',
  },
  right: {
    width: getRealSize(44),
    height: getRealSize(44),
    position: 'absolute',
    right: 0,
    top: 0,
    zIndex: 1,
  },
});
