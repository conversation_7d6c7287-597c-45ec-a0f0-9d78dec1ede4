import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { Line } from 'react-native-svg';

const DashedLine = ({
  axis = 'horizontal',
  dashGap = 2,
  dashLength = 4,
  dashThickness = 1,
  dashColor = 'rgba(0,0,0,0.05)',
  dashStyle = {},
  style = {},
}) => {
  const isRow = axis === 'horizontal';

  // 计算 SVG 的 strokeDasharray 值
  // strokeDasharray 格式: "dashLength dashGap"
  const strokeDasharray = `${dashLength} ${dashGap}`;

  return (
    <View style={[isRow ? styles.row : styles.column, style]}>
      <Svg width='100%' height='100%' style={{ position: 'absolute' }}>
        <Line
          x1={isRow ? 0 : '50%'}
          y1={isRow ? `${dashThickness / 2}px` : 0}
          x2={isRow ? '100%' : '50%'}
          y2={isRow ? `${dashThickness / 2}px` : '100%'}
          stroke={dashColor}
          strokeWidth={dashThickness}
          strokeDasharray={strokeDasharray}
          strokeLinecap='butt'
          {...dashStyle}
        />
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    height: 1,
  },
  column: {
    flexDirection: 'column',
    width: 1,
  },
});

export default DashedLine;
