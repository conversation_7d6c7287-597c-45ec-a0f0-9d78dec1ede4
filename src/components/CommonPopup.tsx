import React, {
  useState,
  useCallback,
  useMemo,
  forwardRef,
  useImperativeHandle,
} from 'react';
import {
  View,
  Text,
  TextInput,
  FlatList,
  Image,
  StyleSheet,
  ListRenderItem,
  Platform,
} from 'react-native';
import { flatListProps } from '@/constant/flatlist_props';
import { ATrack } from '@soyoung/react-native-container';
import Modal from 'react-native-modal';
import { modalAnimation } from '@/constant/modal_animation';
import { getRealSize } from '@/common/utils';
import Empty from './Empty';

/**
 * 选项接口
 */
export interface Options {
  label: string;
  value: number | string;
}

/**
 * 激活项接口
 */
export interface ActiveItem {
  id: number | string;
  name: string;
}

/**
 * 激活类型
 */
export type Active = ActiveItem | ActiveItem[];

/**
 * 弹窗组件属性
 */
export interface CommonPopupProps {
  /** 是否可搜索 */
  searchable?: boolean;
  /** 当前激活的项 */
  active: Active;
  /** 弹窗标题 */
  title: string;
  /** 是否多选模式 */
  multiple: boolean;
  /** 选项列表 */
  options: Options[];
  /** 最大行数 */
  maxLines?: number;
  /** 关闭回调 */
  onClose?: () => void;
  /** 激活状态变更回调 */
  onActiveChange?: (active: Active) => void;
  /** 选择变更回调 */
  onChange?: (active: Active) => void;
}

/**
 * 弹窗组件暴露的方法
 */
export interface CommonPopupRef {
  open: () => void;
  close: () => void;
}

/**
 * 通用选择弹窗组件
 * 支持单选/多选模式，包含搜索功能和完整的UI交互
 */
const CommonPopup = forwardRef<CommonPopupRef, CommonPopupProps>(
  (
    {
      searchable = true,
      active,
      title,
      multiple,
      options: propOptions,
      maxLines,
      onClose,
      onActiveChange,
      onChange,
    },
    ref
  ) => {
    const [visible, setVisible] = useState(false);
    const [searchInputValue, setSearchInputValue] = useState('');
    const [searchValue, setSearchValue] = useState('');
    const [listActive, setListActive] = useState<Active>(active);

    // 原始选项数据
    const originOptions = useMemo(() => propOptions, [propOptions]);

    // 过滤后的选项
    const filteredOptions = useMemo(() => {
      if (!searchValue.trim()) {
        return originOptions;
      }
      return originOptions.filter(item =>
        item.label.toLowerCase().includes(searchValue.toLowerCase())
      );
    }, [originOptions, searchValue]);

    // 处理搜索输入变化
    const handleSearchInputChange = useCallback((text: string) => {
      setSearchInputValue(text);
    }, []);

    // 处理搜索提交
    const handleSearchSubmit = useCallback(() => {
      setSearchValue(searchInputValue);
    }, [searchInputValue]);

    /**
     * 判断某个选项是否处于激活状态
     */
    const isActive = useCallback(
      (item: Options): boolean => {
        if (multiple) {
          const activeList = listActive as ActiveItem[];
          return activeList.some(
            (activeItem: ActiveItem) => activeItem.id === item.value
          );
        } else {
          const activeItem = listActive as ActiveItem;
          return activeItem && activeItem.id === item.value;
        }
      },
      [listActive, multiple]
    );

    /**
     * 更新激活状态
     */
    const updateActive = useCallback(
      (data: ActiveItem) => {
        if (multiple) {
          const curList = [...(listActive as ActiveItem[])];
          const existingIndex = curList.findIndex(item => item.id === data.id);

          if (existingIndex >= 0) {
            // 如果已存在，则移除
            curList.splice(existingIndex, 1);
          } else {
            // 如果不存在，则添加
            curList.push(data);
          }
          setListActive(curList);
        } else {
          // 单选模式
          const currentActive = listActive as ActiveItem;
          if (currentActive && currentActive.id === data.id) {
            // 如果点击的是当前激活项，则取消选择
            setListActive({ id: '', name: '' });
          } else {
            // 否则选择新项
            setListActive({ ...data });
          }
        }
      },
      [listActive, multiple]
    );

    /**
     * 打开弹窗
     */
    const open = useCallback(() => {
      setListActive(
        multiple ? [...(active as ActiveItem[])] : { ...(active as ActiveItem) }
      );
      setVisible(true);
    }, [active, multiple]);

    /**
     * 关闭弹窗
     */
    const close = useCallback(() => {
      console.log('close');
      setVisible(false);
      setSearchInputValue('');
      setSearchValue('');
      setListActive(active); // 重置为原始状态
      onClose?.();
    }, [active, onClose]);

    /**
     * 取消操作
     */
    const handleCancel = useCallback(() => {
      close();
    }, [close]);

    /**
     * 确认操作
     */
    const handleConfirm = useCallback(() => {
      onActiveChange?.(listActive);
      onChange?.(listActive);
      setVisible(false);
      setSearchInputValue('');
      setSearchValue('');
      onClose?.();
    }, [listActive, onActiveChange, onChange, onClose]);

    /**
     * 清除搜索
     */
    const handleClearSearch = useCallback(() => {
      setSearchInputValue('');
      setSearchValue('');
    }, []);

    // 暴露方法给父组件
    useImperativeHandle(
      ref,
      () => ({
        open,
        close,
      }),
      [open, close]
    );

    /**
     * FlatList 渲染选项项
     */
    const renderItem: ListRenderItem<Options> = useCallback(
      ({ item }) => {
        const itemActive = isActive(item);
        const data: ActiveItem = {
          id: item.value as number,
          name: item.label,
        };
        const props: any = {};
        if (maxLines) {
          props.numberOfLines = maxLines;
        }
        return (
          <ATrack onPress={() => updateActive(data)}>
            <View style={styles.popupItem}>
              <View
                style={[
                  styles.popupItemBody,
                  itemActive && styles.popupItemBodyActive,
                ]}
              >
                <Text
                  style={[
                    styles.popupItemText,
                    itemActive && styles.popupItemTextActive,
                  ]}
                  {...props}
                >
                  {item.label}
                </Text>
              </View>
            </View>
          </ATrack>
        );
      },
      [isActive, updateActive]
    );

    /**
     * FlatList keyExtractor
     */
    const keyExtractor = useCallback((item: Options) => `${item.value}`, []);

    /**
     * FlatList getItemLayout 优化
     */
    const getItemLayout = useCallback(
      (_data: Options[] | null | undefined, index: number) => ({
        length: getRealSize(64), // popupItem marginTop(10) + popupItemBody height(54)
        offset: getRealSize(64) * index,
        index,
      }),
      []
    );

    const renderContent = () => {
      return (
        <>
          {/* 顶部栏 */}
          <View style={styles.topSection}>
            {/* 标题栏 */}
            <View style={styles.titleBar}>
              <ATrack onPress={handleCancel}>
                <View style={styles.button}>
                  <Text style={styles.cancelText}>取消</Text>
                </View>
              </ATrack>
              <Text style={styles.titleText}>{title}</Text>
              <ATrack onPress={handleConfirm}>
                <View style={styles.button}>
                  <Text style={styles.confirmText}>确认</Text>
                </View>
              </ATrack>
            </View>

            {/* 搜索栏 */}
            {searchable && (
              <View style={styles.searchContainer}>
                <Image
                  source={{
                    uri: 'https://static.soyoung.com/sy-pre/1y4a2d4fr2l31-1711955400686.png',
                  }}
                  style={styles.searchIcon}
                />
                <TextInput
                  value={searchInputValue}
                  style={styles.searchInput}
                  onChangeText={handleSearchInputChange}
                  placeholder={`请输入${title}`}
                  placeholderTextColor='#DEDEDE'
                  returnKeyType='done'
                  onSubmitEditing={handleSearchSubmit}
                />
                {searchInputValue.length > 0 && (
                  <ATrack onPress={handleClearSearch}>
                    <View style={styles.clearIconWrapper}>
                      <Image
                        source={{
                          uri: 'https://static.soyoung.com/sy-pre/219l297us0uts-1711617000689.png',
                        }}
                        style={styles.clearIcon}
                      />
                    </View>
                  </ATrack>
                )}
              </View>
            )}
          </View>
          {/* 内容区域 */}
          {filteredOptions.length > 0 ? (
            <FlatList
              {...flatListProps}
              style={styles.popupBody}
              data={filteredOptions}
              renderItem={renderItem}
              keyExtractor={keyExtractor}
              getItemLayout={getItemLayout}
              showsVerticalScrollIndicator={false}
              removeClippedSubviews={true}
              maxToRenderPerBatch={10}
              windowSize={10}
              initialNumToRender={10}
              updateCellsBatchingPeriod={50}
              contentContainerStyle={styles.listContainer}
            />
          ) : (
            <Empty />
          )}
        </>
      );
    };

    return (
      <Modal
        isVisible={visible}
        {...modalAnimation}
        onBackdropPress={handleCancel}
        animationIn='slideInUp'
        animationOut='slideOutDown'
        style={styles.modal}
        avoidKeyboard={false}
        statusBarTranslucent={Platform.OS === 'android'}
      >
        {Platform.OS === 'android' ? (
          <View
            style={{
              flex: 1,
              justifyContent: 'flex-end', // 使用固定定位避免键盘影响
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
            }}
          >
            <View
              style={{
                ...styles.container,
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
              }}
            >
              {renderContent()}
            </View>
          </View>
        ) : (
          <View style={styles.container}>{renderContent()}</View>
        )}
      </Modal>
    );
  }
);

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  container: {
    width: '100%',
    height: getRealSize(547), // 1094rpx
    backgroundColor: '#ffffff',
  },
  topSection: {
    paddingBottom: getRealSize(10), // 20rpx
  },
  titleBar: {
    height: getRealSize(44), // 88rpx
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  button: {
    width: getRealSize(60),
    height: getRealSize(40),
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15), // 30rpx
    color: '#999999',
    fontWeight: '400',
  },
  titleText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(16), // 32rpx
    color: '#333333',
    fontWeight: '500',
  },
  confirmText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15), // 30rpx
    color: '#61B43E',
    fontWeight: '400',
  },
  searchContainer: {
    height: getRealSize(36), // 88rpx
    backgroundColor: '#f8f8f8',
    marginHorizontal: getRealSize(15), // 30rpx
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getRealSize(10), // 20rpx
  },
  searchIcon: {
    width: getRealSize(14), // 28rpx
    height: getRealSize(14), // 28rpx
    marginRight: getRealSize(5), // 10rpx
  },
  searchInput: {
    flex: 1,
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(14), // 28rpx
    color: '#333333',
    padding: 0,
  },
  clearIconWrapper: {
    padding: getRealSize(5), // 10rpx
  },
  clearIcon: {
    width: getRealSize(12), // 24rpx
    height: getRealSize(12), // 24rpx
  },
  popupBody: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  listContainer: {
    paddingHorizontal: getRealSize(15),
    paddingBottom: getRealSize(10),
  },
  popupItem: {
    marginTop: getRealSize(10),
  },
  popupItemBody: {
    height: getRealSize(54),
    backgroundColor: '#ffffff',
    paddingHorizontal: getRealSize(15),
    justifyContent: 'center',
    borderWidth: getRealSize(1),
    borderColor: 'transparent',
  },
  popupItemBodyActive: {
    backgroundColor: '#EBFBDC',
    borderColor: '#61B43E',
    borderWidth: getRealSize(1),
  },
  popupItemText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15),
    color: '#161616',
    fontWeight: '400',
  },
  popupItemTextActive: {
    color: '#61B43E',
  },
});

export default CommonPopup;
