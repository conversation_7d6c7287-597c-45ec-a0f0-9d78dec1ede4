import React, { useState, useEffect, memo, useCallback, useMemo } from 'react';
import { View, TextInput, Image, StyleSheet } from 'react-native';
import { ATrack } from '@soyoung/react-native-container';
import { getRealSize } from '../common/utils';

interface InputTextProps {
  value: string;
  placeholder?: string;
  maxLength?: number;
  onChange: (value: string) => void;
}

// 静态数据提取到组件外部
const CLEAR_ICON_URI =
  'https://static.soyoung.com/sy-pre/219l297us0uts-1711617000689.png';
const DEFAULT_PLACEHOLDER = '请输入';
const PLACEHOLDER_COLOR = '#DEDEDE';

const InputText: React.FC<InputTextProps> = memo(
  ({ value, placeholder = DEFAULT_PLACEHOLDER, maxLength, onChange }) => {
    const [inputValue, setInputValue] = useState(value || '');

    // 同步外部value变化
    useEffect(() => {
      setInputValue(value || '');
    }, [value]);

    // 缓存是否显示清除按钮
    const showClearButton = useMemo(() => {
      return inputValue.length > 0;
    }, [inputValue.length]);

    // 处理文本变化
    const handleChangeText = useCallback(
      (text: string) => {
        setInputValue(text);
        onChange(text);
      },
      [onChange]
    );

    // 处理清除
    const handleClear = useCallback(() => {
      setInputValue('');
      onChange('');
    }, [onChange]);

    // 处理maxLength
    const processedMaxLength = useMemo(() => {
      return maxLength === 0 ? undefined : maxLength;
    }, [maxLength]);

    return (
      <View style={styles.container}>
        <TextInput
          style={styles.input}
          value={inputValue}
          placeholder={placeholder}
          placeholderTextColor={PLACEHOLDER_COLOR}
          maxLength={processedMaxLength}
          onChangeText={handleChangeText}
        />
        {showClearButton && (
          <ATrack style={styles.clearButton} onPress={handleClear}>
            <Image source={{ uri: CLEAR_ICON_URI }} style={styles.clearIcon} />
          </ATrack>
        )}
      </View>
    );
  }
);

InputText.displayName = 'InputText';

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    borderBottomWidth: 1,
    borderBottomColor: '#DEDEDE',
    height: getRealSize(38),
  },
  input: {
    flex: 1,
    height: getRealSize(38),
    paddingRight: getRealSize(38),
    fontSize: getRealSize(13),
    color: '#161616',
  },
  clearButton: {
    position: 'absolute',
    right: 0,
    top: 0,
    width: getRealSize(38),
    height: getRealSize(38),
    justifyContent: 'center',
    alignItems: 'center',
  },
  clearIcon: {
    width: getRealSize(16),
    height: getRealSize(16),
  },
});

export default InputText;
