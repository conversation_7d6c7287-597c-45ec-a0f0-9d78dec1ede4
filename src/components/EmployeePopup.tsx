import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useCallback,
  useEffect,
  useMemo,
} from 'react';
import {
  View,
  Text,
  Image,
  TextInput,
  FlatList,
  StyleSheet,
  ListRenderItem,
  Platform,
} from 'react-native';
import { flatListProps } from '@/constant/flatlist_props';
import Modal from 'react-native-modal';
import { modalAnimation } from '@/constant/modal_animation';
import { getRealSize } from '@/common/utils';
import { ATrack } from '@soyoung/react-native-container';
import api, { FetchModule } from '@/common/api';
import Empty from './Empty';

/**
 * 员工选择弹窗组件
 * 支持搜索、单选/多选模式、分页加载
 */

export interface ActiveItem {
  id: number;
  name: string;
}

interface User {
  employee_id: number;
  name: string;
  tenant_list: {
    tenant_id: number;
    name: string;
  }[];
}

export type Active = ActiveItem | ActiveItem[];

export interface EmployeePopupProps {
  /** 当前选中的员工 */
  active?: Active;
  /** 弹窗标题 */
  title?: string;
  /** 员工角色筛选 */
  role?: string;
  /** 是否多选模式 */
  multiple?: boolean;
  /** 选中值变化回调 */
  onActiveChange?: (active: Active) => void;
  /** 关闭弹窗回调 */
  onClose?: () => void;
}

export interface EmployeePopupRef {
  open: () => void;
  close: () => void;
}

const EmployeePopup = forwardRef<EmployeePopupRef, EmployeePopupProps>(
  (
    {
      active,
      title = '',
      role = '',
      multiple = false,
      onActiveChange,
      onClose,
    },
    ref
  ) => {
    const [visible, setVisible] = useState(false);
    const [listActive, setListActive] = useState<Active>(
      multiple
        ? Array.isArray(active)
          ? active
          : []
        : !Array.isArray(active)
          ? active || { id: NaN, name: '' }
          : { id: NaN, name: '' }
    );
    const [searchValue, setSearchValue] = useState('');
    const [userList, setUserList] = useState<User[]>([]);

    // 同步外部 active 到内部状态
    useEffect(() => {
      if (multiple) {
        setListActive(Array.isArray(active) ? active : []);
      } else {
        setListActive(
          !Array.isArray(active)
            ? active || { id: NaN, name: '' }
            : { id: NaN, name: '' }
        );
      }
    }, [active, multiple]);

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
      open: () => {
        setVisible(true);
        getData();
      },
      close: () => {
        setVisible(false);
        setSearchValue('');
        // 重置选中状态到初始值
        if (multiple) {
          setListActive(Array.isArray(active) ? active : []);
        } else {
          setListActive(
            !Array.isArray(active)
              ? active || { id: NaN, name: '' }
              : { id: NaN, name: '' }
          );
        }
        onClose?.();
      },
    }));

    // 更新选中状态
    const updateActive = useCallback(
      (data: ActiveItem) => {
        if (multiple && Array.isArray(listActive)) {
          const exists = listActive.find(item => item.id === data.id);
          let newActive: ActiveItem[];
          if (exists) {
            // 如果已选中，则取消选中
            newActive = listActive.filter(item => item.id !== data.id);
          } else {
            // 如果未选中，则添加选中
            newActive = [...listActive, data];
          }
          setListActive(newActive);
        } else {
          // 单选模式如果是选中状态，则取消选中
          if ((listActive as ActiveItem).id === data.id) {
            setListActive({ id: NaN, name: '' });
          } else {
            setListActive(data);
          }
        }
      },
      [listActive, multiple]
    );

    // 格式化项目列表数据
    const formatList = useCallback((data: User[]): User[] => {
      return data.map(item => ({
        employee_id: item.employee_id,
        name: item.name,
        tenant_list: item.tenant_list,
      }));
    }, []);

    // 过滤后的用户列表
    const filteredUserList = useMemo(() => {
      if (!searchValue.trim()) {
        return userList;
      }
      return userList.filter(item =>
        item.name.toLowerCase().includes(searchValue.toLowerCase())
      );
    }, [userList, searchValue]);

    // 获取员工数据
    const getData = useCallback(
      async (customSearchValue?: string) => {
        try {
          const params: Record<string, string> = {
            name:
              customSearchValue !== undefined ? customSearchValue : searchValue,
          };

          if (role) {
            params.role = role;
          }

          const { responseData, errorCode } = await api.pagefetch({
            path: '/chain-wxapp/v1/employee/search',
            params,
            method: FetchModule.Method.POST,
            isLoading: false,
          });

          if (errorCode === 0) {
            setUserList(formatList(responseData || []));
          }
        } catch (error) {
          // console.error('获取员工列表失败:', error);
        }
      },
      [searchValue, role, formatList]
    );

    // 搜索
    const handleClickToSearch = useCallback(() => {
      getData();
    }, [getData]);

    // 清除搜索
    const handleClearIcon = useCallback(() => {
      setSearchValue('');
      // 清除后重新获取数据，直接传入空字符串
      getData('');
    }, [getData]);

    // 取消操作
    const handleCancel = useCallback(() => {
      setVisible(false);
      setSearchValue('');
      // 重置选中状态到初始值
      if (multiple) {
        setListActive(Array.isArray(active) ? active : []);
      } else {
        setListActive(
          !Array.isArray(active)
            ? active || { id: NaN, name: '' }
            : { id: NaN, name: '' }
        );
      }
      onClose?.();
    }, [onClose, active, multiple]);

    // 确认操作
    const handleConfirm = useCallback(() => {
      onActiveChange?.(listActive);
      setVisible(false);
      setSearchValue('');
      onClose?.();
    }, [listActive, onActiveChange, onClose]);

    // 检查是否选中
    const isSelected = useCallback(
      (selectedItem: User) => {
        if (multiple && Array.isArray(listActive)) {
          return listActive.some(
            activeItem => activeItem.id === selectedItem.employee_id
          );
        } else {
          return (
            !Array.isArray(listActive) &&
            listActive?.id === selectedItem.employee_id
          );
        }
      },
      [listActive, multiple]
    );

    /**
     * FlatList 渲染选项项
     */
    const renderItem: ListRenderItem<User> = useCallback(
      ({ item }) => {
        return (
          <ATrack
            onPress={() =>
              updateActive({
                id: item.employee_id,
                name: item.name,
              })
            }
          >
            <View
              style={[
                styles.userCard,
                isSelected(item) && styles.userCardActive,
              ]}
            >
              {/* 头像部分 */}
              <View style={styles.userCardAvatar}>
                <View style={styles.userCardDefaultAvatar}>
                  <Text style={styles.userCardDefaultAvatarText}>
                    {item.name?.slice(0, 1) || ''}
                  </Text>
                </View>
              </View>

              {/* 信息部分 */}
              <View style={styles.userCardInfo}>
                <Text style={styles.userCardTitle}>{item.name}</Text>
                <Text style={styles.userCardSubtitle} numberOfLines={1}>
                  {`员工号：${item.employee_id}`}
                </Text>
                <Text style={styles.userCardSubtitle} numberOfLines={1}>
                  {item?.tenant_list[0].name}
                </Text>
              </View>
            </View>
          </ATrack>
        );
      },
      [isSelected, updateActive]
    );

    /**
     * FlatList keyExtractor
     */
    const keyExtractor = useCallback((item: User) => `${item.employee_id}`, []);

    /**
     * FlatList getItemLayout 优化
     */
    const getItemLayout = useCallback(
      (_data: User[] | null | undefined, index: number) => {
        // userCard height(90) + marginTop(10) = 100
        const itemHeight = getRealSize(100);
        return {
          length: itemHeight,
          offset: itemHeight * index,
          index,
        };
      },
      []
    );

    return (
      <Modal
        isVisible={visible}
        {...modalAnimation}
        onBackdropPress={handleCancel}
        animationIn='slideInUp'
        animationOut='slideOutDown'
        style={styles.modal}
        useNativeDriver={true}
        avoidKeyboard={false}
        statusBarTranslucent={Platform.OS === 'android'}
      >
        <View style={styles.container}>
          {/* 顶部栏 */}
          <View style={styles.topSection}>
            <View style={styles.titleBar}>
              <ATrack onPress={handleCancel}>
                <Text style={styles.cancelText}>取消</Text>
              </ATrack>
              <Text style={styles.titleText}>{title}</Text>
              <ATrack onPress={handleConfirm}>
                <Text style={styles.confirmText}>确认</Text>
              </ATrack>
            </View>

            {/* 搜索栏 */}
            <View style={styles.searchContainer}>
              <Image
                source={{
                  uri: 'https://static.soyoung.com/sy-pre/1y4a2d4fr2l31-1711955400686.png',
                }}
                style={styles.searchIcon}
              />
              <TextInput
                value={searchValue}
                style={styles.searchInput}
                onChangeText={setSearchValue}
                placeholder='请输入员工名称'
                placeholderTextColor='#DEDEDE'
                onSubmitEditing={handleClickToSearch}
                returnKeyType='done'
              />
              {searchValue.length > 0 && (
                <ATrack onPress={handleClearIcon}>
                  <View style={styles.clearIconWrapper}>
                    <Image
                      source={{
                        uri: 'https://static.soyoung.com/sy-pre/219l297us0uts-1711617000689.png',
                      }}
                      style={styles.clearIcon}
                    />
                  </View>
                </ATrack>
              )}
            </View>
          </View>
          {filteredUserList.length > 0 ? (
            <FlatList
              style={styles.mainContent}
              data={filteredUserList}
              renderItem={renderItem}
              keyExtractor={keyExtractor}
              getItemLayout={getItemLayout}
              showsVerticalScrollIndicator={false}
              maintainVisibleContentPosition={{
                minIndexForVisible: 0,
              }}
              contentContainerStyle={styles.listContainer}
              keyboardShouldPersistTaps='handled'
              overScrollMode='never'
              bounces={false}
              {...flatListProps}
            />
          ) : (
            <Empty />
          )}
        </View>
      </Modal>
    );
  }
);

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  container: {
    width: '100%',
    height: getRealSize(547), // 1094rpx
    backgroundColor: '#ffffff',
  },
  topSection: {
    paddingBottom: getRealSize(10), // 20rpx
  },
  titleBar: {
    height: getRealSize(44), // 88rpx
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: getRealSize(15), // 30rpx
  },
  cancelText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15), // 30rpx
    color: '#999999',
    fontWeight: '400',
  },
  titleText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(16), // 32rpx
    color: '#333333',
    fontWeight: '500',
  },
  confirmText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15), // 30rpx
    color: '#61B43E',
    fontWeight: '400',
  },
  searchContainer: {
    height: getRealSize(36), // 88rpx
    backgroundColor: '#f8f8f8',
    marginHorizontal: getRealSize(15), // 30rpx
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getRealSize(10), // 20rpx
  },
  searchIcon: {
    width: getRealSize(14), // 28rpx
    height: getRealSize(14), // 28rpx
    marginRight: getRealSize(5), // 10rpx
  },
  searchInput: {
    flex: 1,
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(14), // 28rpx
    color: '#333333',
    padding: 0,
  },
  clearIconWrapper: {
    padding: getRealSize(5), // 10rpx
  },
  clearIcon: {
    width: getRealSize(12), // 24rpx
    height: getRealSize(12), // 24rpx
  },
  mainContainer: {
    flex: 1,
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#f6f9f9',
  },
  listContainer: {
    paddingHorizontal: getRealSize(15),
    paddingBottom: getRealSize(10),
  },
  userCard: {
    height: getRealSize(90),
    padding: getRealSize(15), // 30rpx
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderWidth: getRealSize(1), // 2rpx
    borderColor: 'transparent',
    marginTop: getRealSize(10),
  },
  userCardActive: {
    backgroundColor: '#EBFBDC',
    borderColor: '#61B43E',
  },
  userCardAvatar: {
    width: getRealSize(40), // 80rpx
    height: getRealSize(40), // 80rpx
    borderRadius: getRealSize(20), // 40rpx
    overflow: 'hidden',
    marginRight: getRealSize(11), // 22rpx
    flexShrink: 0,
  },
  userCardAvatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: getRealSize(20), // 40rpx
  },
  userCardDefaultAvatar: {
    width: '100%',
    height: '100%',
    borderRadius: getRealSize(20), // 40rpx
    backgroundColor: '#61B43E',
    justifyContent: 'center',
    alignItems: 'center',
  },
  userCardDefaultAvatarText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(18), // 36rpx
    color: '#ffffff',
    fontWeight: '500',
    textAlign: 'center',
  },
  userCardInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  userCardTitle: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(14), // 28rpx
    lineHeight: getRealSize(20), // 40rpx
    color: '#333333',
    fontWeight: '500',
    marginBottom: getRealSize(4), // 8rpx
  },
  userCardSubtitle: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(11), // 22rpx
    lineHeight: getRealSize(16), // 32rpx
    color: '#777777',
    fontWeight: '400',
  },
});

export default EmployeePopup;
