import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, Image, Dimensions } from 'react-native';

import { getRealSize } from '@/common/utils';
import { ATrack } from '@soyoung/react-native-container';
import dayjs from 'dayjs';
import jsApi from '@soyoung/react-native-jsapi';
import DateInput from '@/components/DateInput';
import Modal from 'react-native-modal';
import { modalAnimation } from '../constant/modal_animation';

interface DateCache {
  start: string;
  end: string;
}

interface ConfirmData {
  start: string;
  end: string;
  type: number;
}

interface PopupModalProps {
  visible: boolean;
  onClose: () => void;
  validateFunction?: (start: string, end: string) => boolean;
  onConfirm: (confirmData: ConfirmData) => void;
}

const PopupModal: React.FC<PopupModalProps> = ({
  visible,
  onClose,
  validateFunction,
  onConfirm,
}) => {
  const today = dayjs().format('YYYY-MM-DD');
  const [dateType, setDateType] = useState<number>(1);
  const [cacheDate, setCacheDate] = useState<DateCache>({
    start: '',
    end: '',
  });

  // 处理DateInput日期范围变化
  const handleDateRangeChange = useCallback(
    (value: string | [string, string]) => {
      if (Array.isArray(value)) {
        const newStart = value[0] || '';
        const newEnd = value[1] || '';

        // 时间判断逻辑已经下沉到DateInput组件中，这里直接设置
        setCacheDate({
          start: newStart,
          end: newEnd,
        });
      }
    },
    []
  );

  // 重置时间
  const resetTime = useCallback(() => {
    setCacheDate({
      start: '',
      end: '',
    });
  }, []);

  // 自定义日期确定
  const dateCustomDetermination = useCallback(() => {
    if (!cacheDate.start || !cacheDate.end) {
      jsApi.toNative('showToast', {
        toast: '请选择时间范围',
      });
      return;
    }
    if (validateFunction && !validateFunction(cacheDate.start, cacheDate.end)) {
      return;
    }

    // 时间判断逻辑已经在DateInput组件中处理，直接使用当前值
    onConfirm({
      start: cacheDate.start,
      end: cacheDate.end,
      type: 3,
    });
    onClose();
  }, [cacheDate, onConfirm, onClose, validateFunction]);

  // 设置日期类型
  const handleSetDateType = useCallback(
    (type: number) => {
      setDateType(type);
      if (type === 1) {
        // 今日：直接设置今天的日期
        setCacheDate({
          start: today,
          end: today,
        });
        onConfirm({
          start: today,
          end: today,
          type: 1,
        });
        onClose();
        return;
      }

      if (type === 2) {
        // 本月：直接设置本月第一天到今天
        const startOfMonth = dayjs().startOf('month').format('YYYY-MM-DD');
        const endOfMonth = dayjs().endOf('month').format('YYYY-MM-DD');
        setCacheDate({
          start: startOfMonth,
          end: endOfMonth,
        });
        onConfirm({
          start: startOfMonth,
          end: endOfMonth,
          type: 2,
        });
        onClose();
        return;
      }

      if (type === 3) {
        // 自定义：设置默认值但不关闭弹窗，等待用户选择日期范围
        // setCacheDate({
        //   start: today,
        //   end: today,
        // });
        setCacheDate({
          start: '',
          end: '',
        });
      }
    },
    [today, onConfirm, onClose, setCacheDate]
  );

  return (
    <Modal
      isVisible={visible}
      {...modalAnimation}
      deviceWidth={Dimensions.get('window').width}
      animationIn='slideInUp'
      animationOut='slideOutDown'
      style={styles.modal}
      useNativeDriver={true}
      onBackdropPress={onClose}
    >
      <View style={styles.performancePopup}>
        <View style={styles.performancePopupTitle}>
          <Text style={styles.performancePopupTitleText}>选择时间</Text>
        </View>
        <ATrack style={styles.performancePopupClose} onPress={onClose}>
          <Image
            style={styles.performancePopupCloseIcon}
            source={{
              uri: 'https://static.soyoung.com/sy-design/bzsokyai5osd1753688976847.png',
            }}
          />
        </ATrack>
        <ATrack
          style={styles.performancePopupItem}
          onPress={() => handleSetDateType(1)}
        >
          <Text style={styles.performancePopupItemText}>今日</Text>
          <Image
            style={styles.performancePopupItemSelect}
            source={{
              uri:
                dateType === 1
                  ? 'https://static.soyoung.com/sy-design/1i3fb7pl05wlg1753688976845.png'
                  : 'https://static.soyoung.com/sy-design/8k1ijrc526id1753688976836.png',
            }}
          />
        </ATrack>
        <ATrack
          style={styles.performancePopupItem}
          onPress={() => handleSetDateType(2)}
        >
          <Text style={styles.performancePopupItemText}>本月</Text>
          <Image
            style={styles.performancePopupItemSelect}
            source={{
              uri:
                dateType === 2
                  ? 'https://static.soyoung.com/sy-design/1i3fb7pl05wlg1753688976845.png'
                  : 'https://static.soyoung.com/sy-design/8k1ijrc526id1753688976836.png',
            }}
          />
        </ATrack>
        <ATrack
          style={styles.performancePopupItem}
          onPress={() => handleSetDateType(3)}
        >
          <Text style={styles.performancePopupItemText}>自定义</Text>
          <Image
            style={styles.performancePopupItemSelect}
            source={{
              uri:
                dateType === 3
                  ? 'https://static.soyoung.com/sy-design/1i3fb7pl05wlg1753688976845.png'
                  : 'https://static.soyoung.com/sy-design/8k1ijrc526id1753688976836.png',
            }}
          />
        </ATrack>
        {dateType === 3 && (
          <>
            <View style={styles.performancePopupInterval}>
              <DateInput
                value={[cacheDate.start, cacheDate.end]}
                placeholder={['开始日期', '结束日期']}
                type='year-month-day'
                isRange={true}
                rangeSeparator='至'
                suffixIcon='https://static.soyoung.com/sy-pre/2x9igo23omk4j-1711613400704.png'
                onChange={handleDateRangeChange}
              />
            </View>
            <View style={styles.performancePopupButton}>
              <ATrack
                style={styles.performancePopupButtonReset}
                onPress={resetTime}
              >
                <View style={styles.performancePopupButtonResetTextContainer}>
                  <Text style={styles.performancePopupButtonResetText}>
                    重置
                  </Text>
                </View>
              </ATrack>
              <ATrack
                style={styles.performancePopupButtonConfirm}
                onPress={dateCustomDetermination}
              >
                <Text style={styles.performancePopupButtonConfirmText}>
                  确认
                </Text>
              </ATrack>
            </View>
          </>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  performancePopup: {
    backgroundColor: '#fff',
    paddingHorizontal: getRealSize(15),
    paddingBottom: getRealSize(20),
    // borderBottomLeftRadius: getRealSize(12),
    // borderBottomRightRadius: getRealSize(12),
  },
  performancePopupClose: {
    position: 'absolute',
    right: getRealSize(15),
    top: getRealSize(15),
    width: getRealSize(20),
    height: getRealSize(20),
  },
  performancePopupCloseIcon: {
    width: getRealSize(20),
    height: getRealSize(20),
  },
  performancePopupTitle: {
    paddingVertical: getRealSize(15),
    justifyContent: 'center',
    alignItems: 'center',
  },
  performancePopupTitleText: {
    fontSize: getRealSize(17),
    color: '#030303',
    fontWeight: '500',
    textAlign: 'center',
    fontFamily: 'PingFangSC-Medium',
  },
  performancePopupItem: {
    height: getRealSize(20),
    marginVertical: getRealSize(10),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  performancePopupItemText: {
    fontSize: getRealSize(15),
    color: '#030303',
  },
  performancePopupItemSelect: {
    width: getRealSize(17),
    height: getRealSize(17),
  },
  performancePopupInterval: {
    marginVertical: getRealSize(10),
    paddingHorizontal: getRealSize(8),
  },
  performancePopupButton: {
    marginTop: getRealSize(25),
    flexDirection: 'row',
    justifyContent: 'center',
  },
  performancePopupButtonReset: {
    borderStyle: 'solid',
    width: getRealSize(155),
    height: getRealSize(42),
    alignItems: 'center',
    justifyContent: 'center',
  },
  performancePopupButtonResetTextContainer: {
    width: getRealSize(155),
    height: getRealSize(42),
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#333333',
  },
  performancePopupButtonResetText: {
    fontSize: getRealSize(13),
    color: '#333333',
    fontWeight: '500',
  },
  performancePopupButtonConfirm: {
    marginLeft: getRealSize(15),
    width: getRealSize(155),
    height: getRealSize(42),
    backgroundColor: '#333333',
    alignItems: 'center',
    justifyContent: 'center',
  },
  performancePopupButtonConfirmText: {
    fontSize: getRealSize(13),
    color: '#fff',
    fontWeight: '500',
  },
});

export default PopupModal;
