import React, { useEffect, useState, useCallback } from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import { getRealSize } from '../common/utils';
import { ATrack } from '@soyoung/react-native-container';

/**
 * 多选标签展示组件
 * 用于展示已选择的多个标签，支持添加和删除操作
 */

export interface MultiSelectItem {
  id: number;
  name: string;
}

export interface MultiSelectProps {
  /** 已选中的标签列表 */
  active?: MultiSelectItem[];
  /** 添加按钮显示文字 */
  buttonText?: string;
  /** 点击添加按钮回调 */
  onPress?: () => void;
  /** 点击删除标签回调 */
  onRemove?: (id: number) => void;
}

const MultiSelect: React.FC<MultiSelectProps> = ({
  active = [],
  buttonText = '请选择',
  onPress,
  onRemove,
}) => {
  const [compActive, setCompActive] = useState<MultiSelectItem[]>(active);

  // 监听 active 变化并同步到内部状态
  useEffect(() => {
    setCompActive(active);
  }, [active]);

  const handleOpen = useCallback(() => {
    onPress?.();
  }, [onPress]);

  const handleRemove = useCallback(
    (id: number) => {
      onRemove?.(id);
    },
    [onRemove]
  );

  return (
    <View style={styles.container}>
      {/* 添加按钮 */}
      <ATrack style={styles.addButton} onPress={handleOpen}>
        <Image
          source={{
            uri: 'https://static.soyoung.com/sy-pre/a21xeugzis9k-1713863400669.png',
          }}
          style={styles.addButtonImage}
          resizeMode='contain'
        />
        <Text style={styles.addButtonText}>{buttonText}</Text>
      </ATrack>

      {/* 已选标签列表 */}
      {compActive.map(item => (
        <ATrack
          key={item.id}
          style={styles.selectedItem}
          onPress={() => handleRemove(item.id)}
        >
          <View style={styles.selectedItemBox}>
            <Text style={styles.selectedItemText} numberOfLines={1}>
              {item.name}
            </Text>
            <Image
              source={{
                uri: 'https://static.soyoung.com/sy-pre/219l297us0uts-1711617000689.png',
              }}
              style={styles.selectedItemImage}
              resizeMode='contain'
            />
          </View>
        </ATrack>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: getRealSize(-10), // -20rpx
  },
  addButton: {
    height: getRealSize(38), // 76rpx
    backgroundColor: '#f8f8f8',
    paddingHorizontal: getRealSize(10), // 20rpx
    marginRight: getRealSize(10), // 20rpx
    marginBottom: getRealSize(10), // 20rpx
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: getRealSize(2), // 2rpx
    borderColor: 'transparent',
  },
  addButtonImage: {
    width: getRealSize(8), // 16rpx
    height: getRealSize(8), // 16rpx
    marginRight: getRealSize(5), // 10rpx
  },
  addButtonText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13), // 26rpx
    color: '#333333',
    fontWeight: '400',
  },
  selectedItem: {
    marginRight: getRealSize(10), // 20rpx
    marginBottom: getRealSize(10), // 20rpx
  },
  selectedItemBox: {
    maxWidth: getRealSize(260), // 300rpx
    paddingHorizontal: getRealSize(10), // 30rpx
    flexDirection: 'row',
    alignItems: 'center',
    height: getRealSize(38), // 76rpx
    borderWidth: getRealSize(2), // 2rpx
    borderColor: '#333333',
    backgroundColor: '#ffffff',
  },
  selectedItemText: {
    maxWidth: getRealSize(230), // 300rpx
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13), // 26rpx
    color: '#333333',
    fontWeight: '400',
  },
  selectedItemImage: {
    width: getRealSize(14), // 16rpx
    height: getRealSize(14), // 16rpx
    marginLeft: getRealSize(5), // 10rpx
  },
});

export default MultiSelect;
