{"name": "@components/smart-table", "version": "1.0.0", "description": "一个功能强大、高性能的React Native表格组件，支持吸顶表头、固定第一列、合并表头、排序等功能", "main": "index.tsx", "types": "index.d.ts", "keywords": ["react-native", "table", "component", "sticky", "header", "column", "sort", "performance"], "author": "Staff App Team", "license": "MIT", "files": ["index.tsx", "index.d.ts", "Examples.tsx", "README.md"], "peerDependencies": {"react": ">=17.0.0", "react-native": ">=0.68.0"}, "repository": {"type": "git", "url": "staffrnapp/src/components/SmartTable"}, "bugs": {"url": "staffrnapp/issues"}, "homepage": "staffrnapp/src/components/SmartTable#readme"}