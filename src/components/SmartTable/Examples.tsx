import React, { useState, useCallback } from 'react';
import { View, StyleSheet, ScrollView, Text, Alert } from 'react-native';
import SmartTable, { SortDirection, SortCallback } from './index';
import { getRealSize } from '@/common/utils';

// 示例数据
const createSampleData = () => {
  const employees = [
    '张三',
    '李四',
    '王五',
    '赵六',
    '孙七',
    '周八',
    '吴九',
    '郑十',
  ];
  const months = ['1月', '2月', '3月', '4月', '5月', '6月'];

  return {
    employees,
    months,
    performanceData: employees.map(name => [
      name,
      Math.floor(Math.random() * 50) + 50, // 目标值
      Math.floor(Math.random() * 60) + 40, // 实际完成
      `${Math.floor(Math.random() * 50) + 75}%`, // 完成率
    ]),
    monthlyData: months.map(month => [
      month,
      ...Array(9)
        .fill(0)
        .map(() => Math.floor(Math.random() * 100) + 50), // 3组数据，每组3列
    ]),
  };
};

const SmartTableExamples: React.FC = () => {
  const [sampleData] = useState(createSampleData());

  // 排序处理函数
  const handleSort: SortCallback = useCallback(
    (columnIndex: number, direction: SortDirection) => {
      Alert.alert('排序', `列 ${columnIndex} 按 ${direction} 排序`);
    },
    []
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Text style={styles.title}>SmartTable 组件使用示例</Text>

      {/* 示例1: 吸顶 + 固定第一列 + 普通表头 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          1. 吸顶表格 + 固定第一列 + 普通表头
        </Text>
        <Text style={styles.description}>
          最常用的配置：表头吸顶，第一列固定，支持排序
        </Text>
        <View style={styles.tableContainer}>
          <SmartTable
            headerData={{
              columns: ['员工', '目标值', '实际完成', '完成率'],
            }}
            tableData={sampleData.performanceData}
            widthArr={[
              getRealSize(80),
              getRealSize(100),
              getRealSize(100),
              getRealSize(100),
            ]}
            stickyHeader={true}
            stickyFirstColumn={true}
            sortable={true}
            onSort={handleSort}
            style={{ height: getRealSize(300) }}
          />
        </View>
      </View>

      {/* 示例2: 吸顶 + 合并表头 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>2. 吸顶表格 + 合并表头</Text>
        <Text style={styles.description}>支持分组表头，适用于复杂数据展示</Text>
        <View style={styles.tableContainer}>
          <SmartTable
            headerData={{
              groups: [
                { name: '执行业绩', columns: 3 },
                { name: '咨询业绩', columns: 3 },
                { name: '手术业绩', columns: 3 },
              ],
              columns: [
                '月份',
                '执行-目标值',
                '执行-实际完成',
                '执行-完成率',
                '咨询-目标值',
                '咨询-实际完成',
                '咨询-完成率',
                '手术-目标值',
                '手术-实际完成',
                '手术-完成率',
              ],
            }}
            tableData={sampleData.monthlyData}
            widthArr={[
              getRealSize(60), // 月份
              ...Array(9).fill(getRealSize(80)), // 9列数据
            ]}
            stickyHeader={true}
            stickyFirstColumn={true}
            style={{ height: getRealSize(300) }}
          />
        </View>
      </View>

      {/* 示例3: 普通表格（不吸顶） */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>3. 普通表格（不吸顶不固定）</Text>
        <Text style={styles.description}>传统表格模式，所有内容一起滚动</Text>
        <View style={styles.tableContainer}>
          <SmartTable
            headerData={{
              columns: ['员工', '目标值', '实际完成', '完成率'],
            }}
            tableData={sampleData.performanceData}
            widthArr={[
              getRealSize(80),
              getRealSize(100),
              getRealSize(100),
              getRealSize(100),
            ]}
            stickyHeader={false}
            stickyFirstColumn={false}
            style={{ height: getRealSize(300) }}
          />
        </View>
      </View>

      {/* 示例4: 自定义样式 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>4. 自定义样式表格</Text>
        <Text style={styles.description}>自定义颜色、字体、边框等样式</Text>
        <View style={styles.tableContainer}>
          <SmartTable
            headerData={{
              columns: ['员工', '目标值', '实际完成', '完成率'],
            }}
            tableData={sampleData.performanceData.slice(0, 5)}
            widthArr={[
              getRealSize(80),
              getRealSize(100),
              getRealSize(100),
              getRealSize(100),
            ]}
            stickyHeader={true}
            stickyFirstColumn={true}
            headerStyle={{ backgroundColor: '#4CAF50' }}
            headerTextStyle={{ color: '#ffffff', fontWeight: 'bold' }}
            textStyle={{ color: '#333333', fontSize: getRealSize(15) }}
            borderStyle={{ borderWidth: 2, borderColor: '#4CAF50' }}
            enableAlternateRowColor={true}
            evenRowColor='#f0f8f0'
            oddRowColor='#e8f5e8'
            style={{ height: getRealSize(250) }}
          />
        </View>
      </View>

      {/* 示例5: 只固定表头不固定第一列 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>5. 只固定表头（不固定第一列）</Text>
        <Text style={styles.description}>
          表头吸顶，但第一列不固定，适用于列数较少的场景
        </Text>
        <View style={styles.tableContainer}>
          <SmartTable
            headerData={{
              columns: ['员工', '目标值', '实际完成', '完成率'],
            }}
            tableData={sampleData.performanceData}
            widthArr={[
              getRealSize(80),
              getRealSize(100),
              getRealSize(100),
              getRealSize(100),
            ]}
            stickyHeader={true}
            stickyFirstColumn={false}
            style={{ height: getRealSize(300) }}
          />
        </View>
      </View>

      {/* 示例6: 只固定第一列不吸顶 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>6. 只固定第一列（表头不吸顶）</Text>
        <Text style={styles.description}>第一列固定，但表头不吸顶</Text>
        <View style={styles.tableContainer}>
          <SmartTable
            headerData={{
              columns: ['员工', '目标值', '实际完成', '完成率'],
            }}
            tableData={sampleData.performanceData}
            widthArr={[
              getRealSize(80),
              getRealSize(100),
              getRealSize(100),
              getRealSize(100),
            ]}
            stickyHeader={false}
            stickyFirstColumn={true}
            style={{ height: getRealSize(300) }}
          />
        </View>
      </View>

      {/* 示例7: 性能优化配置 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>7. 大数据量性能优化</Text>
        <Text style={styles.description}>处理大量数据时的性能优化配置</Text>
        <View style={styles.tableContainer}>
          <SmartTable
            headerData={{
              columns: ['序号', '数据1', '数据2', '数据3', '数据4', '数据5'],
            }}
            tableData={Array(100)
              .fill(0)
              .map((_, index) => [
                `${index + 1}`,
                Math.floor(Math.random() * 1000),
                Math.floor(Math.random() * 1000),
                Math.floor(Math.random() * 1000),
                Math.floor(Math.random() * 1000),
                Math.floor(Math.random() * 1000),
              ])}
            widthArr={Array(6).fill(getRealSize(80))}
            stickyHeader={true}
            stickyFirstColumn={true}
            removeClippedSubviews={true}
            maxToRenderPerBatch={20}
            windowSize={15}
            scrollEventThrottle={16}
            style={{ height: getRealSize(300) }}
          />
        </View>
      </View>

      <View style={{ height: getRealSize(50) }} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: getRealSize(16),
  },
  title: {
    fontSize: getRealSize(24),
    fontWeight: 'bold',
    color: '#333333',
    textAlign: 'center',
    marginBottom: getRealSize(20),
  },
  section: {
    marginBottom: getRealSize(30),
  },
  sectionTitle: {
    fontSize: getRealSize(18),
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: getRealSize(8),
  },
  description: {
    fontSize: getRealSize(14),
    color: '#666666',
    marginBottom: getRealSize(12),
    lineHeight: getRealSize(20),
  },
  tableContainer: {
    backgroundColor: '#ffffff',
    borderRadius: getRealSize(8),
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
});

export default SmartTableExamples;
