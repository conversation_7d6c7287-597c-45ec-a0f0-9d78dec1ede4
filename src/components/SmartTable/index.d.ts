// 类型导出
export type SortDirection = 'asc' | 'desc' | 'none';

export interface HeaderGroup {
  name: string;
  columns: number;
}

export interface HeaderData {
  groups?: HeaderGroup[];
  columns: string[];
}

export interface BorderStyle {
  borderWidth?: number;
  borderColor?: string;
}

export type SortCallback = (
  columnIndex: number,
  direction: SortDirection
) => void;

export interface SmartTableProps {
  // 数据相关
  headerData: HeaderData;
  tableData: (string | number)[][];
  widthArr?: number[];

  // 尺寸相关
  rowHeight?: number;
  headerHeight?: number;

  // 样式相关
  style?: import('react-native').ViewStyle;
  headerStyle?: import('react-native').ViewStyle;
  rowStyle?: import('react-native').ViewStyle;
  textStyle?: import('react-native').TextStyle;
  headerTextStyle?: import('react-native').TextStyle;
  firstColumnTextStyle?: import('react-native').TextStyle;
  columnHeaderTextStyle?: import('react-native').TextStyle; // 列表头文字样式
  borderStyle?: BorderStyle;

  // 功能配置
  stickyHeader?: boolean;
  stickyFirstColumn?: boolean;
  sortable?: boolean;
  onSort?: SortCallback;
  currentSortColumn?: number | null;
  currentSortDirection?: SortDirection;

  // 性能优化相关
  removeClippedSubviews?: boolean;
  maxToRenderPerBatch?: number;
  windowSize?: number;
  scrollEventThrottle?: number;

  // 交替行颜色
  enableAlternateRowColor?: boolean;
  evenRowColor?: string;
  oddRowColor?: string;
}

// 主组件
declare const SmartTable: React.FC<SmartTableProps>;
export default SmartTable;
