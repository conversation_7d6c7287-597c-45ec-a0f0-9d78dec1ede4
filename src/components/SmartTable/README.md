# SmartTable 智能表格组件

一个功能强大、高性能的React Native表格组件，支持吸顶表头、固定第一列、合并表头、排序等功能。

## ✨ 特性

- 🚀 **高性能**: 支持大数据量虚拟化渲染
- 📌 **吸顶表头**: 表头固定在顶部，内容可滚动
- 🔒 **固定第一列**: 第一列固定，其他列可横向滚动
- 📊 **合并表头**: 支持分组表头，适用于复杂数据展示
- 🔄 **排序功能**: 内置排序UI，支持自定义排序逻辑
- 🎨 **样式自定义**: 丰富的样式配置选项
- 📱 **响应式**: 适配不同屏幕尺寸
- ⚡ **流畅滚动**: 优化的滚动性能，无卡顿

## 📦 安装

```bash
# 该组件已集成到项目中，无需单独安装
```

## 📁 目录结构

```
src/components/SmartTable/
├── index.tsx          # 主组件文件
├── index.d.ts         # TypeScript 类型声明
├── Examples.tsx       # 使用示例组件
├── exports.ts         # 统一导出文件
├── package.json       # 组件包描述
├── README.md          # 详细文档
└── USAGE.md          # 快速使用指南
```

## 🚀 快速开始

### 基础用法

```tsx
import SmartTable from '@/components/SmartTable';

const BasicExample = () => {
  const headerData = {
    columns: ['姓名', '年龄', '职位', '薪资']
  };
  
  const tableData = [
    ['张三', 28, '前端开发', '15K'],
    ['李四', 32, '后端开发', '18K'],
    ['王五', 25, 'UI设计师', '12K'],
  ];
  
  return (
    <SmartTable
      headerData={headerData}
      tableData={tableData}
      style={{ height: 300 }}
    />
  );
};
```

### 吸顶表格 + 固定第一列

```tsx
const StickyExample = () => {
  return (
    <SmartTable
      headerData={{
        columns: ['员工', '目标值', '实际完成', '完成率']
      }}
      tableData={data}
      stickyHeader={true}           // 表头吸顶
      stickyFirstColumn={true}      // 固定第一列
      widthArr={[80, 100, 100, 100]} // 自定义列宽
      style={{ height: 400 }}
    />
  );
};
```

### 合并表头

```tsx
const GroupedHeaderExample = () => {
  return (
    <SmartTable
      headerData={{
        groups: [
          { name: '执行业绩', columns: 3 },
          { name: '咨询业绩', columns: 3 }
        ],
        columns: [
          '月份',
          '执行-目标值', '执行-实际完成', '执行-完成率',
          '咨询-目标值', '咨询-实际完成', '咨询-完成率'
        ]
      }}
      tableData={monthlyData}
      stickyHeader={true}
      stickyFirstColumn={true}
    />
  );
};
```

### 支持排序

```tsx
const SortableExample = () => {
  const handleSort = (columnIndex: number, direction: 'asc' | 'desc') => {
    console.log(`排序列: ${columnIndex}, 方向: ${direction}`);
    // 实现你的排序逻辑
  };
  
  return (
    <SmartTable
      headerData={headerData}
      tableData={tableData}
      sortable={true}
      onSort={handleSort}
    />
  );
};
```

## 📋 API 文档

### SmartTableProps

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| **数据相关** |
| `headerData` | `HeaderData` | - | 表头数据配置 |
| `tableData` | `(string \| number)[][]` | - | 表格数据 |
| `widthArr` | `number[]` | - | 列宽数组 |
| **尺寸相关** |
| `rowHeight` | `number` | `getRealSize(50)` | 行高 |
| `headerHeight` | `number` | `getRealSize(50)` | 表头高度 |
| **样式相关** |
| `style` | `ViewStyle` | - | 容器样式 |
| `headerStyle` | `ViewStyle` | - | 表头样式 |
| `rowStyle` | `ViewStyle` | - | 行样式 |
| `textStyle` | `TextStyle` | - | 文本样式 |
| `headerTextStyle` | `TextStyle` | - | 表头文本样式 |
| `borderStyle` | `BorderStyle` | `{borderWidth: 1, borderColor: '#e9e9ed'}` | 边框样式 |
| **功能配置** |
| `stickyHeader` | `boolean` | `true` | 是否吸顶 |
| `stickyFirstColumn` | `boolean` | `true` | 是否固定第一列 |
| `sortable` | `boolean` | `false` | 是否支持排序 |
| `onSort` | `SortCallback` | - | 排序回调函数 |
| **性能优化** |
| `removeClippedSubviews` | `boolean` | `true` | 移除屏幕外视图 |
| `maxToRenderPerBatch` | `number` | `10` | 每批渲染数量 |
| `windowSize` | `number` | `10` | 窗口大小 |
| `scrollEventThrottle` | `number` | `32` | 滚动事件节流 |
| **交替行颜色** |
| `enableAlternateRowColor` | `boolean` | `true` | 启用交替行颜色 |
| `evenRowColor` | `string` | `'#ffffff'` | 偶数行颜色 |
| `oddRowColor` | `string` | `'#f8f8f8'` | 奇数行颜色 |

### HeaderData

```tsx
interface HeaderData {
  groups?: HeaderGroup[];  // 合并表头的分组信息
  columns: string[];       // 列标题
}

interface HeaderGroup {
  name: string;    // 分组名称
  columns: number; // 该分组包含的列数
}
```

### SortCallback

```tsx
type SortCallback = (columnIndex: number, direction: SortDirection) => void;
type SortDirection = 'asc' | 'desc' | 'none';
```

## 🎛️ 使用模式

### 1. 吸顶模式 (默认)
- `stickyHeader={true}` + `stickyFirstColumn={true}`
- 适用场景：数据量大，需要始终看到表头和第一列的情况

### 2. 普通模式
- `stickyHeader={false}` + `stickyFirstColumn={false}`
- 适用场景：数据量较小，传统表格展示

### 3. 只吸顶模式
- `stickyHeader={true}` + `stickyFirstColumn={false}`
- 适用场景：列数较少，只需要固定表头

### 4. 只固定第一列
- `stickyHeader={false}` + `stickyFirstColumn={true}`
- 适用场景：第一列是关键信息，表头可以滚动

## 🎨 样式定制

### 自定义主题色

```tsx
<SmartTable
  headerStyle={{ backgroundColor: '#4CAF50' }}
  headerTextStyle={{ color: '#ffffff', fontWeight: 'bold' }}
  borderStyle={{ borderWidth: 2, borderColor: '#4CAF50' }}
  evenRowColor="#f0f8f0"
  oddRowColor="#e8f5e8"
/>
```

### 自定义字体

```tsx
<SmartTable
  textStyle={{ 
    fontSize: 16, 
    fontFamily: 'PingFangSC-Regular',
    color: '#333333'
  }}
  headerTextStyle={{ 
    fontSize: 14, 
    fontFamily: 'PingFangSC-Semibold',
    color: '#666666'
  }}
/>
```

## ⚡ 性能优化

### 大数据量优化

```tsx
<SmartTable
  removeClippedSubviews={true}
  maxToRenderPerBatch={20}
  windowSize={15}
  scrollEventThrottle={16}
  // 其他配置...
/>
```

### 内存优化
- 使用 `removeClippedSubviews` 移除屏幕外视图
- 合理设置 `maxToRenderPerBatch` 控制渲染批次
- 使用 `windowSize` 控制渲染窗口大小

## 📱 响应式设计

组件内部使用 `getRealSize()` 函数适配不同屏幕密度，确保在不同设备上显示一致。

```tsx
import { getRealSize } from '@/common/utils';

const widthArr = [
  getRealSize(80),  // 适配后的像素值
  getRealSize(100),
  getRealSize(120),
];
```

## 🔧 故障排除

### 常见问题

1. **横向滚动不流畅**
   - 检查 `scrollEventThrottle` 设置
   - 确保 `widthArr` 配置正确
   - 减少 `maxToRenderPerBatch` 值

2. **表头与列不对齐**
   - 确保 `widthArr` 长度与列数匹配
   - 检查 `headerData.columns` 配置

3. **大数据量卡顿**
   - 启用 `removeClippedSubviews`
   - 调整 `maxToRenderPerBatch` 和 `windowSize`
   - 优化数据结构

### 调试技巧

```tsx
// 开启性能监控
<SmartTable
  onLayout={(event) => console.log('表格布局:', event.nativeEvent.layout)}
  // 其他配置...
/>
```

## 📚 更多示例

查看 `SmartTableExamples.tsx` 文件获取完整的使用示例，包括：

- 基础表格
- 吸顶表格
- 合并表头
- 自定义样式
- 性能优化
- 大数据量处理

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来完善这个组件！

## �� 许可证

MIT License 