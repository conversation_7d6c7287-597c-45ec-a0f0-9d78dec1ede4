# SmartTable 快速使用指南

## 📦 导入方式

```typescript
// 方式1: 通过组件目录导入 (推荐)
import SmartTable from '@/components/SmartTable';

// 方式2: 通过主索引导入
import { SmartTable } from '@/components';

// 方式3: 导入示例组件
import { SmartTableExamples } from '@/components/SmartTable/exports';

// 方式4: 导入类型
import type { SmartTableProps, HeaderData } from '@/components/SmartTable';
```

## 🚀 基础用法

### 1. 最简单的表格

```tsx
import SmartTable from '@/components/SmartTable';

const SimpleTable = () => (
  <SmartTable
    headerData={{ columns: ['姓名', '年龄', '职位'] }}
    tableData={[
      ['张三', 28, '前端开发'],
      ['李四', 32, '后端开发'],
      ['王五', 25, 'UI设计师']
    ]}
  />
);
```

### 2. 吸顶表格 (默认配置)

```tsx
const StickyTable = () => (
  <SmartTable
    headerData={{ columns: ['员工', '目标值', '实际完成', '完成率'] }}
    tableData={performanceData}
    stickyHeader={true}        // 表头吸顶 (默认)
    stickyFirstColumn={true}   // 固定第一列 (默认)
    style={{ height: 400 }}
  />
);
```

### 3. 合并表头

```tsx
const GroupedHeaderTable = () => (
  <SmartTable
    headerData={{
      groups: [
        { name: '执行业绩', columns: 3 },
        { name: '咨询业绩', columns: 3 }
      ],
      columns: [
        '月份',
        '执行-目标值', '执行-实际完成', '执行-完成率',
        '咨询-目标值', '咨询-实际完成', '咨询-完成率'
      ]
    }}
    tableData={monthlyData}
  />
);
```

## 🎛️ 配置选项

### 功能开关

```tsx
<SmartTable
  // 基础功能
  stickyHeader={true}          // 表头吸顶
  stickyFirstColumn={true}     // 固定第一列
  sortable={true}              // 支持排序
  
  // 性能优化
  removeClippedSubviews={true} // 移除屏幕外视图
  scrollEventThrottle={16}     // 滚动事件节流
  
  // 交替行颜色
  enableAlternateRowColor={true}
  evenRowColor="#ffffff"
  oddRowColor="#f8f8f8"
/>
```

### 样式定制

```tsx
<SmartTable
  // 容器和尺寸
  style={{ height: 400 }}
  rowHeight={getRealSize(50)}
  headerHeight={getRealSize(50)}
  
  // 外观样式
  headerStyle={{ backgroundColor: '#f1f3f3' }}
  borderStyle={{ borderWidth: 1, borderColor: '#e9e9ed' }}
  
  // 文本样式
  textStyle={{ fontSize: 14, color: '#333' }}
  headerTextStyle={{ fontSize: 13, fontWeight: '500' }}
/>
```

## 📋 常用配置模板

### 员工绩效表格

```tsx
const PerformanceTable = () => (
  <SmartTable
    headerData={{ columns: ['员工', '目标值', '实际完成', '完成率'] }}
    tableData={performanceData}
    widthArr={[getRealSize(80), getRealSize(100), getRealSize(100), getRealSize(100)]}
    stickyHeader={true}
    stickyFirstColumn={true}
    sortable={true}
    onSort={handleSort}
    style={{ height: getRealSize(400) }}
  />
);
```

### 月度数据表格

```tsx
const MonthlyDataTable = () => (
  <SmartTable
    headerData={{
      groups: [
        { name: '执行业绩', columns: 3 },
        { name: '咨询业绩', columns: 3 },
        { name: '手术业绩', columns: 3 }
      ],
      columns: [
        '月份',
        '执行-目标值', '执行-实际完成', '执行-完成率',
        '咨询-目标值', '咨询-实际完成', '咨询-完成率',
        '手术-目标值', '手术-实际完成', '手术-完成率'
      ]
    }}
    tableData={monthlyData}
    widthArr={[getRealSize(60), ...Array(9).fill(getRealSize(80))]}
    stickyHeader={true}
    stickyFirstColumn={true}
  />
);
```

### 大数据量表格

```tsx
const BigDataTable = () => (
  <SmartTable
    headerData={{ columns: ['序号', '数据1', '数据2', '数据3', '数据4'] }}
    tableData={bigDataArray}
    // 性能优化配置
    removeClippedSubviews={true}
    maxToRenderPerBatch={20}
    windowSize={15}
    scrollEventThrottle={16}
    style={{ height: getRealSize(400) }}
  />
);
```

## 🔍 排序功能

```tsx
const SortableTable = () => {
  const handleSort = useCallback((columnIndex: number, direction: 'asc' | 'desc' | 'none') => {
    console.log(`排序列: ${columnIndex}, 方向: ${direction}`);
    // 实现排序逻辑
    const sortedData = [...tableData].sort((a, b) => {
      if (direction === 'asc') {
        return a[columnIndex] > b[columnIndex] ? 1 : -1;
      } else if (direction === 'desc') {
        return a[columnIndex] < b[columnIndex] ? 1 : -1;
      }
      return 0;
    });
    setTableData(sortedData);
  }, [tableData]);

  return (
    <SmartTable
      headerData={{ columns: ['姓名', '年龄', '薪资'] }}
      tableData={tableData}
      sortable={true}
      onSort={handleSort}
    />
  );
};
```

## 📱 响应式适配

```tsx
import { getRealSize } from '@/common/utils';

const ResponsiveTable = () => (
  <SmartTable
    headerData={{ columns: ['项目', '数据1', '数据2'] }}
    tableData={data}
    // 使用 getRealSize 适配不同屏幕密度
    widthArr={[getRealSize(100), getRealSize(120), getRealSize(120)]}
    rowHeight={getRealSize(50)}
    headerHeight={getRealSize(45)}
    textStyle={{ fontSize: getRealSize(14) }}
    headerTextStyle={{ fontSize: getRealSize(13) }}
  />
);
```

## 🎨 主题定制

```tsx
// 绿色主题
const GreenThemeTable = () => (
  <SmartTable
    headerData={{ columns: ['项目', '状态', '进度'] }}
    tableData={projectData}
    headerStyle={{ backgroundColor: '#4CAF50' }}
    headerTextStyle={{ color: '#ffffff', fontWeight: 'bold' }}
    borderStyle={{ borderWidth: 2, borderColor: '#4CAF50' }}
    evenRowColor="#f0f8f0"
    oddRowColor="#e8f5e8"
  />
);

// 蓝色主题
const BlueThemeTable = () => (
  <SmartTable
    headerData={{ columns: ['用户', '等级', '积分'] }}
    tableData={userData}
    headerStyle={{ backgroundColor: '#2196F3' }}
    headerTextStyle={{ color: '#ffffff' }}
    borderStyle={{ borderColor: '#2196F3' }}
    evenRowColor="#f3f8ff"
    oddRowColor="#e3f2fd"
  />
);
```

## 🚨 注意事项

1. **数据格式**: `tableData` 必须是二维数组，第一个元素对应第一列
2. **列宽配置**: `widthArr` 长度应与 `headerData.columns` 长度一致
3. **性能优化**: 大数据量时记得启用 `removeClippedSubviews`
4. **滚动同步**: 横向滚动已优化，无需额外配置
5. **样式继承**: 子元素样式会继承父容器的部分样式

## 📚 完整示例

查看 `Examples.tsx` 文件获取更多完整的使用示例。 