import React, { useMemo, useCallback, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
  Image,
} from 'react-native';
import { getRealSize } from '@/common/utils';

// 排序方向
export type SortDirection = 'asc' | 'desc' | 'none';

// 表头组配置
export interface HeaderGroup {
  name: string;
  columns: number;
}

// 表头数据结构
export interface HeaderData {
  groups?: HeaderGroup[]; // 合并表头的分组信息
  columns: string[]; // 列标题
}

// 边框样式
export interface BorderStyle {
  borderWidth?: number;
  borderColor?: string;
}

// 排序回调函数
export type SortCallback = (
  columnIndex: number,
  direction: SortDirection
) => void;

// 主要属性接口
export interface SmartTableProps {
  // 数据相关
  headerData: HeaderData;
  tableData: (string | number)[][];
  widthArr?: number[];

  // 尺寸相关
  rowHeight?: number;
  headerHeight?: number;

  // 样式相关
  style?: ViewStyle;
  headerStyle?: ViewStyle;
  rowStyle?: ViewStyle;
  textStyle?: TextStyle;
  headerTextStyle?: TextStyle;
  firstColumnTextStyle?: TextStyle; // 首列文字样式
  columnHeaderTextStyle?: TextStyle; // 列表头文字样式
  borderStyle?: BorderStyle;

  // 功能配置
  stickyHeader?: boolean; // 是否吸顶
  stickyFirstColumn?: boolean; // 是否固定第一列
  sortable?: boolean; // 是否支持排序
  onSort?: SortCallback;
  currentSortColumn?: number | null; // 当前排序的列索引
  currentSortDirection?: SortDirection; // 当前排序方向

  // 性能优化相关
  removeClippedSubviews?: boolean;
  maxToRenderPerBatch?: number;
  windowSize?: number;
  scrollEventThrottle?: number;

  // 交替行颜色
  enableAlternateRowColor?: boolean;
  evenRowColor?: string;
  oddRowColor?: string;
}

const SmartTable: React.FC<SmartTableProps> = ({
  headerData,
  tableData,
  widthArr,
  rowHeight = getRealSize(49),
  headerHeight = getRealSize(36),
  style,
  headerStyle,
  rowStyle,
  textStyle,
  headerTextStyle,
  firstColumnTextStyle,
  columnHeaderTextStyle,
  borderStyle = { borderWidth: 1, borderColor: '#e9e9ed' },
  stickyHeader = true,
  stickyFirstColumn = true,
  sortable = false,
  onSort,
  currentSortColumn,
  currentSortDirection,
  removeClippedSubviews = true,
  scrollEventThrottle = 32,
  enableAlternateRowColor = true,
  evenRowColor = '#ffffff',
  oddRowColor = '#f8f8f8',
}) => {
  // 滚动引用和状态
  const headerScrollRef = useRef<ScrollView>(null);
  const contentScrollRef = useRef<ScrollView>(null);
  const scrollSyncLock = useRef(false);

  // 计算总宽度
  const totalWidth = useMemo(() => {
    if (widthArr) {
      return widthArr.reduce((sum, width) => sum + width, 0);
    }
    return headerData.columns.length * getRealSize(100);
  }, [widthArr, headerData.columns.length]);

  // 第一列宽度
  const firstColumnWidth = widthArr?.[0] || getRealSize(85);

  // 可滚动区域宽度
  const scrollableWidth =
    totalWidth - (stickyFirstColumn ? firstColumnWidth : 0);

  // 检查是否有分组表头
  const hasGroups = headerData.groups && headerData.groups.length > 0;
  const actualHeaderHeight = hasGroups ? headerHeight * 2 : headerHeight;

  // 滚动同步处理（仅在需要时启用）
  const handleContentScroll = useCallback(
    (event: any) => {
      if (!stickyHeader || scrollSyncLock.current) return;

      const offsetX = event.nativeEvent.contentOffset.x;
      headerScrollRef.current?.scrollTo({ x: offsetX, animated: false });
    },
    [stickyHeader]
  );

  // 排序处理
  const handleSort = useCallback(
    (columnIndex: number) => {
      if (!sortable || !onSort) return;

      // 简单的排序方向切换逻辑，实际应用中可能需要记录当前排序状态
      onSort(columnIndex, 'asc');
    },
    [sortable, onSort]
  );

  // 渲染排序图标
  const renderSortIcon = useCallback(
    (columnIndex: number) => {
      if (!sortable) return null;
      // 获取当前列的排序图标
      const getSortIconUri = () => {
        if (
          currentSortColumn === columnIndex &&
          currentSortDirection !== 'none'
        ) {
          if (currentSortDirection === 'asc') {
            return 'https://static.soyoung.com/sy-pre/1qf3y75jz1uc7-1755148200622.png'; // 向上箭头
          } else if (currentSortDirection === 'desc') {
            return 'https://static.soyoung.com/sy-pre/1jrih3obd0oiu-1755148200622.png'; // 向下箭头
          }
        }
        return 'https://static.soyoung.com/sy-pre/1v91zt10la4ln-1714025400691.png'; // 默认图标
      };

      return (
        <TouchableOpacity
          style={styles.sortButton}
          onPress={() => handleSort(columnIndex)}
          activeOpacity={0.7}
          hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
        >
          <Image source={{ uri: getSortIconUri() }} style={styles.sortIcon} />
        </TouchableOpacity>
      );
    },
    [sortable, handleSort, currentSortColumn, currentSortDirection]
  );

  // 渲染表头内容
  const renderHeaderContent = () => {
    if (hasGroups) {
      // 分组表头
      const { groups = [] } = headerData;
      let columnIndex = 1; // 跳过第一列

      return (
        <View style={styles.groupedHeader}>
          {/* 第一行：分组名称 */}
          <View style={styles.groupRow}>
            {groups.map((group, groupIndex) => {
              const groupWidth =
                group.columns * (widthArr?.[columnIndex] || getRealSize(100));
              columnIndex += group.columns;
              const isLastGroup = groupIndex === groups.length - 1;

              return (
                <View
                  key={`group_${groupIndex}`}
                  style={[
                    styles.groupCell,
                    { width: groupWidth, height: headerHeight },
                    headerStyle,
                    isLastGroup && { borderRightWidth: 0 }, // 移除最右组的右边框
                  ]}
                >
                  <Text
                    style={[styles.groupText, headerTextStyle]}
                    allowFontScaling={false}
                  >
                    {group.name}
                  </Text>
                </View>
              );
            })}
          </View>

          {/* 第二行：列名称 */}
          <View style={styles.columnRow}>
            {headerData.columns.slice(1).map((column, index) => {
              const isLastColumn =
                index === headerData.columns.slice(1).length - 1;
              return (
                <View
                  key={`column_${index}`}
                  style={[
                    styles.columnCell,
                    {
                      width: widthArr?.[index + 1] || getRealSize(100),
                      height: headerHeight,
                    },
                    headerStyle,
                    isLastColumn && { borderRightWidth: 0 }, // 移除最右列的右边框
                  ]}
                >
                  <Text
                    style={[
                      styles.columnText,
                      columnHeaderTextStyle || headerTextStyle,
                    ]}
                    allowFontScaling={false}
                  >
                    {column}
                  </Text>
                  {renderSortIcon(index + 1)}
                </View>
              );
            })}
          </View>
        </View>
      );
    } else {
      // 简单表头
      return (
        <View style={styles.simpleHeader}>
          <View style={styles.columnRow}>
            {headerData.columns.slice(1).map((column, index) => {
              const isLastColumn =
                index === headerData.columns.slice(1).length - 1;
              return (
                <View
                  key={`simple_column_${index}`}
                  style={[
                    styles.columnCell,
                    {
                      width: widthArr?.[index + 1] || getRealSize(100),
                      height: actualHeaderHeight,
                    },
                    headerStyle,
                    isLastColumn && { borderRightWidth: 0 }, // 移除最右列的右边框
                  ]}
                >
                  <Text
                    style={[
                      styles.columnText,
                      columnHeaderTextStyle || headerTextStyle,
                    ]}
                    allowFontScaling={false}
                  >
                    {column}
                  </Text>
                  {renderSortIcon(index + 1)}
                </View>
              );
            })}
          </View>
        </View>
      );
    }
  };

  // 渲染固定表头
  const renderStickyHeader = () => (
    <View style={styles.headerWrapper}>
      <View style={[styles.headerContainer, { height: actualHeaderHeight }]}>
        {/* 固定第一列表头 */}
        {stickyFirstColumn && (
          <View
            style={[
              styles.fixedColumn,
              { width: firstColumnWidth, height: actualHeaderHeight },
            ]}
          >
            <View
              style={[
                styles.headerCell,
                styles.fixedHeaderCell,
                { height: actualHeaderHeight },
                headerStyle,
              ]}
            >
              <Text
                style={[styles.headerText, headerTextStyle]}
                allowFontScaling={false}
              >
                {headerData.columns[0]}
              </Text>
            </View>
          </View>
        )}

        {/* 可滚动表头区域 */}
        <ScrollView
          ref={headerScrollRef}
          horizontal
          style={[
            styles.scrollableHeaderArea,
            { marginLeft: stickyFirstColumn ? 0 : 0 },
          ]}
          showsHorizontalScrollIndicator={false}
          bounces={false}
          scrollEnabled={false}
          scrollEventThrottle={scrollEventThrottle}
        >
          <View style={{ width: scrollableWidth }}>
            {renderHeaderContent()}
          </View>
        </ScrollView>
      </View>
    </View>
  );

  // 渲染普通表头
  const renderNormalHeader = () => (
    <View
      style={[styles.normalHeaderContainer, { height: actualHeaderHeight }]}
    >
      {/* 第一列表头 */}
      <View
        style={[
          styles.headerCell,
          { width: firstColumnWidth, height: actualHeaderHeight },
          headerStyle,
        ]}
      >
        <Text
          style={[styles.headerText, headerTextStyle]}
          allowFontScaling={false}
        >
          {headerData.columns[0]}
        </Text>
      </View>

      {/* 其他列表头 */}
      <ScrollView
        ref={headerScrollRef}
        horizontal
        style={styles.scrollableHeaderArea}
        showsHorizontalScrollIndicator={false}
        bounces={false}
      >
        <View style={{ width: scrollableWidth }}>{renderHeaderContent()}</View>
      </ScrollView>
    </View>
  );

  // 渲染数据行
  const renderDataRows = () => {
    const rows = tableData.map((item, index) => {
      const rowBackgroundColor = enableAlternateRowColor
        ? index % 2 === 0
          ? evenRowColor
          : oddRowColor
        : 'transparent';
      const isLastRow = index === tableData.length - 1;

      return (
        <View
          key={`row_${index}`}
          style={[
            styles.dataRow,
            { height: rowHeight, backgroundColor: rowBackgroundColor },
            rowStyle,
          ]}
        >
          {/* 固定第一列数据 */}
          {stickyFirstColumn && (
            <View
              style={[
                styles.stickyCell,
                { width: firstColumnWidth, height: rowHeight },
                isLastRow && { borderBottomWidth: 0 }, // 移除最后一行的底边框
              ]}
            >
              <Text
                style={[styles.cellText, textStyle, firstColumnTextStyle]}
                allowFontScaling={false}
              >
                {item[0]}
              </Text>
            </View>
          )}

          {/* 可滚动数据区域 */}
          <ScrollView
            horizontal
            style={[
              styles.horizontalScroll,
              { marginLeft: stickyFirstColumn ? 0 : 0 },
            ]}
            showsHorizontalScrollIndicator={false}
            bounces={false}
            scrollEventThrottle={scrollEventThrottle}
            onScroll={index === 0 ? handleContentScroll : undefined} // 只从第一行同步滚动
          >
            <View style={{ width: scrollableWidth, flexDirection: 'row' }}>
              {item
                .slice(stickyFirstColumn ? 1 : 0)
                .map((cellData, cellIndex) => {
                  const actualCellIndex = stickyFirstColumn
                    ? cellIndex + 1
                    : cellIndex;
                  const isLastColumn =
                    cellIndex ===
                    item.slice(stickyFirstColumn ? 1 : 0).length - 1;
                  return (
                    <View
                      key={`data_cell_${cellIndex}`}
                      style={[
                        styles.cell,
                        {
                          width:
                            widthArr?.[actualCellIndex] || getRealSize(100),
                          height: rowHeight,
                        },
                        isLastColumn && { borderRightWidth: 0 }, // 移除最右列的右边框
                        isLastRow && { borderBottomWidth: 0 }, // 移除最后一行的底边框
                      ]}
                    >
                      <Text
                        style={[styles.cellText, textStyle]}
                        allowFontScaling={false}
                      >
                        {cellData}
                      </Text>
                    </View>
                  );
                })}
            </View>
          </ScrollView>
        </View>
      );
    });

    return rows;
  };

  // 根据配置渲染不同模式的表格
  if (stickyHeader) {
    // 吸顶模式
    return (
      <View style={[styles.container, borderStyle, style]}>
        {/* 固定表头 */}
        <View style={styles.stickyHeaderContainer}>{renderStickyHeader()}</View>

        {/* 主滚动区域 */}
        <ScrollView
          style={styles.mainScrollView}
          showsVerticalScrollIndicator={false}
          scrollEventThrottle={scrollEventThrottle}
          nestedScrollEnabled={true}
          removeClippedSubviews={removeClippedSubviews}
          contentContainerStyle={{ flexGrow: 0 }}
        >
          {/* 内容区域 - 为固定表头留出空间 */}
          <View
            style={[styles.contentArea, { paddingTop: actualHeaderHeight }]}
          >
            <View style={styles.contentContainer}>
              {/* 固定第一列 */}
              {stickyFirstColumn && (
                <View
                  style={[
                    styles.stickyFirstColumn,
                    { width: firstColumnWidth },
                  ]}
                >
                  {tableData.map((item, index) => {
                    const rowBackgroundColor = enableAlternateRowColor
                      ? index % 2 === 0
                        ? evenRowColor
                        : oddRowColor
                      : 'transparent';
                    const isLastRow = index === tableData.length - 1;

                    return (
                      <View
                        key={`sticky_cell_${index}`}
                        style={[
                          styles.stickyCell,
                          {
                            height: rowHeight,
                            backgroundColor: rowBackgroundColor,
                          },
                          isLastRow && { borderBottomWidth: 0 }, // 移除最后一行的底边框
                        ]}
                      >
                        <Text
                          style={[
                            styles.cellText,
                            textStyle,
                            firstColumnTextStyle,
                          ]}
                          allowFontScaling={false}
                        >
                          {item[0]}
                        </Text>
                      </View>
                    );
                  })}
                </View>
              )}

              {/* 可滚动内容区域 */}
              <ScrollView
                ref={contentScrollRef}
                horizontal
                style={[
                  styles.horizontalScroll,
                  { marginLeft: stickyFirstColumn ? firstColumnWidth : 0 },
                ]}
                showsHorizontalScrollIndicator={false}
                bounces={false}
                onScroll={handleContentScroll}
                scrollEventThrottle={scrollEventThrottle}
              >
                <View style={{ width: scrollableWidth }}>
                  {tableData.map((item, index) => {
                    const rowBackgroundColor = enableAlternateRowColor
                      ? index % 2 === 0
                        ? evenRowColor
                        : oddRowColor
                      : 'transparent';
                    const isLastRow = index === tableData.length - 1;

                    return (
                      <View
                        key={`row_${index}`}
                        style={[
                          styles.dataRow,
                          {
                            height: rowHeight,
                            backgroundColor: rowBackgroundColor,
                          },
                        ]}
                      >
                        {item
                          .slice(stickyFirstColumn ? 1 : 0)
                          .map((cellData, cellIndex) => {
                            const actualCellIndex = stickyFirstColumn
                              ? cellIndex + 1
                              : cellIndex;
                            const isLastColumn =
                              cellIndex ===
                              item.slice(stickyFirstColumn ? 1 : 0).length - 1;
                            return (
                              <View
                                key={`data_cell_${cellIndex}`}
                                style={[
                                  styles.cell,
                                  {
                                    width:
                                      widthArr?.[actualCellIndex] ||
                                      getRealSize(100),
                                  },
                                  isLastColumn && { borderRightWidth: 0 }, // 移除最右列的右边框
                                  isLastRow && { borderBottomWidth: 0 }, // 移除最后一行的底边框
                                ]}
                              >
                                <Text
                                  style={[styles.cellText, textStyle]}
                                  allowFontScaling={false}
                                >
                                  {cellData}
                                </Text>
                              </View>
                            );
                          })}
                      </View>
                    );
                  })}
                </View>
              </ScrollView>
            </View>
          </View>
        </ScrollView>
      </View>
    );
  } else {
    // 普通模式
    return (
      <View style={[styles.container, borderStyle, style]}>
        <ScrollView
          style={styles.normalScrollView}
          showsVerticalScrollIndicator={false}
          scrollEventThrottle={scrollEventThrottle}
          removeClippedSubviews={removeClippedSubviews}
          contentContainerStyle={{ flexGrow: 0 }}
        >
          {/* 普通表头 */}
          {renderNormalHeader()}

          {/* 数据行 */}
          {renderDataRows()}
        </ScrollView>
      </View>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 0,
    backgroundColor: '#ffffff',
  },

  // 吸顶模式样式
  stickyHeaderContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    backgroundColor: '#ffffff',
  },
  mainScrollView: {
    flex: 0,
  },
  contentArea: {
    flex: 0,
    minHeight: 0, // 确保内容区域不会有最小高度限制
  },
  contentContainer: {
    flexDirection: 'row',
    minHeight: 0, // 确保容器不会有最小高度限制
  },
  stickyFirstColumn: {
    position: 'absolute',
    left: 0,
    top: 0,
    zIndex: 5,
    backgroundColor: '#ffffff',
  },
  horizontalScroll: {
    flex: 0,
  },

  // 普通模式样式
  normalScrollView: {
    flex: 0,
  },
  normalHeaderContainer: {
    flexDirection: 'row',
    backgroundColor: '#f1f3f3',
  },

  // 表头样式
  headerWrapper: {
    backgroundColor: '#ffffff',
  },
  headerContainer: {
    flexDirection: 'row',
  },
  fixedColumn: {
    backgroundColor: '#f1f3f3',
  },
  scrollableHeaderArea: {
    flex: 0,
  },
  headerCell: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: getRealSize(8),
    backgroundColor: '#f1f3f3',
    borderRightWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#e9e9ed',
  },
  fixedHeaderCell: {
    // 移除左边框，避免与容器边框重叠
    // borderLeftWidth: 1,
  },
  headerText: {
    fontSize: getRealSize(13),
    color: '#303233',
    fontWeight: '500',
    textAlign: 'center',
  },

  // 分组表头样式
  groupedHeader: {
    flex: 0,
  },
  groupRow: {
    flexDirection: 'row',
  },
  groupCell: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f1f3f3',
    borderRightWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#e9e9ed',
  },
  groupText: {
    fontSize: getRealSize(13),
    color: '#303233',
    fontWeight: '600',
    textAlign: 'center',
  },
  columnRow: {
    flexDirection: 'row',
  },
  columnCell: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: getRealSize(8),
    backgroundColor: '#f1f3f3',
    borderRightWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#e9e9ed',
    flexDirection: 'row',
  },
  columnText: {
    fontSize: getRealSize(13),
    color: '#303233',
    fontWeight: '500',
    textAlign: 'center',
    flex: 1,
    marginRight: getRealSize(4),
  },

  // 简单表头样式
  simpleHeader: {
    flex: 0,
  },

  // 数据行样式
  dataRow: {
    flexDirection: 'row',
  },
  cell: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: getRealSize(8),
    borderRightWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#e9e9ed',
  },
  cellText: {
    fontSize: getRealSize(14),
    color: '#303233',
    fontWeight: '400',
    textAlign: 'center',
  },
  stickyCell: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: getRealSize(8),
    borderRightWidth: 1,
    borderBottomWidth: 1,
    // 移除左边框，避免与容器边框重叠
    // borderLeftWidth: 1,
    borderColor: '#e9e9ed',
    backgroundColor: '#ffffff',
  },

  // 排序样式
  sortButton: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  sortIcon: {
    width: getRealSize(10),
    height: getRealSize(12),
  },
});

export default SmartTable;
