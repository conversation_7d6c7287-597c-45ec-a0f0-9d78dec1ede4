import React, {
  useState,
  useCallback,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from 'react';
import {
  View,
  Text,
  TextInput,
  ScrollView,
  StyleSheet,
  Image,
  Platform,
} from 'react-native';
import Modal from 'react-native-modal';
import { ATrack } from '@soyoung/react-native-container';
import { getRealSize } from '@/common/utils';
import api, { FetchModule } from '@/common/api';
import Empty from './Empty';
import { modalAnimation } from '../constant/modal_animation';

export interface ActiveItem {
  id: number;
  name: string;
}

interface PerformanceTagItem {
  tag_id: number;
  tag_name: string;
}

export type Active = ActiveItem | ActiveItem[];
/**
 * PerformanceTagPopup 组件属性接口
 */
export interface PerformanceTagPopupProps {
  /** 当前选中的员工 */
  active?: Active;
  /** 弹窗标题 */
  title?: string;
  /** 父级ID */
  pid?: number;
  /** 是否多选模式 */
  multiple?: boolean;
  /** 选中值变化回调 */
  onActiveChange?: (active: Active) => void;
  /** 关闭弹窗回调 */
  onClose?: () => void;
}

/**
 * PerformanceTagPopup 组件暴露的方法接口
 */
export interface PerformanceTagPopupRef {
  open: () => void;
  close: () => void;
}

/**
 * 绩效标签选择弹窗组件
 *
 * 提供标签搜索和选择功能，支持单选/多选模式，
 * 包含搜索功能和完整的弹窗交互体验。
 *
 * @param props - 组件属性
 * @param ref - 组件引用
 * @returns JSX.Element
 */
const PerformanceTagPopup = forwardRef<
  PerformanceTagPopupRef,
  PerformanceTagPopupProps
>(
  (
    { active, title = '', multiple = false, onActiveChange, onClose, pid },
    ref
  ) => {
    const [visible, setVisible] = useState(false);
    const [listActive, setListActive] = useState<Active>(
      multiple
        ? Array.isArray(active)
          ? active
          : []
        : !Array.isArray(active)
          ? active || { id: NaN, name: '' }
          : { id: NaN, name: '' }
    );
    const [searchValue, setSearchValue] = useState('');
    const [performanceTagList, setPerformanceTagList] = useState<
      PerformanceTagItem[]
    >([]);
    const [originPerformanceTagList, setOriginPerformanceTagList] = useState<
      PerformanceTagItem[]
    >([]);

    // 同步外部 active 到内部状态
    useEffect(() => {
      if (multiple) {
        setListActive(Array.isArray(active) ? active : []);
      } else {
        setListActive(
          !Array.isArray(active)
            ? active || { id: NaN, name: '' }
            : { id: NaN, name: '' }
        );
      }
    }, [active, multiple]);

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
      open: () => {
        setVisible(true);
        getData();
      },
      close: () => {
        setVisible(false);
        setSearchValue('');
        onClose?.();
      },
    }));

    // 更新选中状态
    const updateActive = useCallback(
      (data: ActiveItem) => {
        if (multiple && Array.isArray(listActive)) {
          const exists = listActive.find(item => item.id === data.id);
          let newActive: ActiveItem[];

          if (exists) {
            // 如果已选中，则取消选中
            newActive = listActive.filter(item => item.id !== data.id);
          } else {
            // 如果未选中，则添加选中
            newActive = [...listActive, data];
          }
          setListActive(newActive);
        } else {
          // 单选模式
          if ((listActive as ActiveItem).id === data.id) {
            setListActive({ id: NaN, name: '' });
          } else {
            setListActive(data);
          }
        }
      },
      [listActive, multiple]
    );

    // 格式化项目列表数据
    const formatList = useCallback(
      (data: PerformanceTagItem[]): PerformanceTagItem[] => {
        return data.map(item => ({
          tag_id: item.tag_id,
          tag_name: item.tag_name,
        }));
      },
      []
    );
    /**
     * 获取标签数据
     */
    const getData = useCallback(async () => {
      try {
        const { responseData, errorCode } = await api.pagefetch({
          path: '/chain-wxapp/v1/customer/getTagList',
          params: {
            tag_pid: pid,
            status: 1,
          },
          method: FetchModule.Method.POST,
          isLoading: false,
        });

        if (errorCode === 0) {
          const tagList = responseData?.tag_list || [];
          setPerformanceTagList(formatList(tagList));
          setOriginPerformanceTagList(formatList(tagList));
        }
      } catch (error) {
        console.error('获取标签列表失败:', error);
      }
    }, [formatList, pid]);

    /**
     * 搜索树形结构
     * @param trees - 标签树
     * @param searchString - 搜索字符串
     * @returns 搜索结果
     */
    const searchTree = useCallback(
      (
        trees: PerformanceTagItem[],
        searchString: string
      ): PerformanceTagItem[] => {
        const result: PerformanceTagItem[] = [];
        trees.forEach(tree => {
          if (tree.tag_name.includes(searchString)) {
            result.push(tree);
          }
        });
        return result;
      },
      []
    );

    const handleClickToSearch = useCallback(() => {
      if (searchValue === '') {
        setPerformanceTagList([...originPerformanceTagList]);
      } else {
        setPerformanceTagList(
          searchTree(originPerformanceTagList, searchValue)
        );
      }
    }, [searchValue, originPerformanceTagList, searchTree]);

    /**
     * 清除搜索
     */
    const handleClearIcon = useCallback(() => {
      setSearchValue('');
      setPerformanceTagList([...originPerformanceTagList]);
    }, [originPerformanceTagList]);

    // 取消操作
    const handleCancel = useCallback(() => {
      setVisible(false);
      setSearchValue('');
      // 重置选中状态到初始值
      if (multiple) {
        setListActive(Array.isArray(active) ? active : []);
      } else {
        setListActive(
          !Array.isArray(active)
            ? active || { id: NaN, name: '' }
            : { id: NaN, name: '' }
        );
      }
      onClose?.();
    }, [onClose, active, multiple]);

    // 确认操作
    const handleConfirm = useCallback(() => {
      onActiveChange?.(listActive);
      setVisible(false);
      setSearchValue('');
      onClose?.();
    }, [listActive, onActiveChange, onClose]);

    // 检查是否选中
    const isSelected = useCallback(
      (selectedItem: PerformanceTagItem) => {
        if (multiple && Array.isArray(listActive)) {
          return listActive.some(
            activeItem => activeItem.id === selectedItem.tag_id
          );
        } else {
          return (
            !Array.isArray(listActive) && listActive?.id === selectedItem.tag_id
          );
        }
      },
      [listActive, multiple]
    );

    // 监听搜索值变化
    useEffect(() => {
      if (searchValue === '') {
        setPerformanceTagList([...originPerformanceTagList]);
      }
    }, [searchValue, originPerformanceTagList]);

    return (
      <Modal
        isVisible={visible}
        {...modalAnimation}
        style={styles.modal}
        onBackdropPress={handleCancel}
        onBackButtonPress={handleCancel}
        animationIn='slideInUp'
        animationOut='slideOutDown'
        useNativeDriver={true}
        avoidKeyboard={false}
        statusBarTranslucent={Platform.OS === 'android'}
      >
        <View style={styles.container}>
          <View style={styles.topSection}>
            <View style={styles.titleBar}>
              <ATrack onPress={handleCancel}>
                <Text style={styles.cancelText}>取消</Text>
              </ATrack>
              <Text style={styles.titleText}>{title}</Text>
              <ATrack onPress={handleConfirm}>
                <Text style={styles.confirmText}>确认</Text>
              </ATrack>
            </View>

            {/* 搜索栏 */}
            <View style={styles.searchContainer}>
              <Image
                source={{
                  uri: 'https://static.soyoung.com/sy-pre/1y4a2d4fr2l31-1711955400686.png',
                }}
                style={styles.searchIcon}
              />
              <TextInput
                style={styles.searchInput}
                placeholder='请输入标签名称'
                placeholderTextColor='#DEDEDE'
                value={searchValue}
                onChangeText={setSearchValue}
                onSubmitEditing={handleClickToSearch}
                returnKeyType='done'
              />
              {searchValue.length > 0 && (
                <ATrack onPress={handleClearIcon}>
                  <View style={styles.clearIconWrapper}>
                    <Image
                      source={{
                        uri: 'https://static.soyoung.com/sy-pre/219l297us0uts-1711617000689.png',
                      }}
                      style={styles.clearIcon}
                    />
                  </View>
                </ATrack>
              )}
            </View>
          </View>

          {/* 标签列表 */}
          <ScrollView
            style={styles.tagBody}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
            {performanceTagList.length > 0 ? (
              <View style={styles.tagContainer}>
                {performanceTagList.map((item, index) => {
                  return (
                    <ATrack
                      key={index}
                      style={styles.tagItem}
                      onPress={() =>
                        updateActive({
                          id: item.tag_id,
                          name: item.tag_name,
                        })
                      }
                    >
                      <View
                        style={[
                          styles.tagItemContent,
                          isSelected(item) && styles.tagItemContentSelected,
                        ]}
                      >
                        <Text style={styles.tagText}>{item.tag_name}</Text>
                        {isSelected(item) ? (
                          <Image
                            source={{
                              uri: 'https://static.soyoung.com/sy-design/3o6q6zpv0xqzv1753259861585.png',
                            }}
                            style={styles.checkIcon}
                          />
                        ) : null}
                      </View>
                    </ATrack>
                  );
                })}
              </View>
            ) : (
              <Empty />
            )}
          </ScrollView>
        </View>
      </Modal>
    );
  }
);

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  container: {
    width: '100%',
    height: getRealSize(547),
    backgroundColor: '#ffffff',
  },
  topSection: {
    paddingBottom: getRealSize(10), // 20rpx
  },
  titleBar: {
    height: getRealSize(44), // 88rpx
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: getRealSize(15), // 30rpx
  },
  cancelText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15), // 30rpx
    color: '#999999',
    fontWeight: '400',
  },
  titleText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(16), // 32rpx
    color: '#333333',
    fontWeight: '500',
  },
  confirmText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15), // 30rpx
    color: '#61B43E',
    fontWeight: '400',
  },
  searchContainer: {
    height: getRealSize(36), // 88rpx
    backgroundColor: '#f5f5f5',
    marginHorizontal: getRealSize(15), // 30rpx
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getRealSize(10), // 20rpx
  },
  searchIcon: {
    width: getRealSize(14), // 28rpx
    height: getRealSize(14), // 28rpx
    marginRight: getRealSize(5), // 10rpx
  },
  searchInput: {
    flex: 1,
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(14), // 28rpx
    color: '#333333',
    padding: 0,
  },
  clearIconWrapper: {
    padding: getRealSize(5), // 10rpx
  },
  clearIcon: {
    width: getRealSize(12), // 24rpx
    height: getRealSize(12), // 24rpx
  },
  tagBody: {
    paddingVertical: getRealSize(10),
    paddingHorizontal: getRealSize(15),
  },
  scrollContent: {
    flexGrow: 1,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingBottom: getRealSize(40),
  },
  tagItem: {
    marginRight: getRealSize(10),
    marginBottom: getRealSize(10),
  },
  tagItemContent: {
    height: getRealSize(38),
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: getRealSize(16),
    backgroundColor: '#F8F8F8',
    borderWidth: 2,
    borderColor: 'transparent',
    position: 'relative',
  },
  tagItemContentSelected: {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#333333',
    borderStyle: 'solid',
  },
  tagText: {
    fontSize: getRealSize(13),
    color: '#333333',
    textAlign: 'center',
  },
  checkIcon: {
    position: 'absolute',
    bottom: getRealSize(-1),
    right: getRealSize(-1),
    width: getRealSize(18),
    height: getRealSize(13),
  },
});

PerformanceTagPopup.displayName = 'PerformanceTagPopup';

export default PerformanceTagPopup;
