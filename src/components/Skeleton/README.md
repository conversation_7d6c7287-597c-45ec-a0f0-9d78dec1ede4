# 骨架图组件使用指南

## 概述

骨架图（Skeleton）组件用于在页面数据加载期间提供占位效果，提升用户体验。

## 组件结构

### 1. 通用骨架组件 (`Skeleton/index.tsx`)

提供基础的闪烁动画占位块：

```typescript
import Skeleton from '../../../components/Skeleton';

<Skeleton
  width={getRealSize(80)}        // 宽度
  height={20}                   // 高度
  borderRadius={4}              // 圆角
  marginBottom={8}              // 下边距
/>
```

**主要特性：**
- 自动闪烁动画效果
- 支持自定义尺寸和边距
- 基于设计稿的响应式适配

### 2. 我的页面专用骨架图 (`MySkeleton.tsx`)

为"我的"页面定制的完整骨架图布局：

```typescript
import MySkeleton from '../components/MySkeleton';

// 在数据加载时显示
{loading && !userInfo ? <MySkeleton /> : <RealContent />}
```

**包含区域：**
- 顶部背景图（203px高度）
- 用户信息区域（头像、姓名、手机号、角色标签）
- 认证组件区域（90px高度）
- 功能菜单列表（门店、账号协同、设置）

## 集成方式

在页面组件中，根据数据加载状态切换显示：

```typescript
const MyPageLayout: React.FC<MyProps> = props => {
  const [loading, setLoading] = useState(true);
  const [userInfo, setUserInfo] = useState(null);

  // 数据加载中显示骨架图
  if (loading && !userInfo) {
    return <MySkeleton />;
  }

  // 数据加载完成显示真实内容
  return <RealPageContent />;
};
```

## 设计规范

- **颜色：** #E1E9EE（浅灰色占位块）
- **动画：** 0.3-1.0透明度闪烁，800ms周期
- **尺寸：** 基于750rpx设计稿，使用getRealSize()适配
- **布局：** 与真实页面保持一致的间距和比例

## 注意事项

1. 骨架图应与真实内容布局完全对应
2. 确保在数据加载完成后及时切换显示
3. 动画效果使用原生驱动，性能优化
4. 遵循项目的响应式设计规范
