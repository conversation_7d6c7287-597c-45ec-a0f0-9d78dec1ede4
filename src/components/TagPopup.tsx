import React, {
  useState,
  useEffect,
  useCallback,
  forwardRef,
  useImperativeHandle,
} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  Image,
  ScrollView,
  Alert,
  Platform,
} from 'react-native';
import Modal from 'react-native-modal';
import { modalAnimation } from '@/constant/modal_animation';
import { ATrack } from '@soyoung/react-native-container';
import Empty from './Empty';
import { getRealSize } from '@/common/utils';
import api, { FetchModule } from '@/common/api';

/**
 * 标签项数据接口
 */
interface TagItem {
  /** 标签ID */
  tag_id: number;
  /** 标签名称 */
  tag_name: string;
  /** 标签颜色 */
  tag_color: string;
  /** 父级标签ID */
  tag_pid: number;
  /** 标签状态（1-有效）0-无效） */
  status: number;
}

/**
 * 标签组数据接口
 */
interface TagGroup {
  /** 标签组ID */
  tag_id: number;
  /** 标签组名称 */
  tag_name: string;
  /** 选择类型（1-单选，2-多选） */
  select_type: number;
  /** 子标签列表 */
  children: TagItem[];
}

/**
 * 活动标签项接口
 */
interface ActiveItem {
  /** 标签ID */
  tag_id: number;
  /** 标签名称 */
  name: string;
  /** 标签颜色 */
  tag_color: string;
  /** 选择类型 */
  select_type: number;
  /** 父级标签ID */
  tag_pid: number;
}

/**
 * TagPopup 组件 Props
 */
interface TagPopupProps {
  /** 当前选中的标签ID（或ID数组） */
  active: number | number[];
  /** 标签类型（1-客户标签，2-敏感标签，3-动态标签） */
  type?: number;
  /** 弹窗标题 */
  title?: string;
  /** 是否多选模式 */
  multiple?: boolean;
  /** 标签变更回调 */
  onChange: (data: ActiveItem | ActiveItem[]) => void;
  /** 弹窗关闭回调 */
  onClose: () => void;
}

/**
 * TagPopup 组件引用接口
 */
export interface TagPopupRef {
  /** 打开弹窗 */
  open: (type: number) => void;
  /** 关闭弹窗 */
  close: () => void;
}

/**
 * TagPopup 标签编辑弹窗组件
 *
 * 功能特性：
 * - 标签编辑弹窗，支持搜索
 * - 多选/单选模式
 * - 分组显示标签
 * - API: /chain-wxapp/v1/customer/searchGetGroupTagList
 * - 使用 react-native-modal 实现弹窗功能
 * - 支持组内单选限制
 * - 响应式尺寸适配
 *
 * @param props - 组件属性
 * @param ref - 组件引用
 * @returns JSX.Element
 */

const TagPopup = forwardRef<TagPopupRef, TagPopupProps>(
  (
    { active, title = '客户标签', multiple = false, onChange, onClose },
    ref
  ) => {
    // 状态管理
    const [visible, setVisible] = useState<boolean>(false);
    const [searchValue, setSearchValue] = useState<string>('');
    const [tagList, setTagList] = useState<TagGroup[]>([]);
    const [originTagList, setOriginTagList] = useState<TagGroup[]>([]);
    const [listActive, setListActive] = useState<ActiveItem | ActiveItem[]>(
      multiple
        ? []
        : {
            tag_id: NaN,
            name: '',
            tag_color: '',
            select_type: 1,
            tag_pid: NaN,
          }
    );
    const [tagType, setTagType] = useState<number>(1);

    // 工具函数：在树中查找节点值
    const findNodeValueById = useCallback(
      (tree: TagGroup[], id: number, key: string): string | number => {
        for (const item of tree) {
          if (item.tag_id === id) {
            return (item as any)[key] || '';
          }
          if (item.children) {
            for (const child of item.children) {
              if (child.tag_id === id) {
                return (child as any)[key] || '';
              }
            }
          }
        }
        return '';
      },
      []
    );

    // 工具函数：查找父级 select_type
    const findParentSelectTypeById = useCallback(
      (tree: TagGroup[], id: number): number => {
        for (const item of tree) {
          if (item.children) {
            for (const child of item.children) {
              if (child.tag_id === id) {
                return item.select_type;
              }
            }
          }
        }
        return 1;
      },
      []
    );

    // 更新选中状态的函数
    const updateActiveFromProps = useCallback(() => {
      if (multiple) {
        const activeIds = active as number[];
        setListActive(
          activeIds.map((id: number) => ({
            tag_id: id,
            name: findNodeValueById(originTagList, id, 'tag_name') as string,
            tag_color: findNodeValueById(
              originTagList,
              id,
              'tag_color'
            ) as string,
            select_type: findParentSelectTypeById(originTagList, id),
            tag_pid:
              (findNodeValueById(originTagList, id, 'tag_pid') as number) || 0,
          }))
        );
      } else {
        const activeId = active as number;
        setListActive({
          tag_id: activeId,
          name: findNodeValueById(
            originTagList,
            activeId,
            'tag_name'
          ) as string,
          tag_color: findNodeValueById(
            originTagList,
            activeId,
            'tag_color'
          ) as string,
          select_type: findParentSelectTypeById(originTagList, activeId),
          tag_pid:
            (findNodeValueById(originTagList, activeId, 'tag_pid') as number) ||
            0,
        });
      }
    }, [
      active,
      multiple,
      findNodeValueById,
      findParentSelectTypeById,
      originTagList,
    ]);

    // 获取标签数据
    const getData = useCallback(
      async (searchType: number, keyword?: string) => {
        const searchKeyword = keyword !== undefined ? keyword : searchValue;
        try {
          const response = await api.pagefetch({
            path: '/chain-wxapp/v1/customer/searchGetGroupTagList',
            params: {
              tag_type: searchType,
              key_word: searchKeyword,
            },
            method: FetchModule.Method.POST,
          });

          if (response.errorCode === 0) {
            const tagListData = response.responseData?.tag_list || [];
            setTagList(tagListData);

            if (!searchKeyword) {
              setOriginTagList(tagListData);
            }

            // 更新选中状态
            updateActiveFromProps();
          }
        } catch (error) {
          console.error('获取标签数据失败:', error);
          Alert.alert('提示', '获取标签数据失败');
        }
      },
      [searchValue, updateActiveFromProps]
    );

    // 监听 active prop 变化，当标签数据已加载时更新选中状态
    useEffect(() => {
      if (originTagList.length > 0) {
        updateActiveFromProps();
      }
    }, [active, originTagList, updateActiveFromProps]);

    // 更新选中状态
    const updateActive = useCallback(
      (data: ActiveItem) => {
        if (multiple) {
          let curList = [...(listActive as ActiveItem[])];
          const existingIndex = curList.findIndex(
            item => item.tag_id === data.tag_id
          );

          if (existingIndex >= 0) {
            // 已选中则移除
            curList.splice(existingIndex, 1);
          } else {
            // 新增选择
            if (data.select_type === 1) {
              // 组内单选，移除同组其他选项
              curList = curList.filter(item => item.tag_pid !== data.tag_pid);
            }
            curList.push(data);
          }
          setListActive(curList);
        } else {
          const currentActive = listActive as ActiveItem;
          if (currentActive.tag_id === data.tag_id) {
            // 已选中则取消选择
            setListActive({
              tag_id: NaN,
              name: '',
              tag_color: '',
              select_type: 1,
              tag_pid: NaN,
            });
          } else {
            // 单选模式下，直接替换当前选择
            setListActive(data);
          }
        }
      },
      [multiple, listActive]
    );

    // 搜索功能
    const handleSearch = useCallback(() => {
      setTagList([]);
      getData(tagType);
    }, [tagType, getData]);

    // 清除搜索
    const handleClearSearch = useCallback(() => {
      setSearchValue('');
      setTagList([]);
      getData(tagType, ''); // 直接传入空字符串作为搜索关键词
    }, [tagType, getData]);

    // 取消操作
    const handleCancel = useCallback(() => {
      setVisible(false);
      setSearchValue(''); // 清空搜索框
      setTagList(originTagList); // 重置数据为未搜索状态
      // 重置选中状态到初始值
      updateActiveFromProps();
      onClose();
    }, [onClose, originTagList, updateActiveFromProps]);

    // 确认操作
    const handleConfirm = useCallback(() => {
      onChange(listActive);
      setVisible(false);
      setSearchValue(''); // 清空搜索框
      setTagList(originTagList); // 重置数据为未搜索状态
      onClose();
    }, [listActive, onChange, onClose, originTagList]);

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
      open: (openType: number) => {
        setSearchValue('');
        setTagList([]);
        setTagType(openType);
        getData(openType);
        setVisible(true);
      },
      close: () => {
        setVisible(false);
        setSearchValue(''); // 清空搜索框
        setTagList(originTagList); // 重置数据为未搜索状态
        // 重置选中状态到初始值
        updateActiveFromProps();
        onClose();
      },
    }));

    // 检查标签是否被选中
    const isTagActive = useCallback(
      (tagId: number) => {
        if (multiple) {
          return (listActive as ActiveItem[]).some(
            item => item.tag_id === tagId
          );
        } else {
          return (listActive as ActiveItem).tag_id === tagId;
        }
      },
      [multiple, listActive]
    );

    // 渲染标签按钮
    const renderTagButton = (tag: TagItem, group: TagGroup) => {
      const isActive = isTagActive(tag.tag_id);
      return (
        <ATrack
          key={tag.tag_id}
          style={styles.tagButton}
          onPress={() =>
            updateActive({
              tag_id: tag.tag_id,
              name: tag.tag_name,
              tag_color: tag.tag_color,
              select_type: group.select_type,
              tag_pid: tag.tag_pid,
            })
          }
        >
          <View
            style={[
              styles.tagButtonContent,
              isActive ? styles.tagButtonContentActive : {},
            ]}
          >
            <Text style={styles.tagButtonText}>{tag.tag_name}</Text>
            {isActive ? (
              <Image
                source={{
                  uri: 'https://static.soyoung.com/sy-design/3o6q6zpv0xqzv1753259861585.png',
                }}
                style={styles.checkIcon}
              />
            ) : null}
          </View>
        </ATrack>
      );
    };

    // 过滤标签组
    const filteredTagList = tagList.filter(group => {
      if (!group.children) return false;
      return (
        group.children.filter(child => {
          return child.status === 1 || isTagActive(child.tag_id);
        }).length > 0
      );
    });

    const renderContent = () => {
      return (
        <>
          {/* 顶部栏 */}
          <View style={styles.topBar}>
            <ATrack onPress={handleCancel}>
              <Text style={styles.cancelText}>取消</Text>
            </ATrack>
            <Text style={styles.titleText}>{title}</Text>
            <ATrack onPress={handleConfirm}>
              <Text style={styles.confirmText}>确认</Text>
            </ATrack>
          </View>

          {/* 搜索栏 */}
          <View style={styles.searchContainer}>
            <Image
              style={styles.searchIcon}
              source={{
                uri: 'https://static.soyoung.com/sy-pre/1y4a2d4fr2l31-1711955400686.png',
              }}
            />
            <TextInput
              style={styles.searchInput}
              value={searchValue}
              onChangeText={setSearchValue}
              placeholder='请输入标签或标签组'
              placeholderTextColor='#DEDEDE'
              onSubmitEditing={handleSearch}
              returnKeyType='done'
            />
            {searchValue.length > 0 && (
              <ATrack style={styles.clearButton} onPress={handleClearSearch}>
                <Image
                  style={styles.clearIcon}
                  source={{
                    uri: 'https://static.soyoung.com/sy-pre/219l297us0uts-1711617000689.png',
                  }}
                />
              </ATrack>
            )}
          </View>

          {/* 内容区域 */}
          <View style={styles.body}>
            {filteredTagList.length > 0 ? (
              <ScrollView
                style={styles.scrollView}
                showsVerticalScrollIndicator={false}
              >
                {filteredTagList.map(group => (
                  <View key={group.tag_id} style={styles.groupItem}>
                    <Text style={styles.groupTitle}>{group.tag_name}</Text>
                    <View style={styles.tagButtonList}>
                      {group.children
                        .filter(
                          child =>
                            child.status === 1 || isTagActive(child.tag_id)
                        )
                        .map(child => renderTagButton(child, group))}
                    </View>
                  </View>
                ))}
              </ScrollView>
            ) : (
              <Empty />
            )}
          </View>
        </>
      );
    };
    return (
      <Modal
        isVisible={visible}
        {...modalAnimation}
        onBackdropPress={handleCancel}
        animationIn='slideInUp'
        animationOut='slideOutDown'
        style={styles.modal}
        backdropOpacity={0.7}
        backdropColor='#000'
        avoidKeyboard={false}
        statusBarTranslucent={Platform.OS === 'android'}
      >
        <View style={styles.popupContent}>{renderContent()}</View>
      </Modal>
    );
  }
);

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  popupContent: {
    width: '100%',
    height: getRealSize(547), // 1094rpx / 2
    backgroundColor: '#ffffff',
  },
  topBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: getRealSize(15), // 30rpx / 2
    paddingTop: getRealSize(15),
    paddingBottom: getRealSize(10), // 20rpx / 2
  },
  cancelText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15), // 30rpx / 2
    color: '#777777',
    fontWeight: '400',
  },
  titleText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(16), // 32rpx / 2
    color: '#333333',
    fontWeight: '500',
  },
  confirmText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(15), // 30rpx / 2
    color: '#61B43E',
    fontWeight: '500',
  },
  searchContainer: {
    marginTop: getRealSize(16), // 32rpx / 2
    marginHorizontal: getRealSize(15), // 30rpx / 2
    height: getRealSize(36), // 72rpx / 2
    backgroundColor: '#f8f8f8',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getRealSize(15), // 30rpx / 2
    position: 'relative',
  },
  searchIcon: {
    width: getRealSize(16), // 32rpx / 2
    height: getRealSize(16),
    marginRight: getRealSize(8), // 16rpx / 2
  },
  searchInput: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(15), // 30rpx / 2
    color: '#161616',
    fontWeight: '400',
    padding: 0,
    flex: 1,
  },
  clearButton: {
    width: getRealSize(38), // 76rpx / 2
    height: getRealSize(38),
    position: 'absolute',
    right: 0,
    top: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  clearIcon: {
    width: getRealSize(16), // 32rpx / 2
    height: getRealSize(16),
  },
  body: {
    flex: 1,
    backgroundColor: '#f6f9f9',
    marginTop: getRealSize(10), // 20rpx / 2
  },
  scrollView: {
    flex: 1,
    backgroundColor: '#ffffff',
    paddingHorizontal: getRealSize(15), // 30rpx / 2
    paddingVertical: getRealSize(20), // 40rpx / 2
  },
  groupItem: {
    marginBottom: getRealSize(20), // 40rpx / 2
  },
  groupTitle: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: getRealSize(13), // 32rpx / 2
    lineHeight: getRealSize(17),
    color: '#333333',
    fontWeight: '500',
    marginBottom: getRealSize(10), // 20rpx / 2
  },
  tagButtonList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tagButton: {
    height: getRealSize(38), // 76rpx / 2
    marginBottom: getRealSize(10), // 20rpx / 2
    marginRight: getRealSize(10), // 20rpx / 2
  },
  tagButtonContent: {
    height: getRealSize(38), // 76rpx / 2
    backgroundColor: '#f8f8f8',
    borderWidth: 2,
    borderColor: 'transparent',
    borderStyle: 'solid',
    paddingHorizontal: getRealSize(15), // 30rpx / 2
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  tagButtonContentActive: {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#333333',
    borderStyle: 'solid',
  },
  tagButtonText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: getRealSize(13), // 26rpx / 2
    color: '#555555',
    fontWeight: '400',
    textAlign: 'center',
  },
  checkIcon: {
    position: 'absolute',
    bottom: getRealSize(-1),
    right: getRealSize(-1),
    width: getRealSize(18),
    height: getRealSize(13),
  },
});

export default TagPopup;
