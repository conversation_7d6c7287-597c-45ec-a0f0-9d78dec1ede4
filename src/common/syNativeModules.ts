/**
 * 内部 native 定义的原生方法
 *
 * 这个module为了承接 NativeModules，并且有更好的约束和提示，避免业务层直接接触 native 的模块
 *
 */
import { NativeModules, Platform } from 'react-native';

/**
 * 埋点上报所需类型
 */
export module ReportType {
  export interface ReportParams {
    [k: string]: any;
  }
  export enum ReportEventType {
    NORMAL = 1,
    EXPOSE = 3,
  }

  export interface ReportEventInfoServer {
    [k: string]: any;
  }
  export interface ReportEventInfoClient {
    [k: string]: any;
  }
  export interface PageAutoReportProperty {
    soyoungPageName?: () => string;
    soyoungPageInfo?: () => {
      [key: string]: number | string | boolean | null | undefined;
    };
  }

  /**
   * 埋点上报
   * @param eventName
   * @param eventInfo
   * @param eventType
   */
  export type NativeReportEvent = (
    eventName: string,
    eventInfo: ReportType.ReportParams,
    eventType: ReportType.ReportEventType,
    uuid: string | undefined
  ) => void;

  export type traceEvent = (
    eventName: string,
    eventInfoServer: ReportType.ReportEventInfoServer,
    eventInfoClient: ReportType.ReportEventInfoClient,
    eventType: ReportType.ReportEventType,
    uuid: string | undefined
  ) => void;
}

export const nativeReportEvent = (
  eventName: string,
  eventInfo: ReportType.ReportParams,
  eventType: ReportType.ReportEventType,
  uuid: string | undefined
) => {
  if (Platform.OS === 'ios') {
    NativeModules.SYReactNativeManager.reportEvent(
      eventName,
      eventInfo,
      eventType,
      uuid
    );
  } else {
    NativeModules.SYReactNativeManager.reportEvent(
      eventName,
      eventInfo,
      eventType
    );
  }
};

export const traceEvent = (
  eventInfoClient: ReportType.ReportEventInfoClient = {},
  eventInfoServer: string,
  eventType: ReportType.ReportEventType,
  eventName: string | undefined,
  uuid: string | undefined
) => {
  if (Platform.OS === 'ios') {
    NativeModules.SYReactNativeManager.traceEvent(
      eventInfoClient,
      eventInfoServer,
      eventType,
      eventName,
      uuid
    );
  } else {
    NativeModules.SYReactNativeManager.traceEvent(
      eventInfoClient,
      eventInfoServer,
      eventType,
      eventName
    );
  }
};

/**
 * 网络请求
 */
export module FetchModule {
  export enum API_DOMAIN_TYPE {
    API = '1',
    H5INAPP = '2',
    M = '3',
    WWW = '4',
    IM = '5',
  }
  export interface FetchOptions {
    host: string;
    path: string;
    method: Method;
    isLoading: boolean;
    data: {
      [k: string]: any;
    };
    uuid: string;
  }
  export enum Method {
    GET = 'GET',
    POST = 'POST',
    PATCH = 'PATCH',
    DELETE = 'DELETE',
    POSTRAW = 'POSTRAW',
  }
  export type successCallback = (res: any) => void;
  export type failCallback = (res: any) => void;

  export interface ResponseData<T> {
    errorCode: number;
    errorMsg: string;
    responseData: T & {
      [key: string]: any;
    };
  }
  /**
   * 请求方法类型
   */
  export type FetchFunction = (
    options: FetchModule.FetchOptions,
    success: FetchModule.successCallback,
    fail: FetchModule.failCallback
  ) => void;
}

// 网络请求实现;
export const nativeFetch: FetchModule.FetchFunction =
  NativeModules.SYReactNativeManager.fetch;

/**
 * 路由机制
 */
export declare module NavigationModule {
  export interface RouterForwardParams {
    transitionType: number; // 渐变方式：1 => presenter
    disableAnimation: number; // 是否开启动画：1 => no animation
    isTransparent: boolean;
  }
  export type RouterForwardCallback = (
    error: Error,
    successFlag: boolean
  ) => void;
  export interface NativeNavigation {
    /**
     * 页面跳转。
     * @param url 跳转地址， eg: sy.clinic://
     * @param params 传递的传输，可选
     * @param callback 回调函数，可选
     */
    navigateWithPath: (
      url: string,
      params?: NavigationModule.RouterForwardParams,
      callback?: NavigationModule.RouterForwardCallback
    ) => void;
    /**
     * 页面回退。
     * @param widthAnimated 是否开启动画，true = 开启，false = 关闭
     */
    backAnimated: (widthAnimated: boolean) => void;
  }
}

export const nativeNavigation: NavigationModule.NativeNavigation =
  NativeModules.JSBridgeNavigationPIPE;

/**
 * 桥接
 */
export declare module BridgeModule {
  export interface Js2NativeWithParams {
    // bridge 类型
    bridge_type?: string;
    // bridge 名称
    bridge_name?: string;
    // 回调id;
    seqId?: string;
    // webview Id
    instanceId?: string;
    // 请求数据
    request?: { [k: string]: any };
  }

  export type Js2NativeWithCallback = (
    error: Error,
    seqId: string,
    callbackDataString: string
  ) => void;

  export interface CallbackData {
    status: string;
    xy_device_token?: string;
    uid?: string;
    xy_token?: string;
    // 扩展字段以支持API请求
    user_id?: string;
    token?: string;
    tenant_id?: string;
    lat?: string;
    lng?: string;
    staff_user_id?: string;
  }

  export type Js2NativeWith = (
    bridgeName: string,
    params: Js2NativeWithParams,
    callback: Js2NativeWithCallback
  ) => void;

  export type NativeCurrentEnvBaseUrl = (
    type: FetchModule.API_DOMAIN_TYPE
  ) => Promise<string>;

  export interface JSBridgePIPE {
    js2NativeWith: Js2NativeWith;
    nativeCurrentEnvBaseUrl: NativeCurrentEnvBaseUrl;
  }

  /**
   * 获取当前domain的类型
   */
  export type NativeGetEnvDomain = (
    type: FetchModule.API_DOMAIN_TYPE
  ) => Promise<string>;
}
export const js2Native: BridgeModule.Js2NativeWith =
  NativeModules.JSBridgePIPE.js2NativeWith;

/**
 * 获取当前domain实现
 */
export const nativeGetEnvDomain: BridgeModule.NativeCurrentEnvBaseUrl =
  NativeModules.JSBridgePIPE.nativeCurrentEnvBaseUrl;

export module SYRNContainerEnv {
  // 内置于native的一个计数器
  export function native_counter(key: string): Promise<number> {
    return NativeModules.SYRNContainerEnv.native_counter(key);
  }

  // 内置于native的RN环境获取
  export function native_environment(): Promise<
    'development' | 'production' | 'rn_production'
  > {
    return NativeModules.SYRNContainerEnv.native_environment();
  }
}

export module SYRNPermissionBridge {
  export function getLocationPermission(key: string): Promise<boolean> {
    return NativeModules.SYRNPermissionBridge.getLocationPermission({
      request: {
        key: key,
      },
    });
  }
}

/**
 * 通知Native业务渲染完成，用于性能统计
 */

export const pageRenderFinished = (
  timeInterval: string,
  uuid: string = '',
  userInfo: { [k: string]: string } = {}
) => {
  NativeModules.PageRenderPerformance &&
    NativeModules.PageRenderPerformance.onPageRenderFinishedWithTimeInterval &&
    NativeModules.PageRenderPerformance.onPageRenderFinishedWithTimeInterval(
      timeInterval,
      uuid,
      userInfo
    );
};

export const getDomainPromise = async () => {
  return nativeGetEnvDomain(FetchModule.API_DOMAIN_TYPE.API).catch(
    // 桥接访问肯定是异步的，如果失败了，有一个打底
    () => 'https://api.soyoung.com/'
  );
};

export module PotentialCustomerBridge {
  export function configDoctorQktService(doctorId: string, onScroll: boolean) {
    NativeModules.PotentialCustomerBridge.configQktService({
      request: {
        source_type: '5',
        index_id: doctorId,
        onScroll: onScroll,
      },
    });
  }
}
