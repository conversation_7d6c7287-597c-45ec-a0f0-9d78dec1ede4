import jsApi from '@soyoung/react-native-jsapi';
/**
 * 登录后根据权限判断是否有权限
 * 替换vue中的uni.$storage.permissionList
 */

const checkPermission = async (permission: string) => {
  try {
    // 参数验证
    if (!permission) {
      return false;
    }

    const appInfo: any = await jsApi.toNative('getLoginInfo');

    // 验证appInfo
    if (!appInfo) {
      return false;
    }

    const extInfo: any = appInfo?.ext_info
      ? JSON.parse(appInfo.ext_info || '{}')
      : null;
    if (extInfo?.cross_token) {
      let permissionList = extInfo.cross_permissions || [];
      if (!Array.isArray(permissionList)) {
        if (typeof permissionList === 'string') {
          try {
            permissionList = JSON.parse(permissionList);
          } catch (_) {
            permissionList = [];
          }
        } else {
          permissionList = [];
        }
      }
      return permissionList.includes(permission);
    } else {
      let permissionList = (appInfo as any).permissions || [];
      if (!Array.isArray(permissionList)) {
        if (typeof permissionList === 'string') {
          try {
            permissionList = JSON.parse(permissionList);
          } catch (_) {
            permissionList = [];
          }
        } else {
          permissionList = [];
        }
      }
      return permissionList.includes(permission);
    }
  } catch (error) {
    console.error('checkPermission error:', error);
    return false;
  }
};

const checkRoles = async (roles: string) => {
  try {
    // 参数验证
    if (!roles) {
      return undefined;
    }

    const appInfo: any = await jsApi.toNative('getLoginInfo');

    // 验证appInfo
    if (!appInfo) {
      return undefined;
    }

    const extInfo: any = appInfo?.ext_info
      ? JSON.parse(appInfo.ext_info || '{}')
      : null;
    if (extInfo?.cross_token) {
      let crossRoles: Array<{ id: string; [key: string]: any }> = [];
      const rawCrossRoles = extInfo?.cross_roles;

      if (typeof rawCrossRoles === 'string') {
        try {
          const parsed = JSON.parse(rawCrossRoles);
          crossRoles = Array.isArray(parsed)
            ? (parsed as Array<{ id: string; [key: string]: any }>)
            : [];
        } catch (_) {
          crossRoles = [];
        }
      } else if (Array.isArray(rawCrossRoles)) {
        crossRoles = rawCrossRoles as Array<{ id: string; [key: string]: any }>;
      }

      return crossRoles.find((role: { id: string }) => role?.id === roles);
    } else {
      // 确保roles是一个数组
      let rolesArray = (appInfo as any)?.roles;
      if (!Array.isArray(rolesArray)) {
        if (typeof rolesArray === 'string') {
          rolesArray = JSON.parse(rolesArray);
        } else {
          rolesArray = [];
        }
      }

      return rolesArray.find((role: { id: string }) => role?.id === roles);
    }
  } catch (error) {
    console.error('checkRoles error:', error);
    return undefined;
  }
};

export { checkPermission, checkRoles };
