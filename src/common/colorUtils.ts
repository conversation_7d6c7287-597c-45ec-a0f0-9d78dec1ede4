// 颜色处理工具函数

/**
 * 将十六进制颜色转换为rgba格式
 * @param hex 十六进制颜色值 如 #ffffff
 * @param alpha 透明度 0-1
 * @returns rgba字符串
 */
export const hexToRgb = (hex: string, alpha: number = 1): string => {
  // 移除 # 号
  hex = hex.replace('#', '');

  // 处理三位数的hex
  if (hex.length === 3) {
    hex = hex
      .split('')
      .map(char => char + char)
      .join('');
  }

  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

/**
 * 获取颜色的rgb值
 * @param hex 十六进制颜色值
 * @returns {r, g, b}
 */
export const hexToRgbObject = (hex: string) => {
  hex = hex.replace('#', '');

  if (hex.length === 3) {
    hex = hex
      .split('')
      .map(char => char + char)
      .join('');
  }

  return {
    r: parseInt(hex.substring(0, 2), 16),
    g: parseInt(hex.substring(2, 4), 16),
    b: parseInt(hex.substring(4, 6), 16),
  };
};

/**
 * 判断颜色是否为浅色
 * @param hex 十六进制颜色值
 * @returns boolean
 */
export const isLightColor = (hex: string): boolean => {
  const { r, g, b } = hexToRgbObject(hex);

  // 使用标准亮度计算公式
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;

  return brightness > 128;
};
