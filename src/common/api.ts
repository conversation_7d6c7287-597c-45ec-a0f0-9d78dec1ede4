import {
  nativeFetch,
  nativeGetEnvDomain,
  FetchModule,
  getDomainPromise,
} from './syNativeModules';
export { nativeGetEnvDomain, FetchModule } from './syNativeModules';
import { getNativeLoginInfo } from './getNativeLoginInfo';
module api {
  // 定义登录信息的接口类型1

  const domainPromise = nativeGetEnvDomain(
    FetchModule.API_DOMAIN_TYPE.API
  ).catch(
    // 桥接访问肯定是异步的，如果失败了，有一个打底
    () => 'https://prime-crm.soyoung.com/'
  );

  const addRequestCommonParams = async (params: any) => {
    const storageAppInfo = await getNativeLoginInfo();
    // console.log('storageAppInfo', storageAppInfo);
    const { tenant_id } = storageAppInfo;
    // 使用 Object.assign 合并参数，优先使用传入的参数
    const mergedParams = Object.assign(
      {},
      {
        is_rn: 1, // RN 页面发起的请求统一添加 is_rn 字段
        tenant_id: Number(tenant_id),
      },
      params
    );

    // 将合并后的参数重新赋值给 params
    Object.assign(params, mergedParams);
    return params;
  };

  /**
   * @param path 请求的路径
   * @param params 请求的参数，是一个对象，默认：{}
   * @param method 请求的方法，类型FetchModule.Method，默认：POST
   * @param isLoading 请求过程是否现在loading状态，默认：开启
   * @param domain 请求的域名，如果传入，那么使用传入的值；如果没有传入，根据当前native环境获取域名
   * @param uuid
   * @returns
   */
  export function reactNativeFetch(
    path: string,
    params = {},
    method = FetchModule.Method.POST,
    isLoading = true,
    domain = '',
    uuid: string = ''
  ): Promise<FetchModule.ResponseData<any>> {
    if (path && path.startsWith('/')) {
      path = path.slice(1);
    }

    // 强制关闭loading
    let _isLoading = isLoading;
    _isLoading = false;

    return new Promise(async (resolve, reject) => {
      try {
        const processedParams = await addRequestCommonParams(params);
        const invoker = (host: string) =>
          nativeFetch(
            {
              host,
              path,
              method,
              isLoading: _isLoading,
              data: processedParams,
              uuid: uuid,
            },
            resolve,
            reject
          );
        // 如果开发者自己传入了domain，那么使用开发者的，否则跟随native自身系统
        // console.log('请求参数', domain, path);
        if (domain) {
          return invoker(domain);
        } else {
          return domainPromise.then((host: string) => invoker(host));
        }
      } catch (err) {
        console.log('nativeFetch params error:', err);
        reject(err);
      }
    });
  }

  export async function pagefetch(params: {
    path: string;
    method?: FetchModule.Method;
    params?: any;
    isLoading?: boolean;
    domain?: string;
    uuid?: string;
  }) {
    params.params = params.params || {};
    params.method = params.method || FetchModule.Method.POST;
    params.isLoading = params.isLoading === undefined ? true : params.isLoading;
    params.domain = params.domain || (await getDomainPromise()) || '';
    params.uuid = params.uuid || '';
    return reactNativeFetch(
      params.path,
      params.params,
      params.method,
      params.isLoading,
      params.domain,
      params.uuid
    );
  }
}

export default api;
