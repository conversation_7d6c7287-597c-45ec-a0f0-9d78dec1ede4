/*
 * @Descripttion: your project
 * @Author: 技安
 * @Date: 2023-03-22 17:18:16
 * @LastEditors: 技安
 * @LastEditTime: 2023-12-26 14:40:32
 * 抛出一些常用方法
 */
import { Dimensions, PixelRatio } from 'react-native';

// 设计稿基准宽度 (375px)
const DESIGN_WIDTH = 375;

// 核心尺寸转换函数
export const getRealSize = (size: number): number => {
  const { width: screenWidth } = Dimensions.get('window');
  const scale = screenWidth / DESIGN_WIDTH;
  const realSize = size * scale;
  return PixelRatio.roundToNearestPixel(realSize);
};

// 快捷别名
export const rpx = getRealSize;

/**
 * 节流
 */
export const throttle = <T extends (...args: any[]) => void>(
  func: T,
  delay: number,
  options?: { leading?: boolean }
): T => {
  let timer: ReturnType<typeof setTimeout> | null = null;
  let lastArgs: Parameters<T> | null = null;
  let lastThis: any = null;
  const leading = options?.leading ?? true;
  return function (this: any, ...args: Parameters<T>) {
    if (!timer && leading) {
      func.apply(this, args);
    } else {
      lastArgs = args;
      lastThis = this;
    }
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      if (!leading || (leading && lastArgs)) {
        func.apply(lastThis, lastArgs!);
        lastArgs = null;
        lastThis = null;
      }
      timer = null;
    }, delay);
  } as T;
};

/**
 * 判断空数据
 */
export const isEmpty = (data: any): boolean => {
  // 当数据类型为函数，数字，布尔值时，直接返回false, 表明非空数据
  if (['function', 'number', 'boolean'].includes(typeof data)) {
    return false;
  }
  // 当数据类型为字符串时，去掉空格后看是否为空字符串
  if (typeof data === 'string') {
    return !data.replace(/ /g, '').length;
  }
  // 当数据类型为数组时，看是否是空数组
  if (data instanceof Array) {
    return !data.length;
  }
  // null
  if (data === null) {
    return true;
  }
  // undefind
  if (data === undefined) {
    return true;
  }
  // normal object
  if (data instanceof Object) {
    return !Object.keys(data).length;
  }

  return false;
};

/**
 * 将颜色值转换为指定透明度的rgba格式
 * 支持多种输入格式：hex (#ff0000), rgb (rgb(255,0,0)), rgba (rgba(255,0,0,0.8))
 * @param colorValue 颜色值（支持 hex、rgb、rgba 格式）
 * @param opacity 透明度值，默认为1（不透明）
 * @returns rgba 颜色字符串
 */
export const hexToRgb = (colorValue: string, opacity: number = 1): string => {
  // 处理 rgba 格式输入，提取RGB值并替换透明度
  const rgbaRegex =
    /^rgba?\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})(?:,\s*[\d.]+)?\)$/i;
  const rgbaMatch = colorValue.match(rgbaRegex);

  if (rgbaMatch) {
    const [, r, g, b] = rgbaMatch;
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }

  // 处理十六进制格式输入
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(colorValue);
  if (result) {
    const r = parseInt(result[1], 16);
    const g = parseInt(result[2], 16);
    const b = parseInt(result[3], 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }

  // 如果无法解析，返回默认颜色
  console.warn(`无法解析颜色值: ${colorValue}`);
  return `rgba(0, 0, 0, ${opacity})`;
};

export const isVersionGreaterThanTarget = (version: string, target: string) => {
  const regex = /^\d+(\.\d+){0,2}$/; // 验证版本号格式的正则表达式
  if (!regex.test(version) || !regex.test(target)) {
    console.error('版本号格式不正确');
    return false;
  }

  const currentVersion = version.split('.').map(Number);
  const targetVersion = target.split('.').map(Number);

  for (
    let i = 0;
    i < Math.max(currentVersion.length, targetVersion.length);
    i++
  ) {
    const current = currentVersion[i] || 0;
    const tar = targetVersion[i] || 0;
    console.log('current', current, 'tar', tar);
    if (current > tar) {
      return true;
    } else if (current < tar) {
      return false;
    }
  }

  return true;
};

export const isHexColorValid = (hexColor: string) => {
  if (!hexColor) {
    return false;
  }
  // 判断是否是以 # 开头的 3 个或 6 个十六进制字符
  const regex = /^#([A-Fa-f0-9]{3}){1,2}$/;
  return regex.test(hexColor);
};

/**
 * 安全地处理错误对象，避免访问undefined属性
 * @param error 错误对象
 * @returns 格式化的错误信息
 */
export const safeErrorHandler = (error: any) => {
  const errorInfo = {
    message: '未知错误',
    stack: '无堆栈信息',
    type: typeof error,
    raw: error,
  };

  if (error) {
    if (typeof error === 'string') {
      errorInfo.message = error;
    } else if (error instanceof Error) {
      errorInfo.message = error.message || '错误对象无message属性';
      errorInfo.stack = error.stack || '无堆栈信息';
    } else if (typeof error === 'object') {
      errorInfo.message =
        error.message || error.msg || error.errorMsg || '对象类型错误';
      errorInfo.stack = error.stack || '无堆栈信息';
    }
  }

  return errorInfo;
};
