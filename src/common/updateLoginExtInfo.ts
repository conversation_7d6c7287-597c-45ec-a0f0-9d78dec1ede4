import jsApi from '@soyoung/react-native-jsapi';
import type {
  UpdateLoginExtInfoAction,
  UpdateLoginExtInfoInfo,
  ExtInfo,
  ParamsInfo,
} from '../types/updateLoginExtInfo';

/**
 * 类型安全的 updateLoginExtInfo 调用函数
 * 根据 action 类型自动推断 info 的类型
 */
export const updateLoginExtInfo = <T extends UpdateLoginExtInfoAction>(
  action: T,
  info: UpdateLoginExtInfoInfo<T>
): Promise<any> => {
  // 在调用原生前，将 cross_roles（如果是数组）序列化为字符串，业务层仍可传数组
  const infoForNative: any = { ...(info as any) };
  if (Array.isArray((info as Partial<ExtInfo>)?.cross_roles)) {
    infoForNative.cross_roles = JSON.stringify(
      (info as Partial<ExtInfo>).cross_roles
    );
  }

  if (Array.isArray((info as Partial<ExtInfo>)?.cross_permissions)) {
    infoForNative.cross_permissions = JSON.stringify(
      (info as Partial<ExtInfo>).cross_permissions
    );
  }

  return (jsApi.toNative as any)('updateLoginExtInfo', {
    action,
    info: infoForNative,
  });
};

/**
 * 更新协同信息的便捷函数
 */
export const updateExtInfo = (info: ExtInfo): Promise<any> => {
  return updateLoginExtInfo('ext_info', info);
};

/**
 * 更新基本参数信息的便捷函数
 */
export const updateParamsInfo = (info: ParamsInfo): Promise<any> => {
  return updateLoginExtInfo('params_info', info);
};

/**
 * 清理协同信息的便捷函数
 */
export const clearExtInfo = (): Promise<any> => {
  return updateExtInfo({
    cross_token: undefined,
    cross_permissions: undefined,
    cross_roles: undefined,
    cross_tenant_id: undefined,
    cross_to_uid: undefined,
    cross_tenant_user_id: undefined,
  });
};

/**
 * 设置协同信息的便捷函数
 */
export const setExtInfo = (extInfo: {
  cross_token: string;
  cross_to_uid: string;
  cross_permissions: string[];
  cross_roles: Array<{ id: string; [key: string]: any }>;
  cross_tenant_id: string;
}): Promise<any> => {
  return updateExtInfo(extInfo);
};

/**
 * 设置基本参数信息的便捷函数
 */
export const setParamsInfo = (paramsInfo: {
  token: string;
  tenant_id: string;
}): Promise<any> => {
  return updateParamsInfo(paramsInfo);
};
