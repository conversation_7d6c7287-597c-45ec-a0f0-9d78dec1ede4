import jsApi from '@soyoung/react-native-jsapi';
import { TabBarName } from '../types/common';

const jumpPage = (url: string) => {
  jsApi.toNative('navigateWithPath', {
    url: url,
    params: {
      transitionType: '0',
      disableAnimation: '0',
      isTransparent: false,
    },
  });
};

const jumpReactNativePage = (url: string) => {
  // 拼一下路由协议
  // sy.staff://rn/income/performanceScore?tenant_id=10009&type=2&tenant_user_id=39032
  // const appurl = `app.soyoung://rn/${url}`;
  const appurl = `sy.staff://rn/${url}`;

  jsApi.toNative('navigateWithPath', {
    url: appurl,
    params: {
      transitionType: '0',
      disableAnimation: '0',
      isTransparent: false,
    },
  });
};

const switchTab = (tabBarName: TabBarName) => {
  const appurl = `sy.staff://main/container?page_name=${tabBarName}`;

  jsApi.toNative('navigateWithPath', {
    url: appurl,
    params: {
      transitionType: '0',
      disableAnimation: '0',
      isTransparent: false,
    },
  });
};

export { jumpPage, jumpReactNativePage, switchTab };
