import jsApi from '@soyoung/react-native-jsapi';
import type { ExtInfo } from '../types/updateLoginExtInfo';

interface NativeLoginInfo {
  uid?: string;
  xy_device_token?: string;
  xy_token?: string;
  user_id?: string;
  token?: string;
  tenant_id?: string;
  tenant_user_id?: string;
  lat?: string;
  lng?: string;
  staff_user_id?: string;
  permissions?: string[];
  ext_info?: string | ExtInfo; // JSON 字符串
}

const getNativeLoginInfo = async (): Promise<NativeLoginInfo> => {
  return await jsApi.toNative('getLoginInfo');
};

export { getNativeLoginInfo };
