// @ts-ignore
const EventEmitter = require('react-native/Libraries/vendor/emitter/EventEmitter');
type EventSubscription = any;

class EventBus {
  private emitter: typeof EventEmitter;
  constructor() {
    this.emitter = new EventEmitter();
  }

  static single() {
    return new EventBus();
  }

  generateUUID() {
    let dt = new Date().getTime(); // 获取当前时间戳
    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
      /[xy]/g,
      function (c) {
        const r = (dt + Math.random() * 16) % 16 | 0;
        dt = Math.floor(dt / 16);
        return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
      }
    );
    return uuid;
  }

  addListener(
    eventType: string,
    listener: (...args: any[]) => any,
    context?: any
  ): EventSubscription {
    return this.emitter.addListener(eventType, listener, context);
  }

  addOnceListener(eventType: string, listener: (...args: any[]) => any) {
    const call = (...args: any[]) => {
      listener && listener.apply(null, args);
      subscription && subscription.remove();
    };
    const subscription: EventSubscription = this.emitter.addListener(
      eventType,
      call
    );
    return subscription;
  }

  emit(eventType: string, ...args: any[]) {
    this.emitter.emit(eventType, ...args);
  }
}

const EventBusInstance = EventBus.single();

export default EventBusInstance;
