import api, { FetchModule } from '@/common/api';
import { getNativeLoginInfo } from './getNativeLoginInfo';
import jsApi from '@soyoung/react-native-jsapi';
import { NotifyCrossStatusChangeFunc } from '../constant/cross_event';
import { updateParamsInfo } from './updateLoginExtInfo';

/**
 * 门店切换功能
 *
 * 功能描述：
 * 用于在 React Native 应用中切换用户的门店身份。当用户需要访问不同门店的数据时，
 * 调用此函数可以更新用户的登录信息和门店权限。
 *
 * 主要功能：
 * 1. 验证当前门店状态，避免重复切换
 * 2. 调用后端 API 切换门店
 * 3. 更新本地登录信息（token）
 * 4. 通知跨门店状态变更
 *
 * 使用场景：
 * - url 地址上有 tenant_id 和 tenant_user_id 参数时，会自动切换门店
 *
 * 参数说明：
 * @param tenantId - 目标门店ID
 * @param tenantUserId - 目标门店的用户ID
 *
 * 返回值：
 * @returns Promise<boolean> - 返回切换是否成功
 *   - true: 切换成功或无需切换（门店一致/已有穿越状态）
 *   - false: 切换失败（参数无效/API错误/网络异常）
 *
 * 使用示例：
 * ```typescript
 * // 在页面组件中使用
 * import { pushChangeTenant } from '@/common/pushChangeTenant';
 *
 * const MyComponent = () => {
 *   const router = useRoute();
 *
 *   const handleTenantChange = async () => {
 *     const tenantId = router.params?.params?.tenant_id;
 *     const tenantUserId = router.params?.params?.tenant_user_id;
 *
 *     if (tenantId && tenantUserId) {
 *       // 切换门店成功失败 再执行后续逻辑
 *       await pushChangeTenant(tenantId, tenantUserId);
 *     }
 *   };
 * };
 * ```
 *
 * 注意事项：
 * 1. 确保传入的 tenantId 和 tenantUserId 参数有效
 * 2. 函数内部会处理重复切换的情况，无需外部判断
 * 3. 切换成功后会更新本地 token，后续 API 调用将使用新的权限
 * 4. 建议在页面初始化时调用，确保数据权限正确
 *
 * 相关文件：
 * - 使用示例：src/views/income/page/performanceScore.tsx
 * - 依赖模块：@/common/api, @/common/getNativeLoginInfo
 * - 事件通知：@/constant/cross_event
 */

/**
 * 切换门店
 * @param tenantId 门店id
 * @param tenantUserId 门店用户id
 * @example sy.staff://rn/income/performanceScore?tenant_id=10009&type=2&tenant_user_id=39032
 * @returns 是否成功切换门店
 */
const pushChangeTenant = async (
  tenantId: string,
  tenantUserId: string
): Promise<boolean> => {
  if (!tenantId || !tenantUserId) {
    return false;
  }

  try {
    const storageAppInfo = await getNativeLoginInfo();
    const extInfo = storageAppInfo?.ext_info
      ? JSON.parse(storageAppInfo?.ext_info || '{}')
      : null;

    if (extInfo?.cross_token) {
      console.log('有穿越，不更新 token');
      return true; // 有穿越状态视为成功
    }
    console.log(
      'tenantId-pushChangeTenant',
      tenantId,
      storageAppInfo?.tenant_id
    );
    if (
      tenantId &&
      storageAppInfo?.tenant_id &&
      parseInt(tenantId, 10) === parseInt(storageAppInfo?.tenant_id, 10)
    ) {
      console.log('门店id一致，不更新 token');
      return true; // 门店id一致视为成功, 不更新 token，不切换门店
    }

    const res = await api.reactNativeFetch(
      '/chain-wxapp/v1/user/changeTenant',
      {
        tenant_user_id: tenantUserId,
        // tenant_id: tenant_id,
      },
      FetchModule.Method.POST
    );

    const { errorCode, responseData } = res;

    if (errorCode === 0 && responseData) {
      await updateParamsInfo({
        token: responseData.token,
        tenant_id: responseData.tenant_id.toString(),
        roles: responseData.roles,
        permissions: responseData.permissions,
        tenant_user_id: responseData.tenant_user_id.toString(),
      });
      NotifyCrossStatusChangeFunc({
        status: true,
      });
      return true; // 成功
    } else {
      console.error('pushChangeTenant API error:', errorCode, responseData);
      return false; // API 返回错误
    }
  } catch (error) {
    console.error('pushChangeTenant error:', error);
    return false; // 异常情况
  }
};

export { pushChangeTenant };
