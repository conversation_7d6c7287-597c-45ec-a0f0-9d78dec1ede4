import { nativeReportEvent, ReportType, traceEvent } from './syNativeModules';
export { ReportType } from './syNativeModules';

/**
 * 页面埋点是自动上报
 * 通过在页面的class组件定义
 * soyoungPageName?: Function;
 * soyoungPageInfo?: Function;
 * 实现
 */

/**
 * 事件上报方法
 * @param eventName 事件的名称，类型：string
 * @param eventInfo 事件的拓展参数，默认：{}
 * @param eventType 事件的类型，两种情况：0 = 正常，1 = 曝光；默认是正常，
 * @param uuid
 * @returns
 */
export default function (
  eventName: string,
  eventInfo: ReportType.ReportParams = {},
  eventType: ReportType.ReportEventType = ReportType.ReportEventType.NORMAL,
  uuid: string | undefined = undefined
): void {
  return nativeReportEvent(eventName, eventInfo, eventType, uuid);
}

/**
 * 事件上报方法
 * @param eventInfoClient 客户端添加的埋点，默认 {}
 * @param eventInfoServer 服务端下发的埋点，string
 * @param eventType 事件的类型，两种情况：1 = 点击，3 = 曝光；默认是点击，
 * @param eventName 页面名称 可以是空的
 * @param uuid
 * @returns
 */
export function reportEvent(
  eventInfoClient: ReportType.ReportEventInfoClient = {},
  eventInfoServer: string,
  eventType: ReportType.ReportEventType = ReportType.ReportEventType.NORMAL,
  eventName: string | undefined = undefined,
  uuid: string | undefined = undefined
): void {
  return traceEvent(
    eventInfoClient,
    eventInfoServer,
    eventType,
    eventName,
    uuid
  );
}
