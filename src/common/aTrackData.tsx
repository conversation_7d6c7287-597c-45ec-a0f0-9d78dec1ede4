interface Track {
  /**
   * 点击埋点名.
   * @type {string}
   */
  en_click?: string;
  /**
   * 曝光埋点名
   * @type {string}
   */
  en_exp?: string;
  /**
   * 客户端埋点的ext
   * @type {Object}
   */
  cep?: {
    [key: string]: any;
  };
}

const getATrackData = (track: Track) => {
  return {
    en: {
      en_click: track.en_click || null,
      en_exp: track.en_exp || null,
    },
    ep: JSON.stringify({
      backend: {},
    }),
    cep: track.cep || {},
  };
};

export { getATrackData };
