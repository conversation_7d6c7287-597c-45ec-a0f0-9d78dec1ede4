import { js2Native, BridgeModule } from './syNativeModules';
export namespace Bridge {
  export function getUuid(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
      /[xy]/g,
      function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      }
    );
  }
  export function showToast(message: string) {
    const callbackId = getUuid();
    js2Native(
      'showToast',
      {
        seqId: callbackId,
        request: {
          toast: message,
        },
      },
      () => {}
    );
  }
  /**
   * 验证是否登录
   * @returns Promise<CallbackData>
   */
  export function checkLogin(): Promise<BridgeModule.CallbackData> {
    return new Promise((resolve, reject) => {
      const callbackId = getUuid();
      js2Native(
        'isLogin',
        {
          seqId: callbackId,
        },
        function (error: any, seqId: any, callbackDataString: any) {
          if (error) {
            reject(seqId);
            return;
          }
          // 如果不是当前的回调
          if (callbackId !== seqId) {
            return;
          }
          let callbackData: BridgeModule.CallbackData = { status: '0' };
          try {
            //callbackData = JSON.parse(callbackDataString.replaceAll('\\', ''));
            callbackData = JSON.parse(callbackDataString.replace(/\\/g, ''));
          } catch (e) {
            console.error(e);
          } finally {
            // 调用成功
            const { status, uid, xy_token } = callbackData;
            if (status === '1' && uid && xy_token) {
              resolve(callbackData);
            } else {
              reject(seqId);
            }
          }
        }
      );
    });
  }
  /**
   * 登录
   * @returns Promise<CallbackData>
   */
  export function login(): Promise<any> {
    const callbackId = getUuid();
    return new Promise((resolve, reject) => {
      js2Native(
        'apploginplus',
        {
          seqId: callbackId,
        },
        function (error: any, seqId: any, callbackDataString: any) {
          if (error) {
            reject(seqId);
            return;
          }
          let callbackData: BridgeModule.CallbackData = { status: '0' };
          try {
            //callbackData = JSON.parse(callbackDataString.replaceAll('\\', ''));
            callbackData = JSON.parse(callbackDataString.replace(/\\/g, ''));
          } catch (e) {
            reject(e);
          } finally {
            // 调用成功
            const { status } = callbackData;
            if (status === '1') {
              resolve(status);
            } else {
              reject(status);
            }
          }
        }
      );
    });
  }
  export function getAppInfo(): Promise<any> {
    const callbackId = getUuid();
    return new Promise((resolve, reject) => {
      js2Native(
        'getAppInfo',
        {
          seqId: callbackId,
        },
        function (error: any, seqId: any, callbackDataString: any) {
          if (error) {
            reject(seqId);
            return;
          }
          let callbackData: BridgeModule.CallbackData = { status: '0' };
          try {
            //callbackData = JSON.parse(callbackDataString.replaceAll('\\', ''));
            callbackData = JSON.parse(callbackDataString.replace(/\\/g, ''));
          } catch (e) {
            reject(e);
          } finally {
            // 调用成功
            const { status } = callbackData;
            if (status === '1') {
              resolve(callbackData);
            } else {
              reject(callbackData);
            }
          }
        }
      );
    });
  }
  export function channelFoldClick(data: any): Promise<any> {
    const callbackId = getUuid();
    return new Promise((resolve, reject) => {
      js2Native(
        'channelFoldClick',
        {
          seqId: callbackId,
          ...data,
        },
        function (error: any, seqId: any, callbackDataString: any) {
          if (error) {
            reject(seqId);
            return;
          }
          let callbackData: BridgeModule.CallbackData = { status: '0' };
          try {
            //callbackData = JSON.parse(callbackDataString.replaceAll('\\', ''));
            callbackData = JSON.parse(callbackDataString.replace(/\\/g, ''));
          } catch (e) {
            reject(e);
          } finally {
            // 调用成功
            const { status } = callbackData;
            if (status === '1') {
              resolve(callbackData);
            } else {
              reject(callbackData);
            }
          }
        }
      );
    });
  }
  export function shareAction(data: any): Promise<any> {
    const callbackId = getUuid();
    return new Promise((resolve, reject) => {
      js2Native(
        'shareAction',
        {
          seqId: callbackId,
          ...data,
        },
        function (error: any, seqId: any, callbackDataString: any) {
          if (error) {
            reject(seqId);
            return;
          }
          let callbackData: BridgeModule.CallbackData = { status: '0' };
          try {
            callbackData = JSON.parse(callbackDataString.replace(/\\/g, ''));
          } catch (e) {
            reject(e);
          } finally {
            // 调用成功
            const { status } = callbackData;
            if (status === '1') {
              console.log('调用成功');
              resolve(callbackData);
            } else {
              reject(callbackData);
            }
          }
        }
      );
    });
  }
  export function getLocationPermission(data: any): Promise<any> {
    const callbackId = getUuid();
    return new Promise((resolve, reject) => {
      js2Native(
        'getLocationPermission',
        {
          seqId: callbackId,
          request: {
            ...data,
          },
        },
        function (error: any, seqId: any, callbackDataString: any) {
          if (error) {
            reject(seqId);
            return;
          }
          let callbackData: BridgeModule.CallbackData = { status: '0' };
          try {
            //callbackData = JSON.parse(callbackDataString.replaceAll('\\', ''));
            callbackData = JSON.parse(callbackDataString.replace(/\\/g, ''));
          } catch (e) {
            reject(e);
          } finally {
            // 调用成功
            const { status } = callbackData;
            if (status) {
              resolve(callbackData);
            } else {
              reject(callbackData);
            }
          }
        }
      );
    });
  }
  export function openCall(tel: string) {
    const callbackId = getUuid();
    js2Native(
      'openCall',
      {
        seqId: callbackId,
        request: {
          tel: tel,
        },
      },
      () => {}
    );
  }

  export function turnDoctorAllDynamic(url: string, interaction: string) {
    const callbackId = getUuid();
    js2Native(
      'turnDoctorAllDynamic',
      {
        seqId: callbackId,
        request: {
          url: url,
          interaction: interaction,
        },
      },
      () => {}
    );
  }

  export function configDoctorQktService(doctorId: string, onScroll: boolean) {
    const callbackId = getUuid();
    js2Native(
      'configQktService',
      {
        seqId: callbackId,
        request: {
          source_type: '5',
          index_id: doctorId,
          onScrolled: onScroll,
        },
      },
      () => {}
    );
  }

  export function configProductDetailQktService(
    prodcutId: string,
    onScroll: boolean,
    hospital_id: string,
    certified_id: string
  ) {
    const callbackId = getUuid();
    js2Native(
      'configQktService',
      {
        seqId: callbackId,
        request: {
          source_type: '4',
          index_id: prodcutId || '',
          pid: prodcutId || '',
          onScrolled: onScroll,
          hospital_id: hospital_id || '',
          certified_id: certified_id || '',
          ignore_check_page: '1',
        },
      },
      () => {}
    );
  }
}
