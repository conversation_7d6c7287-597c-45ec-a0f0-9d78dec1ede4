# staffRnApp

新氧员工APP React Native工程

## 起步
1. 确保nrm已经安装，如果没有安装，执行`npm install -g nrm`安装nrm
2. 确保nvm已经安装，如果没有安装，执行`brew install nvm`安装nvm
3. 执行`nvm install 16.20.2 `安装node 16.20.2
4. 执行`nvm use 16.20.2`切换node版本
5. 切换nrm源，执行`nrm use sy`
6. 执行`npm install`安装依赖
7. 执行`npm run start`启动项目
8. 浏览器自动打开二维码页面，扫描二维码即可在手机上查看项目
9. 都需要手机上有安装好的主App小眼睛包。
   安卓需要先进入app然后打开扫码验真看
   ios不需要先进入app，直接系统相机扫点击就能打开
10. 



## 发布到预发布和线上

1. 合并代码到master分支
2. 打开https://jenkins.sy.soyoung.com/job/staffrnapp_unpacking/ 看上一次打包的版本号
3. jenkins上点击参数构建，version在上一次打包的版本号基础上+1。基础包内容看情况是否也要修改，如不知道沿用上次。构建分支选择master
4. 构建完成后，打开 http://maven1.sy.soyoung.com/#browse/browse:RN:staff_upacking%2Forigin_master 下载对应的包
5. 打开发布系统 https://sdeploy.sy.soyoung.com/codepush
6. 选择 "iOS员工-正式"，联调环境不带分区就是预发布
7. 上传包，并完善上线信息，点击发布
8. 带小眼睛的app包，切换到预发布环境，热更新，下次生效
9. android步骤同iOS一致，再来一次

# sy-chain → staffrnapp 迁移指南

## 📋 项目概述
- **源项目**: sy-chain (uni-app Vue 小程序)
- **目标项目**: staffrnapp (React Native APP) 
- **核心目标**: 保持功能和UI完整一致性

## 🔄 技术栈转换对照

| 技术点 | uni-app | React Native | 复杂度 |
|-------|---------|--------------|--------|
| **语法** | Vue Options/Composition | React Hooks | 🔴 高 |
| **模板** | `<template>` | JSX | 🔴 高 |
| **样式** | CSS + rpx | StyleSheet | 🟡 中 |
| **组件** | `<view>`, `<text>` | `<View>`, `<Text>` | 🟡 中 |
| **状态** | Vue Data | useState | 🔴 高 |
| **API** | uni.request() | api.fetch | 🟡 中 |
| **导航** | uni.navigateTo() | navigation.navigate | 🟡 中 |

## 🛠 迁移步骤

### Phase 1: 源码分析 (2-4h)
**分析清单**:
- [ ] `.vue` 文件结构 (`<template>`, `<script>`, `<style>`)
- [ ] 生命周期 (`onLoad`, `onShow`, `onHide`)
- [ ] 状态管理 (data, computed, watch, methods)
- [ ] uni-app API 使用 (uni.xxx 方法)
- [ ] 样式系统 (CSS/SCSS, rpx 单位)
- [ ] 事件处理和业务逻辑

**关键分析点**:
1. 数据流: Vue data → computed → methods
2. 生命周期时机: 何时获取数据、更新状态
3. 用户交互: 表单、列表、导航等
4. 异步处理: API调用、loading、错误处理

### Phase 2: 架构设计 (1-2h)

#### 2.1 🎯 核心：页面埋点架构规范
**⚠️ 重要：所有新建 RN 页面必须采用统一的埋点架构模式**

**标准页面结构**：
```typescript
// index.tsx - 页面埋点壳组件（Class Component）
class PageName extends React.Component<PageProps> {
  constructor(props: PageProps) {
    super(props);
    this.state = {
      pageShow: true,
    };
  }

  // 页面名称（用于埋点）
  soyoungPageName() {
    return "PageName";
  }

  // 页面埋点信息
  soyoungPageInfo() {
    return {
      page_name: "页面中文名",
      page_type: "page_type_value"
    };
  }

  // 页面显示生命周期
  didAppear() {
    this.setState({ pageShow: true });
  }

  // 页面隐藏生命周期
  willDisappear() {
    this.setState({ pageShow: false });
  }

  render() {
    return (
      <PageNameTarget {...this.props} pageShowFlag={(this.state as any).pageShow} />
    );
  }
}

export default PageName;
```

**目录结构**:
```
staffrnapp/src/views/[页面名]/
├── index.tsx          # 页面埋点壳组件（Class Component）
├── [PageName]Target.tsx  # 实际页面业务组件（Function Component）
├── components/        # 子组件
├── hooks/            # 自定义hooks
├── types.ts          # 类型定义
└── styles.ts         # 样式文件
```

**架构优势**：
- 🎯 **统一埋点管理**：所有页面埋点逻辑统一在 Class 组件中
- 🔄 **生命周期统一**：didAppear/willDisappear 标准化
- 📊 **数据收集规范**：soyoungPageName/soyoungPageInfo 标准化
- 🛠 **业务逻辑分离**：页面业务逻辑在 Target 组件中，保持清晰
- 📱 **pageShowFlag 传递**：页面显示状态统一管理

#### 2.2 目录和组件规范

### Phase 3: 组件转换 (6-10h)

#### 3.1 Template → JSX 转换规则
| Vue 语法 | RN JSX | 说明 |
|---------|--------|------|
| `<template>` | `return (...)` | 模板重构 |
| `<view>` | `<View>` | 直接对应 |
| `<text>` | `<Text>` | 直接对应 |
| `<button>` | `<TouchableOpacity>` | 需重构 |
| `<input>` | `<TextInput>` | 属性调整 |
| `v-if` | `{condition && </>}` | 条件渲染 |
| `v-for` | `{list.map(...)}` | 列表渲染 |
| `v-model` | `value + onChangeText` | 双向绑定 |
| `@click` | `onPress` | 事件处理 |

#### 3.2 Script → Hooks 转换
```typescript
// Vue 示例
export default {
  data() { return { list: [], loading: false } },
  computed: { filteredList() { return this.list.filter(...) } },
  onLoad() { this.fetchData() },
  methods: { async fetchData() {...} }
}

// RN 转换后
const PageComponent = () => {
  const [list, setList] = useState([]);           // data → useState
  const [loading, setLoading] = useState(false);
  
  const filteredList = useMemo(() => {            // computed → useMemo
    return list.filter(item => item.active);
  }, [list]);
  
  const fetchData = useCallback(async () => {     // methods → useCallback
    setLoading(true);
    const response = await api.fetch('/api/data');
    setList(response.data);
    setLoading(false);
  }, []);
  
  useFocusEffect(useCallback(() => {             // onLoad → useFocusEffect
    fetchData();
  }, [fetchData]));
};
```

#### 3.3 生命周期对照
| uni-app | React Native | 说明 |
|---------|--------------|------|
| `onLoad` | `useEffect(() => {}, [])` | 页面加载 |
| `onShow` | `useFocusEffect()` | 页面显示 |
| `onHide` | `useFocusEffect` 清理函数 | 页面隐藏 |
| `onUnload` | `useEffect` 清理函数 | 页面卸载 |

### Phase 4: 样式迁移 (4-6h)

#### 4.1 CSS → StyleSheet 基础转换
```scss
// uni-app 样式
.container {
  padding: 30rpx;
  background-color: #f5f5f5;
}
```

```typescript
// RN 转换后
const styles = StyleSheet.create({
  container: {
    padding: getRealSize(30),
    backgroundColor: '#f5f5f5',
  },
});
```

#### 4.2 🎯 核心：rpx 单位转换
**⚠️ 重要：统一采用 getRealSize() 方法，确保不同机型适配**

```typescript
import { Dimensions, PixelRatio } from 'react-native';

// 设计稿基准宽度 (375px)
const DESIGN_WIDTH = 375;

// 核心尺寸转换函数
export const getRealSize = (size: number): number => {
  const { width: screenWidth } = Dimensions.get('window');
  const scale = screenWidth / DESIGN_WIDTH;
  const realSize = size * scale;
  return PixelRatio.roundToNearestPixel(realSize);
};

// 快捷别名
export const rpx = getRealSize;

// 扩展工具
export const responsive = {
  getRealSize,
  rpx: getRealSize,
  // 字体适配
  font: (size: number): number => {
    const fontSize = getRealSize(size);
    const fontScale = PixelRatio.getFontScale();
    return fontSize / fontScale;
  },
  // 平板检测
  isTablet: (): boolean => {
    const { width, height } = Dimensions.get('window');
    return Math.min(width, height) >= 768;
  },
};
```

#### 4.3 使用示例
```typescript
// 使用 getRealSize 处理所有尺寸
const styles = StyleSheet.create({
  container: {
    width: getRealSize(375),          // 全屏宽
    padding: getRealSize(30),         // 30rpx 内边距
  },
  text: {
    fontSize: responsive.font(32),    // 字体适配
    lineHeight: getRealSize(48),      // 行高
  },
});
```

### Phase 5: API和数据层 (3-5h)

#### 5.1 API调用转换
```typescript
// uni-app
uni.request({
  url: '/api/user/info',
  method: 'POST',
  data: { id: 123 },
  success: (res) => { this.userInfo = res.data; }
});

// RN (使用项目现有api模块)
const fetchUserInfo = async (id: number) => {
  try {
    setLoading(true);
    const response = await api.fetch('/api/user/info', { id }, 'POST');
    if (response.errorCode === 0) {
      setUserInfo(response.data);
    }
  } finally {
    setLoading(false);
  }
};
```

#### 5.2 存储转换
```typescript
// uni-app
uni.setStorageSync('key', 'value');

// RN AsyncStorage
import AsyncStorage from '@react-native-async-storage/async-storage';
await AsyncStorage.setItem('key', 'value');
```

### Phase 5.5: 🎯 核心：弹窗迁移 (2-3h)
**⚠️ 重要：所有半弹窗统一采用 react-native-modal 库**

#### 5.5.1 安装和基础组件
```bash
# 安装依赖
npm install react-native-modal
cd ios && pod install
```

```typescript
// 通用弹窗基础组件
import Modal from 'react-native-modal';
import { getRealSize } from '../utils/responsive';

export const BaseModal: React.FC<{
  visible: boolean;
  onClose: () => void;
  children: React.ReactNode;
  position?: 'bottom' | 'center' | 'top';
}> = ({ visible, onClose, children, position = 'bottom' }) => {
  return (
    <Modal
      isVisible={visible}
      onBackdropPress={onClose}
      animationIn={position === 'bottom' ? 'slideInUp' : 'fadeIn'}
      animationOut={position === 'bottom' ? 'slideOutDown' : 'fadeOut'}
      style={{ margin: 0, justifyContent: position === 'bottom' ? 'flex-end' : 'center' }}
    >
      <View style={{ backgroundColor: '#fff', borderRadius: getRealSize(20) }}>
        {children}
      </View>
    </Modal>
  );
};
```

#### 5.5.2 常用组件实现
```typescript
// ActionSheet (替代 uni.showActionSheet)
export const ActionSheet = ({ visible, onClose, options, title }) => (
  <BaseModal visible={visible} onClose={onClose} position="bottom">
    {title && <Text>{title}</Text>}
    {options.map(option => (
      <TouchableOpacity onPress={() => { option.onPress(); onClose(); }}>
        <Text>{option.text}</Text>
      </TouchableOpacity>
    ))}
  </BaseModal>
);

// PickerModal (替代 uni.showPicker)
export const PickerModal = ({ visible, onClose, onConfirm, data, title }) => (
  <BaseModal visible={visible} onClose={onClose} position="bottom">
    <View>
      <Text>{title}</Text>
      <TouchableOpacity onPress={onClose}><Text>取消</Text></TouchableOpacity>
      <TouchableOpacity onPress={() => onConfirm(selectedValue)}><Text>确定</Text></TouchableOpacity>
    </View>
  </BaseModal>
);
```

#### 5.5.3 弹窗迁移对照表
| uni-app 弹窗 | React Native 替代 | 复杂度 |
|-------------|------------------|--------|
| `uni.showModal()` | `BaseModal` + 内容 | 🟡 中 |
| `uni.showActionSheet()` | `ActionSheet` | 🟢 低 |
| `uni.showToast()` | `toast-message` | 🟢 低 |
| `uni.showPicker()` | `PickerModal` | 🟡 中 |
| 半屏弹窗 | `BaseModal` bottom | 🟡 中 |
| 居中对话框 | `BaseModal` center | 🟡 中 |

### Phase 6: 导航系统迁移 (2-3小时)

#### 6.1 页面导航转换

```typescript
// ✅ uni-app 导航
uni.navigateTo({ url: '/pages/detail/index?id=123' });
uni.navigateBack({ delta: 1 });
uni.redirectTo({ url: '/pages/login/index' });

// ✅ React Native 导航 (使用项目现有导航)
import { jumpPage } from '../../common/jumpPage';
jumpPage('/pages/detail/index?id=123');
navigation.navigate('Detail', { id: 123 });
navigation.goBack();
```

### Phase 7: 测试与验证 (3-4小时)

#### 7.1 功能完整性测试

**测试清单**:
- [ ] **页面渲染**: 所有内容正确显示，无 UI 异常
- [ ] **用户交互**: 按钮点击、输入响应、滑动操作等
- [ ] **数据流**: 状态更新、API 调用、数据绑定
- [ ] **页面导航**: 跳转、返回、参数传递
- [ ] **生命周期**: 页面加载、显示、隐藏时的逻辑
- [ ] **异常处理**: 网络错误、加载失败、边界情况
- [ ] **性能表现**: 页面加载速度、滚动流畅度

#### 7.2 跨平台兼容性测试

```typescript
// 平台差异处理
import { Platform } from 'react-native';

const styles = StyleSheet.create({
  container: {
    paddingTop: Platform.select({
      ios: 44, // iOS 安全区域
      android: 25, // Android 状态栏
    }),
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 5,
      },
    }),
  },
});
```

### Phase 8: 代码质量保证 (2-3小时)

#### 8.1 TypeScript 类型完善

```typescript
// 完整的类型定义示例
export interface PageProps {
  navigation: NavigationProp<any>;
  route: RouteProp<any>;
}

export interface PageState {
  loading: boolean;
  data: DataItem[];
  error: string | null;
}

export interface DataItem {
  id: number;
  title: string;
  description: string;
  createTime: string;
  status: 'active' | 'inactive' | 'pending';
}
```

#### 8.2 ESLint 修复和代码规范

```bash
# 代码检查和修复
npx eslint src/views/[页面名] --ext .tsx,.ts --fix
npx tsc --noEmit
npx prettier --write "src/views/[页面名]/**/*.{ts,tsx}"
```

## 📅 迁移实施计划

### 时间分配 (总计 20-30 小时)

| 阶段 | 时间 | 主要任务 | 交付成果 |
|-----|------|---------|----------|
| Phase 1 | 2-4h | 源码深度分析 | 完整的代码分析报告 |
| Phase 2 | 1-2h | 架构设计 | 技术方案和目录结构 |
| Phase 3 | 6-10h | 核心组件转换 | 完整的页面组件 |
| Phase 4 | 4-6h | 样式系统迁移 | StyleSheet 样式文件 |
| Phase 5 | 3-5h | API 数据层 | 完整的数据层逻辑 |
| Phase 6 | 2-3h | 导航系统 | 页面跳转逻辑 |
| Phase 7 | 3-4h | 测试验证 | 功能测试报告 |
| Phase 8 | 2-3h | 代码质量 | Lint 通过的规范代码 |

### 关键里程碑

1. **第一里程碑** (Phase 1-2 完成): 源码分析和技术方案确定
2. **第二里程碑** (Phase 3-4 完成): 页面基础结构和样式完成
3. **第三里程碑** (Phase 5-6 完成): 完整功能实现
4. **最终里程碑** (Phase 7-8 完成): 测试通过，代码规范

## ⚠️ 风险识别与应对

### 高风险项

1. **Vue → React 范式转换**
   - **风险**: Options API 到 Hooks 的思维转换复杂
   - **应对**: 先理解 Vue 逻辑，再设计对应的 Hook 结构

2. **复杂样式布局**
   - **风险**: CSS 到 StyleSheet 可能存在布局差异
   - **应对**: 逐个对比调试，必要时重构布局方案

3. **异步逻辑和状态管理**
   - **风险**: Vue 的 data/computed/watch 转换为 React state 可能出现逻辑错误
   - **应对**: 深入理解原有数据流，设计合理的 Hook 组合

### 中风险项

1. **第三方依赖适配**
   - **风险**: uni-app 插件在 RN 中无法使用
   - **应对**: 寻找 RN 替代方案或自行实现

2. **性能差异**
   - **风险**: RN 渲染性能与小程序不同
   - **应对**: 使用 React.memo、useMemo 等优化手段

## ✅ 质量标准

### 功能标准
- ✅ 业务逻辑 100% 还原
- ✅ 交互体验与原页面一致
- ✅ API 调用成功率 100%
- ✅ 异常处理完整覆盖

### 代码标准
- ✅ TypeScript 严格模式无错误
- ✅ ESLint 规范检查通过
- ✅ 组件复用度 ≥ 80%
- ✅ 代码注释覆盖率 ≥ 60%

### 性能标准
- ✅ 页面首次渲染 < 1s
- ✅ 列表滚动 FPS ≥ 55
- ✅ 内存使用稳定，无明显泄漏
- ✅ 支持 iOS 和 Android 双端

## 🚀 开始执行

等待您指定具体要迁移的页面后，将按照以上 Workflow 开始执行：

```bash
# 1. 告知要迁移的页面路径
echo "请指定 sy-chain 中要迁移的页面路径"

# 2. 开始深度分析源码
find sy-chain/pages/[指定页面] -name "*.vue"

# 3. 按 Phase 逐步执行迁移
```

> **注意**: 每完成一个 Phase，都要测试验证，确保迁移质量。同时会根据具体页面的复杂程度动态调整时间分配和技术方案。