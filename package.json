{"name": "staffRnApp", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "node ./prestart.js && react-native start --reset-cache", "test": "jest", "lint": "eslint src/ --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src/ --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx}\"", "type-check": "tsc --noEmit", "prepare": "husky", "patch": "patch-package", "patch-ios": "sy-ios-patch", "patch-android": "sy-android-patch", "patch-js": "sy-js-patch", "patch-split-bundle": "sy-split-bundle-patch", "postinstall": "npm run patch-split-bundle && npm run patch-js && npm run patch-ios && npm run patch-android"}, "devDependencies": {"@babel/core": "^7.12.9", "@babel/plugin-proposal-decorators": "^7.22.3", "@babel/runtime": "^7.12.5", "@react-native-community/eslint-config": "3.2.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/jest": "^29.5.4", "@types/lodash-es": "^4.17.7", "@types/react": "17.0.2", "@types/react-native": "0.68.4", "@types/react-test-renderer": "^17.0.2", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "babel-jest": "^26.6.3", "babel-plugin-module-resolver": "^5.0.2", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "8.48.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react-hooks": "^4.6.2", "husky": "^9.1.7", "jest": "^26.6.3", "lint-staged": "^16.1.2", "metro-react-native-babel-preset": "^0.67.0", "patch-package": "^6.4.7", "prettier": "^3.3.3", "react-test-renderer": "17.0.2", "typescript": "^5.0.4"}, "resolutions": {"@types/react": "^17"}, "jest": {"preset": "react-native", "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "dependencies": {"@soyoung/react-native-base": "^0.1.166", "@soyoung/react-native-entry": "1.0.58", "@soyoung/react-native-image-crop-picker": "0.41.8", "dayjs": "^1.11.13", "react-native": "0.68.4", "react-native-chart-kit": "6.12.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-modal": "13.0.1", "react-native-svg": "12.5.1"}, "engines": {"node": ">= 16.0.0", "npm": ">= 8.0.0"}, "volta": {"node": "22.17.1", "npm": "10.9.2"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["prettier --write", "bash -c 'eslint --fix \"$@\" || true' --"], "src/**/*.{json,md}": ["prettier --write"]}}